<launch>
<!-- <param name="use_sim_time" value="true"/> -->
  <!-- Use this to debug aggregator node in GDB -->
  <!-- launch-prefix="xterm -e gdb -args"  -->
  <node 
        pkg="cotek_diagnostic" type="cotek_diagnostic_node"
        name="cotek_diagnostic_node" args="CPP" respawn="true" output="screen" >
    <rosparam command="load" 
              file="$(find cotek_diagnostic)/config/cotek_diagnostic_params.yaml" />
  </node>

  <!-- <node 
        pkg="diagnostic_aggregator" type="aggregator_node"
        name="diag_agg" args="CPP" output="screen" >
    <rosparam command="load" 
              file="$(find cotek_diagnostic)/config/device_analyzers.yaml" />
  </node> -->
</launch>
