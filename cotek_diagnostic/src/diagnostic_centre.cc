/**
 *  Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_diagnostic/diagnostic_centre.h"

#include <cstdint>
#include <exception>
#include <string>

#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/node_diagnostic_translator.h"
#include "cotek_msgs/agv_position.h"
#include "cotek_msgs/module_check.h"
#include "cotek_msgs/self_check.h"
#include "device_interface.h"
#include "ros/duration.h"
#include "ros/time.h"

namespace cotek_diagnostic {
using namespace cotek_topic;
using namespace install_device_name_list;

constexpr char kMessageCacheSize = 2;

DiagnosticCentre::DiagnosticCentre(const DiagnosticOption& option)
    : option_(option), run_executor_(nullptr) {
  ros::NodeHandle nh;
  start_time_ = ros::Time::now();
  fault_report_pub_ = nh.advertise<cotek_msgs::fault_report>(
      cotek_topic::kFaultReportTopic, 20);

  self_check_pub_ =
      nh.advertise<cotek_msgs::self_check>(cotek_topic::kSelfCheckTopic, 20);
}

void DiagnosticCentre::Run() {
  run_executor_ =
      std::make_shared<std::thread>(std::bind(&DiagnosticCentre::Runner, this));
  LOG_INFO("diagnostic centre run.");
}

void DiagnosticCentre::SelfCheck() {
  // 上电延时发送
  if (ros::Time::now() - start_time_ <
      ros::Duration(option_.fault_report_delay)) {
    return;
  }

  try {
    // 网络连接检查
    cotek_msgs::module_check communicate_ret;
    communicate_ret.result = true;
    communicate_ret.type = "与服务器通讯/communicate with the server";
    if (device_map_.count(kCommunicateDiagnosticTopic) > 0) {
      for (auto& error :
           device_map_.at(kCommunicateDiagnosticTopic)->GetFaultMsg().errors) {
        if (error >
            static_cast<int>(cotek_diagnostic::CommunicateNodeStatus::WARN)) {
          communicate_ret.result = false;
          communicate_ret.info.push_back(GetDiagnosticTranlator(error));
        }
      }
    }

    cotek_msgs::module_check motor_ret;
    motor_ret.type = "底盘传感器/sensor";
    motor_ret.result = true;
    if (device_map_.count(kRunnerNodeDiagnosticTopic) > 0) {
      for (auto& error :
           device_map_.at(kRunnerNodeDiagnosticTopic)->GetFaultMsg().errors) {
        // 添加必要传感器错误中英文注释
        if (error >= static_cast<int>(
                         cotek_diagnostic::RunnerNodeStatus::CONFIG_ERROR) &&
            error <= static_cast<int>(
                         cotek_diagnostic::RunnerNodeStatus::IO2_ERROR)) {
          motor_ret.result = false;
          motor_ret.info.push_back(GetDiagnosticTranlator(error));
        }
      }
    }

    // 避障激光自检检查
    cotek_msgs::module_check avoid_laser_ret;
    avoid_laser_ret.result = true;
    avoid_laser_ret.type = "避障激光/avoid laser";
    if (device_map_.count(kAvoidDiagnosticTopic) > 0) {
      for (auto& error :
           device_map_.at(kAvoidDiagnosticTopic)->GetFaultMsg().errors) {
        if (error >=
                static_cast<int>(
                    cotek_diagnostic::AvoidNodeStatus::AVOID_LASER_0_TIMEOUT) &&
            error <=
                static_cast<int>(
                    cotek_diagnostic::AvoidNodeStatus::AVOID_LASER_5_TIMEOUT)) {
          avoid_laser_ret.result = false;
          avoid_laser_ret.info.push_back(GetDiagnosticTranlator(error));
        }
      }
    }

    cotek_msgs::module_check navi_laser_ret;
    navi_laser_ret.result = true;
    navi_laser_ret.type = "导航激光/navigation laser";
    if (device_map_.count(kAvoidDiagnosticTopic) > 0) {
      for (auto& error :
           device_map_.at(kAvoidDiagnosticTopic)->GetFaultMsg().errors) {
        if (error >= static_cast<int>(
                         cotek_diagnostic::AvoidNodeStatus::LASER_0_TIMEOUT) &&
            error <= static_cast<int>(
                         cotek_diagnostic::AvoidNodeStatus::LASER_2_TIMEOUT)) {
          navi_laser_ret.result = false;
          navi_laser_ret.info.push_back(GetDiagnosticTranlator(error));
        }
      }
    }

    cotek_msgs::module_check avoid_camera_ret;
    avoid_camera_ret.result = true;
    avoid_camera_ret.type = "避障相机/avoid cmaera";
    if (device_map_.count(kAvoidDiagnosticTopic) > 0) {
      for (auto& error :
           device_map_.at(kAvoidDiagnosticTopic)->GetFaultMsg().errors) {
        if (error >= static_cast<int>(cotek_diagnostic::AvoidNodeStatus::
                                          AVOID_CAMERA_0_TIMEOUT) &&
            error <= static_cast<int>(cotek_diagnostic::AvoidNodeStatus::
                                          AVOID_CAMERA_5_TIMEOUT)) {
          avoid_camera_ret.result = false;
          avoid_camera_ret.info.push_back(GetDiagnosticTranlator(error));
        }
      }
    }

    // 定位检查
    cotek_msgs::module_check location_ret;
    location_ret.result = true;
    location_ret.type = "定位/location";
    if (ros::Time::now() - agv_position_.header.stamp > ros::Duration(1.0) ||
        agv_position_.position_initialized == false) {
      location_ret.result = false;
      location_ret.info.push_back(GetDiagnosticTranlator(static_cast<uint16_t>(
          LocalizerNodeStatus::LOCALIZER_INIT_POSE_ERROR)));
    }

    cotek_msgs::self_check msg;

    if (communicate_ret.result && avoid_laser_ret.result && motor_ret.result &&
        navi_laser_ret.result && avoid_camera_ret.result &&
        location_ret.result) {
      msg.result = true;
    }

    msg.infos.push_back(communicate_ret);
    msg.infos.push_back(motor_ret);
    msg.infos.push_back(avoid_laser_ret);
    msg.infos.push_back(navi_laser_ret);
    msg.infos.push_back(avoid_camera_ret);
    msg.infos.push_back(location_ret);
    msg.header.stamp = ros::Time::now();
    self_check_pub_.publish(msg);

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void DiagnosticCentre::Runner() {
  ros::Rate rate(option_.control_frequency);
  while (ros::ok()) {
    LOG_DEBUG("[DiagnosticCentre Cycle]");

    SelfCheck();

    Diagnostic2Decision();

    if (rate.cycleTime() > ros::Duration(1.0 / option_.control_frequency)) {
      LOG_WARN(
          "Control loop missed its desired rate of %.2fHz... the heartbeat "
          "actually took %.2f seconds",
          option_.control_frequency, rate.cycleTime().toSec());
    }
    // sleep to make sure the control frequency
    rate.sleep();
  }
}

void DiagnosticCentre::Diagnostic2Decision() {
  // 上电延时发送
  if (ros::Time::now() - start_time_ <
      ros::Duration(option_.fault_report_delay)) {
    return;
  }
  UpdateFaultReport();
  fault_report_.header.stamp = ros::Time::now();
  fault_report_pub_.publish(fault_report_);
  fault_report_.errors.clear();
}

// update FaultReport
void DiagnosticCentre::UpdateFaultReport() {
  for (auto& device : device_map_) {
    FaultMsg&& msg = device.second->GetFaultMsg();
    if (!msg.level.empty()) {
      cotek_msgs::error error;
      error.error_type = device.first;
      error.error_level = msg.level;
      for (auto& temp : msg.errors) {
        cotek_msgs::error_reference reference;
        reference.reference_key = temp;
        error.error_references.push_back(reference);
      }
      fault_report_.errors.push_back(error);
    }
  }
}

void DiagnosticCentre::HandleAgvpositionData(
    const cotek_msgs::agv_position::ConstPtr& data) {
  agv_position_ = *data;
}

bool DiagnosticCentre::Init(ros::NodeHandle* nh) {
  // clear subscribers before init
  subscriber_.clear();
  device_map_.clear();

  subscriber_.emplace_back(nh->subscribe<cotek_msgs::agv_position>(
      cotek_topic::kAgvPositionTopic, kMessageCacheSize,
      boost::bind(&DiagnosticCentre::HandleAgvpositionData, this, _1)));

  // add node list
  device_map_.emplace(kLocalizerDiagnosticTopic,
                      std::make_shared<NodeInterface>(
                          kLocalizerDiagnosticTopic,
                          static_cast<uint16_t>(LocalizerNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kLocalizerDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kLocalizerDiagnosticTopic), _1)));

  device_map_.emplace(kRunnerNodeDiagnosticTopic,
                      std::make_shared<NodeInterface>(
                          kRunnerNodeDiagnosticTopic,
                          static_cast<uint16_t>(RunnerNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kRunnerNodeDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kRunnerNodeDiagnosticTopic), _1)));

  device_map_.emplace(
      kCalibrationDiagnosticTopic,
      std::make_shared<NodeInterface>(
          kCalibrationDiagnosticTopic,
          static_cast<uint16_t>(CalibrationNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kCalibrationDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kCalibrationDiagnosticTopic), _1)));

  device_map_.emplace(kActionDiagnosticTopic,
                      std::make_shared<NodeInterface>(
                          kActionDiagnosticTopic,
                          static_cast<uint16_t>(ActionNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kActionDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kActionDiagnosticTopic), _1)));

  device_map_.emplace(kNavigationDiagnosticTopic,
                      std::make_shared<NodeInterface>(
                          kNavigationDiagnosticTopic,
                          static_cast<uint16_t>(NavigationNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kNavigationDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kNavigationDiagnosticTopic), _1)));

  device_map_.emplace(
      kCommunicateDiagnosticTopic,
      std::make_shared<NodeInterface>(
          kCommunicateDiagnosticTopic,
          static_cast<uint16_t>(CommunicateNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kCommunicateDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kCommunicateDiagnosticTopic), _1)));

  device_map_.emplace(kAvoidDiagnosticTopic,
                      std::make_shared<NodeInterface>(
                          kAvoidDiagnosticTopic,
                          static_cast<uint16_t>(AvoidNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kAvoidDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kAvoidDiagnosticTopic), _1)));

  device_map_.emplace(
      kDecisionDiagnosticTopic,
      std::make_shared<NodeInterface>(
          kDecisionDiagnosticTopic,
          static_cast<uint16_t>(DecisionMakerNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kDecisionDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kDecisionDiagnosticTopic), _1)));

  device_map_.emplace(kStorageDiagnosticTopic,
                      std::make_shared<NodeInterface>(
                          kStorageDiagnosticTopic,
                          static_cast<uint16_t>(StorageNodeStatus::NORMAL)));
  subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
      cotek_topic::kStorageDiagnosticTopic, kMessageCacheSize,
      boost::bind(&DeviceInterface::HandleNodeData<
                      cotek_msgs::node_diagnostic::ConstPtr>,
                  device_map_.at(kStorageDiagnosticTopic), _1)));

  if (1) {
    device_map_.emplace(kVisaulDiagnosticTopic,
                        std::make_shared<NodeInterface>(
                            kVisaulDiagnosticTopic,
                            static_cast<uint16_t>(VisualNodeStatus::NORMAL)));
    subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
        cotek_topic::kVisaulDiagnosticTopic, kMessageCacheSize,
        boost::bind(&DeviceInterface::HandleNodeData<
                        cotek_msgs::node_diagnostic::ConstPtr>,
                    device_map_.at(kVisaulDiagnosticTopic), _1)));
  }
  if (1) {
    device_map_.emplace(kCartoRosDiagnosticTopic,
                        std::make_shared<NodeInterface>(
                            kCartoRosDiagnosticTopic,
                            static_cast<uint16_t>(CartoRosNodeStatus::NORMAL)));
    subscriber_.emplace_back(nh->subscribe<cotek_msgs::node_diagnostic>(
        cotek_topic::kCartoRosDiagnosticTopic, kMessageCacheSize,
        boost::bind(&DeviceInterface::HandleNodeData<
                        cotek_msgs::node_diagnostic::ConstPtr>,
                    device_map_.at(kCartoRosDiagnosticTopic), _1)));
  }
  return true;
}

}  // namespace cotek_diagnostic
