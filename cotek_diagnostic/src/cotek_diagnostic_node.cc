/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "node/cotek_diagnostic_node.h"

#include "cotek_common/cotek_node_name.h"

int main(int argc, char** argv) {
  ros::init(argc, argv, cotek_node::kDiagnosticNode);
  // Set ros log level:
  if (ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME,
                                     ros::console::levels::Info)) {
    ros::console::notifyLoggerLevelsChanged();
  }
  // create node handle
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");
  // load options
  cotek_diagnostic::DiagnosticOption option;
  private_nh.param("control_frequency", option.control_frequency, 20.);
  private_nh.param("fault_report_delay", option.fault_report_delay, 6.0);

  cotek_diagnostic::DiagnosticNode node(option);

  while (!node.Init(&nh)) {
    LOG_ERROR_ONCE("diagnostic init failed. retry");
  }
  node.Run();
  ros::AsyncSpinner s(2);
  s.start();
  ros::waitForShutdown();
  return 0;
}
