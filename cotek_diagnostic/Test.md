# 诊断节点测试

## 软件测试

- 测试准备:
```
配置诊断设备表(测试时诊断所有设备)
启动 cotek_diagnostic 节点
```

- 测试过程:
```
模拟发布 /xxx_node_diagnostic      话题循环输入不同错误码 (xxx为节点名,如embedded_node_diagnostic)
模拟发布 /curtis_1220c_1232e       话题循环输入不同错误码
模拟发布 /audio                    话题循环输入不同错误码
模拟发布 /io                       话题循环输入不同错误码
模拟发布 /left_single_line_laser   话题循环输入不同错误码
模拟发布 /right_single_line_laser  话题循环输入不同错误码
模拟发布 /pgv_r2100                话题循环输入不同错误码
模拟发布 /gyro_rion                话题循环输入不同错误码
模拟发布 /sick_mls_sensor          话题循环输入不同错误码
模拟发布 /rfid                     话题循环输入不同错误码
模拟发布 /pgv100                   话题循环输入不同错误码
模拟发布 /bmmsk34_encoder          话题循环输入不同错误码
模拟发布 /motec_left_move_motor    话题循环输入不同错误码
模拟发布 /motec_right_move_motor   话题循环输入不同错误码
模拟发布 /syntron_left_move_motor  话题循环输入不同错误码
模拟发布 /syntron_right_move_motor 话题循环输入不同错误码
模拟发布 /syntron_lift_move_motor  话题循环输入不同错误码
模拟发布 /syntron_rotate_move_motor话题循环输入不同错误码
模拟发布 /single_line_laser        话题循环输入不同错误码
模拟发布 /battery_rebot_monitor    话题循环输入不同错误码
模拟发布 /battery_zl_monitor       话题循环输入不同错误码
模拟发布 /battery_baoe_monitor     话题循环输入不同错误码

注: /xxx_node_diagnostic 节点话题可以输入不同时间,检测超时判断功能
```
- 测试通过要求
```
   观察 /fault_report         若错误码与输入的错误码相同,且输出等级与错误码表匹配则通过
```

## 实车长稳测试
- 测试准备：
```
充满电的叉车(烧完软件，并做过设备检测)
能稳定运行的调度系统
通过地图编辑器编辑完成的地图场景
至少有24小时能连续运行的场地
```
- 测试过程：
```
给 agv 发送循环任务
在任务中执行过程中,人为制造故障,如手动牵引至脱轨报错、插拔io设备板
长时间运行后,同样人为制造故障(制造全部错误码的情况)
```

- 测试通过要求：
> 当制造故障时,能在调度界面查询到该故障便视为测试通过
