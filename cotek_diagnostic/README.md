# cotek_diagnostic
- 节点说明:
  订阅各硬件、节点的数据,统筹所有节点错误码并根据错误的严重情况将结果分为OK、WARN、ERROR、FATAL并输出给decision节点.

 > 注：反光板托盘车用..

## 相关硬件
  ```
  根据车型配置,配置文件与嵌入式节点一致.
  ```
## 包结构
```cpp
--- cotek_diagnostic
 |--config
 |  |- cotek_diagnostic_params.yaml  //参数配置表
 |--include
 |  |--cotek_diagnostic
 |  |  |- config_helper.h            // ConfigHelper类定义
 |  |  |- device_interface.h         // DeviceInterface接口类定义
 |  |  |- device_case.h              // 各设备子类定义
 |  |  |- diagnostic_centre.h        // DiagnosticCentre类定义 数据诊断中心
 |  |  |- diagnostic_options.h       // DiagnosticOption类定义 配置参数结构
 |  |--node
 |    |- cotek_diagnostic_node.h     // DiagnosticNode类定义
 |--launch 
 |  |- cotek_diagnostic.launch         
 |--src
 |  |- config_helper.cc              // 参数配置实现
 |  |- diagnostic_centre.cc          // 数据收集 数据诊断 数据归类 错误发送
 |  |- cotek_diagnostic_node.cc      // main函数 
 |--CMakeList.txt
 |--package.xml

```
## 依赖关系
### 节点依赖
- 本节点主动和哪些节点通信:
--cotek_action
--cotek_calibration
--cotek_avoid
--cotek_calibration
--cotek_communicate
--cotek_embedded
--cotek_localizer
--cotek_navigation
--cotek_reflector_localizer

- 哪些节点主动和本节点通信:
--decision_maker
  
### 通信依赖
#### 话题:
- 发布:
fault_report   (内容:归类后的错误等级及类型)
```cpp
// example
level: ERROR
error_code :
{
  id:navigation_node   // 导航节点
  state :  2           // 2对应故障表中出轨
}
```
- 订阅(可配置):
```
        curtis_1220c_1232e   (内容:柯蒂斯信息)
        io   (内容:io板数据信息)
        rfid   (内容:rfid数据信息)
        pgv100   (内容:pgv100数据信息)
        bmmsk34_encoder   (内容:拉线编码器数据信息)
        audio   (内容:io板数据信息)
        sick_mls_sensor   (内容:sick激光数据信息)
        battery_baoe_monitor   (内容:baoe电池数据信息)
        battery_rebot_monitor   (内容:rebot电池数据信息)
        battery_zl_monitor   (内容:中力电池数据信息)
        left_single_line_laser   (内容:左叉腿单线激光数据信息)
        right_single_line_laser   (内容:右叉腿单线激光数据信息)
        gyro_rion   (内容:gyro_rion数据信息)
        pgv_r2100   (内容:pgv_r2100数据信息)
        motec_left_move_motor   (内容:motec左电机数据信息)
        motec_right_move_motor   (内容:motec右电机数据信息)
        syntron_left_move_motor   (内容:syntron左电机数据信息)
        syntron_right_move_motor   (内容:syntron右电机数据信息)
        syntron_lift_motor   (内容:syntron抬升电机数据信息)
        syntron_rotate_motor   (内容:syntron自转数据信息)
```
### 类、函数设计
```cpp
- ConfigHelper      //订阅数据配置类
1.Instance()        //单例实现
2.LoadConfig()      //载入json文件配置
3.GetSensorList()   //获取订阅的传感器清单
```
```cpp
- DeviceInterface          //订阅数据接口类
1.Init()                   //初始化状态
SetFaultMsg()              //设置输入到decision中的消息
GetFaultMsg()              //获取消息
HandleNodeData()           //处理节点信息(通用)
HandleSensorData()         //处理设备信息(通用)
CheckTimeOut()             //超时判断
```
```cpp
- DiagnosticCentre         //故障数据中心处理类
1.Init()                   //根据ConfigHelper订阅相应的传感器
2.Diagnostic2Decision()    //发送故障信息fault_report至decision节点,频率(20Hz,可调)
3.UpdateFaultReport()      //汇总各传感器、节点的错误信息,输出唯一错误等级以及所有错误
```
```cpp
- xxx_updater      //具体数据分析类,例curtis_updater为柯蒂斯数据分析类,继承于DeviceInterface
```


## 参数配置
 
```cpp
enable_sensor_boot: true                                    // 是否读取本地设备配置文件
sensor_list_filename: pallet_forklift_embedded_config.json  // 本地设备配置文件名
conrtol_frequency: 20.0                                     // 错误输出频率
fault_report_delay: 6.0                                     // 上电延迟报警时长

```

## 错误码枚举
```cpp
enum class CommunicateNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,          // 节点配置错误
  TIMEOUT = 2,               // 超时
  MSG_TYPE_ABNORMAL = 21     // 消息类型非法
};

enum class DecisionMakerNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,          // 节点配置错误
  TASK_APPEND_ERROR = 2,     // 任务插入错误
  TASK_REPEAT = 21           // 重复任务
};

enum class EmbeddedNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,           // 节点配置错误
  CAN_INIT_ERROR = 2,         // can初始化错误
  AGV_HARDWARE_INIT_ERROR = 3,// 小车初始化错误
  CAN_0_SEND_ERROR = 4,       // can0数据发送错误
  CAN_1_SEND_ERROR = 5,       // can1数据发送错误
  CAN_CYCLE_TIME_OUT = 21     // can循环超时
};

enum class ReflectorNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,             // 节点配置错误
  GET_REFLECTOR_MAP_ERROR = 2,  // 从服务器拉反光板地图错误
  LASER_MESSAGE_EMPTY = 10,     // 激光雷达数据空
  STATIC_LOCALIZER_ERROR = 11,  // 静态定位错误
  GET_POSE_ERROR = 12,          // 定位错误
  ENVIROMENT_MATCH_WARING = 21   // 反光板匹配 < 3个
};

enum class CalibrationNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,             // 节点配置错误
  GET_REFLECTOR_MAP_ERROR = 2,  // 从服务器拉反光板地图错误
  LASER_MESSAGE_EMPTY = 11,     // 激光雷达数据空
  GET_POSE_ERROR = 12,          // 定位错误
  ENVIROMENT_MATCH_WARING = 21   // 反光板匹配 < 3个
};

enum class NavigationNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,             // 节点配置错误
  OUT_OF_THE_ROUTE = 2,         // 小车出轨错误
  GOAL_ROUTE_ERROR = 3          // B样条目标点计算失败错误
};

enum class AvoidNodeStatus : uint16_t {
  NORMAL = 0,
  CONFIG_ERROR = 1,            // 节点配置错误
  BUMP_ERROR = 2,              // 防撞条错误
  AVOID_MAP_ERROR = 3,         // 避障地图错误
  AVOID_TIME_OUT = 21,         // 避障超时
  FORK_LEG_AVOID = 22          // 叉腿避障
};

enum class ActionNodeStatus : uint16_t {
  NORMAL = 0,
  AGV_TYPE_ERR = 1,            // 配置错误
  ACTION_TYPE_ERR = 2,         // 动作类型错误
  BAD_PARAMETER_ERR = 3,       // 参数错误
  PALLET_UP_ERR = 4,           // 托盘抬升错误
  PALLET_DOWN_ERR = 5,         // 托盘下降错误
  PALLET_ROTATE_ERR = 6,       // 托盘自转错误
  CHARGE_ERR = 7,              // 充电错误
  CHARGE_TIMEOUT_ERR = 8,      // 充电超时
  ACTION_NODE_TIMEOUT_ERR = 9, // 动作超时
  PALLET_MOVE_ERR = 11,        // 托盘移动错误
  EXCEPTION_NO_PALLET_ERR = 12 // 未检测到托盘
};

enum class ErrorCodeType : uint16_t {
  NONE = 0,
  SENDFAILED = 0xFFFC,      // 数据发送错误
  INITFAILED = 0xFFFD,  /   // 初始化错误    
  ILLEGAL = 0xFFFE,         // 数据非法
  DISCONNECTED = 0xFFFF     // 连接中断
};

```






