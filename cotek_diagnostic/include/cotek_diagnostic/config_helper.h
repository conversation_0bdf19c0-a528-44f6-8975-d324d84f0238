/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_CONFIG_HELPER_H_
#define COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_CONFIG_HELPER_H_
#include <ros/package.h>

#include <map>
#include <string>
#include <vector>

#include "cotek_common/log_porting.h"
#include "cotek_common/util/json11.h"
#include "cotek_common/util/local_service.h"

namespace cotek_diagnostic {
// 读取 json 文件 加载配置

class ConfigHelper {
 public:
  ~ConfigHelper() {}
  // 单例实现
  static ConfigHelper& Instance() {
    static ConfigHelper instance;
    return instance;
  }

  ConfigHelper(const ConfigHelper&) = delete;
  ConfigHelper& operator=(const ConfigHelper&) = delete;

  // 加载 json 文件获取配置
  bool LoadConfig(const std::string& json_str);

  inline const std::vector<std::string>& GetSensorList() {
    return sensor_list_;
  }

 private:
  ConfigHelper() {}

  std::vector<std::string> sensor_list_;
};

}  // namespace cotek_diagnostic
#endif  // COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_CONFIG_HELPER_H_
