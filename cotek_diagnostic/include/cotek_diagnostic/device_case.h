/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DEVICE_CASE_H_
#define COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DEVICE_CASE_H_
#include <cotek_msgs/node_diagnostic.h>

#include <string>

#include "cotek_diagnostic/device_interface.h"
#include "cotek_msgs/audio_feedback.h"
#include "cotek_msgs/battery_feedback.h"
#include "cotek_msgs/bmmsk34_encoder_feedback.h"
#include "cotek_msgs/curtis_feedback.h"
#include "cotek_msgs/gyro_rion_feedback.h"
#include "cotek_msgs/io_feedback.h"
#include "cotek_msgs/linde_kob_feedback.h"
#include "cotek_msgs/motor_motec_feedback.h"
#include "cotek_msgs/motor_syntro_feedback.h"
#include "cotek_msgs/pgv100_feedback.h"
#include "cotek_msgs/pgv_r2100_feedback.h"
#include "cotek_msgs/rfid_feedback.h"
#include "cotek_msgs/sick_mls_feedback.h"
#include "cotek_msgs/single_line_laser_feedback.h"
#include "cotek_msgs/weighing_feedback.h"

namespace cotek_diagnostic {

namespace {
constexpr double kTimeOut = 3.;
}
class NodeInterface : public DeviceInterface {
 public:
  explicit NodeInterface(const std::string& device_name,
                         const uint16_t& error_base)
      : DeviceInterface(device_name) {
    ros::NodeHandle private_nh("~");
    timer_ = private_nh.createTimer(ros::Duration(0.5),
                                    &NodeInterface::CheckTimeOut, this);
    last_time_ = ros::Time::now() + ros::Duration(10.);
    error_base_ = error_base;
  }
  virtual ~NodeInterface() {}

  // 超时报警故障码为9
  void CheckTimeOut(const ros::TimerEvent& e) {
    if (ros::Time::now().toSec() - last_time_.toSec() > kTimeOut) {
      FaultMsg fault_temp;
      fault_temp.level = kFatal;
      fault_temp.errors.push_back(error_base_ + 999);
      SetFaultMsg(fault_temp);
    }
  }
};

class AudioUpdater : public DeviceInterface {
 public:
  explicit AudioUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~AudioUpdater() {}
  // 多态编写
};

class BatteryBaoeUpdater : public DeviceInterface {
 public:
  explicit BatteryBaoeUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~BatteryBaoeUpdater() {}
  // 多态编写
};

class BatteryRebotUpdater : public DeviceInterface {
 public:
  explicit BatteryRebotUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~BatteryRebotUpdater() {}
  // 多态编写
};

class BatteryZlUpdater : public DeviceInterface {
 public:
  explicit BatteryZlUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~BatteryZlUpdater() {}
  // 多态编写
};

class CurtisUpdater : public DeviceInterface {
 public:
  explicit CurtisUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~CurtisUpdater() {}
  // 多态编写
};

class GyroRionUpdater : public DeviceInterface {
 public:
  explicit GyroRionUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~GyroRionUpdater() {}
  // 多态编写
};

class IoUpdater : public DeviceInterface {
 public:
  explicit IoUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~IoUpdater() {}
  // 多态编写
};

class LeftSingleLineUpdater : public DeviceInterface {
 public:
  explicit LeftSingleLineUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~LeftSingleLineUpdater() {}
  // 多态编写
};

class RightSingleLineUpdater : public DeviceInterface {
 public:
  explicit RightSingleLineUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~RightSingleLineUpdater() {}
  // 多态编写
};

class PgvR2100Updater : public DeviceInterface {
 public:
  explicit PgvR2100Updater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~PgvR2100Updater() {}
  // 多态编写
};

class Pgv100Updater : public DeviceInterface {
 public:
  explicit Pgv100Updater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~Pgv100Updater() {}
  // 多态编写
};

class RfidUpdater : public DeviceInterface {
 public:
  explicit RfidUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~RfidUpdater() {}
  // 多态编写
};

class WeightUpdater : public DeviceInterface {
 public:
  explicit WeightUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~WeightUpdater() {}
  // 多态编写
};

class SickMlsUpdater : public DeviceInterface {
 public:
  explicit SickMlsUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~SickMlsUpdater() {}
  // 多态编写
};

class MotecLeftUpdater : public DeviceInterface {
 public:
  explicit MotecLeftUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~MotecLeftUpdater() {}
  // 多态编写
};

class MotecRightUpdater : public DeviceInterface {
 public:
  explicit MotecRightUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~MotecRightUpdater() {}
  // 多态编写
};

class SyntronLeftUpdater : public DeviceInterface {
 public:
  explicit SyntronLeftUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~SyntronLeftUpdater() {}
  // 多态编写
};

class SyntronRightUpdater : public DeviceInterface {
 public:
  explicit SyntronRightUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~SyntronRightUpdater() {}
  // 多态编写
};

class SyntronLiftUpdater : public DeviceInterface {
 public:
  explicit SyntronLiftUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~SyntronLiftUpdater() {}
  // 多态编写
};

class SyntronRotateUpdater : public DeviceInterface {
 public:
  explicit SyntronRotateUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~SyntronRotateUpdater() {}
  // 多态编写
};

class LindeKobUpdater : public DeviceInterface {
 public:
  explicit LindeKobUpdater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~LindeKobUpdater() {}
  // 多态编写
};

class Bmmsk34Updater : public DeviceInterface {
 public:
  explicit Bmmsk34Updater(const std::string& device_name)
      : DeviceInterface(device_name) {
    /* fill in constructor */
  }

  ~Bmmsk34Updater() {}
  // 多态编写
};

}  // namespace cotek_diagnostic

#endif  // COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DEVICE_CASE_H_
