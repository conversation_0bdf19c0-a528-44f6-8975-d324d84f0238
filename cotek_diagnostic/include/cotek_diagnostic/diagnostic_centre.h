/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DIAGNOSTIC_CENTRE_H_
#define COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DIAGNOSTIC_CENTRE_H_

#include <ros/ros.h>

#include <map>
#include <memory>
#include <string>
#include <thread>
#include <vector>

#include "cotek_common/cotek_node_name.h"
#include "cotek_common/cotek_topic_name.h"
// #include "cotek_diagnostic/config_helper.h"
#include "cotek_common/device_table_loader.h"
#include "cotek_common/node_diagnostic_info.h"
#include "cotek_diagnostic/device_case.h"
#include "cotek_diagnostic/diagnostic_options.h"
#include "cotek_msgs/agv_position.h"
#include "cotek_msgs/fault_report.h"
#include "cotek_msgs/node_diagnostic.h"
#include "cotek_msgs/self_check.h"

namespace cotek_diagnostic {

class DiagnosticCentre final {
 public:
  struct SelfCheckRet {
    bool state = true;
    std::vector<std::string> info;
  };

  DiagnosticCentre() = delete;

  explicit DiagnosticCentre(const cotek_diagnostic::DiagnosticOption& option);

  ~DiagnosticCentre() {}

  bool Init(ros::NodeHandle* nh);

  void Run();

  void Runner();

  void Diagnostic2Decision();

  void UpdateFaultReport();

 private:
  void SelfCheck();

  void HandleAgvpositionData(const cotek_msgs::agv_position::ConstPtr& data);

  cotek_diagnostic::DiagnosticOption option_;
  std::vector<ros::Subscriber> subscriber_;
  std::map<std::string, std::shared_ptr<DeviceInterface>> device_map_;
  ros::Publisher fault_report_pub_;
  ros::Publisher self_check_pub_;
  ros::Time start_time_;
  cotek_msgs::fault_report fault_report_;
  std::shared_ptr<std::thread> run_executor_;

  // TODO(@ssh) 此处暂时通过订阅agv位置来判断定位是否ok，后续需优化逻辑
  cotek_msgs::agv_position agv_position_;
};

}  // namespace cotek_diagnostic

#endif  // COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DIAGNOSTIC_CENTRE_H_
