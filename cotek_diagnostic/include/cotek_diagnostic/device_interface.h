/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DEVICE_INTERFACE_H_
#define COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DEVICE_INTERFACE_H_

#include <ros/ros.h>

#include <cstdint>
#include <exception>
#include <mutex>
#include <string>
#include <vector>

#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/log_porting.h"

namespace cotek_diagnostic {

using AgvErrorLevel = common::AgvErrorLevel;

constexpr char kWarn[] = "WARNING";
constexpr char kFatal[] = "FATAL";

struct FaultMsg {
  std::string level;  // WARNING  FATAL
  std::vector<uint32_t> errors;
};

// enum { OK = 0, WARN = 1, ERROR = 2, FATAL = 3 };

class DeviceInterface {
 public:
  explicit DeviceInterface(const std::string& device_name)
      : device_name_(device_name) {
    /* fill in constructor */
  }

  virtual ~DeviceInterface() {}
  virtual void Init() { /* fill in */
  }

  inline virtual void SetFaultMsg(const cotek_diagnostic::FaultMsg& msg) {
    std::unique_lock<std::mutex> lock(mutex_);
    fault_msg_ = msg;
  }

  inline virtual cotek_diagnostic::FaultMsg GetFaultMsg() {
    std::unique_lock<std::mutex> lock(mutex_);
    return fault_msg_;
  }
  // 通用处理函数
  template <typename T>
  void HandleNodeData(const T& data) {
    FaultMsg fault_temp;
    // 上电6s内不计入错误
    if (last_time_ > ros::Time::now()) return;

    try {
      uint8_t warn_cnt = 0;
      uint8_t error_cnt = 0;
      for (auto status : data->status) {
        if (status == 0) continue;
        auto class_code = status % 1000;
        if (class_code >= 300 && class_code < 600) {
          warn_cnt++;
        } else if (class_code >= 600 && class_code < 1000) {
          error_cnt++;
        }
        fault_temp.errors.push_back(status);
      }

      if (error_cnt > 0) {
        fault_temp.level = kFatal;
      } else if (warn_cnt > 0) {
        fault_temp.level = kWarn;
      }

      SetFaultMsg(fault_temp);

      last_time_ = ros::Time::now();

    } catch (const std::exception& ex) {
      LOG_ERROR_STREAM(ex.what());
    }
  }

  // 通用处理函数
  template <typename T>
  void HandleSensorData(const T& data) {
    FaultMsg fault_temp;
    if (data->error_code) fault_temp.level = fault_temp.level = kFatal;

    fault_temp.errors.push_back(std::to_string(data->error_code));

    SetFaultMsg(fault_temp);
  }

 protected:
  ros::Time last_time_;
  ros::Timer timer_;
  uint16_t error_base_;

 private:
  std::mutex mutex_;

  std::string device_name_;
  FaultMsg fault_msg_;
};

}  // namespace cotek_diagnostic
#endif  // COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DEVICE_INTERFACE_H_
