/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DIAGNOSTIC_OPTIONS_H_
#define COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DIAGNOSTIC_OPTIONS_H_
#include <string>

namespace cotek_diagnostic {
struct DiagnosticOption {
  double control_frequency;
  double fault_report_delay;
};

}  // namespace cotek_diagnostic

#endif  // COTEK_DIAGNOSTIC_INCLUDE_COTEK_DIAGNOSTIC_DIAGNOSTIC_OPTIONS_H_
