/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_DIAGNOSTIC_INCLUDE_NODE_COTEK_DIAGNOSTIC_NODE_H_
#define COTEK_DIAGNOSTIC_INCLUDE_NODE_COTEK_DIAGNOSTIC_NODE_H_

#include <ros/package.h>
#include <ros/ros.h>

#include <memory>
#include <string>
#include <vector>

#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/util/local_service.h"
#include "cotek_diagnostic/diagnostic_centre.h"
#include "cotek_diagnostic/diagnostic_options.h"

namespace cotek_diagnostic {

constexpr char kSensorPackageName[] = "cotek_embedded";

class DiagnosticNode final {
 public:
  DiagnosticNode() = delete;
  explicit DiagnosticNode(const DiagnosticOption& option) : option_(option) {
    diagnostic_centre_ptr_ = std::make_shared<DiagnosticCentre>(option_);
  }

  inline bool Init(ros::NodeHandle* nh) {
    std::string sensor_boot_json_str;
    // sensor_boot_json_str = BasicConfigHelper::Instance().GetConfig(
    //     cotek_config::ConfigType::DEVICE_TABLE);
    // if (DeviceTableLoader::Instance().LoadConfig(sensor_boot_json_str)) {
    //   LOG_INFO("load sensor list success.");
    // } else {
    //   LOG_ERROR("load sensor list error.");
    //   return false;
    // }

    return diagnostic_centre_ptr_->Init(nh);
  }
  inline void Run() { diagnostic_centre_ptr_->Run(); }

 private:
  DiagnosticOption option_;
  std::shared_ptr<DiagnosticCentre> diagnostic_centre_ptr_;
};
}  // namespace cotek_diagnostic
#endif  // COTEK_DIAGNOSTIC_INCLUDE_NODE_COTEK_DIAGNOSTIC_NODE_H_
