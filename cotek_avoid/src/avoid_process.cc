/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_avoid/avoid_process.h"

#include <pcl/common/common.h>
#include <pcl/common/io.h>
#include <pcl/filters/crop_box.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/passthrough.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/io/png_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/search/kdtree.h>
#include <pcl/segmentation/extract_clusters.h>
#include <pcl_conversions/pcl_conversions.h>

#include <cstdint>
#include <exception>
#include <list>
#include <string>

#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/geometry/cotek_geometry.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/math.h"
#include "cotek_msgs/avoid_camera.h"
#include "cotek_msgs/safety_states.h"
#include "ros/duration.h"
#include "ros/time.h"

namespace cotek_avoid {
namespace {
constexpr double kReserveTime = 0.5;
constexpr double kIngoreLaserDistance = 0.3;
constexpr double kAvoidOvertime = 60 * 5;
}  // namespace

AvoidProcess::AvoidProcess(
    const AvoidOption &option, std::shared_ptr<StateManager> ptr,
    std::shared_ptr<NodeStatusManager> node_status_manager_ptr)
    : option_(option),
      reserve_time_(ros::Duration(kReserveTime)),
      lastest_stop_time_(ros::Time::now()),
      io_state_(AvoidLevel::NONE),
      manual_(false),
      avoid_time_out_cnt_(0),
      active_avoid_map_(AvoidMapType::NONE),
      fork_up_down_state_(static_cast<uint8_t>(ForkStateType::MIDDLE)),
      state_manager_ptr_(ptr),
      node_status_manager_ptr_(node_status_manager_ptr),
      set_map_init_(false) {}
/**
 * \brief config default config and param..
 */
bool AvoidProcess::Init() {
  try {
    default_map_ = ConfigHelper::Instance().GetAvoidMap().at(
        static_cast<int>(AvoidMapType::FORWARD));
  } catch (const std::out_of_range &e) {
    LOG_ERROR_STREAM_THROTTLE(1, "[Handle Error] configuration file error");
    LOG_WARN_STREAM_THROTTLE(1, e.what());
    node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::CONFIG_ERROR);
    return false;
  }
  default_strategy_ = GetAvoidMap(default_map_);
  active_map_ = default_strategy_;
  // 临时用1号地图做行人检测避障
  pederstrain_strategy_ =
      GetAvoidMap(ConfigHelper::Instance().GetAvoidMap().at(1));
  return true;
}

SensorTimeOut AvoidProcess::SensorIsTimeout() {
  SensorTimeOut sto{false, {}};
  for (const auto &sampler : state_manager_ptr_->samplers()) {
    if (sampler.second->IsTimeout()) {
      LOG_WARN_STREAM_THROTTLE(1, "[" << sampler.first << "] is time out !!!");
      sto.timeout = true;
      sto.type_vec.push_back(
          static_cast<int32_t>(GetTimeoutType(sampler.first)));
      node_status_manager_ptr_->SetNodeStatus(GetTimeoutError(sampler.first));
    }
  }
  return sto;
}

AvoidNodeStatus AvoidProcess::GetTimeoutError(const std::string &frame) {
  if (frame == cotek_topic::kNaviLaser0Topic)
    return AvoidNodeStatus::LASER_0_TIMEOUT;
  if (frame == cotek_topic::kNaviLaser1Topic)
    return AvoidNodeStatus::LASER_1_TIMEOUT;
  if (frame == cotek_topic::kNaviLaser2Topic)
    return AvoidNodeStatus::LASER_2_TIMEOUT;

  if (frame == cotek_topic::kAvoidLaser0Topic)
    return AvoidNodeStatus::AVOID_LASER_0_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser1Topic)
    return AvoidNodeStatus::AVOID_LASER_1_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser2Topic)
    return AvoidNodeStatus::AVOID_LASER_2_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser3Topic)
    return AvoidNodeStatus::AVOID_LASER_3_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser4Topic)
    return AvoidNodeStatus::AVOID_LASER_4_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser5Topic)
    return AvoidNodeStatus::AVOID_LASER_5_TIMEOUT;

  if (frame == cotek_topic::kAvoidCloud0Topic)
    return AvoidNodeStatus::AVOID_CLOUD_0_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud1Topic)
    return AvoidNodeStatus::AVOID_CLOUD_1_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud2Topic)
    return AvoidNodeStatus::AVOID_CLOUD_2_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud3Topic)
    return AvoidNodeStatus::AVOID_CLOUD_3_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud4Topic)
    return AvoidNodeStatus::AVOID_CLOUD_4_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud5Topic)
    return AvoidNodeStatus::AVOID_CLOUD_5_TIMEOUT;

  if (frame == cotek_topic::kAvoidCamera0Topic)
    return AvoidNodeStatus::AVOID_CAMERA_0_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera1Topic)
    return AvoidNodeStatus::AVOID_CAMERA_1_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera2Topic)
    return AvoidNodeStatus::AVOID_CAMERA_2_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera3Topic)
    return AvoidNodeStatus::AVOID_CAMERA_3_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera4Topic)
    return AvoidNodeStatus::AVOID_CAMERA_4_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera5Topic)
    return AvoidNodeStatus::AVOID_CAMERA_5_TIMEOUT;

  if (frame == cotek_topic::kSafetyIoStateTopic)
    return AvoidNodeStatus::SAFETY_IO_TIMEOUT;

  if (frame == cotek_topic::kUltarsonicTopic)
    return AvoidNodeStatus::ULTRASONIC_TIMEOUT;

  LOG_WARN_STREAM(frame << " not define timeout type!!! ");

  return AvoidNodeStatus::NORMAL;
}

AvoidLevel AvoidProcess::GetTimeoutType(const std::string &frame) {
  if (frame == cotek_topic::kNaviLaser0Topic)
    return AvoidLevel::LASER_0_TIMEOUT;
  if (frame == cotek_topic::kNaviLaser1Topic)
    return AvoidLevel::LASER_1_TIMEOUT;
  if (frame == cotek_topic::kNaviLaser2Topic)
    return AvoidLevel::LASER_2_TIMEOUT;

  if (frame == cotek_topic::kAvoidLaser0Topic)
    return AvoidLevel::AVOID_LASER_0_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser1Topic)
    return AvoidLevel::AVOID_LASER_1_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser2Topic)
    return AvoidLevel::AVOID_LASER_2_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser3Topic)
    return AvoidLevel::AVOID_LASER_3_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser4Topic)
    return AvoidLevel::AVOID_LASER_4_TIMEOUT;
  if (frame == cotek_topic::kAvoidLaser5Topic)
    return AvoidLevel::AVOID_LASER_5_TIMEOUT;

  if (frame == cotek_topic::kAvoidCloud0Topic)
    return AvoidLevel::AVOID_CLOUD_0_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud1Topic)
    return AvoidLevel::AVOID_CLOUD_1_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud2Topic)
    return AvoidLevel::AVOID_CLOUD_2_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud3Topic)
    return AvoidLevel::AVOID_CLOUD_3_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud4Topic)
    return AvoidLevel::AVOID_CLOUD_4_TIMEOUT;
  if (frame == cotek_topic::kAvoidCloud5Topic)
    return AvoidLevel::AVOID_CLOUD_5_TIMEOUT;

  if (frame == cotek_topic::kAvoidCamera0Topic)
    return AvoidLevel::AVOID_CAMERA_0_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera1Topic)
    return AvoidLevel::AVOID_CAMERA_1_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera2Topic)
    return AvoidLevel::AVOID_CAMERA_2_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera3Topic)
    return AvoidLevel::AVOID_CAMERA_3_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera4Topic)
    return AvoidLevel::AVOID_CAMERA_4_TIMEOUT;
  if (frame == cotek_topic::kAvoidCamera5Topic)
    return AvoidLevel::AVOID_CAMERA_5_TIMEOUT;

  if (frame == cotek_topic::kSafetyIoStateTopic)
    return AvoidLevel::SAFETY_IO_TIMEOUT;

  if (frame == cotek_topic::kUltarsonicTopic)
    return AvoidLevel::ULTRASONIC_TIMEOUT;

  LOG_WARN_STREAM(frame << " not define timeout type!!! ");

  return AvoidLevel::NONE;
}

cotek_msgs::safety_states AvoidProcess::GetAvoidState() {
  cotek_msgs::safety_states states;

  states = msg_queue_.Filter(reserve_time_);

  SensorTimeOut &&timeout = SensorIsTimeout();

  if (timeout.timeout) {
    LOG_ERROR_STREAM_THROTTLE(1, "[Handle Error] get data time out");
    // node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::DATA_TIME_OUT,
    //                                         SensorIsTimeout().type_vec);
    states.avoid_type = static_cast<int>(timeout.type_vec[0]);
    states.avoid_state = static_cast<int>(AvoidSpeedLevel::STOP);
    states.avoid_dist = 0.;
    if (states.safety_states.empty()) {
      cotek_msgs::safety_state state;
      state.avoid_type = static_cast<int>(timeout.type_vec[0]);
      state.avoid_state = static_cast<int>(AvoidSpeedLevel::STOP);
      state.avoid_dist = 0.;
      states.safety_states.push_back(state);
    }
  }

  if (AvoidSpeedLevel::STOP != static_cast<AvoidSpeedLevel>(states.avoid_state))
    lastest_stop_time_ = ros::Time::now();

  // 避障超时初始允许100个周期以缩小避障地图运行
  if (ros::Time::now().toSec() - lastest_stop_time_.toSec() >
      option_.overtime * 0.5) {
    avoid_time_out_cnt_ = 100;
  } else {
    avoid_time_out_cnt_ = avoid_time_out_cnt_ > 0 ? (avoid_time_out_cnt_ - 1)
                                                  : avoid_time_out_cnt_;
  }

  if (ros::Time::now().toSec() - lastest_stop_time_.toSec() >
      option_.overtime) {
    LOG_WARN_STREAM_THROTTLE(1, "[Handle Error] avoid overtime");
    node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::AVOID_TIME_OUT);
    states.avoid_overtime = true;
  }

  // 避免避障语音混乱，3级避障类型只采用第一次连续触发的
  if (states.avoid_state == static_cast<int>(AvoidSpeedLevel::STOP) &&
      last_safety_state_.avoid_state ==
          static_cast<int>(AvoidSpeedLevel::STOP)) {
    states.avoid_type = last_safety_state_.avoid_type;
  }
  last_safety_state_.avoid_type = states.avoid_type;
  last_safety_state_.avoid_state = states.avoid_state;

  return states;
}

double AvoidProcess::CalIoAvoidDist(AvoidSpeedLevel avoid_level,
                                    const double &current_v,
                                    const double &controller_frequency) {
  // No avoid return a large number avoid dist
  static double end_dis = 100.0;
  static bool enable_avoid_lv1 = false;
  static bool enable_avoid_lv2 = false;
  switch (avoid_level) {
    case AvoidSpeedLevel::FREE: {
      enable_avoid_lv1 = false;
      enable_avoid_lv2 = false;
      end_dis = 100.0;
      return end_dis;
    }
    case AvoidSpeedLevel::SLOWLEVEL1: {
      if (!enable_avoid_lv1) {
        end_dis = 2.0;
        enable_avoid_lv1 = true;
      } else {
        end_dis = end_dis - (controller_frequency * std::fabs(current_v));
        end_dis = boost::algorithm::clamp(end_dis, 0.05, 2.0);
      }
      return end_dis;
    }
    case AvoidSpeedLevel::SLOWLEVEL2: {
      if (!enable_avoid_lv2) {
        end_dis = boost::algorithm::clamp(end_dis, 0.05, 0.8);
        enable_avoid_lv2 = true;
      } else {
        end_dis = end_dis - (controller_frequency * std::fabs(current_v));
        end_dis = boost::algorithm::clamp(end_dis, 0.05, 0.8);
      }
      return end_dis;
    }
    case AvoidSpeedLevel::STOP: {
      // enable_avoid_lv1 = false;
      // enable_avoid_lv2 = false;
      end_dis = 0.;
      return end_dis;
    }
    default: {
      enable_avoid_lv1 = false;
      enable_avoid_lv2 = false;
      end_dis = 100.0;
      return end_dis;
    }
  }
}

static AvoidFacotr GetForkIoAvoid(
    const AvoidLevel &io_state,
    const std::map<std::string, bool> &avoid_strategys, const int &direction) {
  AvoidFacotr lv;
  lv.type = io_state;
  // 仅倒车触发叉尖避障
  if (direction >= 0) {
    return lv;
  }

  if (io_state == AvoidLevel::FORK_LEFT_LEG &&
      avoid_strategys.count("leftFork") > 0) {
    return lv;
  }

  if (io_state == AvoidLevel::FORK_RIGHT_LEG &&
      avoid_strategys.count("rightFork") > 0) {
    return lv;
  }

  lv.lv = AvoidSpeedLevel::STOP;
  lv.dist = 0.;
  return lv;
}

void AvoidProcess::SafetyIOStateCallback(
    const std_msgs::Int32::ConstPtr &state) {
  static ros::WallTime last_time = ros::WallTime::now();

  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kSafetyIoStateTopic)
             ->Pulse()) {
      return;
    }
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM_THROTTLE(1, e.what() << ", cannot find sampler ratio of "
                                         << cotek_topic::kSafetyIoStateTopic);
    return;
  }

  AvoidFacotr io_lv;

  io_state_ = static_cast<AvoidLevel>(state->data);
  io_lv.type = io_state_;

  if (io_state_ == AvoidLevel::NONE || io_state_ == AvoidLevel::LEVEL_I ||
      io_state_ == AvoidLevel::LEVEL_II || io_state_ == AvoidLevel::LEVEL_III) {
    io_lv.lv = static_cast<AvoidSpeedLevel>(io_state_);

    io_lv.dist = CalIoAvoidDist(static_cast<AvoidSpeedLevel>(io_state_),
                                state_manager_ptr_->velocity_factor().current_v,
                                (ros::WallTime::now() - last_time).toSec());
  } else if (io_state_ == AvoidLevel::BUMP) {
    io_lv.lv = AvoidSpeedLevel::STOP;
    node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::BUMP_ERROR);
    io_lv.dist = 0;
  } else if (io_state_ == AvoidLevel::FORK_LEFT_LEG ||
             io_state_ == AvoidLevel::FORK_RIGHT_LEG ||
             io_state_ == AvoidLevel::FORK_LEG_BOTH) {
    auto &&temp_lv =
        GetForkIoAvoid(io_state_, avoid_strategy_, move_cmd_.direction);

    io_lv.lv = temp_lv.lv;
    io_lv.dist = temp_lv.dist;

  } else if (io_state_ == AvoidLevel::UP_FORWARD_LASER_OBSTACLE) {
    io_lv.lv = AvoidSpeedLevel::STOP;
    io_lv.dist = 0;
  } else {
    io_lv.lv = AvoidSpeedLevel::STOP;
    io_lv.dist = 0;
  }
  last_time = ros::WallTime::now();
  sensor_avoid_level_.io_state = io_lv;
  // TODO(@ssh) 临时用FORK_LEG_BOTH代替io复合类型
  msg_queue_.PushData(ros::Time::now(), io_lv, AvoidLevel::FORK_LEG_BOTH);
  return;
}

// void AvoidProcess::PederstrianPoseCallback(
//     const cotek_msgs::multi_pedestrian_pose::ConstPtr &pose_list) {
//   AvoidFacotr pederstrian_lv;
//   if (pose_list->multi_pose.empty()) return;

//   const Transform &camera_tf =
//       ConfigHelper::Instance().GetSensorTransform().at(std::string("camera_1"));
//   pederstrian_lv = GetPederstrianAvoidLevel(
//       pose_list, pederstrain_strategy_.slow_level1,
//       pederstrain_strategy_.slow_level2, pederstrain_strategy_.stop,
//       camera_tf);

//   pederstrian_lv.type = AvoidLevel::PEDERSTRIAN_DETECT;
//   sensor_avoid_level_.pederstrian_avoid = pederstrian_lv;
//   msg_queue_.PushData(ros::Time::now(), pederstrian_lv);
// }

void AvoidProcess::AddMoveCmd(const cotek_msgs::move_cmd::ConstPtr &move_cmd) {
  move_cmd_ = *move_cmd;
}

void AvoidProcess::AddHightBmmsk34Encode(
    const cotek_msgs::wire_encoder_feedback::ConstPtr &bmmsk34) {
  if (!bmmsk34->error_code) {
    vaild_height_.vaild = true;
    vaild_height_.height = bmmsk34->data;
  } else {
    vaild_height_.vaild = false;
  }
}

void AvoidProcess::AddWeakenLaser(
    const cotek_msgs::weaken_avoid::ConstPtr &feed) {
  weaken_avoid_info_.time = ros::Time::now();
}

void AvoidProcess::AddForkliftPalleState(
    const std_msgs::Int32::ConstPtr &pallet_fork_io_state) {
  fork_up_down_state_ = pallet_fork_io_state->data;
}

std::string AvoidProcess::GetAvoidCameraFrame(const std::string &name) {
  if (name.empty()) return std::string();
  std::string camera_index(1, name.back());
  return std::string("camera") + camera_index;
}

AvoidLevel AvoidProcess::GetAvoidType(const std::string &frame) {
  if (frame == cotek_topic::kNaviLaser0Topic) return AvoidLevel::NAVI_LASER_0;
  if (frame == cotek_topic::kNaviLaser1Topic) return AvoidLevel::NAVI_LASER_1;
  if (frame == cotek_topic::kNaviLaser2Topic) return AvoidLevel::NAVI_LASER_2;

  if (frame == cotek_topic::kAvoidLaser0Topic) return AvoidLevel::AVOID_LASER_0;
  if (frame == cotek_topic::kAvoidLaser1Topic) return AvoidLevel::AVOID_LASER_1;
  if (frame == cotek_topic::kAvoidLaser2Topic) return AvoidLevel::AVOID_LASER_2;
  if (frame == cotek_topic::kAvoidLaser3Topic) return AvoidLevel::AVOID_LASER_3;
  if (frame == cotek_topic::kAvoidLaser4Topic) return AvoidLevel::AVOID_LASER_4;
  if (frame == cotek_topic::kAvoidLaser5Topic) return AvoidLevel::AVOID_LASER_5;

  if (frame == cotek_topic::kAvoidCloud0Topic) return AvoidLevel::AVOID_CLOUD_0;
  if (frame == cotek_topic::kAvoidCloud1Topic) return AvoidLevel::AVOID_CLOUD_1;
  if (frame == cotek_topic::kAvoidCloud2Topic) return AvoidLevel::AVOID_CLOUD_2;
  if (frame == cotek_topic::kAvoidCloud3Topic) return AvoidLevel::AVOID_CLOUD_3;
  if (frame == cotek_topic::kAvoidCloud4Topic) return AvoidLevel::AVOID_CLOUD_4;
  if (frame == cotek_topic::kAvoidCloud5Topic) return AvoidLevel::AVOID_CLOUD_5;

  if (frame == cotek_topic::kAvoidCamera0Topic)
    return AvoidLevel::AVOID_CAMERA_0;
  if (frame == cotek_topic::kAvoidCamera1Topic)
    return AvoidLevel::AVOID_CAMERA_1;
  if (frame == cotek_topic::kAvoidCamera2Topic)
    return AvoidLevel::AVOID_CAMERA_2;
  if (frame == cotek_topic::kAvoidCamera3Topic)
    return AvoidLevel::AVOID_CAMERA_3;
  if (frame == cotek_topic::kAvoidCamera4Topic)
    return AvoidLevel::AVOID_CAMERA_4;
  if (frame == cotek_topic::kAvoidCamera5Topic)
    return AvoidLevel::AVOID_CAMERA_5;

  return AvoidLevel::NONE;
}

static void RoiFilterCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_ptr,
                           const double &x_min, const double &x_max,
                           const double &y_min, const double &y_max,
                           const double &z_min, const double &z_max) {
  pcl::PassThrough<pcl::PointXYZ> pass;

  pass.setInputCloud(cloud_ptr);
  pass.setFilterFieldName("x");
  pass.setFilterLimits(x_min, x_max);
  pass.filter(*cloud_ptr);

  pass.setInputCloud(cloud_ptr);
  pass.setFilterFieldName("y");
  pass.setFilterLimits(y_min, y_max);
  pass.filter(*cloud_ptr);

  pass.setInputCloud(cloud_ptr);
  pass.setFilterFieldName("z");
  pass.setFilterLimits(z_min, z_max);
  pass.filter(*cloud_ptr);
}

static void CropBoxFilterCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_ptr,
                               const double &x_min, const double &x_max,
                               const double &y_min, const double &y_max,
                               const double &z_min, const double &z_max,
                               const bool &negative) {
  pcl::CropBox<pcl::PointXYZ> box_filter;
  box_filter.setMin(Eigen::Vector4f(x_min, y_min, z_min, 1.));
  box_filter.setMax(Eigen::Vector4f(x_max, y_max, z_max, 1.));
  box_filter.setInputCloud(cloud_ptr);
  box_filter.setNegative(negative);
  box_filter.filter(*cloud_ptr);
}

static std::string GetUserName() {
  uid_t userid;
  struct passwd *pwd;
  userid = getuid();
  pwd = getpwuid(userid);
  return pwd->pw_name;
}

AvoidFacotr AvoidProcess::GetCloudAvoidLevel(
    const sensor_msgs::PointCloud2::ConstPtr &cloud, const std::string &frame,
    const Avoid3DOption &al_option, const LaserScanOption &laser_option,
    const AvoidMap &avoid_map) {
  AvoidFacotr laser_lv;
  laser_lv.type = GetAvoidType(frame);

  std::vector<double> dist_list;
  dist_list.emplace_back(laser_lv.dist);

  // 避障2级触发了就不需要计算避障1级触发的距离
  bool slow_lv2_trigged = false;

  pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_ptr(
      new pcl::PointCloud<pcl::PointXYZ>);
  pcl::fromROSMsg(*cloud, *cloud_ptr);

  if (cloud_ptr->points.empty()) {
    // 激光点云为空视为故障激光
    LOG_ERROR("Cloud is empty!");
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    return laser_lv;
  }

  // roi滤波
  RoiFilterCloud(cloud_ptr, al_option.roi_area.x_min, al_option.roi_area.x_max,
                 al_option.roi_area.y_min, al_option.roi_area.y_max,
                 al_option.roi_area.z_min, al_option.roi_area.z_max);
                 
  pcl::VoxelGrid<pcl::PointXYZ> grid;
  grid.setLeafSize(0.05, 0.05, 0.05);
  grid.setInputCloud(cloud_ptr);
  grid.filter(*cloud_ptr);

  // 先补偿旋转到标准姿态
  if (al_option.offset_tf.roll != 0. || al_option.offset_tf.pitch != 0 ||
      al_option.offset_tf.yaw != 0) {
    Eigen::Affine3f transform = Eigen::Affine3f::Identity();
    transform.translation() << 0, 0, 0;  // 平移
    transform.rotate(
        Eigen::AngleAxisf(al_option.offset_tf.roll, Eigen::Vector3f::UnitX()));
    transform.rotate(
        Eigen::AngleAxisf(al_option.offset_tf.pitch, Eigen::Vector3f::UnitY()));
    transform.rotate(
        Eigen::AngleAxisf(al_option.offset_tf.yaw, Eigen::Vector3f::UnitZ()));
    pcl::transformPointCloud(*cloud_ptr, *cloud_ptr, transform);
  }

  // const std::string saved_pcd_path =
  //     "/home/" + GetUserName() + "/avoid_orign.pcd";
  // if (!cloud_ptr->empty()) {
  //   LOG_ERROR("SAVE POINTCLOUD!!!");
  //   if (-1 == pcl::io::savePCDFile(saved_pcd_path, *cloud_ptr)) {
  //     std::cout << "save pcd file failed." << std::endl;
  //     return laser_lv;
  //   } else {
  //     std::cout << "pcd file saved at " << saved_pcd_path << std::endl
  //               << std::endl;
  //   }
  // }

  // // roi滤波
  // RoiFilterCloud(cloud_ptr, al_option.roi_area.x_min, al_option.roi_area.x_max,
  //                al_option.roi_area.y_min, al_option.roi_area.y_max,
  //                al_option.roi_area.z_min, al_option.roi_area.z_max);

  // const std::string saved_pcd_path1 =
  //     "/home/" + GetUserName() + "/avoid_roi.pcd";
  // if (!cloud_ptr->empty()) {
  //   LOG_ERROR("SAVE POINTCLOUD!!!");
  //   if (-1 == pcl::io::savePCDFile(saved_pcd_path1, *cloud_ptr)) {
  //     std::cout << "save pcd file failed." << std::endl;
  //     return laser_lv;
  //   } else {
  //     std::cout << "pcd file saved at " << saved_pcd_path1 << std::endl
  //               << std::endl;
  //   }
  // }

  CropBoxFilterCloud(cloud_ptr, al_option.crop_area.x_min,
                     al_option.crop_area.x_max, al_option.crop_area.y_min,
                     al_option.crop_area.y_max, al_option.crop_area.z_min,
                     al_option.roi_area.z_max, true /* 过滤crop区域*/);

  // const std::string saved_pcd_path2 =
  //     "/home/" + GetUserName() + "/avoid_crop.pcd";
  // if (!cloud_ptr->empty()) {
  //   LOG_ERROR("SAVE POINTCLOUD!!!");
  //   if (-1 == pcl::io::savePCDFile(saved_pcd_path2, *cloud_ptr)) {
  //     std::cout << "save pcd file failed." << std::endl;
  //     return laser_lv;
  //   } else {
  //     std::cout << "pcd file saved at " << saved_pcd_path2 << std::endl
  //               << std::endl;
  //   }
  // }

  if (cloud_ptr->points.empty()) {
    laser_lv.lv = AvoidSpeedLevel::FREE;
    laser_lv.dist = 100.;
    return laser_lv;
  }
  // 旋转到车体坐标系下
  {
    Eigen::Affine3f transform = Eigen::Affine3f::Identity();
    transform.translation() << laser_option.tf.x, laser_option.tf.y,
        laser_option.tf.z;  // 平移
    transform.rotate(
        Eigen::AngleAxisf(laser_option.tf.roll, Eigen::Vector3f::UnitX()));
    transform.rotate(
        Eigen::AngleAxisf(laser_option.tf.pitch, Eigen::Vector3f::UnitY()));
    transform.rotate(
        Eigen::AngleAxisf(laser_option.tf.yaw, Eigen::Vector3f::UnitZ()));
    pcl::transformPointCloud(*cloud_ptr, *cloud_ptr, transform);
  }

  // const std::string saved_pcd_path3 =
  //     "/home/" + GetUserName() + "/avoid_transform.pcd";
  // if (!cloud_ptr->empty()) {
  //   LOG_ERROR("SAVE POINTCLOUD!!!");
  //   if (-1 == pcl::io::savePCDFile(saved_pcd_path3, *cloud_ptr)) {
  //     std::cout << "save pcd file failed." << std::endl;
  //     return laser_lv;
  //   } else {
  //     std::cout << "pcd file saved at " << saved_pcd_path3 << std::endl
  //               << std::endl;
  //   }
  // }

  std::vector<pcl::PointIndices> cluster_indices;
  // 欧式聚类分割
  pcl::search::KdTree<pcl::PointXYZ>::Ptr tree(
      new pcl::search::KdTree<pcl::PointXYZ>);
  tree->setInputCloud(cloud_ptr);
  pcl::EuclideanClusterExtraction<pcl::PointXYZ> ec;  // 欧式聚类对象
  ec.setClusterTolerance(0.07);  // 设置近邻搜索的搜索半径为4cm
  ec.setMinClusterSize(3);   // 设置一个聚类需要的最少的点数目
  ec.setSearchMethod(tree);  // 设置点云的搜索机制
  ec.setInputCloud(cloud_ptr);
  ec.extract(cluster_indices);

  // 从点云中提取聚类，并将点云索引保存在cluster_indices中
  if (cluster_indices.size() < 1) {
    laser_lv.lv = AvoidSpeedLevel::FREE;
    laser_lv.dist = 100.;
    return laser_lv;
  }

  for (int i = 0; i < cluster_indices.size(); i++) {
    for (auto &it : cluster_indices[i].indices) {
      Boost_Point pt(cloud_ptr->points[it].x, cloud_ptr->points[it].y);

      if (bg::within(pt, avoid_map.stop)) {
        laser_lv.lv = AvoidSpeedLevel::STOP;
        laser_lv.dist = 0;
        laser_lv.avoid_pt = Point(pt.x(), pt.y());
        return laser_lv;
      } else if (bg::within(pt, avoid_map.slow_level2)) {
        double dist = GetMinDistFromPtAndPoly(pt, avoid_map.stop);
        dist_list.emplace_back(dist);
        laser_lv.lv = std::max(laser_lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
        laser_lv.avoid_pt = Point(pt.x(), pt.y());
        slow_lv2_trigged = true;
      } else if (bg::within(pt, avoid_map.slow_level1) && !slow_lv2_trigged) {
        double dist = GetMinDistFromPtAndPoly(pt, avoid_map.stop);
        dist_list.emplace_back(dist);
        laser_lv.lv = std::max(laser_lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
        laser_lv.avoid_pt = Point(pt.x(), pt.y());
      } else {
        continue;
      }
    }
  }
  laser_lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
  return laser_lv;
}

void AvoidProcess::AvoidCloud0Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kAvoidCloud0Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kAvoidCloud0Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kAvoidCloud0Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kAvoidCloud0Topic);

    AvoidMap avoid_map;
    avoid_map = active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kAvoidCloud0Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::AvoidCloud1Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kAvoidCloud1Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kAvoidCloud1Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kAvoidCloud1Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kAvoidCloud1Topic);

    AvoidMap avoid_map;
    avoid_map = active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kAvoidCloud1Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::AvoidCloud2Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kAvoidCloud2Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kAvoidCloud2Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kAvoidCloud2Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kAvoidCloud2Topic);

    AvoidMap avoid_map;
    avoid_map = active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kAvoidCloud2Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::AvoidCloud3Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  std::string frame = cotek_topic::kAvoidCloud3Topic;
  laser_lv.type = GetAvoidType(frame);
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kAvoidCloud3Topic)
             ->Pulse()) {
      return;
    }

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kAvoidCloud3Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kAvoidCloud3Topic);

    // 前进时不认定为避障
    if (move_cmd_.velocity >= 0. && move_cmd_.direction >= 0.) {
      laser_lv.lv = AvoidSpeedLevel::FREE;
      laser_lv.dist = 100.;
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    // 堆高车叉腿高度处于低矮状态 取消避障
    if (vaild_height_.vaild && vaild_height_.height < 2500) {
      laser_lv.lv = AvoidSpeedLevel::FREE;
      laser_lv.dist = 100.;
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }
    // 3号点云激光处于取货路径时关闭避障
    if (weaken_avoid_info_.Vaild()) {
      laser_lv.lv = AvoidSpeedLevel::FREE;
      laser_lv.dist = 100.;
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    // 根据叉腿高度确定识别roi_zmin
    if (vaild_height_.vaild) {
      al_option.roi_area.z_min =
          (vaild_height_.height > 3500)
              ? al_option.roi_area.z_max -
                    math::ZeroPointMM2Meter(vaild_height_.height - 2500)
              : al_option.roi_area.z_max - 0.1;
    }

    AvoidMap avoid_map;
    avoid_map = active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::AvoidCloud4Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  std::string frame = cotek_topic::kAvoidCloud4Topic;
  laser_lv.type = GetAvoidType(frame);
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kAvoidCloud4Topic)
             ->Pulse()) {
      return;
    }

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kAvoidCloud4Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kAvoidCloud4Topic);

    // 前进时不认定为避障
    if (move_cmd_.velocity >= 0 && move_cmd_.direction >= 0) {
      laser_lv.lv = AvoidSpeedLevel::FREE;
      laser_lv.dist = 100.;
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    AvoidMap avoid_map;
    avoid_map = active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kAvoidCloud4Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::AvoidCloud5Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;

  std::string frame = cotek_topic::kAvoidCloud5Topic;
  laser_lv.type = GetAvoidType(frame);
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kAvoidCloud5Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kAvoidCloud5Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kAvoidCloud5Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kAvoidCloud5Topic);

    // 前进时不认定为避障
    if (move_cmd_.velocity >= 0 && move_cmd_.direction >= 0) {
      laser_lv.lv = AvoidSpeedLevel::FREE;
      laser_lv.dist = 100.;
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    AvoidMap avoid_map;
    avoid_map = active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kAvoidCloud5Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}

void AvoidProcess::NaviCloud0Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kNaviLaser0Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kNaviLaser0Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kNaviLaser0Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kNaviLaser0Topic);

    AvoidMap avoid_map;
    avoid_map = manual_ ? default_strategy_ : active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kNaviLaser0Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::NaviCloud1Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kNaviLaser1Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kNaviLaser1Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kNaviLaser1Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kNaviLaser1Topic);

    AvoidMap avoid_map;
    avoid_map = manual_ ? default_strategy_ : active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kNaviLaser1Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}
void AvoidProcess::NaviCloud2Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  AvoidFacotr laser_lv;
  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kNaviLaser2Topic)
             ->Pulse()) {
      return;
    }

    std::string frame = cotek_topic::kNaviLaser2Topic;

    // 屏蔽某避障激光
    if (avoid_strategy_.count(frame) > 0) {
      laser_lv.type = GetAvoidType(frame);
      msg_queue_.PushData(ros::Time::now(), laser_lv);
      return;
    }

    auto al_option = option_.algorithm_option.avoid_3d_options.at(
        cotek_topic::kNaviLaser2Topic);

    auto laser_option = ConfigHelper::Instance().GetLaserMap().at(
        cotek_topic::kNaviLaser2Topic);

    AvoidMap avoid_map;
    avoid_map = manual_ ? default_strategy_ : active_map_;

    auto factor =
        GetCloudAvoidLevel(cloud, frame, al_option, laser_option, avoid_map);

    msg_queue_.PushData(ros::Time::now(), factor);

  } catch (const std::exception &e) {
    LOG_ERROR(e.what());
    laser_lv.lv = AvoidSpeedLevel::STOP;
    laser_lv.dist = 0.;
    laser_lv.type = GetAvoidType(cotek_topic::kNaviLaser2Topic);
    msg_queue_.PushData(ros::Time::now(), laser_lv);
  }
}

// avoid laser 避障
void AvoidProcess::AvoidLaserCallback(
    const sensor_msgs::LaserScan::ConstPtr &scan) {
  AvoidFacotr avoid_laser_lv;

  try {
    if (!state_manager_ptr_->samplers().at(scan->header.frame_id)->Pulse()) {
      return;
    }
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM_THROTTLE(1, e.what() << ", cannot find sampler ratio of "
                                         << scan->header.frame_id);
    return;
  }
  // 屏蔽某避障激光

  if (avoid_strategy_.count(scan->header.frame_id) > 0) {
    avoid_laser_lv.type = GetAvoidType(scan->header.frame_id);
    msg_queue_.PushData(ros::Time::now(), avoid_laser_lv);
    return;
  }

  // get current message's avoid level, then filter
  try {
    const Transform &avoid_laser_tf =
        ConfigHelper::Instance().GetSensorTransform().at(scan->header.frame_id);
    auto &inner = ConfigHelper::Instance().GetAgvInner();
    const auto &laser_option =
        ConfigHelper::Instance().GetLaserMap().at(scan->header.frame_id);

    avoid_laser_lv = GetAvoidLaserAvoidLevel(
        scan, active_map_.slow_level1, active_map_.slow_level2,
        active_map_.stop, inner, laser_option, avoid_laser_tf);
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM("No configured avoid_laser_lv with frame "
                    << scan->header.frame_id);
    avoid_laser_lv.lv = AvoidSpeedLevel::STOP;
    avoid_laser_lv.dist = 0.;
  }

  avoid_laser_lv.type = GetAvoidType(scan->header.frame_id);

  msg_queue_.PushData(ros::Time::now(), avoid_laser_lv);
}

void AvoidProcess::LaserScanCallback(
    const sensor_msgs::LaserScan::ConstPtr &scan) {
  AvoidFacotr r2000_lv;

  try {
    if (!state_manager_ptr_->samplers().at(scan->header.frame_id)->Pulse()) {
      return;
    }
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM_THROTTLE(1, e.what() << ", cannot find sampler ratio of "
                                         << scan->header.frame_id);
    return;
  }

  // 屏蔽某避障导航

  if (avoid_strategy_.count(scan->header.frame_id) > 0) {
    r2000_lv.type = GetAvoidType(scan->header.frame_id);
    msg_queue_.PushData(ros::Time::now(), r2000_lv);
    return;
  }

  // get current message's avoid level, then filter
  try {
    const Transform &r2000_tf =
        ConfigHelper::Instance().GetSensorTransform().at(scan->header.frame_id);
    auto &inner = ConfigHelper::Instance().GetAgvInner();
    auto laser_option =
        ConfigHelper::Instance().GetLaserMap().at(scan->header.frame_id);

    AvoidMap avoid_map;
    avoid_map = manual_ ? default_strategy_ : active_map_;
    //  手动情况下导航激光依旧有避障，且手动导航激光为360
    if (manual_ &&
        BasicConfigHelper::Instance().agv_type() == AgvType::FORKLIFT) {
      laser_option.filter.angle_min = -3.1415926;
      laser_option.filter.angle_max = 3.1415926;
    }

    r2000_lv =
        GetScanAvoidLevel(scan, avoid_map.slow_level1, avoid_map.slow_level2,
                          avoid_map.stop, inner, laser_option, r2000_tf);
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM("No configured navi_laser with frame "
                    << scan->header.frame_id);
    r2000_lv.lv = AvoidSpeedLevel::STOP;
    r2000_lv.dist = 0.;
  }
  r2000_lv.type = GetAvoidType(scan->header.frame_id);
  sensor_avoid_level_.navi_laser = r2000_lv;
  msg_queue_.PushData(ros::Time::now(), r2000_lv);
}

void AvoidProcess::UltarsonicCallback(
    const cotek_msgs::ultrasonic_feedback::ConstPtr &data) {
  AvoidFacotr ultarsonic_lv;

  try {
    if (!state_manager_ptr_->samplers()
             .at(cotek_topic::kUltarsonicTopic)
             ->Pulse()) {
      return;
    }
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM_THROTTLE(
        1, e.what() << ", cannot find sampler ratio of ultrasonic");
    ultarsonic_lv.lv = AvoidSpeedLevel::STOP;
    ultarsonic_lv.dist = 0.;
    return;
  }

  // 关闭超声波避障
  if (avoid_strategy_.count(cotek_topic::kUltarsonicTopic) > 0) {
    ultarsonic_lv.type = AvoidLevel::AVOID_ULTRASONIC;
    msg_queue_.PushData(ros::Time::now(), ultarsonic_lv);
    return;
  }

  std::vector<Boost_Point> bt_point;
  for (int i = 0; i < 8; i++) {
    std::string tf_name = "ultrasonic" + std::to_string(i + 1);
    auto &&tf_map = ConfigHelper::Instance().GetSensorTransform();
    if (tf_map.count(tf_name) == 0) continue;

    const Transform &ultrasonic_tf =
        tf_map.at("ultrasonic" + std::to_string(i + 1));

    Boost_Point scan_pt(data->length[i], 0.);
    Boost_Point to_pt(0., 0.);
    Translate translate(ultrasonic_tf.x, ultrasonic_tf.y);
    // boot库旋转是左手坐标系
    Rotate rotate(-ultrasonic_tf.yaw);
    bg::transform(scan_pt, to_pt, rotate);
    bg::transform(to_pt, to_pt, translate);
    bt_point.push_back(to_pt);
  }

  ultarsonic_lv =
      GetUltrasonicAvoidLevel(bt_point, active_map_.slow_level1,
                              active_map_.slow_level2, active_map_.stop);

  sensor_avoid_level_.ultarsonic_avoid = ultarsonic_lv;
  ultarsonic_lv.type = AvoidLevel::AVOID_ULTRASONIC;
  msg_queue_.PushData(ros::Time::now(), ultarsonic_lv);
}

void AvoidProcess::SafetySettingCallback(
    const cotek_msgs::safety_setting::ConstPtr &setting) {
  static cotek_msgs::safety_setting last = *setting;
  // 避免重复赋值
  if (set_map_init_ && last == *setting) return;

  AvoidFacotr error_lv;
  if (0 == ConfigHelper::Instance().GetAvoidMap().count(setting->avoid_map)) {
    active_map_ = default_strategy_;
    LOG_ERROR_STREAM("Cannot find avoid strategy with id: "
                     << setting->avoid_map << "; set default as active");
    node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::CONFIG_ERROR);
    error_lv.lv = AvoidSpeedLevel::STOP;
    error_lv.dist = 0.;
    msg_queue_.PushData(ros::Time::now(), error_lv);
    return;
  }

  try {
    // 随时间缩小避障地图类型&&避障超时 启用最小避障地图
    if (setting->avoid_map ==
            static_cast<int>(AvoidMapType::TIME_DECREASE_AVOID_AREA) &&
        avoid_time_out_cnt_ > 0) {
      active_map_ = GetAvoidMap(ConfigHelper::Instance().GetAvoidMap().at(
          static_cast<int>(AvoidMapType::TIME_MIN_AVOID_AREA)));
    } else {
      active_map_ = GetAvoidMap(
          ConfigHelper::Instance().GetAvoidMap().at(setting->avoid_map));
    }

    avoid_strategy_.clear();
    for (const auto &stragety : setting->avoid_strategys) {
      avoid_strategy_[stragety] = true;
    }
    // 记录当前使能避障地图类型，用于特殊业务
    active_avoid_map_ = static_cast<AvoidMapType>(setting->avoid_map);

    set_map_init_ = true;
    last = *setting;

    // LOG_INFO_STREAM("Change avoid strategy to: " << setting->avoid_map);
  } catch (const std::out_of_range &e) {
    LOG_ERROR_STREAM("No this avoid map id: " << setting->avoid_map);
    node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::CONFIG_ERROR);
    error_lv.lv = AvoidSpeedLevel::STOP;
    error_lv.dist = 0.;
    msg_queue_.PushData(ros::Time::now(), error_lv);
    return;
  }
  return;
}

void AvoidProcess::ManualCallback(const std_msgs::Int32::ConstPtr &manual) {
  manual_ = manual->data;
  return;
}

void AvoidProcess::AddBumpReset(const cotek_msgs::bump_reset::ConstPtr &state) {
  return;
}

double AvoidProcess::CalFrontCameraDist(const std::string &name,
                                        const double &current_dist) {
  double mindst = kSafetyDist;

  if (front_camera_dist_map_.count(name) == 0) {
    front_camera_dist_map_[name] = FrontAvoidCamera();
  }

  front_camera_dist_map_[name].dist_list.emplace_back(current_dist);
  if (front_camera_dist_map_[name].dist_list.size() < 6) {
    return mindst;
  }

  std::list<double> rs_avoid_tmp(front_camera_dist_map_[name].dist_list);
  rs_avoid_tmp.sort();

  front_camera_dist_map_[name].dist_list.pop_front();

  std::list<double>::iterator iter = rs_avoid_tmp.begin();
  if (std::fabs(front_camera_dist_map_[name].previous_value - *iter) < 0.15) {
    mindst = front_camera_dist_map_[name].previous_value;
  } else {
    front_camera_dist_map_[name].previous_value = *iter;
    mindst = front_camera_dist_map_[name].previous_value;
  }
  return mindst;
}

void AvoidProcess::AvoidCameraCallback(
    const cotek_msgs::avoid_camera::ConstPtr &data) {
  try {
    if (!state_manager_ptr_->samplers().at(data->header.frame_id)->Pulse()) {
      return;
    }
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM_THROTTLE(1, e.what() << ", cannot find sampler ratio of "
                                         << data->header.frame_id);
    return;
  }

  AvoidFacotr af;

  // 屏蔽某避障相机
  if (avoid_strategy_.count(data->header.frame_id) > 0) {
    af.type = GetAvoidType(data->header.frame_id);
    msg_queue_.PushData(ros::Time::now(), af);
    return;
  }

  if (data->avoid_point_list.empty()) {
    af.lv = AvoidSpeedLevel::STOP;
    af.avoid_pt = Point(0., 0.);
    af.dist = 0.;
  } else {
    // 前向相机暂时只出前方一个点
    double mindst = data->avoid_point_list[0].x;

    mindst = CalFrontCameraDist(data->header.frame_id, mindst);

    const Transform &camera_tf =
        ConfigHelper::Instance().GetSensorTransform().at(
            GetAvoidCameraFrame(data->header.frame_id));

    af = GetCameraDistAvoidLevel(mindst, active_map_.slow_level1,
                                 active_map_.slow_level2, active_map_.stop,
                                 camera_tf);
  }

  af.type = GetAvoidType(data->header.frame_id);
  msg_queue_.PushData(ros::Time::now(), af);
}

void AvoidProcess::BackAvoidCameraCallback(
    const cotek_msgs::avoid_camera::ConstPtr &data) {
  try {
    if (!state_manager_ptr_->samplers().at(data->header.frame_id)->Pulse()) {
      return;
    }
  } catch (const std::out_of_range &e) {
    LOG_WARN_STREAM_THROTTLE(1, e.what() << ", cannot find sampler ratio of "
                                         << data->header.frame_id);
    return;
  }

  AvoidFacotr af;
  // 屏蔽某避障相机
  if (avoid_strategy_.count(data->header.frame_id) > 0) {
    af.type = GetAvoidType(data->header.frame_id);
    msg_queue_.PushData(ros::Time::now(), af);
    return;
  }

  const Transform &camera_tf = ConfigHelper::Instance().GetSensorTransform().at(
      GetAvoidCameraFrame(data->header.frame_id));

  af = GetCameraPointsAvoidLevel(data, active_map_.slow_level1,
                                 active_map_.slow_level2, active_map_.stop,
                                 camera_tf);

  af.type = GetAvoidType(data->header.frame_id);
  msg_queue_.PushData(ros::Time::now(), af);
}

AvoidFacotr AvoidProcess::GetCameraPointsAvoidLevel(
    const cotek_msgs::avoid_camera::ConstPtr &data, const Polygon &slow_level1,
    const Polygon &slow_level2, const Polygon &stop, const Transform &tf) {
  AvoidFacotr lv;

  std::vector<double> dist_list;
  dist_list.emplace_back(lv.dist);

  // 避障2级触发了就不需要计算避障1级触发的距离
  bool slow_lv2_trigged = false;

  for (auto &point : data->avoid_point_list) {
    Boost_Point camera_pt(point.x, point.y);
    Boost_Point to_pt(0., 0.);
    Translate translate(tf.x, tf.y);
    // boost库旋转是左手坐标系
    Rotate rotate(-tf.yaw);
    bg::transform(camera_pt, to_pt, rotate);
    bg::transform(to_pt, to_pt, translate);

    if (bg::within(to_pt, stop)) {
      lv.lv = AvoidSpeedLevel::STOP;
      lv.dist = 0;
      return lv;
    } else if (bg::within(to_pt, slow_level2)) {
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
      slow_lv2_trigged = true;
    } else if (bg::within(to_pt, slow_level1) && !slow_lv2_trigged) {
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
    } else {
      continue;
    }
  }
  lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
  return lv;
}

AvoidFacotr AvoidProcess::GetCameraDistAvoidLevel(const double &dist,
                                                  const Polygon &slow_level1,
                                                  const Polygon &slow_level2,
                                                  const Polygon &stop,
                                                  const Transform &tf) {
  AvoidFacotr lv;
  Boost_Point camera_pt(dist, 0.);
  Boost_Point to_pt(0., 0.);
  Translate translate(tf.x, tf.y);
  // boost库旋转是左手坐标系
  Rotate rotate(-tf.yaw);

  bg::transform(camera_pt, to_pt, rotate);
  bg::transform(to_pt, to_pt, translate);

  if (bg::within(to_pt, stop)) {
    // 自定义地图8避免相机干涉问题,仅减速不避障
    if (active_avoid_map_ == AvoidMapType::TIME_MIN_AVOID_AREA) {
      lv.lv = AvoidSpeedLevel::SLOWLEVEL2;
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      lv.dist = 0.1;
    } else {
      lv.lv = AvoidSpeedLevel::STOP;
      lv.dist = 0;
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      return lv;
    }

  } else if (bg::within(to_pt, slow_level2)) {
    lv.lv = AvoidSpeedLevel::SLOWLEVEL2;
    lv.dist = GetMinDistFromPtAndPoly(to_pt, stop);
    lv.avoid_pt = Point(to_pt.x(), to_pt.y());
  } else if (bg::within(to_pt, slow_level1)) {
    lv.lv = AvoidSpeedLevel::SLOWLEVEL1;
    lv.dist = GetMinDistFromPtAndPoly(to_pt, slow_level2);
    lv.avoid_pt = Point(to_pt.x(), to_pt.y());
  }
  return lv;
}

// AvoidFacotr AvoidProcess::GetPederstrianAvoidLevel(
//     const cotek_msgs::multi_pedestrian_pose::ConstPtr &pose_list,
//     const Polygon &slow_level1, const Polygon &slow_level2, const Polygon
//     &stop, const Transform &tf) {
//   AvoidFacotr lv;
//   std::vector<double> dist_list;

//   dist_list.emplace_back(lv.dist);
//   // 避障2级触发了就不需要计算避障1级触发的距离
//   bool slow_lv2_trigged = false;

//   for (const auto &pose : pose_list->multi_pose) {
//     Boost_Point pt(pose.x, pose.y);
//     Boost_Point to_pt(0., 0.);
//     Translate translate(tf.x, tf.y);
//     Rotate rotate(tf.yaw);

//     bg::transform(pt, to_pt, rotate);
//     bg::transform(to_pt, to_pt, translate);

//     if (bg::within(to_pt, stop)) {
//       lv.lv = AvoidSpeedLevel::STOP;
//       lv.dist = 0;
//       return lv;
//     } else if (bg::within(to_pt, slow_level2)) {
//       double dist = GetMinDistFromPtAndPoly(to_pt, stop);
//       dist_list.emplace_back(dist);
//       lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
//       slow_lv2_trigged = true;
//     } else if (bg::within(to_pt, slow_level1) && !slow_lv2_trigged) {
//       lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
//       double dist = GetMinDistFromPtAndPoly(to_pt, stop);
//       dist_list.emplace_back(dist);
//     } else {
//       continue;
//     }
//   }
//   lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
//   return lv;
// }

AvoidFacotr AvoidProcess::GetUltrasonicAvoidLevel(
    const std::vector<Boost_Point> &scan_pt, const Polygon &slow_level1,
    const Polygon &slow_level2, const Polygon &stop) {
  AvoidFacotr lv;

  // 避障2级触发了就不需要计算避障1级触发的距离
  bool slow_lv2_trigged = false;
  std::vector<double> dist_list;
  dist_list.emplace_back(lv.dist);

  for (auto to_pt : scan_pt) {
    if (bg::within(to_pt, stop)) {
      lv.lv = AvoidSpeedLevel::STOP;
      lv.dist = 0;
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      return lv;
    } else if (bg::within(to_pt, slow_level2)) {
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      slow_lv2_trigged = true;
    } else if (bg::within(to_pt, slow_level1) && !slow_lv2_trigged) {
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      dist_list.emplace_back(dist);
    } else {
      continue;
    }
  }
  lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
  return lv;
}

AvoidFacotr AvoidProcess::GetScanAvoidLevel(
    const sensor_msgs::LaserScan::ConstPtr &scan, const Polygon &slow_level1,
    const Polygon &slow_level2, const Polygon &stop, const Polygon &inner,
    const LaserScanOption &option, const Transform &tf) {
  AvoidFacotr lv;
  if (scan->ranges.empty()) {
    lv.lv = AvoidSpeedLevel::STOP;
    lv.dist = 0.;
    lv.avoid_pt = Point(0, 0);
    return lv;
  }
  std::vector<double> dist_list;
  dist_list.emplace_back(lv.dist);
  Eigen::Vector2d yaw_vector(cos(state_manager_ptr_->pose().x()),
                             sin(state_manager_ptr_->pose().y()));
  // 避障2级触发了就不需要计算避障1级触发的距离
  bool slow_lv2_trigged = false;
  int stop1_cnt = 0;
  int stop2_cnt = 0;
  for (std::size_t i = 0; i < scan->ranges.size();
       i += option.scan_sample_step) {
    auto angle = option.filter.positive
                     ? angles::normalize_angle(option.angle_min +
                                               i * option.angle_increment)
                     : angles::normalize_angle(option.angle_max -
                                               i * option.angle_increment);

    if (angle < option.filter.angle_min || angle > option.filter.angle_max ||
        scan->ranges[i] > option.range_max ||
        scan->ranges[i] < option.range_min) {
      continue;
    }

    if (!CheckLaserPointNeiberVaild(scan, i, option, 0.1)) continue;

    Boost_Point scan_pt(scan->ranges[i] * std::cos(angle),
                        scan->ranges[i] * std::sin(angle));
    Boost_Point to_pt(0., 0.);
    Translate translate(tf.x, tf.y);
    // boost库旋转是左手坐标系
    Rotate rotate(-tf.yaw);

    bg::transform(scan_pt, to_pt, rotate);
    bg::transform(to_pt, to_pt, translate);
    Eigen::Vector2d scan_vector(to_pt.x(), to_pt.y());

    if (bg::within(to_pt, stop)) {
      if (scan->ranges[i] < 0.5) {
        if (stop1_cnt++ > option.filter.filter_laser_point) {
          lv.lv = AvoidSpeedLevel::STOP;
          lv.dist = 0;
          lv.avoid_pt = Point(to_pt.x(), to_pt.y());
          return lv;
        }
        continue;
      }
      stop1_cnt = 0;

      if (scan->ranges[i] < 1.0) {
        if (stop2_cnt++ > option.filter.filter_laser_point * 0.3) {
          lv.lv = AvoidSpeedLevel::STOP;
          lv.dist = 0;
          lv.avoid_pt = Point(to_pt.x(), to_pt.y());
          return lv;
        }
        continue;
      }
      stop2_cnt = 0;

      lv.lv = AvoidSpeedLevel::STOP;
      lv.dist = 0;
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      return lv;
    }

    else if (bg::within(to_pt, slow_level2)) {
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      slow_lv2_trigged = true;
    } else if (bg::within(to_pt, slow_level1) && !slow_lv2_trigged) {
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
    } else {
      continue;
    }
  }
  lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
  return lv;
}

bool AvoidProcess::CheckLaserPointNeiberVaild(
    const sensor_msgs::LaserScan::ConstPtr &scan, const int &index,
    const LaserScanOption &option, const double &margin) {
  if ((index + 1) > (scan->ranges.size() - 1)) return false;

  auto angle = option.filter.positive
                   ? angles::normalize_angle(option.angle_min +
                                             index * option.angle_increment)
                   : angles::normalize_angle(option.angle_max -
                                             index * option.angle_increment);

  auto next_angle =
      option.filter.positive
          ? angles::normalize_angle(option.angle_min +
                                    (index + 1) * option.angle_increment)
          : angles::normalize_angle(option.angle_max -
                                    (index + 1) * option.angle_increment);

  cotek_geometry::Point scan_pt(scan->ranges[index] * std::cos(angle),
                                scan->ranges[index] * std::sin(angle));

  cotek_geometry::Point next_pt(scan->ranges[index + 1] * std::cos(next_angle),
                                scan->ranges[index + 1] * std::sin(next_angle));

  if (math::GetDistance(next_pt, scan_pt) < margin) return true;

  return false;
}

AvoidFacotr AvoidProcess::GetSpecialLaserAvoidLevel(
    const std::string &topic, const sensor_msgs::LaserScan::ConstPtr &scan,
    const Polygon &slow_level1, const Polygon &slow_level2, const Polygon &stop,
    const Polygon &inner, const LaserScanOption &option, const Transform &tf) {
  AvoidFacotr lv;
  if (scan->ranges.empty()) {
    lv.lv = AvoidSpeedLevel::STOP;
    lv.dist = 0.;
    return lv;
  }
  // 前进时不认定为避障
  if (move_cmd_.velocity >= 0 && move_cmd_.direction >= 0) {
    lv.lv = AvoidSpeedLevel::FREE;
    lv.type = AvoidLevel::NONE;
    lv.dist = 100.;
    return lv;
  }

  // 拉线编码器在底部的时候该激光不避障
  if (topic == cotek_topic::kAvoidLaser3Topic && vaild_height_.vaild &&
      vaild_height_.height < 2500) {
    lv.lv = AvoidSpeedLevel::FREE;
    lv.type = AvoidLevel::NONE;
    lv.dist = 100.;
    return lv;
  }

  std::vector<double> dist_list;
  dist_list.emplace_back(lv.dist);

  // 避障2级触发了就不需要计算避障1级触发的距离
  bool slow_lv2_trigged = false;
  int stop1_cnt = 0;
  int stop2_cnt = 0;

  for (std::size_t i = 0; i < scan->ranges.size();
       i += option.scan_sample_step) {
    auto angle = option.filter.positive
                     ? angles::normalize_angle(option.angle_min +
                                               i * option.angle_increment)
                     : angles::normalize_angle(option.angle_max -
                                               i * option.angle_increment);

    if (angle < option.filter.angle_min || angle > option.filter.angle_max ||
        scan->ranges[i] > option.range_max ||
        scan->ranges[i] < option.range_min) {
      continue;
    }

    /*叉腿底部时倒车激光视为单线激光*/
    if ((topic == cotek_topic::kAvoidLaser4Topic ||
         topic == cotek_topic::kAvoidLaser5Topic) &&
        weaken_avoid_info_.Vaild() &&
        fork_up_down_state_ == static_cast<uint8_t>(ForkStateType::DOWN) &&
        ((angle < -0.03 || angle > 0.03))) {
      continue;
    }

    if (!CheckLaserPointNeiberVaild(scan, i, option, 0.1)) continue;

    Boost_Point scan_pt(scan->ranges[i] * std::cos(angle),
                        scan->ranges[i] * std::sin(angle));
    Boost_Point to_pt(0., 0.);
    Translate translate(tf.x, tf.y);
    // boot库旋转是左手坐标系
    Rotate rotate(-tf.yaw);

    bg::transform(scan_pt, to_pt, rotate);
    bg::transform(to_pt, to_pt, translate);
    Eigen::Vector2d scan_vector(to_pt.x(), to_pt.y());

    if (bg::within(to_pt, stop)) {
      // 激光有杂点 需超过多个点才算障碍物
      if (scan->ranges[i] < 0.4) {
        if (stop1_cnt++ > option.filter.filter_laser_point) {
          lv.lv = AvoidSpeedLevel::STOP;
          lv.dist = 0;
          lv.avoid_pt = Point(to_pt.x(), to_pt.y());
          return lv;
        }
        continue;
      }
      stop1_cnt = 0;

      if (scan->ranges[i] < 0.7) {
        if (stop2_cnt++ > option.filter.filter_laser_point * 0.3) {
          lv.lv = AvoidSpeedLevel::STOP;
          lv.dist = 0;
          lv.avoid_pt = Point(to_pt.x(), to_pt.y());
          return lv;
        }
        continue;
      }
      stop2_cnt = 0;

      lv.lv = AvoidSpeedLevel::STOP;
      lv.dist = 0;
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      return lv;
    } else if (bg::within(to_pt, slow_level2)) {
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      slow_lv2_trigged = true;
    } else if (bg::within(to_pt, slow_level1) && !slow_lv2_trigged) {
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
      double dist = GetMinDistFromPtAndPoly(to_pt, slow_level2);
      dist_list.emplace_back(dist);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
    } else {
      continue;
    }
  }
  lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
  return lv;
}
AvoidFacotr AvoidProcess::GetAvoidLaserAvoidLevel(
    const sensor_msgs::LaserScan::ConstPtr &scan, const Polygon &slow_level1,
    const Polygon &slow_level2, const Polygon &stop, const Polygon &inner,
    const LaserScanOption &option, const Transform &tf) {
  if (scan->header.frame_id == cotek_topic::kAvoidLaser3Topic ||
      scan->header.frame_id == cotek_topic::kAvoidLaser4Topic ||
      scan->header.frame_id == cotek_topic::kAvoidLaser5Topic) {
    return GetSpecialLaserAvoidLevel(scan->header.frame_id, scan, slow_level1,
                                     slow_level2, stop, inner, option, tf);
  }

  AvoidFacotr lv;
  if (scan->ranges.empty()) {
    lv.lv = AvoidSpeedLevel::STOP;
    lv.dist = 0.;
    return lv;
  }

  std::vector<double> dist_list;
  dist_list.emplace_back(lv.dist);

  // 避障2级触发了就不需要计算避障1级触发的距离
  bool slow_lv2_trigged = false;
  int stop1_cnt = 0;
  int stop2_cnt = 0;

  for (std::size_t i = 0; i < scan->ranges.size();
       i += option.scan_sample_step) {
    auto angle = option.filter.positive
                     ? angles::normalize_angle(option.angle_min +
                                               i * option.angle_increment)
                     : angles::normalize_angle(option.angle_max -
                                               i * option.angle_increment);

    if (angle < option.filter.angle_min || angle > option.filter.angle_max ||
        scan->ranges[i] > option.range_max ||
        scan->ranges[i] < option.range_min) {
      continue;
    }

    if (!CheckLaserPointNeiberVaild(scan, i, option, 0.1)) continue;

    Boost_Point scan_pt(scan->ranges[i] * std::cos(angle),
                        scan->ranges[i] * std::sin(angle));
    Boost_Point to_pt(0., 0.);
    Translate translate(tf.x, tf.y);
    // boot库旋转是左手坐标系
    Rotate rotate(-tf.yaw);

    bg::transform(scan_pt, to_pt, rotate);
    bg::transform(to_pt, to_pt, translate);
    Eigen::Vector2d scan_vector(to_pt.x(), to_pt.y());

    if (bg::within(to_pt, stop)) {
      // 激光有杂点 需超过多个点才算障碍物
      if (scan->ranges[i] < 0.4) {
        if (stop1_cnt++ > option.filter.filter_laser_point) {
          lv.lv = AvoidSpeedLevel::STOP;
          lv.dist = 0;
          lv.avoid_pt = Point(to_pt.x(), to_pt.y());
          return lv;
        }
        continue;
      }
      stop1_cnt = 0;

      if (scan->ranges[i] < 0.7) {
        if (stop2_cnt++ > option.filter.filter_laser_point * 0.3) {
          lv.lv = AvoidSpeedLevel::STOP;
          lv.dist = 0;
          lv.avoid_pt = Point(to_pt.x(), to_pt.y());
          return lv;
        }
        continue;
      }
      stop2_cnt = 0;

      lv.lv = AvoidSpeedLevel::STOP;
      lv.dist = 0;
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      return lv;
    } else if (bg::within(to_pt, slow_level2)) {
      double dist = GetMinDistFromPtAndPoly(to_pt, stop);
      dist_list.emplace_back(dist);
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL2);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
      slow_lv2_trigged = true;
    } else if (bg::within(to_pt, slow_level1) && !slow_lv2_trigged) {
      lv.lv = std::max(lv.lv, AvoidSpeedLevel::SLOWLEVEL1);
      double dist = GetMinDistFromPtAndPoly(to_pt, slow_level2);
      dist_list.emplace_back(dist);
      lv.avoid_pt = Point(to_pt.x(), to_pt.y());
    } else {
      continue;
    }
  }
  lv.dist = *(std::min_element(dist_list.begin(), dist_list.end()));
  return lv;
}

Polygon AvoidProcess::TransformPoly(const Polygon &from_poly,
                                    const Transform &tf) {
  Translate translate(-tf.x, -tf.y);
  Rotate rotate(tf.yaw);

  Polygon to_poly;
  for (const auto &from_pt : from_poly.outer()) {
    Boost_Point to_pt(0.0, 0.0);
    bg::transform(from_pt, to_pt, translate);
    bg::transform(to_pt, to_pt, rotate);
    bg::append(to_poly.outer(), to_pt);
  }

  to_poly.inners().resize(1);
  for (const auto &from_pt : from_poly.inners()[0]) {
    Boost_Point to_pt(0.0, 0.0);
    bg::transform(from_pt, to_pt, translate);
    bg::transform(to_pt, to_pt, rotate);
    bg::append(to_poly.inners()[0], to_pt);
  }

  return to_poly;
}

AvoidMap AvoidProcess::GetAvoidMap(
    const std::map<std::string, std::vector<Boost_Point>> area_map) {
  AvoidMap s;

  // 有空地图区域则返回默认避障策略对应的区域
  const auto &slow_level1 = area_map.at(kAvoidSlowLevel1Name).empty()
                                ? default_map_.at(kAvoidSlowLevel1Name)
                                : area_map.at(kAvoidSlowLevel1Name);
  const auto &slow_level2 = area_map.at(kAvoidSlowLevel2Name).empty()
                                ? default_map_.at(kAvoidSlowLevel2Name)
                                : area_map.at(kAvoidSlowLevel2Name);
  const auto &stop = area_map.at(kAvoidStopName).empty()
                         ? default_map_.at(kAvoidStopName)
                         : area_map.at(kAvoidStopName);

  const auto &inner = ConfigHelper::Instance().GetAgvInner();

  // set avoid area: seq:  slow_level1 --> level2 --> stop
  for (auto &point : slow_level1) {
    bg::append(s.slow_level1.outer(), point);
  }
  bg::append(s.slow_level1.outer(), slow_level1.at(0));

  s.slow_level1.inners().resize(1);
  for (auto &point : slow_level2) {
    // bg::append(s.slow_level1.inners()[0], point);
    bg::append(s.slow_level2.outer(), point);
  }
  // bg::append(s.slow_level1.inners()[0], slow_level2.at(0));
  bg::append(s.slow_level2.outer(), slow_level2.at(0));

  s.slow_level2.inners().resize(1);
  for (auto &point : stop) {
    // bg::append(s.slow_level2.inners()[0], point);
    bg::append(s.stop.outer(), point);
  }
  // bg::append(s.slow_level2.inners()[0], stop.at(0));
  bg::append(s.stop.outer(), stop.at(0));

  s.stop.inners().resize(1);
  for (auto &point : inner.outer()) {
    bg::append(s.slow_level1.inners()[0], point);
    bg::append(s.slow_level2.inners()[0], point);
    bg::append(s.stop.inners()[0], point);
  }
  bg::append(s.slow_level1.inners()[0], inner.outer().at(0));
  bg::append(s.slow_level2.inners()[0], inner.outer().at(0));
  bg::append(s.stop.inners()[0], inner.outer().at(0));

  return s;
}

double AvoidProcess::GetMinDistFromPtAndPoly(const Boost_Point &pt,
                                             const Polygon &poly) {
  if (poly.outer().size() < 4) {
    LOG_ERROR("Poly must have more then 4 points !!!");
    return 0.;
  }
  double dist = 100;
  Boost_Point last_point = poly.outer()[0];
  for (auto iter = poly.outer().begin() + 1; iter != poly.outer().end();
       iter++) {
    Boost_Segment segment_tmp(last_point, *iter);
    dist = std::min(dist, bg::distance(pt, segment_tmp /*点与线段距离*/));
    last_point = *iter;
  }
  return dist;
}

}  // namespace cotek_avoid
