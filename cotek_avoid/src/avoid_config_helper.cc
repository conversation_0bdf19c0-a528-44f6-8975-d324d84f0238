/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include <cotek_avoid/avoid_config_helper.h>

#include <exception>
#include <fstream>

#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/math.h"

namespace cotek_avoid {

bool ConfigHelper::LoadBasicConfig() {
  if (!BasicConfigHelper::Instance().ReLoadConfig()) {
    return false;
  }
  sensor_tf_ = BasicConfigHelper::Instance().transform_map();
  LOG_INFO("--------- TF config ---------");

  for (auto &tf : sensor_tf_) {
    LOG_INFO_STREAM(tf.first << ": "
                             << "(" << tf.second.x << "," << tf.second.y << ","
                             << math::Rad2Deg(tf.second.yaw) << ")");
  }

  laser_map_ = BasicConfigHelper::Instance().laser_map();

  for (auto &laser : laser_map_) {
    LOG_INFO_STREAM("-----------" << laser.first << "-----------");
    LOG_INFO_STREAM("topic: " << laser.second.topic);
    LOG_INFO_STREAM("use_3d: " << laser.second.use_3d);
    LOG_INFO_STREAM("scan_sample_step: " << laser.second.scan_sample_step);
    LOG_INFO_STREAM("angle_min: " << laser.second.angle_min);
    LOG_INFO_STREAM("angle_max: " << laser.second.angle_max);
    LOG_INFO_STREAM("range_min: " << laser.second.range_min);
    LOG_INFO_STREAM("range_max: " << laser.second.range_max);
    LOG_INFO_STREAM("angle_increment: " << laser.second.angle_increment);
    LOG_INFO_STREAM("filter positive : " << laser.second.filter.positive);
    LOG_INFO_STREAM("filter angle_min : " << laser.second.filter.angle_min);
    LOG_INFO_STREAM("filter angle_max : " << laser.second.filter.angle_max);
    LOG_INFO_STREAM("filter filter_laser_point : "
                    << laser.second.filter.filter_laser_point);
  }
  return true;
}

bool ConfigHelper::LoadAvoidAreaConfig(const std::string &json_str) {
  try {
    auto &&json = Json::parse(json_str);
    if (!json.contains("map_area")) return false;
    LOG_INFO_STREAM("Map_area list contains " << json["map_area"].size()
                                              << " map(s).");
    // agv inner config
    LOG_INFO("--------- agv inner ---------");

    for (uint8_t i = 0; i < 4; i++) {
      double x = 0.;
      double y = 0.;
      bg::append(agv_inner_.outer(), Boost_Point(x, y));
    }

    LOG_INFO("--------- Map area ---------");

    auto &map_array = json["map_area"];
    for (auto &map : map_array) {
      std::map<std::string, std::vector<Boost_Point>> strategy;

      auto &&slow_level1 = map[kAvoidSlowLevel1Name];
      auto &&slow_level2 = map[kAvoidSlowLevel2Name];
      auto &&stop = map[kAvoidStopName];

      LOG_INFO_STREAM("Map_id: " << map["id"].get<int>());

      LOG_INFO_STREAM("Slow1:");
      strategy.emplace(kAvoidSlowLevel1Name, GetCoordinateVector(slow_level1));

      LOG_INFO_STREAM("Slow2:");
      strategy.emplace(kAvoidSlowLevel2Name, GetCoordinateVector(slow_level2));

      LOG_INFO_STREAM("Stop:");
      strategy.emplace(kAvoidStopName, GetCoordinateVector(stop));

      avoid_map_.emplace(map["id"].get<int>(), strategy);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    LOG_ERROR_STREAM("Avoid area json parse is error !!!");
    return false;
  }

  return true;
}

std::vector<Boost_Point> ConfigHelper::GetCoordinateVector(const Json &vj) {
  std::vector<Boost_Point> vp;
  for (auto &coord : vj) {
    double x = coord["x"].get<double>();
    double y = coord["y"].get<double>();
    LOG_INFO_STREAM("coord:(" << x << " ," << y << ")");
    vp.emplace_back(x, y);
  }
  return vp;
}

bool ConfigHelper::LoadAvoidConfig(const std::string &json_str,
                                   cotek_avoid::AvoidOption *option) {
  try {
    auto &&json = Json::parse(json_str);
    LOG_INFO("------------------AVOID_CONFIG---------------");
    LOG_INFO("---------------common_config option---------------");
    option->controller_frequency =
        json["common_config"]["controller_frequency"].get<double>();
    LOG_INFO_STREAM("controller_frequency: " << option->controller_frequency);

    option->node_diagnostic_frequency =
        json["common_config"]["node_diagnostic_frequency"].get<double>();
    LOG_INFO_STREAM(
        "node_diagnostic_frequency: " << option->node_diagnostic_frequency);

    if (json["common_config"].contains("overtime")) {
      option->overtime = json["common_config"]["overtime"].get<double>();
    } else {
      option->overtime = 60.;
    }
    LOG_INFO_STREAM("overtime: " << option->overtime);

    LOG_INFO("---------------topic option---------------");

    option->topic_option.io_state =
        json["enable_config"]["enable_io_state"].get<bool>();
    LOG_INFO_STREAM("enable_io_state: " << option->topic_option.io_state);

    option->topic_option.avoid_ultrasonic =
        json["enable_config"]["enable_ultrasonic"].get<bool>();
    LOG_INFO_STREAM(
        "enable_ultrasonic: " << option->topic_option.avoid_ultrasonic);

    option->topic_option.navi_laser_0 =
        json["enable_config"]["enable_navi_laser_0"].get<bool>();
    LOG_INFO_STREAM(
        "enable_navi_laser_0: " << option->topic_option.navi_laser_0);

    option->topic_option.navi_laser_1 =
        json["enable_config"]["enable_navi_laser_1"].get<bool>();
    LOG_INFO_STREAM(
        "enable_navi_laser_1: " << option->topic_option.navi_laser_1);

    option->topic_option.navi_laser_2 =
        json["enable_config"]["enable_navi_laser_2"].get<bool>();
    LOG_INFO_STREAM(
        "enable_navi_laser_2: " << option->topic_option.navi_laser_2);

    option->topic_option.avoid_laser_0 =
        json["enable_config"]["enable_avoid_laser_0"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_laser_0: " << option->topic_option.avoid_laser_0);

    option->topic_option.avoid_laser_1 =
        json["enable_config"]["enable_avoid_laser_1"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_laser_1: " << option->topic_option.avoid_laser_1);

    option->topic_option.avoid_laser_2 =
        json["enable_config"]["enable_avoid_laser_2"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_laser_2: " << option->topic_option.avoid_laser_2);

    option->topic_option.avoid_laser_3 =
        json["enable_config"]["enable_avoid_laser_3"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_laser_3: " << option->topic_option.avoid_laser_3);

    option->topic_option.avoid_laser_4 =
        json["enable_config"]["enable_avoid_laser_4"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_laser_4: " << option->topic_option.avoid_laser_4);

    option->topic_option.avoid_laser_5 =
        json["enable_config"]["enable_avoid_laser_5"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_laser_5: " << option->topic_option.avoid_laser_5);

    if (json["enable_config"].contains("enable_avoid_cloud_0")) {
      option->topic_option.avoid_cloud_0 =
          json["enable_config"]["enable_avoid_cloud_0"].get<bool>();
    } else {
      option->topic_option.avoid_cloud_0 = false;
    }
    LOG_INFO_STREAM(
        "enable_avoid_cloud_0: " << option->topic_option.avoid_cloud_0);

    if (json["enable_config"].contains("enable_avoid_cloud_1")) {
      option->topic_option.avoid_cloud_1 =
          json["enable_config"]["enable_avoid_cloud_1"].get<bool>();
    } else {
      option->topic_option.avoid_cloud_1 = false;
    }
    LOG_INFO_STREAM(
        "enable_avoid_cloud_1: " << option->topic_option.avoid_cloud_1);

    if (json["enable_config"].contains("enable_avoid_cloud_2")) {
      option->topic_option.avoid_cloud_2 =
          json["enable_config"]["enable_avoid_cloud_2"].get<bool>();
    } else {
      option->topic_option.avoid_cloud_2 = false;
    }
    LOG_INFO_STREAM(
        "enable_avoid_cloud_2: " << option->topic_option.avoid_cloud_2);

    if (json["enable_config"].contains("enable_avoid_cloud_3")) {
      option->topic_option.avoid_cloud_3 =
          json["enable_config"]["enable_avoid_cloud_3"].get<bool>();
    } else {
      option->topic_option.avoid_cloud_3 = false;
    }
    LOG_INFO_STREAM(
        "enable_avoid_cloud_3: " << option->topic_option.avoid_cloud_3);

    if (json["enable_config"].contains("enable_avoid_cloud_4")) {
      option->topic_option.avoid_cloud_4 =
          json["enable_config"]["enable_avoid_cloud_4"].get<bool>();
    } else {
      option->topic_option.avoid_cloud_4 = false;
    }
    LOG_INFO_STREAM(
        "enable_avoid_cloud_4: " << option->topic_option.avoid_cloud_4);

    if (json["enable_config"].contains("enable_avoid_cloud_5")) {
      option->topic_option.avoid_cloud_5 =
          json["enable_config"]["enable_avoid_cloud_5"].get<bool>();
    } else {
      option->topic_option.avoid_cloud_5 = false;
    }
    LOG_INFO_STREAM(
        "enable_avoid_cloud_5: " << option->topic_option.avoid_cloud_5);

    option->topic_option.avoid_camera_0 =
        json["enable_config"]["enable_avoid_camera_0"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_camera_0: " << option->topic_option.avoid_camera_0);

    option->topic_option.avoid_camera_1 =
        json["enable_config"]["enable_avoid_camera_1"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_camera_1: " << option->topic_option.avoid_camera_1);

    option->topic_option.avoid_camera_2 =
        json["enable_config"]["enable_avoid_camera_2"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_camera_2: " << option->topic_option.avoid_camera_2);

    option->topic_option.avoid_camera_3 =
        json["enable_config"]["enable_avoid_camera_3"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_camera_3: " << option->topic_option.avoid_camera_3);

    option->topic_option.avoid_camera_4 =
        json["enable_config"]["enable_avoid_camera_4"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_camera_4: " << option->topic_option.avoid_camera_4);

    option->topic_option.avoid_camera_5 =
        json["enable_config"]["enable_avoid_camera_5"].get<bool>();
    LOG_INFO_STREAM(
        "enable_avoid_camera_5: " << option->topic_option.avoid_camera_5);

    auto &&al_config = json["algorithm_config"];

    for (auto &al : al_config) {
      // 导航3D避障参数
      if (al["type"].get<std::string>() == "naviLaser1") {
        LOG_INFO("---------------naviLaser1 config---------------");
        cotek_avoid::Avoid3DOption al_option;
        al_option.type = al["type"].get<std::string>();

        cotek_avoid::BoxArea roi_box;
        roi_box.x_min = al["roi_area"]["x_min"].get<double>();
        roi_box.x_max = al["roi_area"]["x_max"].get<double>();

        roi_box.y_min = al["roi_area"]["y_min"].get<double>();
        roi_box.y_max = al["roi_area"]["y_max"].get<double>();

        roi_box.z_min = al["roi_area"]["z_min"].get<double>();
        roi_box.z_max = al["roi_area"]["z_max"].get<double>();

        LOG_INFO_STREAM("roi x_min: " << roi_box.x_min);
        LOG_INFO_STREAM("roi x_max: " << roi_box.x_max);
        LOG_INFO_STREAM("roi y_min: " << roi_box.y_min);
        LOG_INFO_STREAM("roi y_max: " << roi_box.y_max);
        LOG_INFO_STREAM("roi z_min: " << roi_box.z_min);
        LOG_INFO_STREAM("roi z_max: " << roi_box.z_max);

        al_option.roi_area = roi_box;

        cotek_avoid::BoxArea crop_box;
        crop_box.x_min = al["crop_area"]["x_min"].get<double>();
        crop_box.x_max = al["crop_area"]["x_max"].get<double>();

        crop_box.y_min = al["crop_area"]["y_min"].get<double>();
        crop_box.y_max = al["crop_area"]["y_max"].get<double>();

        crop_box.z_min = al["crop_area"]["z_min"].get<double>();
        crop_box.z_max = al["crop_area"]["z_max"].get<double>();

        LOG_INFO_STREAM("crop x_min: " << crop_box.x_min);
        LOG_INFO_STREAM("crop x_max: " << crop_box.x_max);
        LOG_INFO_STREAM("crop y_min: " << crop_box.y_min);
        LOG_INFO_STREAM("crop y_max: " << crop_box.y_max);
        LOG_INFO_STREAM("crop z_min: " << crop_box.z_min);
        LOG_INFO_STREAM("crop z_max: " << crop_box.z_max);

        al_option.crop_area = crop_box;

        al_option.offset_tf.roll =
            math::Deg2Rad(al["offset_roll"].get<double>());
        al_option.offset_tf.pitch =
            math::Deg2Rad(al["offset_pitch"].get<double>());
        al_option.offset_tf.yaw = math::Deg2Rad(al["offset_yaw"].get<double>());

        LOG_INFO_STREAM("offset_roll: " << al["offset_roll"].get<double>());
        LOG_INFO_STREAM("offset_pitch: " << al["offset_pitch"].get<double>());
        LOG_INFO_STREAM("offset_yaw: " << al["offset_yaw"].get<double>());
        option->algorithm_option.avoid_3d_options["naviLaser1"] = al_option;
      }

      // 1号避障3D参数
      if (al["type"].get<std::string>() == "avoidCloud1") {
        LOG_INFO("---------------avoidCloud1 config---------------");
        cotek_avoid::Avoid3DOption al_option;
        al_option.type = al["type"].get<std::string>();

        cotek_avoid::BoxArea roi_box;
        roi_box.x_min = al["roi_area"]["x_min"].get<double>();
        roi_box.x_max = al["roi_area"]["x_max"].get<double>();

        roi_box.y_min = al["roi_area"]["y_min"].get<double>();
        roi_box.y_max = al["roi_area"]["y_max"].get<double>();

        roi_box.z_min = al["roi_area"]["z_min"].get<double>();
        roi_box.z_max = al["roi_area"]["z_max"].get<double>();

        LOG_INFO_STREAM("roi x_min: " << roi_box.x_min);
        LOG_INFO_STREAM("roi x_max: " << roi_box.x_max);
        LOG_INFO_STREAM("roi y_min: " << roi_box.y_min);
        LOG_INFO_STREAM("roi y_max: " << roi_box.y_max);
        LOG_INFO_STREAM("roi z_min: " << roi_box.z_min);
        LOG_INFO_STREAM("roi z_max: " << roi_box.z_max);

        al_option.roi_area = roi_box;

        cotek_avoid::BoxArea crop_box;
        crop_box.x_min = al["crop_area"]["x_min"].get<double>();
        crop_box.x_max = al["crop_area"]["x_max"].get<double>();

        crop_box.y_min = al["crop_area"]["y_min"].get<double>();
        crop_box.y_max = al["crop_area"]["y_max"].get<double>();

        crop_box.z_min = al["crop_area"]["z_min"].get<double>();
        crop_box.z_max = al["crop_area"]["z_max"].get<double>();

        LOG_INFO_STREAM("crop x_min: " << crop_box.x_min);
        LOG_INFO_STREAM("crop x_max: " << crop_box.x_max);
        LOG_INFO_STREAM("crop y_min: " << crop_box.y_min);
        LOG_INFO_STREAM("crop y_max: " << crop_box.y_max);
        LOG_INFO_STREAM("crop z_min: " << crop_box.z_min);
        LOG_INFO_STREAM("crop z_max: " << crop_box.z_max);

        al_option.crop_area = crop_box;

        al_option.offset_tf.roll =
            math::Deg2Rad(al["offset_roll"].get<double>());
        al_option.offset_tf.pitch =
            math::Deg2Rad(al["offset_pitch"].get<double>());
        al_option.offset_tf.yaw = math::Deg2Rad(al["offset_yaw"].get<double>());

        LOG_INFO_STREAM("offset_roll: " << al["offset_roll"].get<double>());
        LOG_INFO_STREAM("offset_pitch: " << al["offset_pitch"].get<double>());
        LOG_INFO_STREAM("offset_yaw: " << al["offset_yaw"].get<double>());
        option->algorithm_option.avoid_3d_options["avoidCloud1"] = al_option;
      }

      // 3号避障3D参数
      if (al["type"].get<std::string>() == "avoidCloud3") {
        LOG_INFO("---------------avoidCloud3 config---------------");
        cotek_avoid::Avoid3DOption al_option;
        al_option.type = al["type"].get<std::string>();

        cotek_avoid::BoxArea roi_box;
        roi_box.x_min = al["roi_area"]["x_min"].get<double>();
        roi_box.x_max = al["roi_area"]["x_max"].get<double>();

        roi_box.y_min = al["roi_area"]["y_min"].get<double>();
        roi_box.y_max = al["roi_area"]["y_max"].get<double>();

        roi_box.z_min = al["roi_area"]["z_min"].get<double>();
        roi_box.z_max = al["roi_area"]["z_max"].get<double>();

        LOG_INFO_STREAM("roi x_min: " << roi_box.x_min);
        LOG_INFO_STREAM("roi x_max: " << roi_box.x_max);
        LOG_INFO_STREAM("roi y_min: " << roi_box.y_min);
        LOG_INFO_STREAM("roi y_max: " << roi_box.y_max);
        LOG_INFO_STREAM("roi z_min: " << roi_box.z_min);
        LOG_INFO_STREAM("roi z_max: " << roi_box.z_max);

        al_option.roi_area = roi_box;

        cotek_avoid::BoxArea crop_box;
        crop_box.x_min = al["crop_area"]["x_min"].get<double>();
        crop_box.x_max = al["crop_area"]["x_max"].get<double>();

        crop_box.y_min = al["crop_area"]["y_min"].get<double>();
        crop_box.y_max = al["crop_area"]["y_max"].get<double>();

        crop_box.z_min = al["crop_area"]["z_min"].get<double>();
        crop_box.z_max = al["crop_area"]["z_max"].get<double>();

        LOG_INFO_STREAM("crop x_min: " << crop_box.x_min);
        LOG_INFO_STREAM("crop x_max: " << crop_box.x_max);
        LOG_INFO_STREAM("crop y_min: " << crop_box.y_min);
        LOG_INFO_STREAM("crop y_max: " << crop_box.y_max);
        LOG_INFO_STREAM("crop z_min: " << crop_box.z_min);
        LOG_INFO_STREAM("crop z_max: " << crop_box.z_max);

        al_option.crop_area = crop_box;

        al_option.offset_tf.roll =
            math::Deg2Rad(al["offset_roll"].get<double>());
        al_option.offset_tf.pitch =
            math::Deg2Rad(al["offset_pitch"].get<double>());
        al_option.offset_tf.yaw = math::Deg2Rad(al["offset_yaw"].get<double>());

        LOG_INFO_STREAM("offset_roll: " << al["offset_roll"].get<double>());
        LOG_INFO_STREAM("offset_pitch: " << al["offset_pitch"].get<double>());
        LOG_INFO_STREAM("offset_yaw: " << al["offset_yaw"].get<double>());
        option->algorithm_option.avoid_3d_options["avoidCloud3"] = al_option;
      }
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    LOG_ERROR_STREAM("Avoid option json parse is error !!!");
    return false;
  }

  return true;
}

}  // namespace cotek_avoid
