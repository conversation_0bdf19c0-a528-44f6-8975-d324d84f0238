/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "cotek_avoid/avoid.h"

#include "cotek_msgs/avoid_camera.h"

namespace cotek_avoid {
namespace {
constexpr char kSafetyStateTopic[] = "safety_state";
}  // namespace

Avoid::Avoid(const AvoidOption &option,
             std::shared_ptr<NodeStatusManager> node_status_manager_ptr,
             std::shared_ptr<StateManager> state_manager)
    : ap_(nullptr),
      node_status_manager_ptr_(node_status_manager_ptr),
      state_manager_(state_manager) {
  option_ = option;
  avoid_level_pub_ = nh.advertise<cotek_msgs::safety_states>(
      cotek_topic::kSafetyStateTopic, 10);
  ap_ = std::make_shared<AvoidProcess>(option_, state_manager_,
                                       node_status_manager_ptr);
}

Avoid::~Avoid() {
  if (executor_) {
    executor_->join();
    executor_ = nullptr;
  }
}

void Avoid::Run() {
  executor_ = std::make_shared<std::thread>(std::bind(&Avoid::Runner, this));
}

void Avoid::Runner() {
  ros::Rate rate(option_.controller_frequency);
  while (ros::ok()) {
    LOG_DEBUG("[Avoid Cycle]");
    // UpdateRobotPose();
    auto state = ap_->GetAvoidState();
    avoid_level_pub_.publish(state);

    if (rate.cycleTime() > ros::Duration(1.0 / option_.controller_frequency)) {
      LOG_WARN(
          "Control loop missed its desired rate of %.2fHz... the heartbeat "
          "actually took %.2f seconds",
          option_.controller_frequency, rate.cycleTime().toSec());
    }

    rate.sleep();
  }
}

void Avoid::AddSafetyIOStateCallback(const std_msgs::Int32::ConstPtr &state) {
  ap_->SafetyIOStateCallback(state);
}

// void Avoid::AddPederstrianPoseCallback(
//     const cotek_msgs::multi_pedestrian_pose::ConstPtr &pose_list) {
//   ap_->PederstrianPoseCallback(pose_list);
// }

void Avoid::AddLaserScanCallback(const sensor_msgs::LaserScan::ConstPtr &scan) {
  ap_->LaserScanCallback(scan);
}

void Avoid::AddUltarsonicCallback(
    const cotek_msgs::ultrasonic_feedback::ConstPtr &data) {
  ap_->UltarsonicCallback(data);
}

void Avoid::AddForkliftPalleState(
    const std_msgs::Int32::ConstPtr &pallet_fork_io_state) {
  ap_->AddForkliftPalleState(pallet_fork_io_state);
}

void Avoid::AddAvoidLaser1Callback(
    const sensor_msgs::LaserScan::ConstPtr &scan) {
  ap_->AvoidLaserCallback(scan);
}

void Avoid::AddAvoidCloud0Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->AvoidCloud0Callback(cloud);
}

void Avoid::AddAvoidCloud1Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->AvoidCloud1Callback(cloud);
}

void Avoid::AddAvoidCloud2Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->AvoidCloud2Callback(cloud);
}

void Avoid::AddAvoidCloud3Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->AvoidCloud3Callback(cloud);
}

void Avoid::AddAvoidCloud4Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->AvoidCloud4Callback(cloud);
}

void Avoid::AddAvoidCloud5Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->AvoidCloud5Callback(cloud);
}

void Avoid::AddNaviCloud0Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->NaviCloud0Callback(cloud);
}

void Avoid::AddNaviCloud1Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->NaviCloud1Callback(cloud);
}

void Avoid::AddNaviCloud2Callback(
    const sensor_msgs::PointCloud2::ConstPtr &cloud) {
  ap_->NaviCloud2Callback(cloud);
}

void Avoid::AddAvoidCameraCallback(
    const cotek_msgs::avoid_camera::ConstPtr &data) {
  ap_->AvoidCameraCallback(data);
}

void Avoid::AddBackAvoidCameraCallback(
    const cotek_msgs::avoid_camera::ConstPtr &data) {
  ap_->BackAvoidCameraCallback(data);
}

void Avoid::AddSafetySettingCallback(
    const cotek_msgs::safety_setting::ConstPtr &setting) {
  ap_->SafetySettingCallback(setting);
}

void Avoid::AddManualCallback(const std_msgs::Int32::ConstPtr &manual) {
  ap_->ManualCallback(manual);
}

void Avoid::AddBumpReset(const cotek_msgs::bump_reset::ConstPtr &feedback) {
  ap_->AddBumpReset(feedback);
}

void Avoid::AddMoveFeedback(
    const cotek_msgs::move_feedback::ConstPtr &feedback) {
  VelocityFactor tmp;
  tmp.vaild = true;
  tmp.current_v = feedback->velocity;
  tmp.current_w = feedback->omega;
  state_manager_->set_velocity_factor(tmp);
}

void Avoid::AddMoveCmd(const cotek_msgs::move_cmd::ConstPtr &move_cmd) {
  ap_->AddMoveCmd(move_cmd);
}

void Avoid::AddHightBmmsk34Encode(
    const cotek_msgs::wire_encoder_feedback::ConstPtr &bmmsk34) {
  ap_->AddHightBmmsk34Encode(bmmsk34);
}

void Avoid::AddWeakenLaser(const cotek_msgs::weaken_avoid::ConstPtr &feed) {
  ap_->AddWeakenLaser(feed);
}

void Avoid::UpdateRobotPose() {
  tf::StampedTransform transform;
  tf::TransformListener tf;
  try {
    tf.lookupTransform(cotek_tf::kMapFrame, cotek_tf::kBaseFrame, ros::Time(0),
                       transform);
    if (ros::Duration(ros::Time::now() - transform.stamp_) >
        ros::Duration(0.2)) {
      LOG_WARN_STREAM("Current robot pose is out of date.");
      return;
    }
  } catch (tf::TransformException &ex) {
    LOG_WARN_STREAM_THROTTLE(1, ex.what());
    return;
  }
  state_manager_->set_pose(Pose(transform.getOrigin().x(),
                                transform.getOrigin().y(),
                                tf::getYaw(transform.getRotation())));
}

}  // namespace cotek_avoid
