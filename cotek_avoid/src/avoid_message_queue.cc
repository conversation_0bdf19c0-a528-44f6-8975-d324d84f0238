/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_avoid/avoid_message_queue.h"

#include <exception>
#include <memory>

#include "cotek_common/cotek_topic_name.h"
#include "cotek_msgs/pose_2d.h"

namespace cotek_avoid {
/**
 * \brief find the max value of avoid level
 */

AvoidLevel AvoidFacotrDeque::GetTimeoutType(const AvoidLevel& type) {
  if (type == AvoidLevel::NAVI_LASER_0) return AvoidLevel::LASER_0_TIMEOUT;
  if (type == AvoidLevel::NAVI_LASER_1) return AvoidLevel::LASER_1_TIMEOUT;
  if (type == AvoidLevel::NAVI_LASER_2) return AvoidLevel::LASER_2_TIMEOUT;

  if (type == AvoidLevel::AVOID_LASER_0)
    return AvoidLevel::AVOID_LASER_0_TIMEOUT;
  if (type == AvoidLevel::AVOID_LASER_1)
    return AvoidLevel::AVOID_LASER_1_TIMEOUT;
  if (type == AvoidLevel::AVOID_LASER_2)
    return AvoidLevel::AVOID_LASER_2_TIMEOUT;
  if (type == AvoidLevel::AVOID_LASER_3)
    return AvoidLevel::AVOID_LASER_3_TIMEOUT;
  if (type == AvoidLevel::AVOID_LASER_4)
    return AvoidLevel::AVOID_LASER_4_TIMEOUT;
  if (type == AvoidLevel::AVOID_LASER_5)
    return AvoidLevel::AVOID_LASER_5_TIMEOUT;

  if (type == AvoidLevel::AVOID_CAMERA_0)
    return AvoidLevel::AVOID_CAMERA_0_TIMEOUT;
  if (type == AvoidLevel::AVOID_CAMERA_1)
    return AvoidLevel::AVOID_CAMERA_1_TIMEOUT;
  if (type == AvoidLevel::AVOID_CAMERA_2)
    return AvoidLevel::AVOID_CAMERA_2_TIMEOUT;
  if (type == AvoidLevel::AVOID_CAMERA_3)
    return AvoidLevel::AVOID_CAMERA_3_TIMEOUT;
  if (type == AvoidLevel::AVOID_CAMERA_4)
    return AvoidLevel::AVOID_CAMERA_4_TIMEOUT;
  if (type == AvoidLevel::AVOID_CAMERA_5)
    return AvoidLevel::AVOID_CAMERA_5_TIMEOUT;

  if (type == AvoidLevel::AVOID_ULTRASONIC)
    return AvoidLevel::ULTRASONIC_TIMEOUT;

  if (type == AvoidLevel::FORK_LEG_BOTH) return AvoidLevel::FORK_LEG_BOTH;

  LOG_WARN_STREAM(static_cast<int>(type) << " not define timeout type!!! ");

  return AvoidLevel::NONE;
}

AvoidFacotrDeque::AvoidFacotrDeque(const AvoidLevel& type)
    : type_(type), start_time_(ros::Time::now()) {}

AvoidFacotr AvoidFacotrDeque::Filter(const ros::Duration& reserve_time) {
  AvoidFacotr lv;
  std::unique_lock<std::mutex> lock(mutex_);
  while (!empty() && (ros::Time::now() - front().first > reserve_time)) {
    pop_front();
  }
  // 上电5s之后,若无避障数据则停车保护
  if (empty() && (ros::Time::now() - start_time_) > ros::Duration(5.0)) {
    lv.type = GetTimeoutType(type_);
    lv.lv = AvoidSpeedLevel::STOP;
    lv.dist = 0.;
    return lv;
  }
  for (const auto& time_lvl : *this) {
    if (time_lvl.second.dist < lv.dist) {
      lv.lv = time_lvl.second.lv;
      lv.type = time_lvl.second.type;
      lv.dist = time_lvl.second.dist;
      lv.avoid_pt = time_lvl.second.avoid_pt;
    }
  }
  return lv;
}

void AvoidFacotrDeque::PushData(const ros::Time& time,
                                const AvoidFacotr& factor) {
  std::unique_lock<std::mutex> lock(mutex_);
  emplace_back(time, factor);
}

cotek_msgs::safety_states AvoidMessageQueue::Filter(
    const ros::Duration& reserve_time) {
  cotek_msgs::safety_states states;
  cotek_msgs::safety_state state;
  AvoidFacotr min_af;
  for (auto& queue : af_deque_) {
    AvoidFacotr lv = queue.second->Filter(reserve_time);

    state.avoid_state = static_cast<int>(lv.lv);
    state.avoid_type = static_cast<int>(lv.type);
    state.avoid_dist = lv.dist;
    state.avoid_point.x = lv.avoid_pt.x();
    state.avoid_point.y = lv.avoid_pt.y();

    if (state.avoid_state != static_cast<int>(AvoidSpeedLevel::FREE)) {
      states.safety_states.push_back(state);
    }

    // 筛选各类型距离最新的避障信息
    if (lv.dist<min_af.dist&& static_cast<int>(lv.lv)> static_cast<int>(
            min_af.lv)) {
      min_af = lv;
    }
  }

  states.avoid_dist = min_af.dist;
  states.avoid_state = static_cast<int>(min_af.lv);
  states.avoid_type = static_cast<int>(min_af.type);
  return states;
}
void AvoidMessageQueue::PushData(const ros::Time& time,
                                 const AvoidFacotr& factor) {
  try {
    if (af_deque_.count(factor.type) == 0) {
      af_deque_[factor.type] = std::make_shared<AvoidFacotrDeque>(factor.type);
    }
    af_deque_.at(factor.type)->PushData(time, factor);

  } catch (std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void AvoidMessageQueue::PushData(const ros::Time& time,
                                 const AvoidFacotr& factor,
                                 const AvoidLevel& complex_type) {
  try {
    if (af_deque_.count(complex_type) == 0) {
      af_deque_[complex_type] =
          std::make_shared<AvoidFacotrDeque>(complex_type);
    }
    af_deque_.at(complex_type)->PushData(time, factor);

  } catch (std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

}  // namespace cotek_avoid
