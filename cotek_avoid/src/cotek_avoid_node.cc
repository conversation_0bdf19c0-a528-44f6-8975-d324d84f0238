/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "node/cotek_avoid_node.h"

#include "cotek_common/cotek_node_name.h"

constexpr char kConfigPackageName[] = "cotek_avoid_node";

int main(int argc, char **argv) {
  ros::init(argc, argv, cotek_node::kAvoidNode);

  if (ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME,
                                     ros::console::levels::Info)) {
    ros::console::notifyLoggerLevelsChanged();
  }

  ros::NodeHandle nh;

  AvoidNode node(&nh);

  ros::Rate rate(0.3);
  ros::AsyncSpinner s(1);
  s.start();

  while (!node.Init(&nh) && ros::ok()) {
    LOG_ERROR_THROTTLE(1, "cotek_avoid_node init failed. retry");
    rate.sleep();
  }
  node.Run();
  ros::waitForShutdown();

  return 0;
}
