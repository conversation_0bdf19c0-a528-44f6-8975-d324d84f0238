/**
 * Copyright (c) 2023 COTEK Inc. Copied from google's cartographer code.
 */
#include "cotek_avoid/fixed_ratio_sampler.h"

#include <ros/ros.h>

namespace cotek_avoid {

FixedRatioSampler::FixedRatioSampler(const double& ratio,
                                     const double& time_out)
    : ratio_(ratio), time_out_(time_out) {
  time_ = ros::Time::now() + ros::Duration(15.0);
  LOG_WARN_COND(ratio == 0., "FixedRatioSampler is dropping all data.");
  LOG_ASSERT(ratio >= 0. && ratio <= 1.);
}

FixedRatioSampler::~FixedRatioSampler() {}

void FixedRatioSampler::UpdateSamplerTime() { time_ = ros::Time::now(); }

bool FixedRatioSampler::IsTimeout() {
  return ros::Time::now() - time_ > ros::Duration(time_out_);
}

bool FixedRatioSampler::Pulse() {
  ++num_pulses_;
  if (static_cast<double>(num_samples_) / num_pulses_ < ratio_) {
    ++num_samples_;
    UpdateSamplerTime();
    return true;
  }
  return false;
}

std::string FixedRatioSampler::DebugString() {
  return std::to_string(num_samples_) + " (" +
         std::to_string(100. * num_samples_ / num_pulses_) + "%)";
}

}  // namespace cotek_avoid
