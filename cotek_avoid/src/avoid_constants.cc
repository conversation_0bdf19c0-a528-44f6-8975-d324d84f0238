/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_avoid/avoid_constants.h"

namespace cotek_avoid {

std::vector<std::string>
ComputeRepeatedTopicNames(const std::string& topic, const int num_topics) {
  if (num_topics == 1) {
    return {topic};
  }
  std::vector<std::string> topics;
  topics.reserve(num_topics);
  for (int i = 0; i < num_topics; ++i) {
    topics.emplace_back(topic + "_" + std::to_string(i + 1));
  }
  return topics;
}

}  // namespace cotek_avoid
