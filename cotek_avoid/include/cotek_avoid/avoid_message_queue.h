/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_MESSAGE_QUEUE_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_MESSAGE_QUEUE_H_
#include <ros/ros.h>

#include <deque>
#include <memory>
#include <mutex>
#include <string>
#include <utility>

#include "cotek_avoid/avoid_constants.h"
#include "cotek_avoid/avoid_options.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/log_porting.h"
#include "cotek_msgs/safety_states.h"

namespace cotek_avoid {

class AvoidFacotrDeque : public std::deque<std::pair<ros::Time, AvoidFacotr>> {
 public:
  explicit AvoidFacotrDeque(const AvoidLevel& type);
  AvoidFacotr Filter(const ros::Duration& reserve_time);

  void PushData(const ros::Time& time, const AvoidFacotr& factor);

 private:
  AvoidLevel GetTimeoutType(const AvoidLevel& type);
  AvoidLevel type_;
  ros::Time start_time_;
  std::mutex mutex_;
};
class AvoidMessageQueue {
 public:
  AvoidMessageQueue() {}
  cotek_msgs::safety_states Filter(const ros::Duration& reserve_time);

  void PushData(const ros::Time& time, const AvoidFacotr& factor);

  // 添加复合避障类型数据，例io
  void PushData(const ros::Time& time, const AvoidFacotr& factor,
                const AvoidLevel& complex_type);

 private:
  std::map<AvoidLevel, std::shared_ptr<AvoidFacotrDeque>> af_deque_;
};

}  // namespace cotek_avoid
#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_MESSAGE_QUEUE_H_
