/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_H_
#include <ros/ros.h>
#include <sensor_msgs/LaserScan.h>
#include <sensor_msgs/PointCloud2.h>
#include <tf/transform_listener.h>

#include <boost/shared_ptr.hpp>
#include <deque>
#include <string>
#include <thread>
#include <utility>

#include "cotek_avoid/avoid_constants.h"
#include "cotek_avoid/avoid_options.h"
#include "cotek_avoid/avoid_process.h"
#include "cotek_avoid/state_manager.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/node_diagnostic_info.h"
#include "cotek_msgs/avoid_camera.h"
#include "cotek_msgs/bmmsk34_encoder_feedback.h"
#include "cotek_msgs/bump_reset.h"
#include "cotek_msgs/front_camera_distance.h"
#include "cotek_msgs/manual.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/node_diagnostic.h"
#include "cotek_msgs/pallet_stablizer.h"
#include "cotek_msgs/safety_states.h"
#include "cotek_msgs/ultrasonic_feedback.h"
#include "cotek_msgs/weaken_avoid.h"
#include "std_msgs/Int32.h"
namespace cotek_avoid {

class Avoid {
 public:
  /**
   * \brief default constructor
   */
  explicit Avoid(const AvoidOption &option,
                 std::shared_ptr<NodeStatusManager> node_status_manager_ptr,
                 std::shared_ptr<StateManager> state_manager);

  /**
   * \brief construct object with options
   * \param option all Avoid related parameters
   */
  // explicit Avoid(const AvoidOption& option);

  /**
   * \brief destructor
   */
  ~Avoid();

  /**
   * \brief option getter
   * \return LocalizerOption
   */
  inline AvoidOption Option() const { return option_; }

  /**
   * \brief option getter
   * \return LocalizerOption
   */
  bool UpdateOption(const AvoidOption &option) {
    option_ = option;
    return ap_->UpdateOption(option);
  }

  bool UpdateAvoidMap() { return ap_->UpdateAvoidMap(); }

  /**
   * \brief do initialization
   * \return true if init succeeded
   */
  bool Init() { return ap_->Init(); }

  /**
   * \brief start a thread and excute cycle
   */
  void Run();

  void AddSafetyIOStateCallback(const std_msgs::Int32::ConstPtr &state);

  //   void AddPederstrianPoseCallback(
  //       const cotek_msgs::multi_pedestrian_pose::ConstPtr &pose_list);

  void AddLaserScanCallback(const sensor_msgs::LaserScan::ConstPtr &scan);

  void AddAvoidLaser1Callback(const sensor_msgs::LaserScan::ConstPtr &scan);

  void AddAvoidCloud0Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddAvoidCloud1Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddAvoidCloud2Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddAvoidCloud3Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddAvoidCloud4Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddAvoidCloud5Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddNaviCloud0Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddNaviCloud1Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddNaviCloud2Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void AddUltarsonicCallback(
      const cotek_msgs::ultrasonic_feedback::ConstPtr &data);

  void AddSafetySettingCallback(
      const cotek_msgs::safety_setting::ConstPtr &setting);

  void AddManualCallback(const std_msgs::Int32::ConstPtr &manual);

  void AddAvoidCameraCallback(const cotek_msgs::avoid_camera::ConstPtr &data);

  void AddBackAvoidCameraCallback(
      const cotek_msgs::avoid_camera::ConstPtr &data);

  void AddForkliftPalleState(
      const std_msgs::Int32::ConstPtr &pallet_fork_io_state);

  void AddMoveFeedback(const cotek_msgs::move_feedback::ConstPtr &feedback);

  void AddHightBmmsk34Encode(
      const cotek_msgs::wire_encoder_feedback::ConstPtr &bmmsk34);

  void AddWeakenLaser(const cotek_msgs::weaken_avoid::ConstPtr &feed);

  void AddBumpReset(const cotek_msgs::bump_reset::ConstPtr &feedback);

  void UpdateRobotPose();

  void AddMoveCmd(const cotek_msgs::move_cmd::ConstPtr &move_cmd);

 private:
  void Runner();
  /**
   * \brief handle error
   * \return true if can continue, false otherwise
   */
  ros::NodeHandle nh;

  void NodeDiagnostic(const ros::TimerEvent &e);

  AvoidOption option_;
  std::shared_ptr<std::thread> executor_;

  ros::Publisher avoid_level_pub_;
  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;
  std::shared_ptr<AvoidProcess> ap_;
  std::shared_ptr<StateManager> state_manager_;
};

}  // namespace cotek_avoid

#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_H_
