/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_STATE_MANAGER_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_STATE_MANAGER_H_
#include <ros/ros.h>
#include <tf/transform_listener.h>

#include <string>
#include <thread>

#include "cotek_avoid/avoid_options.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/cotek_tf_name.h"
#include "cotek_common/geometry/cotek_geometry.h"
#include "cotek_common/math.h"
#include "cotek_msgs/battery_feedback.h"
#include "cotek_msgs/jack_up_io_state.h"
#include "cotek_msgs/motor_syntro_feedback.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/move_feedback.h"
#include "cotek_msgs/pallet_fork_io_state.h"
#include "cotek_msgs/safety_state.h"
#include "cotek_msgs/stop_protect.h"

namespace cotek_avoid {

using Pose = cotek_geometry::Pose;

class StateManager {
 public:
  /**
   * \brief getter
   */
  inline Pose pose() const { return pose_; }

  inline VelocityFactor velocity_factor() const { return velocity_factor_; }

  /**
   * \brief setter
   */
  inline void set_pose(const Pose& pose) { pose_ = pose; }

  inline void set_velocity_factor(const VelocityFactor& t) {
    velocity_factor_ = t;
  }

  std::map<std::string, std::shared_ptr<FixedRatioSampler>>& samplers() {
    return samplers_;
  }

 private:
  Pose pose_;
  VelocityFactor velocity_factor_;
  std::map<std::string, std::shared_ptr<FixedRatioSampler>> samplers_;
};

}  // namespace cotek_avoid

#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_STATE_MANAGER_H_
