/**
 * Copyright (c) 2023 COTEK Inc. Copied from google's cartographer code.
 */
#pragma once
#include <ros/ros.h>

#include <string>

#include "cotek_common/log_porting.h"

namespace cotek_avoid {

// Signals when a sample should be taken from a stream of data to select a
// uniformly distributed fraction of the data.
class FixedRatioSampler {
 public:
  explicit FixedRatioSampler(const double &ratio, const double &time_out);
  ~FixedRatioSampler();

  FixedRatioSampler(const FixedRatioSampler &) = delete;
  FixedRatioSampler &operator=(const FixedRatioSampler &) = delete;

  // Returns true if this pulse should result in an sample.
  bool Pulse();

  // Update time when collate sensor data
  void UpdateSamplerTime();

  bool IsTimeout();

  // Returns a debug string describing the current ratio of samples to pulses.
  std::string DebugString();

 private:
  // Sampling occurs if the proportion of samples to pulses drops below this
  // number.
  const double ratio_;
  const double time_out_;

  ros::Time time_;

  int64_t num_pulses_ = 0;
  int64_t num_samples_ = 0;
};

}  // namespace cotek_avoid
