/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_CONFIG_HELPER_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_CONFIG_HELPER_H_
#include <map>
#include <string>
#include <vector>

#include "cotek_avoid/avoid_constants.h"
#include "cotek_avoid/avoid_options.h"
#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/util/json11.h"

namespace cotek_avoid {

// <map_id, <area_name, [coordinate_vector]>>
using Json = nlohmann::ordered_json;
using AvoidArea = std::map<int, std::map<std::string, std::vector<Boost_Point>>>;
using Inner = std::vector<Boost_Point>;

// 读取 json 文件 加载配置
class ConfigHelper {
 public:
  ~ConfigHelper() {}
  static ConfigHelper& Instance() {
    static ConfigHelper instance;
    return instance;
  }
  ConfigHelper(const ConfigHelper&) = delete;
  ConfigHelper& operator=(const ConfigHelper&) = delete;
  /**
   * \brief load config param from file path
   * \param the path of config param
   */
  bool LoadAvoidAreaConfig(const std::string& json_str);
  /**
   * \brief load Param config from file path
   * \param the path of config param
   */
  bool LoadAvoidConfig(const std::string& json_str,
                       cotek_avoid::AvoidOption* option);
  /**
   * \brief get coordinate point from file
   */

  bool LoadBasicConfig();

  std::vector<Boost_Point> GetCoordinateVector(const Json& vj);

  inline const AvoidArea& GetAvoidMap() { return avoid_map_; }

  inline const Polygon& GetAgvInner() { return agv_inner_; }

  inline const TransformMap& GetSensorTransform() { return sensor_tf_; }

  inline const LaserMap& GetLaserMap() { return laser_map_; }

 private:
  ConfigHelper() {}

  AvoidArea avoid_map_;
  Polygon agv_inner_;

  TransformMap sensor_tf_;
  LaserMap laser_map_;
};
}  // namespace cotek_avoid
#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_CONFIG_HELPER_H_
