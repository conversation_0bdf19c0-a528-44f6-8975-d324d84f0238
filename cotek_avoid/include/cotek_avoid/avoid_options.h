/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_OPTIONS_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_OPTIONS_H_
#include <map>
#include <string>

#include "cotek_common/agv_basic_option.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/geometry/point.h"
namespace cotek_avoid {

using Point = cotek_geometry::Point;
constexpr double kSafetyDist = 100.;

struct TopicOption {
  bool io_state = false;
  bool avoid_ultrasonic = false;  // 超声波避障
  bool navi_laser_0 = false;
  bool navi_laser_1 = false;
  bool navi_laser_2 = false;

  bool avoid_laser_0 = false;  // 前左避障激光
  bool avoid_laser_1 = false;  // 前中避障激光
  bool avoid_laser_2 = false;  // 前右避障激光
  bool avoid_laser_3 = false;  // 叉腿中间避障激光
  bool avoid_laser_4 = false;  // 叉腿右侧避障激光
  bool avoid_laser_5 = false;  // 叉腿左侧避障激光

  bool avoid_cloud_0 = false;
  bool avoid_cloud_1 = false;
  bool avoid_cloud_2 = false;
  bool avoid_cloud_3 = false;
  bool avoid_cloud_4 = false;
  bool avoid_cloud_5 = false;

  bool avoid_camera_0 = false;  // 前置左摄像头
  bool avoid_camera_1 = false;  // 前置中摄像头
  bool avoid_camera_2 = false;  // 前置右摄像头
  bool avoid_camera_3 = false;  // 叉腿中侧摄像头
  bool avoid_camera_4 = false;  // 叉腿右侧摄像头
  bool avoid_camera_5 = false;  // 叉腿左侧摄像头
};

struct BoxArea {
  double x_min{0.0};
  double x_max{0.0};
  double y_min{0.0};
  double y_max{0.0};
  double z_min{0.0};
  double z_max{0.0};
};

struct TF {
  double x{0.0};
  double y{0.0};
  double z{0.0};
  double roll{0.0};
  double pitch{0.0};
  double yaw{0.0};
};

struct Avoid3DOption {
  std::string type;

  BoxArea roi_area;
  BoxArea crop_area;

  TF offset_tf;
};

struct AlgorithmOption {
  std::map<std::string, Avoid3DOption> avoid_3d_options;
};

struct AvoidOption {
  TopicOption topic_option;
  double controller_frequency;
  double node_diagnostic_frequency;
  double overtime;
  std::string config_filename;
  AlgorithmOption algorithm_option;
};

struct AvoidFacotr {
  AvoidLevel type;
  AvoidSpeedLevel lv;
  Point avoid_pt;
  double dist;
  AvoidFacotr()
      : type(AvoidLevel::NONE),
        lv(AvoidSpeedLevel::FREE),
        avoid_pt(Point(88888., 88888.)),
        dist(kSafetyDist) {}
};

struct VelocityFactor {
  bool vaild;
  double current_v;
  double current_w;
  VelocityFactor() : vaild(false), current_v(0.), current_w(0.) {}
};

}  // namespace cotek_avoid

#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_OPTIONS_H_
