/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#pragma once
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_CONSTANTS_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_CONSTANTS_H_
#include <boost/geometry.hpp>
#include <boost/geometry/geometries/point_xy.hpp>
#include <string>
#include <vector>

#include "cotek_common/log_porting.h"

namespace cotek_avoid {

namespace bg = boost::geometry;
namespace btf = bg::strategy::transform;

using Boost_Point = bg::model::d2::point_xy<double>;
using Boost_Line = bg::model::linestring<Boost_Point>;
using Polygon = bg::model::polygon<Boost_Point, true, true>;
using Boost_Segment = bg::model::segment<Boost_Point>;
using Translate = btf::translate_transformer<double, 2, 2>;
using Rotate = btf::rotate_transformer<boost::geometry::radian, double, 2, 2>;

constexpr char kLaserScanTopic[] = "scan";
constexpr char kAvoidSlowLevel1Name[] = "slow_level1";
constexpr char kAvoidSlowLevel2Name[] = "slow_level2";
constexpr char kAvoidStopName[] = "stop";

std::vector<std::string> ComputeRepeatedTopicNames(const std::string& topic,
                                                   int num_topics);

}  // namespace cotek_avoid

#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_CONSTANTS_H_
