/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_PROCESS_H_
#define COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_PROCESS_H_
#include <geometry_msgs/PointStamped.h>
#include <geometry_msgs/PolygonStamped.h>
#include <sensor_msgs/LaserScan.h>
#include <sensor_msgs/PointCloud2.h>

#include <boost/shared_ptr.hpp>
#include <boost/thread.hpp>
#include <cstdint>
#include <list>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "boost/algorithm/clamp.hpp"
#include "cotek_avoid/avoid_config_helper.h"
#include "cotek_avoid/avoid_constants.h"
#include "cotek_avoid/avoid_message_queue.h"
#include "cotek_avoid/avoid_options.h"
#include "cotek_avoid/fixed_ratio_sampler.h"
#include "cotek_avoid/state_manager.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/math.h"
#include "cotek_common/node_diagnostic_manager.h"
#include "cotek_msgs/avoid_camera.h"
#include "cotek_msgs/bmmsk34_encoder_feedback.h"
#include "cotek_msgs/bump_reset.h"
#include "cotek_msgs/front_camera_distance.h"
#include "cotek_msgs/manual.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/multi_pedestrian_pose.h"
#include "cotek_msgs/pallet_stablizer.h"
#include "cotek_msgs/pgv_r2100_feedback.h"
#include "cotek_msgs/safety_io_state.h"
#include "cotek_msgs/safety_setting.h"
#include "cotek_msgs/safety_state.h"
#include "cotek_msgs/safety_states.h"
#include "cotek_msgs/ultrasonic_feedback.h"
#include "cotek_msgs/weaken_avoid.h"
#include "cotek_msgs/wire_encoder_feedback.h"
#include "eigen3/Eigen/Dense"
#include "std_msgs/Int32.h"

namespace cotek_avoid {

using AvoidNodeStatus = cotek_diagnostic::AvoidNodeStatus;
using NodeStatusManager = cotek_diagnostic::NodeStatusManager<AvoidNodeStatus>;

enum class ErrorCode : uint8_t {
  NONE = 0,
  CONFIG_ERR,
  AVOID_SETTING_ERR,
  DATA_CHECK_ERR,
  DATA_TIME_OUT,
  AVOID_OVERTIME,
  BUMP_ERR,
  FORK_LEG_AVOID,
  ULTRASOUND_AVOID,
  UP_FORWARD_LASER_OBSTACLE,
  SENSOR_PROTECT,
  PEDERSTRAIN_WARING
};

struct AvoidMap {
  Polygon slow_level1;
  Polygon slow_level2;
  Polygon stop;
  Polygon laser_protect;
};

struct SensorAvoidLevel {
  AvoidFacotr navi_laser;
  AvoidFacotr avoid_laser_1;
  AvoidFacotr pgv_r2100;
  AvoidFacotr io_state;
  AvoidFacotr ks_avoid;
  AvoidFacotr rs_avoid;  // 视觉避障
  AvoidFacotr pederstrian_avoid;
  AvoidFacotr ultarsonic_avoid;
};

struct UltrasoundAvoid {
  double length1;
  double length2;
  double length3;
};

struct SensorTimeOut {
  bool timeout;
  std::vector<double> type_vec;
};

struct TimedWeakenAvoidInfo {
  ros::Time time;
  bool weaken_avoid;
  bool Vaild() { return ros::Time::now() - time < ros::Duration(10.0); }
  TimedWeakenAvoidInfo() : time(ros::Time()), weaken_avoid(false) {}
};
struct FrontAvoidCamera {
  double previous_value = 0.7;
  std::list<double> dist_list;
};

struct VaildHeight {
  bool vaild{false};
  double height{0.0};
};

class AvoidProcess {
 public:
  explicit AvoidProcess(
      const AvoidOption &option, std::shared_ptr<StateManager> ptr,
      std::shared_ptr<NodeStatusManager> node_status_manager_ptr);
  /**
   * \brief config default config and param..
   */
  bool Init();

  bool UpdateOption(const AvoidOption &option) {
    option_ = option;
    set_map_init_ = false;
    return true;
  }

  bool UpdateAvoidMap() {
    set_map_init_ = false;
    return true;
  }

  cotek_msgs::safety_states GetAvoidState();

  void SafetyIOStateCallback(const std_msgs::Int32::ConstPtr &state);

  //   void PederstrianPoseCallback(
  //       const cotek_msgs::multi_pedestrian_pose::ConstPtr &pose_list);

  void LaserScanCallback(const sensor_msgs::LaserScan::ConstPtr &scan);

  void AvoidLaserCallback(const sensor_msgs::LaserScan::ConstPtr &scan);

  void AvoidCloud0Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void AvoidCloud1Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void AvoidCloud2Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void AvoidCloud3Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void AvoidCloud4Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void AvoidCloud5Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void NaviCloud0Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void NaviCloud1Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);
  void NaviCloud2Callback(const sensor_msgs::PointCloud2::ConstPtr &cloud);

  void UltarsonicCallback(
      const cotek_msgs::ultrasonic_feedback::ConstPtr &data);

  void SafetySettingCallback(
      const cotek_msgs::safety_setting::ConstPtr &setting);

  void ManualCallback(const std_msgs::Int32::ConstPtr &manual);

  void AddWeakenLaser(const cotek_msgs::weaken_avoid::ConstPtr &feed);

  void AddBumpReset(const cotek_msgs::bump_reset::ConstPtr &state);

  void AddForkliftPalleState(
      const std_msgs::Int32::ConstPtr &pallet_fork_io_state);

  void AddHightBmmsk34Encode(
      const cotek_msgs::wire_encoder_feedback::ConstPtr &bmmsk34);

  void AvoidCameraCallback(const cotek_msgs::avoid_camera::ConstPtr &data);

  void BackAvoidCameraCallback(const cotek_msgs::avoid_camera::ConstPtr &data);

  void AddMoveCmd(const cotek_msgs::move_cmd::ConstPtr &move_cmd);

  inline void ClearTimeOutCnt() { avoid_time_out_cnt_ = 0; }

 private:
  bool CheckLaserPointNeiberVaild(const sensor_msgs::LaserScan::ConstPtr &scan,
                                  const int &index,
                                  const LaserScanOption &option,
                                  const double &margin);

  AvoidFacotr GetCloudAvoidLevel(
      const sensor_msgs::PointCloud2::ConstPtr &cloud, const std::string &frame,
      const Avoid3DOption &option, const LaserScanOption &laser_option,
      const AvoidMap &avoid_map);

  std::string GetAvoidCameraFrame(const std::string &name);
  AvoidLevel GetAvoidType(const std::string &frame);
  AvoidLevel GetTimeoutType(const std::string &frame);

  AvoidNodeStatus GetTimeoutError(const std::string &frame);

  AvoidFacotr GetCameraPointsAvoidLevel(
      const cotek_msgs::avoid_camera::ConstPtr &data,
      const Polygon &slow_level1, const Polygon &slow_level2,
      const Polygon &stop, const Transform &tf);

  AvoidFacotr GetCameraDistAvoidLevel(const double &dist,
                                      const Polygon &slow_level1,
                                      const Polygon &slow_level2,
                                      const Polygon &stop, const Transform &tf);
  AvoidFacotr GetScanAvoidLevel(const sensor_msgs::LaserScan::ConstPtr &scan,
                                const Polygon &slow_level1,
                                const Polygon &slow_level2, const Polygon &stop,
                                const Polygon &inner,
                                const LaserScanOption &option,
                                const Transform &tf);

  AvoidFacotr GetUltrasonicAvoidLevel(const std::vector<Boost_Point> &scan_pt,
                                      const Polygon &slow_level1,
                                      const Polygon &slow_level2,
                                      const Polygon &stop);

  AvoidFacotr GetAvoidLaserAvoidLevel(
      const sensor_msgs::LaserScan::ConstPtr &scan, const Polygon &slow_level1,
      const Polygon &slow_level2, const Polygon &stop, const Polygon &inner,
      const LaserScanOption &option, const Transform &tf);

  AvoidFacotr GetSpecialLaserAvoidLevel(
      const std::string &topic, const sensor_msgs::LaserScan::ConstPtr &scan,
      const Polygon &slow_level1, const Polygon &slow_level2,
      const Polygon &stop, const Polygon &inner, const LaserScanOption &option,
      const Transform &tf);

  AvoidFacotr GetPederstrianAvoidLevel(
      const cotek_msgs::multi_pedestrian_pose::ConstPtr &pose_list,
      const Polygon &slow_level1, const Polygon &slow_level2,
      const Polygon &stop, const Transform &tf);

  AvoidSpeedLevel GetLaserProtectAvoidLevel(
      const sensor_msgs::LaserScan::ConstPtr &scan, const Polygon &protect_map,
      const LaserScanOption &option);
  /**
   * \brief transform that caculate point to poly
   */
  Polygon TransformPoly(const Polygon &from_poly, const Transform &tf);

  AvoidMap GetAvoidMap(
      const std::map<std::string, std::vector<Boost_Point>> area_map);

  /**
   * \brief  caculate  min dist from point to poly
   */
  double GetMinDistFromPtAndPoly(const Boost_Point &pt, const Polygon &poly);

  double CalIoAvoidDist(AvoidSpeedLevel avoid_level, const double &cmd_v,
                        const double &controller_frequency);

  double CalFrontCameraDist(const std::string &name,
                            const double &current_dist);

  SensorTimeOut SensorIsTimeout();

  bool manual_;
  AvoidOption option_;
  ros::Duration reserve_time_;
  ros::Time lastest_stop_time_;
  SensorAvoidLevel sensor_avoid_level_;
  AvoidLevel io_state_;
  // 避障超时
  uint8_t avoid_time_out_cnt_;
  VaildHeight vaild_height_;
  uint8_t fork_up_down_state_;

  ros::Publisher avoid_pub_;

  std::map<std::string, std::vector<Boost_Point>> default_map_;
  AvoidMapType active_avoid_map_;
  AvoidMap active_map_;
  AvoidMap pederstrain_strategy_;
  AvoidMap default_strategy_;
  std::map<std::string, bool> avoid_strategy_;
  AvoidMessageQueue msg_queue_;
  std::shared_ptr<StateManager> state_manager_ptr_;
  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;

  std::map<std::string, FrontAvoidCamera> front_camera_dist_map_;

  cotek_msgs::safety_state last_safety_state_;

  cotek_msgs::move_cmd move_cmd_;

  TimedWeakenAvoidInfo weaken_avoid_info_;

  bool set_map_init_;
};
}  // namespace cotek_avoid

#endif  // COTEK_AVOID_INCLUDE_COTEK_AVOID_AVOID_PROCESS_H_
