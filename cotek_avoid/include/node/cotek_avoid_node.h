/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_AVOID_INCLUDE_NODE_COTEK_AVOID_NODE_H_
#define COTEK_AVOID_INCLUDE_NODE_COTEK_AVOID_NODE_H_
#include <ros/package.h>
#include <ros/ros.h>
#include <sensor_msgs/LaserScan.h>

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "cotek_avoid/avoid.h"
#include "cotek_avoid/avoid_config_helper.h"
#include "cotek_avoid/avoid_constants.h"
#include "cotek_avoid/avoid_options.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/node_diagnostic_manager.h"
#include "cotek_common/util/local_service.h"
#include "cotek_common/util/remote_service.h"
#include "cotek_msgs/avoid_camera.h"
#include "cotek_msgs/bump_reset.h"
#include "cotek_msgs/front_camera_distance.h"
#include "cotek_msgs/move_feedback.h"
#include "cotek_msgs/pallet_stablizer.h"
#include "cotek_msgs/update_avoid_area_config.h"
#include "cotek_msgs/update_avoid_config.h"
#include "cotek_msgs/update_basic_config.h"
#include "cotek_msgs/update_config.h"
#include "cotek_msgs/weaken_avoid.h"
#include "cotek_msgs/wire_encoder_feedback.h"
#include "ros/service_server.h"
#include "sensor_msgs/PointCloud2.h"
#include "std_msgs/Int32.h"

class AvoidNode final {
  using Avoid = cotek_avoid::Avoid;
  using StateManager = cotek_avoid::StateManager;
  using FixedRatioSampler = cotek_avoid::FixedRatioSampler;
  using AvoidNodeStatus = cotek_diagnostic::AvoidNodeStatus;
  using NodeStatusManager =
      cotek_diagnostic::NodeStatusManager<AvoidNodeStatus>;

 public:
  AvoidNode() = delete;

  explicit AvoidNode(ros::NodeHandle *nh) : avoid_ptr_(nullptr) {
    node_status_manager_ptr_ = std::make_shared<NodeStatusManager>();
    timer_ = nh->createTimer(ros::Duration(1 / 20.), &AvoidNode::NodeDiagnostic,
                             this);
    node_diagnostic_pub_ = nh->advertise<cotek_msgs::node_diagnostic>(
        cotek_topic::kAvoidDiagnosticTopic, 10);
  }

  void NodeDiagnostic(const ros::TimerEvent &e) {
    cotek_msgs::node_diagnostic msg;
    msg.header.stamp = ros::Time::now();
    for (auto status : node_status_manager_ptr_->GetNodeStatus()) {
      msg.status.push_back(static_cast<int>(status.second));
    }
    node_diagnostic_pub_.publish(msg);
    node_status_manager_ptr_->PeriodClearNodeStatus(0.5);
  }

  bool Init(ros::NodeHandle *nh) {
    cotek_avoid::AvoidOption option_tmp;

    try {
      // 基础配置
      if (cotek_avoid::ConfigHelper::Instance().LoadBasicConfig()) {
        LOG_INFO("avoid baisc config success.");
      } else {
        node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::CONFIG_ERROR);
        return false;
      }

      // 避障区域配置
      std::string config_json_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::AVOID_AREA);
      if (cotek_avoid::ConfigHelper::Instance().LoadAvoidAreaConfig(
              config_json_str)) {
        LOG_INFO(" avoid area load config success.");
      } else {
        node_status_manager_ptr_->SetNodeStatus(
            AvoidNodeStatus::AVOID_MAP_ERROR);
        return false;
      }

      // 节点配置
      std::string avoid_config_file = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::AVOID_CONFIG);

      if (!cotek_avoid::ConfigHelper::Instance().LoadAvoidConfig(
              avoid_config_file, &option_tmp)) {
        node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::CONFIG_ERROR);
        return false;
      }

      LOG_INFO(" avoid config success.");

      state_manager_ = std::make_shared<StateManager>();
      avoid_ptr_ = std::make_shared<cotek_avoid::Avoid>(
          option_tmp, node_status_manager_ptr_, state_manager_);

      auto option = option_tmp.topic_option;

      if (option.io_state) {
        subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
            cotek_topic::kSafetyIoStateTopic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleSafetyIoStateMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kSafetyIoStateTopic,
            std::make_shared<FixedRatioSampler>(1.0, 1.0));
      }

      if (option.avoid_ultrasonic) {
        subscribers_.emplace_back(
            nh->subscribe<cotek_msgs::ultrasonic_feedback>(
                cotek_topic::kUltarsonicTopic, kTopicReciveCacheSize,
                boost::bind(&AvoidNode::HandleUltrasonicMessage, this, _1)));
        state_manager_->samplers().emplace(
            cotek_topic::kUltarsonicTopic,
            std::make_shared<FixedRatioSampler>(0.5, 1.0));
      }

      auto laser_map = cotek_avoid::ConfigHelper::Instance().GetLaserMap();

      if (option.navi_laser_0) {
        auto laser = laser_map.at(cotek_topic::kNaviLaser0Topic);
        if (!laser.use_3d) {
          subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
              laser.topic, kTopicReciveCacheSize,
              boost::bind(&AvoidNode::HandleLaserScanMessage, this, _1)));

          state_manager_->samplers().emplace(
              laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
        }
      }

      if (option.navi_laser_1) {
        auto laser = laser_map.at(cotek_topic::kNaviLaser1Topic);
        if (!laser.use_3d) {
          subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
              laser.topic, kTopicReciveCacheSize,
              boost::bind(&AvoidNode::HandleLaserScanMessage, this, _1)));

          state_manager_->samplers().emplace(
              laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
        } else {
          subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
              laser.topic, kTopicReciveCacheSize,
              boost::bind(&AvoidNode::HandleNaviCloud1Message, this, _1)));

          state_manager_->samplers().emplace(
              laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
        }
      }

      if (option.navi_laser_2) {
        auto laser = laser_map.at(cotek_topic::kNaviLaser2Topic);
        if (!laser.use_3d) {
          subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
              laser.topic, kTopicReciveCacheSize,
              boost::bind(&AvoidNode::HandleLaserScanMessage, this, _1)));

          state_manager_->samplers().emplace(
              laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
        }
      }

      if (option.avoid_laser_0) {
        auto laser = laser_map.at(cotek_topic::kAvoidLaser0Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidLaserMessage, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_laser_1) {
        auto laser = laser_map.at(cotek_topic::kAvoidLaser1Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidLaserMessage, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_laser_2) {
        auto laser = laser_map.at(cotek_topic::kAvoidLaser2Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidLaserMessage, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_laser_3) {
        auto laser = laser_map.at(cotek_topic::kAvoidLaser3Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidLaserMessage, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_laser_4) {
        auto laser = laser_map.at(cotek_topic::kAvoidLaser4Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidLaserMessage, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_laser_5) {
        auto laser = laser_map.at(cotek_topic::kAvoidLaser5Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::LaserScan>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidLaserMessage, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_cloud_0) {
        auto laser = laser_map.at(cotek_topic::kAvoidCloud0Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCloud0Message, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_cloud_1) {
        auto laser = laser_map.at(cotek_topic::kAvoidCloud1Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCloud1Message, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_cloud_2) {
        auto laser = laser_map.at(cotek_topic::kAvoidCloud2Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCloud2Message, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_cloud_3) {
        auto laser = laser_map.at(cotek_topic::kAvoidCloud3Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCloud3Message, this, _1)));

        LOG_INFO_STREAM(laser.topic);

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_cloud_4) {
        auto laser = laser_map.at(cotek_topic::kAvoidCloud4Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCloud4Message, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      if (option.avoid_cloud_5) {
        auto laser = laser_map.at(cotek_topic::kAvoidCloud5Topic);
        subscribers_.emplace_back(nh->subscribe<sensor_msgs::PointCloud2>(
            laser.topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCloud5Message, this, _1)));

        state_manager_->samplers().emplace(
            laser.frame, std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      // 前置左侧摄像头
      if (option.avoid_camera_0) {
        subscribers_.emplace_back(nh->subscribe<cotek_msgs::avoid_camera>(
            cotek_topic::kAvoidCamera0Topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCameraMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kAvoidCamera0Topic,
            std::make_shared<FixedRatioSampler>(1.0, 0.4));
      }

      // 前置摄像头
      if (option.avoid_camera_1) {
        subscribers_.emplace_back(nh->subscribe<cotek_msgs::avoid_camera>(
            cotek_topic::kAvoidCamera1Topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCameraMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kAvoidCamera1Topic,
            std::make_shared<FixedRatioSampler>(1.0, 0.5));
      }

      // 前置右侧摄像头
      if (option.avoid_camera_2) {
        subscribers_.emplace_back(nh->subscribe<cotek_msgs::avoid_camera>(
            cotek_topic::kAvoidCamera2Topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleAvoidCameraMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kAvoidCamera2Topic,
            std::make_shared<FixedRatioSampler>(1.0, 0.4));
      }

      // 后置叉腿中侧摄像头
      if (option.avoid_camera_3) {
        subscribers_.emplace_back(nh->subscribe<cotek_msgs::avoid_camera>(
            cotek_topic::kAvoidCamera3Topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleBackAvoidCameraMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kAvoidCamera3Topic,
            std::make_shared<FixedRatioSampler>(1.0, 0.4));
      }

      // 后置右侧叉腿摄像头
      if (option.avoid_camera_4) {
        subscribers_.emplace_back(nh->subscribe<cotek_msgs::avoid_camera>(
            cotek_topic::kAvoidCamera4Topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleBackAvoidCameraMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kAvoidCamera4Topic,
            std::make_shared<FixedRatioSampler>(1.0, 0.4));
      }

      // 后置左侧叉腿摄像头
      if (option.avoid_camera_5) {
        subscribers_.emplace_back(nh->subscribe<cotek_msgs::avoid_camera>(
            cotek_topic::kAvoidCamera5Topic, kTopicReciveCacheSize,
            boost::bind(&AvoidNode::HandleBackAvoidCameraMessage, this, _1)));

        state_manager_->samplers().emplace(
            cotek_topic::kAvoidCamera5Topic,
            std::make_shared<FixedRatioSampler>(1.0, 0.4));
      }

      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kManualTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleManualMessage, this, _1)));

      subscribers_.emplace_back(nh->subscribe<cotek_msgs::safety_setting>(
          cotek_topic::kSafetySettingTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleSafetySettingMessage, this, _1)));

      subscribers_.emplace_back(nh->subscribe<cotek_msgs::bump_reset>(
          cotek_topic::kBumpResetTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleBumpResetMessage, this, _1)));

      subscribers_.emplace_back(nh->subscribe<cotek_msgs::move_feedback>(
          cotek_topic::kMoveFeedbackTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleMoveFeedbackMessage, this, _1)));

      // cmd
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::move_cmd>(
          cotek_topic::kMoveCmdTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleMoveCmdMessage, this, _1)));

      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kLimitSwitchStateTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleForkliftPalleStatemsg, this, _1)));

      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::wire_encoder_feedback>(
              cotek_topic::kHeightEncoderTopic, kTopicReciveCacheSize,
              boost::bind(&AvoidNode::HandleHightBmmskencodeFeedbackmsg, this,
                          _1)));

      subscribers_.emplace_back(nh->subscribe<cotek_msgs::weaken_avoid>(
          "weaken_avoid", kTopicReciveCacheSize,
          boost::bind(&AvoidNode::HandleWeakenLaserFeedbackmsg, this, _1)));

      subscribers_.emplace_back(nh->subscribe<cotek_msgs::update_basic_config>(
          cotek_topic::kUpdateBasicConfigTopic, kTopicReciveCacheSize,
          boost::bind(&AvoidNode::UpdateAvoidBasicConfig, this, _1)));

      update_avoid_config_service_server_ =
          nh->advertiseService(cotek_services::kUpdateAvoidConfigService,
                               &AvoidNode::UpdateAvoidConfig, this);
      update_avoid_area_config_service_server_ =
          nh->advertiseService(cotek_services::kUpdateAvoidAreaConfigService,
                               &AvoidNode::UpdateAvoidAreaConfig, this);

    } catch (std::exception &e) {
      LOG_ERROR("[AvoidNode] init error: %s", e.what());
      node_status_manager_ptr_->SetNodeStatus(AvoidNodeStatus::CONFIG_ERROR);
      return false;
    }

    return avoid_ptr_->Init();
  }

  void Run() { avoid_ptr_->Run(); }

 private:
  void HandleSafetyIoStateMessage(const std_msgs::Int32::ConstPtr &state) {
    avoid_ptr_->AddSafetyIOStateCallback(state);
  }

  void HandleForkliftPalleStatemsg(
      const std_msgs::Int32::ConstPtr &pallet_fork_io_state) {
    avoid_ptr_->AddForkliftPalleState(pallet_fork_io_state);
  }

  void HandleLaserScanMessage(const sensor_msgs::LaserScan::ConstPtr &scan) {
    avoid_ptr_->AddLaserScanCallback(scan);
  }

  void HandleUltrasonicMessage(
      const cotek_msgs::ultrasonic_feedback::ConstPtr &data) {
    avoid_ptr_->AddUltarsonicCallback(data);
  }

  void HandleAvoidLaserMessage(const sensor_msgs::LaserScan::ConstPtr &scan) {
    avoid_ptr_->AddAvoidLaser1Callback(scan);
  }

  void HandleAvoidCloud0Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddAvoidCloud0Callback(cloud);
  }
  void HandleAvoidCloud1Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddAvoidCloud1Callback(cloud);
  }
  void HandleAvoidCloud2Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddAvoidCloud2Callback(cloud);
  }
  void HandleAvoidCloud3Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddAvoidCloud3Callback(cloud);
  }
  void HandleAvoidCloud4Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddAvoidCloud4Callback(cloud);
  }
  void HandleAvoidCloud5Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddAvoidCloud5Callback(cloud);
  }

  void HandleNaviCloud0Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddNaviCloud0Callback(cloud);
  }

  void HandleNaviCloud1Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddNaviCloud1Callback(cloud);
  }

  void HandleNaviCloud2Message(
      const sensor_msgs::PointCloud2::ConstPtr &cloud) {
    avoid_ptr_->AddNaviCloud2Callback(cloud);
  }

  void HandleAvoidCameraMessage(
      const cotek_msgs::avoid_camera::ConstPtr &data) {
    avoid_ptr_->AddAvoidCameraCallback(data);
  }

  void HandleBackAvoidCameraMessage(
      const cotek_msgs::avoid_camera::ConstPtr &data) {
    avoid_ptr_->AddBackAvoidCameraCallback(data);
  }

  void HandleSafetySettingMessage(
      const cotek_msgs::safety_setting::ConstPtr &setting) {
    avoid_ptr_->AddSafetySettingCallback(setting);
  }

  void HandleManualMessage(const std_msgs::Int32::ConstPtr &manual) {
    avoid_ptr_->AddManualCallback(manual);
  }

  void HandleMoveFeedbackMessage(
      const cotek_msgs::move_feedback::ConstPtr &feedback) {
    avoid_ptr_->AddMoveFeedback(feedback);
  }

  void HandleMoveCmdMessage(const cotek_msgs::move_cmd::ConstPtr &move_cmd) {
    avoid_ptr_->AddMoveCmd(move_cmd);
  }

  void HandleHightBmmskencodeFeedbackmsg(
      const cotek_msgs::wire_encoder_feedback::ConstPtr &bmmsk34) {
    avoid_ptr_->AddHightBmmsk34Encode(bmmsk34);
  }

  void HandleWeakenLaserFeedbackmsg(
      const cotek_msgs::weaken_avoid::ConstPtr &feed) {
    avoid_ptr_->AddWeakenLaser(feed);
  }

  void HandleBumpResetMessage(
      const cotek_msgs::bump_reset::ConstPtr &feedback) {
    avoid_ptr_->AddBumpReset(feedback);
  }

  bool UpdateAvoidConfig(cotek_msgs::update_config::Request &req,
                         cotek_msgs::update_config::Response &res) {
    std::string avoid_config_str = req.data;

    cotek_avoid::AvoidOption option;
    if (!cotek_avoid::ConfigHelper::Instance().LoadAvoidConfig(avoid_config_str,
                                                               &option)) {
      LOG_ERROR("Update avoid_config failed!!!");
      res.status = cotek_config::kConfigError;
      return false;
    }

    avoid_ptr_->UpdateOption(option);
    res.status = cotek_config::kConfigOk;
    BasicConfigHelper::Instance().SaveLocal(
        cotek_config::ConfigType::AVOID_CONFIG, avoid_config_str);
    LOG_INFO("Update avoid_config success!!!");
    return true;
  }

  bool UpdateAvoidAreaConfig(cotek_msgs::update_config::Request &req,
                             cotek_msgs::update_config::Response &res) {
    // 避障区域配置
    std::string &config_json_str = req.data;
    if (cotek_avoid::ConfigHelper::Instance().LoadAvoidAreaConfig(
            config_json_str)) {
      BasicConfigHelper::Instance().SaveLocal(
          cotek_config::ConfigType::AVOID_AREA, config_json_str);
      avoid_ptr_->UpdateAvoidMap();

    } else {
      LOG_ERROR("Update Avoid area config error.");
      res.status = cotek_config::kConfigError;
      return false;
    }
    res.status = cotek_config::kConfigOk;
    LOG_INFO("Update avoid_area success!!!");
    return true;
  }

  void UpdateAvoidBasicConfig(
      const cotek_msgs::update_basic_config::ConstPtr &feedback) {
    if (!cotek_avoid::ConfigHelper::Instance().LoadBasicConfig()) {
      LOG_ERROR("Update Avoid baisc config error.");
      return;
    }
    LOG_INFO("Update avoid_basic_config success!!!");
  }

  ros::NodeHandle nh;
  ros::Timer timer_;
  std::shared_ptr<cotek_avoid::ConfigHelper> config_;
  std::shared_ptr<Avoid> avoid_ptr_;
  std::vector<ros::Subscriber> subscribers_;
  ros::ServiceServer update_avoid_config_service_server_;
  ros::ServiceServer update_avoid_area_config_service_server_;
  ros::ServiceServer update_config_service_server_;
  ros::Publisher node_diagnostic_pub_;
  std::shared_ptr<StateManager> state_manager_;
  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;
};

#endif  // COTEK_AVOID_INCLUDE_NODE_COTEK_AVOID_NODE_H_
