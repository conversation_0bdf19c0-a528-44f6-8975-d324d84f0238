<?xml version="1.0"?>

<launch>
    <!-- <include file="$(find cotek_avoid)/launch/carto_map.launch"/> -->

    <!-- <param name="robot_description" textfile="$(find cotek_avoid)/config/cotek_agv.urdf" /> -->
    <!-- Avoid Node -->
    <node pkg="cotek_avoid" type="cotek_avoid_node" name="cotek_avoid_node" respawn="true" output="screen">
        <!-- <rosparam command="load" file="$(find cotek_avoid)/config/cotek_avoid_params.yaml" /> -->
    </node>

    <!-- <node name="rviz" pkg="rviz" type="rviz" args="-d $(find cotek_avoid)/config/cotek_avoid.rviz" /> -->
</launch>