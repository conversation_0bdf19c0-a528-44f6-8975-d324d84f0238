<!--
  Copyright (c) 2023 COTEK Inc. All rights reserved. 
-->

<launch>
  <arg name="agv_id" value="$(env AGV_ID)" />
  <arg name="addr" value="/home/<USER>/carto_ws/src/cartographer_ros/cartographer_ros"/>
  <param name="/use_sim_time" value="true" />

  <node name="cartographer_node" pkg="cartographer_ros" type="cartographer_node" args="
          -configuration_directory $(arg addr)/configuration_files
          -configuration_basename cotek_localization.lua
          -load_state_filename $(arg addr)/mapfiles/xus.pbstream" output="screen">
  </node>

  <node if="$(eval arg('agv_id') == 'E101')" name="cartographer_start_trajectory" pkg="cartographer_ros" type="cartographer_start_trajectory" args="
          -configuration_directory $(arg addr)/configuration_files
          -configuration_basename cotek_localization.lua
          -initial_pose {to_trajectory_id=0.,relative_pose={translation={-0.227,0.986,0.},rotation={0.,0.,0.007}},timestamp=0.}" output="screen">
  </node>

  <node if="$(eval arg('agv_id') == 'E102')" name="cartographer_start_trajectory" pkg="cartographer_ros" type="cartographer_start_trajectory" args="
          -configuration_directory $(arg addr)/configuration_files
          -configuration_basename cotek_localization.lua
          -initial_pose {to_trajectory_id=0.,relative_pose={translation={0.,0.,0.},rotation={0.,0.,0.}},timestamp=0.}" output="screen">
  </node>

  <node name="cartographer_occupancy_grid_node" pkg="cartographer_ros" type="cartographer_occupancy_grid_node" args="-resolution 0.05" />

  <node pkg="cotek_sensor" type="cotek_sensor_node" name="cotek_sensor_node" required="true">
    <param name="enable_odometry" value="true" />
    <param name="enable_imu" value="false" />
  </node>

  <group if="$(eval arg('agv_id') == 'E101')">
    <node pkg="tf" type="static_transform_publisher" name="baselink_scan_1_broadcaster" args="0.376782 0.341164 0 0.794508 0 0 base_link laser1 35" />
    <node pkg="tf" type="static_transform_publisher" name="baselink_scan_2_broadcaster" args="-0.378387 -0.340785 0 -2.330812 0 0 base_link laser2 35" />
  </group>

  <group if="$(eval arg('agv_id') == 'E102')">
    <node pkg="tf" type="static_transform_publisher" name="baselink_scan_1_broadcaster" args="0.377964 0.336400 0 0.802037 0 0 base_link laser1 35" />
    <node pkg="tf" type="static_transform_publisher" name="baselink_scan_2_broadcaster" args="-0.375487 -0.334317 0 -2.400146 0 0 base_link laser2 35" />
  </group>
</launch>
                                                                                                                      