# 节点名:cotek_avoid
- 节点说明:
  接收传感器数据，通过配置的地图，输出避障等级
  目前兼容:
  -北洋避障激光（IO信号）
  -倍加福红外避障r2100
  -倍加福导航激光r2000

## 相关硬件
- PGV R2000
- PGV R2100
- 北洋避障激光

## 依赖关系
### 节点依赖
--> decision_maker
--> diagnostic

<-- embedded_node
<-- decision_maker

### 通信依赖
#### 话题:
- 发布:
        /safety_state       (消息内容：避障类型码 消息文件：safety_state.msg)
   /avoid_node_diagnostic   (消息内容：故障码+时间戳 消息文件：node_diagnostic.msg)
- 订阅:
        /safety_io_state    (消息内容：避障等级码 消息文件：safety_io_state.msg)
        /pgv_r2100          (消息内容：11根线距离值 消息文件：pgv_r2100_feedback.msg)
        /sensor_msgs::scan  (消息内容：激光数据 消息文件：sensor_msgs::LaserScan)
        /safety_setting     (消息内容：避障地图数据 消息文件：safety_state.msg)

## 包结构
```cpp
-----avoid
 |--config
 |--include
 |  |- cotek_avoid
 |  |  |--avoid_constants.h     // 节点独有常量
 |  |  |--avoid_message_queue.h // AvoidMessageQueue类定义 避障等级队列处理
 |  |  |--avoid_options.h       // 节点所用数据结构
 |  |  |--avoid_process.h       // AvoidProcess类定义 数据处理
 |  |  |--avoid.h               // Avoid类定义 数据及配置输入
 |  |  |--config_helper.h       // ConfigHelper类定义 配置解析
 |  |  └--fixed_ratio_sampler.h // FixedRatioSampler类定义 多激光处理
 |  └--node
 |     └--cotek_avoid_node.h    // AvoidNode类定义 消息订阅配置输入
 |--launch
 |  |--carto_map.launch
 |  └--cotek_avoid.launch
 |--src
 |  |--avoid_constants.cc       // 通过激光个数输出话题名scan_1、scan_2..
 |  |--avoid_message_queue.cc   // 处理多个激光数据时，将避障等级先放进队列，返回一段时间内的最大避障等级
 |  |--avoid_process.cc         // 激光原始数据的处理（事件回调驱动），根据激光点在避障地图中的位置得到避障等级
 |  |--avoid.cc                 // 数据输入及错误发送
 |  |--config_helper.cc         // 地图文件解析
 |  |--cotek_avoid_node.cc      
 |  └--fixed_ratio_sampler.h    // 针对多激光，采样激光数据
 |--CMakeLists.txt
 └--package.xml
```
### 源码说明
* AvoidNode类
1. Init():初始化消息订阅回调函数
2. run(): 启动循环运行线程

* Avoid类
1. HandleError():错误处理
2. NodeDiagnostic()：错误及故障发布至diagnostic节点

* AvoidProcess类
1. Init():加载避障设置地图，初始化避障测略
2. GetAvoidState():处理传感器数据，获取避障状态
3. GetScanAvoidLevel():处理激光数据，获取避障等级
4. GetAvoidMap():获取避障策略
5. AvoidPolygonPublish():多边形避障区域避障状态发布
6. AvoidPointPublish():避障点发布

###### 注：
> 1、北洋避障激光（IO信号: 直接得到避障等级；\
> 2、倍加福导航激光r2000: 标准激光格式sensor_msgs::scan，使用polygon库判断激光点的在自定义避障地图中的位置，从而返回避障等级；\
> 3、倍加福红外避障r2100: 将11根激光束看做激光点，使用sensor_msgs::scan数据结构处理，处理方法如r2000。

###### config配置文件说明
- avoid.json
读取预先设置好的地图信息

> sensor_tf

frame_id: 用于索引激光数据，务必与scan相对应；\
这里规定：单激光为laser， 两个以上激光为laser1，laser2 ...**
tf_value: 传感器安装位置的坐标，相对于车中心；

> agv_inner

表示相对于车中心的顶点坐标，相邻坐标点连线而成的多边形为车体模型

> map_area

id: 地图编号
slow_level1: 一级减速地图，表示方法如agv_inner
slow_level2: 二级减速地图，表示方法如agv_inner
stop:        停止地图，表示方法如agv_inner


## 参数配置
```cpp
controller_frequency: 20        // 控制循环频率
node_diagnostic_frequency: 20.  // 故障码发送pinlv
enable_local_debug: true        // debug

* 使能传感器话题  
num_laser_scans: 1                  // 激光数量
enable_Sick_LMS_lidar: false        // 使能Sick
enable_PGV_r2000_lidar: true        // 使能PGV_r2000
enable_PGV_r2100_avoid_lidar: false // 使能PGV_r2100
enable_io_state: true               // 使能IO
enable_Ultrasound: false            // 使能超声波

* 数据处理相关
enable_visualize: true              // 避障虚拟多边形
ingore_error_laser_point: true      // 忽略激光杂点

* 导航激光 限制参数
navi_laser_scan_sample_step: 4      // 
navi_laser_samples_per_scan: 1081   // 点数
navi_laser_scan_frame_name: laser   // 参考系名
navi_laser_angle_min: -2.3562       // 起点角度
navi_laser_angle_max: 2.3562        // 结束角度
navi_laser_angle_increment: 0.004   // 角度增量
navi_laser_range_min: 0             // 最小幅度
navi_laser_range_max: 30            // 最大幅度

* 倍加福避障激光 限制参数
pgv_scan_sample_step: 1             // 
pgv_samples_per_scan: 11            // 共11根激光
pgv_scan_frame_name: laser          // 参考系名
pgv_angle_min: -0.6978              // 起点角度
pgv_angle_max: 0.6978               // 结束角度
pgv_angle_increment: 0.1396         // 每根激光间隔8度
pgv_range_min: 0                    // 最小幅度
pgv_range_max: 30                   // 最大幅度

```

###### 避障等级
```cpp
>IO 输入
enum class AvoidLevel {
  NONE = 0,            // 无避障
  LEVEL_I = 1,         // 避障区域 避障等级1 - slowlevel1
  LEVEL_II = 2,        // 避障区域 避障等级2 - slowlevel2
  LEVEL_III = 3,       // 避障区域 避障等级3 - stop
  BUMP = 4,            // 防撞条
  FORK_LEFT_LEG = 5,   // 叉车左叉腿防撞
  FORK_RIGHT_LEG = 6,  // 叉车右叉腿防撞
  FORK_LEG_BOTH = 7    // 叉车左右叉腿都检测到
};
> 等级输出
enum class AvoidSpeedLevel {
  FREE = 0,
  SLOWLEVEL1 = 1,
  SLOWLEVEL2 = 2,
  STOP = 3
};
```
###### 错误码
```cpp
enum class ErrorCode {
  NONE = 0,
  CONFIG_ERR,        // 配置错误
  AVOID_SETTING_ERR, // 设置错误
  DATA_CHECK_ERR,    // 数据错误
  DATA_TIME_OUT,     // 数据超时
  AVOID_OVERTIME,    // 避障超时
  BUMP_ERR           // 防撞触发
};
```




