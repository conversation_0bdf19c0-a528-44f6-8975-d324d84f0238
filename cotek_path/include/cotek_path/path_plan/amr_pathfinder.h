//
// Created by o<PERSON> on 24-4-18.
//

#ifndef ZWORKSPACE_AMR_PATHFINDER_H
#define ZWORKSPACE_AMR_PATHFINDER_H

#include <numeric>
#include "graph_builder.hpp"
#include "shortest_path_algo.h"
#include "start_locator.h"
#include "cotek_common/log_porting.h"

class AmrPathFinder{
    // 原始地图
    Graph graph;

    // 虚拟地图
    Graph virtualGraph;

    // map寻找原始节点对应的虚拟节点
    std::unordered_map<std::string, std::set<Vertex>> map;

    // 一个map记录每条边的头和尾所对应的拓扑节点
    std::map<std::string, std::string> point2TopologyMap;

    // 将多大范围内的角度朝向认为是同一个朝向
    double similarAngleRange = 5.0;
    // 最大允许转向角
    double maxSteeringAngle = 60.0;

    // rtree 用来找起点
    //StartLocator startLocator;
    KDLocator startLocator;

    // 构建地图
    GraphBuilder graphBuilder;

    // 记录所有的任务
    std::vector<cotek::task_t> allTasks;

    // 记录每个任务的路径，用于在（可以按照原任务路线直接走的时候不进行路径规划）
    std::unordered_map<std::string, std::vector<cotek::weighted_edge_t>> taskPathsMap;

    // 记录每个任务 第一个任务点所在的 edge 并标记这个任务点是否在边的末尾，（如果不在末尾那么一定在开头）
    // (用于判断当前位置附近的边是否在任务第一个 Action edge 的前面)
    std::unordered_map<std::string, int> taskFirstActionIndexMap;

    // 记录任务的actions，以及所有 action 的 index
    std::unordered_map<std::string, std::vector<cotek::node_t>> taskActionWithIndexMap;

    // 记录每个任务终点能够执行的下一个任务列表
    std::unordered_map<std::string, std::vector<std::string>> taskNextTasksMap;

    // 库区相关数据
    std::map<std::string, std::vector<GraphBuilder::KuquBinding>> kuqu_bindings;  // 库区绑定信息（支持多个绑定）


public:

    // 获取当前位置执行任务的路径代价
    uint16_t getTaskPathCost(const cotek::pose_t& curr_pos, const std::vector<cotek::node_t> targets, const std::string& task_id);

    // 连续路径规划（中途多个要经过的点）
    std::vector<cotek::weighted_edge_t> getPath(const cotek::pose_t& curr_pos, const std::vector<cotek::node_t> targets, const std::string& task_id, bool replan = false);

    // 多个任务的路径能否组合
    bool canCombineTasks(const cotek::pose_t& curr_pos, const std::vector<std::string>& task_ids);

    // 获取指定任务终点能够执行的下一个任务列表
    std::vector<std::string> getNextTasks(const std::string& task_id) const;

    // 库区任务相关方法
    bool isKuquTask(const std::string& task_id) const;
    std::vector<cotek::weighted_edge_t> getKuquPath(const cotek::pose_t& curr_pos, const std::string& kuqu_id);
    std::vector<std::string> getKuquEndNodes(const std::string& kuqu_id) const;
    std::vector<GraphBuilder::KuquBinding> getKuquBindings(const std::string& kuqu_id) const;

    // 当前位置到目标点路径规划 （给出代价以及路径）
    std::pair<int16_t, std::vector<cotek::weighted_edge_t>> getPathAndCost(const cotek::pose_t& curr_pos, const std::string& target_id, double target_yaw);

    // 考虑转向限制的路径规划
    std::vector<cotek::weighted_edge_t> getPath(const std::string& s, const std::string& t, double s_yaw, double t_yaw, const std::string& task_id);

    // 静态路径规划
    std::vector<cotek::weighted_edge_t> getStaticPath(const std::string& s, const std::string& t);

    // 根据输入位姿找起点
    std::vector<cotek::weighted_edge_t> getPath(cotek::pose_t curr_pos, const std::string& goal_id, double goal_yaw);

    // (重新)构建地图
    bool buildTopologicalGraph(const std::vector<cotek::task_t>& tasks){
        if(tasks.empty()) return false;
        initTaskPathsMap(tasks);
        graphBuilder.buildTopologicalGraph(tasks);
        this->graph = graphBuilder.getGraph().second;
        this->point2TopologyMap = graphBuilder.point2TopologyMap;
        this->kuqu_bindings = graphBuilder.kuqu_bindings;
        // todo:存储point2TopologyMap到数据库
        auto beforeTime = std::chrono::steady_clock::now();
        initVirtualGraph(graph);
        auto afterTime = std::chrono::steady_clock::now();
        double duration_millsecond = std::chrono::duration<double, std::milli>(afterTime - beforeTime).count();
        LOG_INFO_STREAM("building virtual merged graph time cost:" << duration_millsecond << "ms");
        exportVirtualGraph();
        startLocator.init(tasks);
        // 计算任务连接关系
        initTaskConnectionsMap(tasks);
        return true;
    }


    // 加载地图
    bool loadTopologicalGraph(const std::vector<cotek::task_t>& tasks, const std::map<std::string, std::string> &_point2TopologyMap){
        if(tasks.empty()) return false;
        
        if (graphBuilder.getGraph().first) {
            this->graph = graphBuilder.getGraph().second;
        } else {
            return graphBuilder.buildTopologicalGraph(tasks);
        }
        initTaskPathsMap(tasks);
        // 加载map
        this->point2TopologyMap = _point2TopologyMap;
        this->kuqu_bindings = graphBuilder.kuqu_bindings;
        auto beforeTime = std::chrono::steady_clock::now();
        initVirtualGraph(graph);
        auto afterTime = std::chrono::steady_clock::now();
        double duration_millsecond = std::chrono::duration<double, std::milli>(afterTime - beforeTime).count();
        LOG_INFO_STREAM("building virtual merged graph time cost:" << duration_millsecond << "ms");
        exportVirtualGraph();
        startLocator.init(tasks);
        // 计算任务连接关系
        initTaskConnectionsMap(tasks);
        return true;
    }

    void exportVirtualGraph();

    auto getPoint2TopologyMap(){
        return point2TopologyMap;
    }

    AmrPathFinder(double similar_angle_range = 5.0, double max_steering_angle = 60.0)
        : similarAngleRange(similar_angle_range), maxSteeringAngle(max_steering_angle) {
    }

    explicit AmrPathFinder(const Graph& graph){
        auto beforeTime = std::chrono::steady_clock::now();
        this->graph = graph;
        initVirtualGraph(graph);
        auto afterTime = std::chrono::steady_clock::now();
        double duration_millsecond = std::chrono::duration<double, std::milli>(afterTime - beforeTime).count();
        LOG_INFO_STREAM("building virtual merged graph time cost:" << duration_millsecond << "ms");
    }

    explicit AmrPathFinder(const std::vector<cotek::task_t>& tasks){
        buildTopologicalGraph(tasks);
    }

    void updataEdge(const cotek::task_t& task) {
        if (taskPathsMap.find(task.id) != taskPathsMap.end()) {
            for (int i=0; i < taskPathsMap[task.id].size(); i++) {
                taskPathsMap[task.id][i].velocity_level = task.velocity_level;
                taskPathsMap[task.id][i].avoid_level = task.avoid_level;
            }
        } else {
            LOG_ERROR("path finder can't find this task(%s)", task.id.c_str());
            return;
        }
    }

    void updataEdge(const cotek::task_t& task,
                    const std::string& edge_id, 
                    const int& velocity_level, 
                    const int& avoid_level) {
        if (taskPathsMap.find(task.id) != taskPathsMap.end()) {
            for (int i=0; i < taskPathsMap[task.id].size(); i++) {
                if (taskPathsMap[task.id][i].task_edge_id == edge_id) {
                    taskPathsMap[task.id][i].velocity_level = velocity_level;
                    taskPathsMap[task.id][i].avoid_level = avoid_level;
                    break;
                }
            }
        } else {
            LOG_ERROR("path finder can't find this task(%s)", task.id.c_str());
            return;
        }
    }

private:
    // 计算任务连接关系
    void initTaskConnectionsMap(const std::vector<cotek::task_t>& tasks);

    // 存下每个任务的路径
    void initTaskPathsMap(const std::vector<cotek::task_t>& tasks){
        // 清空之前的任务路径
        taskFirstActionIndexMap.clear();
        taskActionWithIndexMap.clear();
        taskPathsMap.clear();
        taskNextTasksMap.clear();

        allTasks = tasks;
        for (const auto& task : tasks){
            std::vector<cotek::weighted_edge_t> tempPath;
            for(const auto& edge : task.edges){
                cotek::weighted_edge_t tempEdge;
                tempEdge.task_id = task.id;
                tempEdge.task_edge_id = edge.id;
                tempEdge.start_index = 0;
                tempEdge.end_index = edge.points.size()-1;
                tempEdge.start_point_id = edge.start_point.id;
                tempEdge.end_point_id = edge.end_point.id;
                tempEdge.velocity_level = edge.velocity_level;
                tempEdge.avoid_level = edge.avoid_level;
                tempEdge.weight = edge.length;                
                tempPath.emplace_back(std::move(tempEdge));
            }
            taskPathsMap[task.id] = tempPath;

            // 初始化 taskFirstActionIndexMap
            taskFirstActionIndexMap[task.id] = firstActionIndex(task);

            // 初始化 taskActionWithIndexMap
            taskActionWithIndexMap[task.id] = getActionWithIndex(task);

        }

    }

    std::vector<cotek::node_t> getActionWithIndex(const cotek::task_t& task){
        if (task.actions.empty()){
            return {};
        }
        
        std::set<std::string> actionIdSet;
        for (const auto& action : task.actions){
            actionIdSet.insert(action.id);
        }

        std::vector<cotek::node_t> res;
        for (int i = 0; i < task.nodes.size(); ++i){
            if (actionIdSet.find(task.nodes[i].id) != actionIdSet.end()){
                cotek::node_t temp = task.nodes[i];
                temp.index = i;
                res.push_back(temp);
            }
        }

        return res;        
    }

    int firstActionIndex(const cotek::task_t& task){
        if (task.actions.empty()){
            return -1;
        }

        const auto& firstActionNode = task.actions[0];
        for (int i = 0; i < task.nodes.size(); ++i){
            if (task.nodes[i].id == firstActionNode.id){
                return i;
            }
        }

        // 
        LOG_ERROR_STREAM("can't find first Action index in:" << task.id);
        return -1;
    }

    // 看是否可以直接找到原任务上的起点，如果可以，直接返回结果
    std::pair<bool, std::vector<cotek::weighted_edge_t>> couldUseOriginalTaskPath(const cotek::pose_t& curr_pos, std::vector<cotek::node_t> targets, const std::string& task_id);

    // 考虑转向限制以及任务偏好的路径规划（输入点为拓扑点）
    std::vector<cotek::weighted_edge_t> getTaskPriorPath(const std::string& s, const std::string& t, double s_yaw, double t_yaw, const std::string& taskId);

    // 考虑转向限制以及终点边的 target_id 路径规划（输入点为拓扑点）
    std::vector<cotek::weighted_edge_t> getPathWithTargetId(const std::string &s, const std::string &t, double s_yaw, double t_yaw, const std::string &targetId);

    // 根据原始地图构建虚拟地图
    void initVirtualGraph(const Graph& g);

    // 在虚拟地图中添加一个虚拟节点，这里判断这个节点是否需要被添加（虚拟节点合并）
    void addVirtualVertex(const std::string& oriId, double heading, const std::string& oriTask);

    // Function to find vertex_descriptor by id
    static Vertex find_vertex_by_id(const Graph& g, const std::string& id);

    // 用于找起点，根据一个查询点找到它所在的边的一小段，以及这条边上target节点以及朝向
    std::vector<std::pair<cotek::weighted_edge_t, VertexData>> findFirstEdgeAndVertex(const Graph& g, const std::vector<QueryPoint>& points);

    // 判定朝向角度相似
    bool anglesSimilar(double angle1, double angle2) const;
    // 判定允许转向
    bool anglesAllowedTurn(double angle1, double angle2) const;

    double angleDifference(double angle1, double angle2);

    // 根据边的descriptor输出路径
    std::vector<cotek::weighted_edge_t> getOutputPath(const std::vector<Edge>& path);

    // 根据边的id找到edge descriptor

    // 获取路径总权重
    static double getPathWeight(const std::vector<cotek::weighted_edge_t>& edges);

    // 根据拟合点找到拓扑节点
    std::pair<bool, std::string> point2TopologicalVertexId(std::string fittingpoint){
        std::string res;
        if (point2TopologyMap.find(fittingpoint) != point2TopologyMap.end()){
            return std::make_pair(true, point2TopologyMap.at(fittingpoint));
        }

        LOG_ERROR_STREAM("can not find topological node for point:"<< fittingpoint);
        return std::make_pair(false, res);
    }

};



#endif //ZWORKSPACE_AMR_PATHFINDER_H
