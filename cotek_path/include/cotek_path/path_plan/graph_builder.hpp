//
// Created by o<PERSON> on 24-4-15.
//
#ifndef MERGED_GRAPH_H
#define MERGED_GRAPH_H
#include <string>
#include <utility>
#include <vector>
#include <boost/graph/adjacency_list.hpp>
#include <boost/graph/graph_utility.hpp>
#include <boost/geometry.hpp>
#include <boost/geometry/geometries/point.hpp>
#include <boost/geometry/geometries/register/point.hpp>
#include <boost/geometry/index/rtree.hpp>
#include <boost/graph/graphviz.hpp>
#include <boost/graph/properties.hpp>
#include <boost/graph/named_function_params.hpp>
#include <cmath>
#include <chrono>
#include <fstream>
#include "var/var.h"
#include "common/chcwd.h"
#include "cotek_common/log_porting.h"

//namespace cotek {
//namespace plan {

struct VertexData{
    std::string id; // 拓扑地图节点id
    double x{};
    double y{};
    std::vector<cotek::node_t> include_points; // 所包含的采集点
    int vertex_type{}; // 0/1/2 采集点/任务点/虚拟点
    double heading{}; // 用于虚拟节点的朝向
    std::string oriTask; // 用于标记这个虚拟节点的任务来源，用于寻找任务所对应的虚拟节点
};

struct EdgeData{
    std::string id;
    std::string ori_id; // 用于虚拟地图边，记录原始边的id
    double weight{};
    //std::string task_id; // 这条边原始任务id，当任务为该id时，调小路径规划的权重

    // 这条边是前进还是倒车, 0前进，1倒车，2都可以
    int move_type{};

    // 边所连接的拓扑节点
    std::string source_id;
    std::string target_id;

    // 边的驶入驶出角
    double source_yaw{};
    double target_yaw{};

    // 隶属于任务中边的编号，起始位置，终止位置
    std::string task_edge_id;
    size_t start_index{};
    size_t end_index{}; // 最后一个元素的索引

    // 任务编号
    std::string task_id;

    // 记录拓扑边的原始 target id，由于拓扑点会合并，
    // 规划的时候需要到达原始的target，而不是经过合并后的其他任务到这位置的target
    std::string ori_target_id; 

    int velocity_level{};       // 路径速度等级 1/2/3
    int avoid_level{};          // 路径避障等级 1/2/3


};

typedef boost::adjacency_list<boost::vecS, boost::vecS,
        boost::directedS,
        VertexData,
        EdgeData> Graph;
typedef Graph::vertex_descriptor Vertex;
typedef Graph::edge_descriptor Edge;
typedef boost::graph_traits<Graph>::vertex_iterator vertex_iterator;
typedef Graph::edge_iterator EdgeIterator;

struct PathTraffic{
    int id;
    size_t start;
    size_t end;
};



// 配置
static double point_overlay_r = 0.1; // 0.15  // 采集点认为重合半径
static double point_overlay_angle = 180;// 采集点认为重合角度范围
static double vertex_r = 0.5;// 节点覆盖范围
// 拓扑地图存储路径
static std::string graphPath = "/home/" + common::get_user_name() + "/config/map/" + "merged_graph.dot";

namespace bg = boost::geometry;
namespace bgi = boost::geometry::index;
// 注册节点结构体为 Boost.Geometry 的点
BOOST_GEOMETRY_REGISTER_POINT_2D(VertexData, double, bg::cs::cartesian, x, y)

class GraphBuilder{

public:
    struct EdgePart{
        std::string edgeId;
        size_t startIndex;
        size_t endIndex;
    };

    struct TrafficArea{
    int id{};
//    std::string line1;
//    // 第一条线上的 区域位置索引
//    std::pair<size_t , size_t> indexPair1;
//    std::string line2;
//    std::pair<size_t , size_t> indexPair2;

    std::string task1;
    std::vector<EdgePart> edges1; // 交管内第一个任务的边id，以及边上的索引（可能是半条边）
    std::string task2;
    std::vector<EdgePart> edges2;
    };

    // 库区边信息
    struct KuquEdge {
        std::string edge_id;           // 库区边ID
        std::string task_id;           // 所属任务ID
        bool is_forward;               // 是否为正向边
        std::vector<cotek::node_t> original_points;  // 原始点集
        std::vector<cotek::node_t> cut_points;       // 切割后的点集
        size_t cut_index;              // 切割点在原始边中的索引
    };

    // 切割点信息
    struct CutPoint {
        size_t index;                  // 在拓扑边中的索引位置
        std::string kuqu_id;           // 所属库区ID
        std::string topology_edge_id;  // 所在拓扑边ID
        cotek::node_t point;           // 切割点坐标
    };

    // 库区绑定信息
    struct KuquBinding {
        std::string kuqu_id;           // 库区ID
        std::string topology_edge_id;  // 绑定的拓扑边ID
        std::vector<CutPoint> cut_points;  // 该拓扑边上的切割点列表
        std::vector<KuquEdge> kuqu_edges;  // 库区中的边列表
        std::string end_node_id;       // 库区规划终点节点ID
    };

    Graph g;

    // 定义 R-tree 来存储节点的坐标信息，用于快速查找
    bgi::rtree<VertexData, bgi::rstar<16>> rtree;

    // 交管索引map
//    std::map<std::string, std::vector<cotek::traffic_t>> edgeTrafficsMap;
//    std::map<int, TrafficArea> trafficIdMap;
    std::map<std::string, std::vector<std::pair<int, EdgePart>>> edgeTrafficsMap;
    std::map<int, TrafficArea> trafficIdMap;

    // todo：处理交管上的两个任务的各条边map

    // 一个map记录每条边的头和尾所对应的拓扑节点
    std::map<std::string, std::string> point2TopologyMap;

    // 库区任务相关数据
    std::vector<cotek::task_t> kuqu_tasks;                    // 库区任务列表
    std::vector<cotek::task_t> normal_tasks;                  // 正常任务列表
    std::map<std::string, KuquBinding> kuqu_bindings;        // 库区绑定关系 key: kuqu_id
    std::map<std::string, std::vector<KuquEdge>> kuqu_edges_map;  // 库区边映射 key: kuqu_id


public:

    // 在指定task的指定index处插入拓扑节点，切割拓扑边
    struct InsertNodeResult {
        bool success;
        std::string new_node_id;
        VertexData new_node_data;
        std::string first_edge_id;   // 切割后的第一条边ID
        std::string second_edge_id;  // 切割后的第二条边ID
        std::string error_message;
    };

    InsertNodeResult insertTopologyNodeAtIndex(const std::string& task_id,
                                               size_t index,
                                               const std::vector<cotek::task_t>& tasks);

    void buildTopologicalGraph(const std::vector<cotek::task_t>& tasks){
        // if (tasks.empty()) return;
        auto beforeTime = std::chrono::steady_clock::now();
        g.clear();
        rtree.clear();
        edgeTrafficsMap.clear();
        trafficIdMap.clear();
        point2TopologyMap.clear();

        // 清空库区相关数据
        kuqu_tasks.clear();
        normal_tasks.clear();
        kuqu_bindings.clear();
        kuqu_edges_map.clear();

        // 分离库区任务和正常任务
        separateKuquTasks(tasks);

        // 先处理正常任务的交管和构建拓扑图
        handleTraffics(normal_tasks);
        constructTopologicalGraph(normal_tasks);

        // 处理库区任务绑定
        processKuquTasks();

        auto afterTime = std::chrono::steady_clock::now();
        //毫秒级
        double duration_millsecond = std::chrono::duration<double, std::milli>(afterTime - beforeTime).count();
        LOG_INFO_STREAM( "building merged graph time cost:" << duration_millsecond << "ms");

        // 构建之后将地图存储为本地文件，避免程序重启之后重复构造
        // 使用绝对路径
        exportDotFile(graphPath);
    }

    void exportDotFile(const std::string& filename);

    static Graph loadMergedGraph(const std::string& fileName);

    std::pair<bool, Graph> getGraph() const{
        if (boost::num_vertices(g) > 0){
            return std::make_pair(true, g);
        }
        // 如果文件存在，则直接加载之后返回
        std::ifstream file(graphPath);
        if (file.good()){
            return std::make_pair(true, loadMergedGraph(graphPath));
        }else{
            LOG_ERROR_STREAM( "merged graph don't exist buildTopologicalGraph() is needed !" );
            return std::make_pair(false, g);
        }
    }
private:

    std::vector<std::vector<cotek::node_t>>  edgeTrafficOtherPoints(const std::vector<cotek::task_t>& tasks, const std::string& currentEdgeId, const std::vector<std::pair<int, EdgePart>>& currentTraffics);

    static std::vector<EdgePart> findEdgesInRange(const std::vector<cotek::edge_t>& edges, int start, int end);

    void handleTraffics(const std::vector<cotek::task_t>& tasks);

    void constructTopologicalGraph(const std::vector<cotek::task_t>& tasks);

    std::pair<Edge, EdgeData> constructVertexAndDividedEdge(const std::vector<cotek::node_t>& path, size_t current_index, Edge& current_edge,
                                       const EdgeData& current_data);

    auto findVertexDescriptor(const VertexData& data);

    //Vertex addMergedVertex(const VertexData& data);

    std::pair<Vertex, VertexData> addMergedVertex(const VertexData& data);

    Edge addEdge(const Vertex& v1, const Vertex& v2, const EdgeData& data);

    void constructAPathTopology(const cotek::edge_t &current_edge,
                                const std::vector<std::vector<cotek::node_t>> &otherPaths,
                                const std::vector<size_t> &traffics, const std::string& current_task_id);

    // 库区任务处理相关方法
    void separateKuquTasks(const std::vector<cotek::task_t>& tasks);
    void processKuquTasks();
    std::pair<bool, std::vector<CutPoint>> canKuquBindToTopologyEdgeWithCutPoints(const std::string& kuqu_id, const EdgeData& topology_edge_data);
    void cutKuquEdges(const std::string& kuqu_id, const std::vector<CutPoint>& cut_points);
    std::string insertEndNodeForKuqu(const std::string& kuqu_id, const std::string& topology_edge_id,
                                     const CutPoint& last_cut_point);


};



//} // plan
//} // cotek

#endif