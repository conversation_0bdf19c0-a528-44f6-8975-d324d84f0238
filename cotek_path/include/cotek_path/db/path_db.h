#ifndef __COTEK_PATH__PATH_DB__H__
#define __COTEK_PATH__PATH_DB__H__

#include <memory>
#include <atomic>
#include "../common/singleton.h"
#include "../var/var.h"

namespace cotek {
namespace db {

class PathDB : public common::singleton<PathDB> {
 public:
  PathDB();
  ~PathDB();

  // task
  int save_task(const task_t &task);
  int load_task(const std::string &task_id, task_t &task);
  int load_task(const std::string &edge_id, std::vector<task_t> &tasks);
  int load_task(std::vector<task_t> &tasks);
  int load_task(std::vector<std::string> &task_names);
  int load_taskid(std::vector<std::string> &task_ids);
  int load_task_map(const std::string &id, std::string &map_id);
  int load_target_task(const std::string& map_id, const int& action_type, std::vector<std::string> &tasks);
  int load_target_task_by_sn(const int& sn, std::vector<std::string> &tasks);
  int update_task(const task_t &task);
  int del_task(const std::string &task_id);
  int del_point_of_task(const std::string &task_id);
  int del_edge_of_task(const std::string &task_id);
  int del_traffic_of_task(const std::string &task_id);
  int del_collision_of_task(const std::string &task_id);

  // point
  int save_point(const node_t &node);
  int save_point(const std::vector<node_t> &nodes, const std::string &task_id = "");
  int load_target_node(const std::string& task_id, const int& action_type, std::vector<node_t> &nodes);
  int load_point(const std::string &id, node_t &node);
  int load_point(std::vector<node_t> &nodes);
  int load_point_by_task_id(std::vector<node_t> &node, const std::string &task_id, int point_type = -1, int point_action = -1);
  int load_point_by_edge_id(std::vector<node_t> &node, const std::string &edge_id, int start = -1, int end = -1, bool find_index = false);
  int delete_point(const std::string &id);
  int delete_point_by_edge_id(const std::string &edge_id);
  int delete_point_by_task_id(const std::string &task_id);
  int update_point(const node_t &node);

  // edge
  int save_edge(const edge_t &edge, const std::string &task_id = "");
  int save_edge(const std::vector<edge_t> &edges, const std::string &task_id = "");
  int load_edge(const std::string &id, edge_t &edge);
  int load_edge(std::vector<edge_t> &edges, const std::string &task_id = "", int start = -1, int end = -1);
  int delete_edge(const std::string &edge_id, const std::string &task_id = "");
  int update_edge(const edge_t &edge);

  // traffic
  int save_traffic(const traffic_t &traffic);
  int save_traffic(const std::vector<traffic_t> &traffics);
  int load_traffic(const std::string &traffic_id, traffic_t &traffic);
  int load_traffic(const std::string &edge_id, std::vector<traffic_t> &traffics);
  int load_traffic(std::vector<traffic_t> &traffics, const std::string &task_id = "");
  int delete_traffic(const std::string &task_id, const std::string &traffic_id = "");

  // collision
  int save_collision(const traffic_t &collision);
  int save_collision(const std::vector<traffic_t> &collisions);
  int load_collision(const std::string &collision_id, traffic_t &collision);
  int load_collision(std::vector<traffic_t> &collisions, const std::string &task_id = "");
  int delete_collision(const std::string &task_id, const std::string &collision_id = "");

  // task result
  int save_task_result(const task_result_t &data);
  int load_task_result(const std::string &id, task_result_t &data);
  int load_task_result(std::vector<task_result_t> &datas);

  // map
  int save_map(const map_t &data);
  int load_map(const std::string &id, map_t &data);
  int load_map(std::vector<map_t> &datas);

  // relocation
  int save_relocation(const relocation_t &data);
  int load_relocation(const std::string &id, relocation_t &data);
  int load_relocation(std::vector<relocation_t> &datas);
  int delete_relocation(const std::string &task_id);

  // topology <-> node
  int save_topology(const topology_map_t &data);
  int load_topology(const std::string &id, std::vector<std::string> &datas);
  int load_topology(std::map<std::string, std::string> &datas);
  int delete_topology(const std::string &id = "");

  // task info 
  int save_task_info(const task_info_t &data);
  int load_task_info(std::vector<task_info_t> &data);
  int save_task_info(const int &result);

  // kuqu
  int load_kuqu(const std::string &task_id, kuqu_t &kuqu);
  int load_kuqu(std::vector<std::string> &kuqu_names);
  int load_storage_by_kuqu_id(std::vector<storage_t> &storage, const std::string &kuqu_id);
  int save_kuqu(const kuqu_t &data);
  int save_kuwei(const storage_t &data);
  int del_kuqu(const std::string &id, std::string &kuqu_id);
  int del_kuwei(const std::string &kuqu_id);
  int update_task_index(const std::string &task_id, const std::string &start, const std::string &end);
  int update_storage_name(const std::string &name, const std::string &task_id);
  int update_storage_size(const kuqu_t &kuqu);
  int update_kuqu(const task_t &task);
  bool delKuQuVertex(const std::string &id);

  // other
  int get_max_collision_id();
  int get_max_traffic_id();
  int64_t get_max_node_id();
  int64_t get_max_edge_id();
  int64_t get_max_kuqu_id();

  int reset_db();
  int reset_task();

  int backup_db();
  int backup_db(const std::string& src, const std::string& dest);

 private:
  int init();
  int deinit();

 private:
  std::atomic_bool is_ready_{false};
};

}  // namespace db
}  // namespace cotek

#define DB cotek::db::PathDB::instance()

#endif  // __COTEK_PATH__PATH_DB__H__