#ifndef __COTEK_PATH__VAR__H__
#define __COTEK_PATH__VAR__H__

#include <cstdint>
#include <string>
#include <vector>
#include <map>
#include "cotek_common/db/cotek_var.h"

namespace cotek {

// 任务命令
enum task_cmd_t {
  kTaskCmdNone,
  kTaskCmdNew,          // 新建
  kTaskCmdPreContinue,  // 预续建
  kTaskCmdContinue,     // 续建
  kTaskCmdPreOverride,  // 预覆盖
  kTaskCmdOverride,     // 覆盖
};

// 节点位置
enum task_action_t {
  kTaskStateNone,       // 无
  kTaskStateStart,      // 开始
  kTaskStateEndSave,    // 终止保存
  kTaskStateEndCancel,  // 终止取消
  kTaskStateCancelAction, // 取消动作点
  kStorageStateStart,   // 库区新建
  kStorageStateSave     // 库区保存
};

// 节点类型
enum node_type_t {
  kNodeSampling,  // 采样点
  kNodeAction,    // 任务点
  kNodeVirtual,   // 拓扑虚拟点
};

// 节点位置
enum node_pos_t {
  kNodePosNone,    // 中间点
  kNodePosStart,   // 起始点
  kNodePosEnd,     // 终止点
};

// 节点动作
enum node_action_t {
  kNodeActionNone,    //无
  kNodeActionLoad,    //装货
  kNodeActionUnload,  //卸货
  kNodeActionRest,    //休息
  kNodeActionConfirm, //人工确认
  kNodeActionCharge,  //充电
  kNodeActionLieUnload, //列-卸货
  kNodeActionOpenDoor,  //自动门开门
  kNodeActionCloseDoor, //自动门关门
  kNodeActionCallDT,  //呼叫电梯
  kNodeActionTakeDT,  //乘坐电梯
  kNodeActionLeaveDT, //离开电梯
  kNodeActionSwitchMap, //区域切换
};

enum build_state_t {
  kBuildNone    = 0,
  kBuildStart   = 1,
  kBuildEnd     = 2,
  kBuildCancel  = 3,
  kBuildActionCancel  = 4,
};

enum running_state_t {
  kRunningEnd   = 0,
  kRunningStart = 1,
  kRunning      = 2,
};

enum task_code_t {
  kTaskSuccess        = 0,
  kTaskInterval       = 1,  // 10s
  kTaskManual         = 2,  // maunal
  kTaskNoLocation     = 3,  // no location
  kTaskError          = 4,  // error
  kTaskOtherState     = 5,  // other state
  kTaskNotFound       = 10, // no find task
  kTaskLoadGraphError = 11, // load graph failed
  kTaskPathPlanError  = 12, // task path plan faild
  kTaskSendTimeout    = 13, // send task timeout
  kTaskSendOtherError = 14, // send task other faild
  kElevatorNotFound   = 15, // no appropriate elevator node
  kFloorNotFound      = 16, // no map correspond floor
  kMergeTaskError     = 17, // merge task path plan failed
  kAtTaskKuQu         = 18, 
  kKuQuActError       = 19,
};

// 权重边 weighted_edge
struct weighted_edge_t {
    std::string task_id;
    std::string edge_id; // 新边id
    std::string task_edge_id; // 附属于边的id
    std::string start_point_id; 
    std::string end_point_id; 
    double weight;
    size_t start_index; // 起始点index
    size_t end_index; // 终止点index
    int velocity_level;       // 路径速度等级 1/2/3
    int avoid_level;          // 路径避障等级 1/2/3
};

struct task_state_t {
  std::string time;                 // "1718959100466"
  std::string order_id;             // "1718957340441787493"
  std::string task_id;              // "1718957340441787493"
  int order_update_id{0};           // 1
  std::string zone_set_id;          // "888888"
  std::string last_node_id;         // "3000"
  int last_node_sequence_id{0};     // 2
  std::string agv_state;            // "Finishing"
  bool operating_mode{true};        // true-AUTOMATIC false-MANUAL
  bool response_finish{false};      // task finish

  // location
  bool position_initialized{false}; // 是否初始化定位成功
  float localization_score{0.0};    // 定位匹配度
  float x{0.0};                     // 单位: m 
  float y{0.0};                     // 单位: m 
  float theta{0.0};                 // 单位:rad 范围: [-Pi ... Pi]
  std::string zone_id;              // 地图编号
  std::string map_id;               // 分区编号
  bool mapping{false};              // 区分建图模式与定位模式
};

using topology_map_t = std::map<std::string, std::string>;

using vel_t = common::vel_t;
using pose_t = common::pose_t;
using odom_t = common::odom_t;

using relocation_t = common::relocation_t;
using map_t = common::map_t;
using node_t = common::node_t;
using edge_t = common::edge_t;
using traffic_t = common::traffic_t;
using task_t = common::task_t;
using kuqu_t = common::kuqu_t;
using storage_t = common::storage_t;
using vertex_t = common::vertex_t;
using kwsize_t = common::kwsize_t;
using task_result_t = common::task_result_t;
using topology_node_t = common::topology_node_t;
using elevator_task_t = common::elevator_task_t;

using db_node_t = common::db_node_t;
using db_edge_t = common::db_edge_t;
using db_edge_node_t = common::db_edge_node_t;
using db_task_node_t = common::db_task_node_t;
using db_task_edge_t = common::db_task_edge_t;
using db_traffic_t = common::db_traffic_t;
using db_collision_t = common::db_collision_t;
using db_task_traffic_t = common::db_task_traffic_t;
using db_task_t = common::db_task_t;
using db_task_result_t = common::db_task_result_t;
using db_kuqu_t = common::db_kuqu_t;
using db_storage_t = common::db_storage_t;
using task_info_t = common::task_info_t;
using db_task_info_t = common::db_task_info_t;
using language_error_t = common::language_error_t;
using db_language_error_t = common::db_language_error_t;
using language_field_t = common::language_field_t;
using db_language_field_t = common::db_language_field_t;

struct learning_state_t {
  int state{0};                // 当前示教状态
  bool allow_extend{false};    // 是否允许扩展
  std::vector<node_t> points;  // 实时采集的点
  std::vector<node_t> exists;  // 已有的点
  std::string task_id;         // 任务id
  int task_loop{1};                 // 循环的圈数
};

struct task_list_t {
  task_info_t task_info;
  std::vector<task_t> tasks;
};

}  // namespace cotek

#endif  // __COTEK_PATH__VAR__H__
