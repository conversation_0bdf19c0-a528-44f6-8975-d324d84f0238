#ifndef __COTEK_PATH__FIND_NEIBORS__H__
#define __COTEK_PATH__FIND_NEIBORS__H__

#include <iostream>
#include <vector>
#include <cmath>
#include "path_plan/nanoflann.hpp"
#include "storage.h"
#include "var/var.h"

using namespace std;
using namespace nanoflann;

struct NeiborPoint{
    double x{0};
    double y{0};
    std::string id{""};
    int column{0};
};

template <typename T>
struct NodeCloud {
    struct Point {
        T x, y;
        // KD树的维度仍然是2D（只考虑x和y）

        // 额外信息，不作为KD树的维度
        std::string id;
    };

    std::vector<Point> pts;

    // 必须提供以下接口供 nanoflann 使用：
    inline size_t kdtree_get_point_count() const { return pts.size(); }

    inline double kdtree_get_pt(const size_t idx, const size_t dim) const {
        return dim == 0 ? pts[idx].x : pts[idx].y;
    }

    template <class BBOX>
    bool kdtree_get_bbox(BBOX&) const { return false; }  // 不使用包围盒
};

// 定义 KD-Tree 类型（使用二维点）
using nodeKDTree = nanoflann::KDTreeSingleIndexAdaptor<
    nanoflann::L2_Simple_Adaptor<double, NodeCloud<double>>,
    NodeCloud<double>,
    2  // 维度
>;

class KdFind{
  public:

    std::vector<NeiborPoint> findNeibors(const std::vector<cotek::node_t> &points, const cotek::vertex_t &target) {
      std::vector<NeiborPoint> neibors;
      NodeCloud<double> cloud;

      for (const auto &p : points) {
          cloud.pts.push_back({p.x , p.y, p.id});
      }
      // 构建 KD-Tree
      nodeKDTree tree(2, cloud, nanoflann::KDTreeSingleIndexAdaptorParams(10));
      tree.buildIndex();

      // 设置搜索半径（欧氏距离的平方）
      double search_radius = 1.0;  // 实际半径
      double search_radius_sq = search_radius * search_radius;  

      // 执行半径范围搜索
      std::vector<nanoflann::ResultItem<uint32_t , double>> ret_matches;
      nanoflann::SearchParameters params;
      const double query_pt[2] = {target.x, target.y};
      const size_t nMatches = tree.radiusSearch(
          &query_pt[0], search_radius_sq, ret_matches, params);    

      if (nMatches < 1) {
          return neibors;
      }

      int column = std::stoi(target.id);
      neibors.reserve(nMatches);
      for (size_t i = 0; i < nMatches; ++i) {
        neibors.emplace_back(NeiborPoint{cloud.pts[ret_matches[i].first].x,
                                         cloud.pts[ret_matches[i].first].y,
                                         cloud.pts[ret_matches[i].first].id,
                                         column});
      }

      return neibors;
    }    

    NeiborPoint getTarget(std::vector<NeiborPoint> &points, const Point &target, Eigen::Vector2d normal) {
      NeiborPoint record;
      double max_angle = 8888;
      for (const auto &point : points) {
        Point p{point.x, point.y};
        Eigen::Vector2d dir(target.x - p.x, target.y - p.y);

        auto res = isAngleLessThan(normal, dir, 1);
        if (res.first == true) {
          if (max_angle == 8888) {
            record = point;
            max_angle = res.second;
          } else {
            if (res.second < max_angle) {
              record = point;
              max_angle = res.second;
            }
          }
        }
      }
      return record;
    }

  private:

    // 判断两个向量夹角是否小于指定角度（单位：度）
    std::pair<bool, double> isAngleLessThan(const Eigen::Vector2d& v1, const Eigen::Vector2d& v2, double angle_threshold_degrees) {
        // 计算夹角的余弦值
        double dot_product = v1.dot(v2);
        double norm_product = v1.norm() * v2.norm();

        if (norm_product == 0.0) {
            std::cerr << "Error: One of the vectors is zero-length.\n";
            return std::make_pair(false, 8888);;
        }

        double cos_theta = dot_product / norm_product;

        // 限制余弦值在 [-1, 1] 避免 acos 出错
        cos_theta = std::max(-1.0, std::min(1.0, cos_theta));

        // 计算夹角（弧度）
        double angle_rad = std::acos(cos_theta);

        // 将角度阈值转换为弧度
        double angle_threshold_rad = angle_threshold_degrees * M_PI / 180.0;

        return std::make_pair(angle_rad < angle_threshold_rad, angle_rad);
    }
};

#endif  // COTEK_TASK_FIND_NEIBORS_H