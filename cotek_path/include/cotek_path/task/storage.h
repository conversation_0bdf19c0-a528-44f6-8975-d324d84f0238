#ifndef __COTEK_PATH__STORAGE__H__
#define __COTEK_PATH__STORAGE__H__
#include <cstdint>
#include <atomic>
#include <thread>
#include <chrono>
#include <tuple>
#include <algorithm>
#include "common/event.h"
#include "var/var.h"
#include "etc/path_etc.h"
#include "fitting/path_traffic.h"

namespace cotek {
namespace task {

class Storage
{
public:
  Storage() = default;
  ~Storage() = default;

  int CalKuQu(kuqu_t &kuqu);
  void CalDetect(kuqu_t &kuqu);
  std::vector<Point> sampleLineSegment(const Point& p1, const Point& p2, double step, bool include_end = true);
  double CalAngle(const Point& p1, const Point& p2);

private:
  int orientation(Point p, Point q, Point r);
  bool onSegment(Point p, Point q, Point r);
  bool doIntersect(Point p1, Point q1, Point p2, Point q2);
  bool isSimplePolygon(const std::vector<Point>& polygon);
  bool isPointInPolygon(Point& p, const std::vector<Point>& polygon);
  Eigen::Vector2d inwardNormal(const Point& A, const Point& B, const std::vector<Point>& polygon);
  bool isRectangleInsidePolygon(std::vector<Point>& rect, const std::vector<Point>& polygon);

  Point moveAlongNormal(const vertex_t& p, const Eigen::Vector2d& normal, double distance);
};

} // namespace task
} // namespace cotek

#endif 