#ifndef __COTEK_PATH__PATH_SAMPLING__H__
#define __COTEK_PATH__PATH_SAMPLING__H__
#include <cstdint>
#include <atomic>
#include <thread>
#include <chrono>
#include <tuple>
#include <algorithm>
#include "common/event.h"
#include "var/var.h"
#include "etc/path_etc.h"

namespace cotek {
namespace task {

class PathSampling : public common::event
{
public:
  PathSampling();
  ~PathSampling();

  int init();
  int deinit();

  // 速度和里程计
  void set_vel(const vel_t &vel);
  void set_pose(const pose_t &pose);

  // 任务命令 
  // state: 0/1/2/3/4 无/开始/结束保存/结束取消/撤销动作点
  // cmd： 0/1/2/3/4/5 无/新建/预续建/续建/预覆盖/覆盖
  int set_task_cmd(int state, int cmd, const std::string &task_name, 
                   const kuqu_t &kuqu, const std::string &task_id = "");
  // 点位动作 0/1/2/3 无/取货/卸货/休息
  int set_action_cmd(int cmd);

  void set_param(const etc::path_param_t &param) { param_ = param; }

  bool get_node(node_t &node);
  std::string get_kuqu_name();
  
private:
  void reset(bool is_reset = false);

  bool get_edge(const std::vector<node_t> &nodes, std::vector<edge_t> &edges, int start_id = 0);
  bool get_edge(const node_t &node, edge_t &edge, bool is_start = true);
  std::string get_name();


  void load_exist_node(int cmd, int state, const std::string &task_id);

  int get_node_id() { return ++node_id_; }
  int get_edge_id() { return ++edge_id_; }
  int get_kuqu_id() { return ++kuqu_id_; }

private:
  vel_t vel_;
  pose_t pose_;
  std::mutex vel_mtx_;
  std::mutex pose_mtx_;

  std::atomic_bool is_loop_{false};
  std::unique_ptr<std::thread> thr_{nullptr};

  std::atomic_uint32_t task_state_{0}; // task runing
  std::atomic_uint32_t task_cmd_{0}; // 0-none 1-new 2-contiue 3-override
  std::atomic_uint32_t sample_state_{0};

  std::chrono::steady_clock::time_point task_time_;
  std::chrono::steady_clock::time_point action_time_;

  edge_t edge_;
  std::atomic_bool edge_start_{false}; // sampling edge start flag and end flag

  task_t task_;
  std::atomic_bool load_task_ok_{false};
  std::mutex task_mtx_;

  vel_t last_vel_;
  etc::path_param_t param_;
  
  learning_state_t state_;

  int exist_point_size_{0};
  int32_t node_id_{0};
  int32_t edge_id_{0};
  int32_t kuqu_id_{0};
};

} // namespace task
} // namespace cotek

#endif // __COTEK_PATH__PATH_SAMPLING__H__