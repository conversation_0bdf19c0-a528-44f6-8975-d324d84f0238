#ifndef __COTEK_PATH__PATH_TASK__H__
#define __COTEK_PATH__PATH_TASK__H__

#include <memory>
#include <map>
#include <string>
#include <chrono>
#include <future>
#include <mutex>
#include <vector>
#include "var/var.h"
#include "common/event.h"
#include "etc/path_etc.h"

class AmrPathFinder;
class TrafficManager;
class PathFitting;
namespace cotek {
namespace task {

class PathSampling;
class PathTask : public common::event
{
public:
  PathTask();
  ~PathTask();

  int init(std::shared_ptr<PathSampling> sampling);
  int deinit();

  int16_t cal_task(std::string &send_id);
  int start(std::string &task_id, std::vector<std::string> &task_actions, 
            bool is_self = false, bool is_continue = false);
  int start(std::vector<std::string> &task_ids, std::vector<std::string> &task_actions,
            bool is_self = false, bool is_continue = false);
  int stop(std::string &task_id);
  int judge();
  int test_start(std::string &send_id);
  int test_save(const std::string& send_id);
  int test_del(const std::string& id);
  // db 
  int get_task(const std::string &id, task_t &task);
  int get_task(std::vector<task_t> &task);
  int update_task(const task_t &task);
  int del_task(const std::string &id);
  int save_task_result(const task_result_t &task);
  int reset_db();
  int reset_task();
  int load_point_by_edge_id(std::vector<node_t> &node, 
                            const std::string &id, 
                            const int &start_index, 
                            const int &end_index, 
                            const bool &find_index);
  int load_traffic(const std::string &id,
                   std::vector<traffic_t> &traffic);
  int load_kuqu(const int32_t &id, kuqu_t &kuqu);

  // interface
  void set_pose(const pose_t &pose) { pose_ = pose; }
  void set_task_state(const task_state_t &state);
  int set_task_info(const task_info_t &info);
  void set_manual(int mode) { manual_mode_ = mode; }

  // node/edge id
  int get_node_id( ) { return ++node_id_; }
  int get_edge_id( ) { return ++edge_id_; }
  elevator_task_t get_elevator_task() { return elevator_task_; }
  void load_task_info();

  int update_edge(const edge_t &edge);
  int update_point(const node_t &node);
  int updata_storage(const kuqu_t &kuqu, const std::string &task_id);

private:
  void path_handler();
  void running_handler();

private:
  etc::path_param_t param_;
  
  std::shared_ptr<PathSampling> sampling_{nullptr};
  std::map<std::string, task_t> path_task_;
  std::map<std::string, task_t> order_task_;
  std::mutex task_mtx_;
  
  std::shared_ptr<PathFitting> path_fitting_{nullptr};
  std::shared_ptr<TrafficManager> traffic_mgr_{nullptr};
  std::shared_ptr<AmrPathFinder> path_plan_{nullptr};

  std::chrono::steady_clock::time_point task_time_;
  std::string last_node_id_;
  std::string last_task_id_;
  std::vector<std::string> last_task_ids_;
  std::vector<std::string> last_task_actions_;
  node_t last_end_point_;
  task_state_t task_state_;
  std::future<int> order_future_;
  task_result_t task_result_;
  task_t last_task_;
  elevator_task_t elevator_task_;

  int manual_mode_{0};

  // loop task
  std::future<void> send_future_;
  int32_t task_loop_{1};
  int32_t last_task_loop_{1};
  std::mutex update_mtx_;
  std::condition_variable update_cv_;

  std::future<void> topo_future_;
  std::future<int> fitting_future_;
  std::atomic_uint32_t build_state_{0};
  std::atomic_bool is_load_graph_ok_{false};

  // real-time path
  std::future<void> realtask_future_;
  std::atomic_bool is_loop_{false};
  int task_running_{0};

  // running task
  learning_state_t state_;
  std::future<void> running_future_;
  int window_popup_{0};

  // current task and pos
  pose_t pose_;
  int32_t node_id_{0};
  int32_t edge_id_{0};
  int32_t kuqu_id_{0};

  std::chrono::steady_clock::time_point total_time_;
  float last_odom_{0.0};
  task_info_t task_info_;

  std::vector<edge_t> fit_edges_;
  int exec_num_{0};
  bool is_merge_{false};

};

} // namespace task
} // namespace cotek

#endif // __COTEK_PATH__PATH_TASK__H__