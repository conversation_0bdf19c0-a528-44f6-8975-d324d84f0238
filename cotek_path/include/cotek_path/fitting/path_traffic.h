#ifndef TRAFFIC_MANAGER_H_
#define TRAFFIC_MANAGER_H_

#include <map>
#include <iostream>
#include <vector>
#include "eigen3/Eigen/Dense"
#include "etc/path_etc.h"

typedef Eigen::Vector4d Vector4d;
typedef Eigen::Vector2d Vector2d;

struct Point {
  double x, y;

  Point operator+(const Point& other) const {
      return {x + other.x, y + other.y};
  }

  Point operator-(const Point& other) const {
      return {x - other.x, y - other.y};
  }

  Point operator*(double scalar) const {
      return {x * scalar, y * scalar};
  }
};

struct TrafficSector {
  int num;
  std::string name;
  int start;
  int end;
};

struct TrafficArea {
  int num; // 区域编号
  std::string name; // 线路名
  Vector4d start; // 当前区域交管起点
  Vector4d end; // 当前区域交管终点
  int start_index;  // 起点索引
  int end_index;  // 终点索引
};

struct CompareArea {
  int num;
  std::string f_name; // 区域编号
  std::string s_name; // 线路名
  int start_index;  // 起点索引
  int end_index;    // 终点索引
};

struct CheckSector {
  int line_start;
  int line_end;
};

struct AllArea {
  bool vaild;
  std::map<std::string, std::vector<TrafficArea>> rough_area;   // 路线碰撞检测区域
  std::map<std::string, std::vector<TrafficArea>> traffic_area; // 最终交管区域
};

class  TrafficManager
{

public:

	TrafficManager() : num_(0), number_(0), is_load_param_(false) {}
	~TrafficManager() {}

  AllArea CalTraffic(
      const std::vector<Vector4d>& line1_points,
      const std::vector<Vector4d>& line2_points,
      const std::string& line1_name,
      const std::string& line2_name,
      const int& rough_base_num, 
      const int& tr_base_num);

private:
  inline const std::map<std::string, std::map<int, Vector4d>> GetLine() {
    return line_vec_;
  }

  std::map<int, Vector4d> getSamples(const std::vector<Vector4d>& points, const std::string& name);

  std::map<std::string, std::vector<TrafficSector>> Compare(
      const std::map<int, Vector4d>& line1, const std::map<int, Vector4d>& line2,
      const std::string& l1_name, const std::string& l2_name);

  AllArea Check(
      const std::map<std::string, std::vector<TrafficSector>>& region, 
      std::map<int, Vector4d> line1_sp, std::map<int, Vector4d> line2_sp,
      std::vector<Vector4d> line1, std::vector<Vector4d> line2,
      std::string l1_name, std::string l2_name,
      const int& rough_base_num, const int& tr_base_num);

  void CompareAreas(std::map<std::string, std::vector<TrafficArea>>& A,
                    const std::map<std::string, std::vector<TrafficArea>>& B);

  std::map<std::string, std::map<int, Vector4d>> line_vec_;

  int num_;
  int number_;
  bool is_load_param_;

  cotek::etc::path_param_t param_;
};

#endif