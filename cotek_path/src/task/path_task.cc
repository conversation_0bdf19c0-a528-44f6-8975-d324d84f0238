#include "task/path_task.h"
#include "task/storage.h"
#include "task/find_neibors.h"
#include <functional>
#include <limits>
#include "common/any.h"
#include "common/time.h"
#include "common/math.h"
#include "common/chcwd.h"
#include "cotek_common/log_porting.h"
#include "task/path_sampling.h"
#include "task/path_transform.h"
#include "db/path_db.h"
#include "fitting/path_traffic.h"
#include "fitting/path_fitting/path_fitting.h"
#include "path_plan/amr_pathfinder.h"
#include "etc/path_etc.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/util/local_service.h"
#include <unistd.h>

using Json = nlohmann::ordered_json;
using n_excetion = nlohmann::json_abi_v3_11_2::detail::exception;

namespace cotek {
namespace task {

#define MAX_LOOP(loop) ((loop) >= 0 ? (loop) : std::numeric_limits<int>::max())
#define SAVE_TASK 0

enum TaskResult { kTaskResultOk, kTaskResultCancel, kTaskResultERROR, kTaskResultFatal};

using traffic_area_t = std::vector<TrafficArea>;
using traffic_map_t = std::map<std::string, traffic_area_t>;

static std::string GetUserName() {
  uid_t userid;
  struct passwd* pwd;
  userid = getuid();
  pwd = getpwuid(userid);
  return pwd->pw_name;
}

static void find_floor(const std::string& cur_map, const std::string& tar_map,
                       std::string& cur_floor, std::string& tar_floor) {
  cur_floor = "8888";
  tar_floor = "8888";
  std::string path = "/home/" + GetUserName() + "/config/map/" + "floor_list.json";
  std::string config_string = util::LocalService::GetStringFromFile(path);

  nlohmann::ordered_json j_config;
  try {
    j_config = nlohmann::ordered_json::parse(config_string);

    for (const auto& item : j_config["list"]) {
      if (item["zone_id"] == "888888" && item["map_id"] == cur_map) {
        cur_floor = item["floor"].get<std::string>();
      }
      if (item["zone_id"] == "888888" && item["map_id"] == tar_map) {
        tar_floor = item["floor"].get<std::string>();
      }
    }

  } catch (const n_excetion &ex) {
    LOG_ERROR("navigation config json file data formats is error.");
    std::cout << (ex.what()) << std::endl;
  }
}

void transform(const TrafficArea & area, traffic_t &data)
{
  data.id           = area.num;
  data.start_index  = area.start_index;
  data.end_index    = area.end_index;
  data.poly.emplace_back(pose_t(area.start.x(), area.start.y(), area.start.z()));
  data.poly.emplace_back(pose_t(area.end.x(), area.end.y(), area.end.z()));
}

PathTask::PathTask()
{

}

PathTask::~PathTask()
{
  deinit();
}

int PathTask::init(std::shared_ptr<PathSampling> sampling)
{
  // 1 init object
  sampling_ = sampling;
  path_fitting_ = std::make_shared<PathFitting>();
  traffic_mgr_ = std::make_shared<TrafficManager>();

  // 2 running task track
  realtask_future_ = std::async(std::launch::async,
    std::bind(&PathTask::path_handler, this));
  running_future_ = std::async(std::launch::async,
    std::bind(&PathTask::running_handler, this));

  // 3 loop task
  send_future_ = std::async(std::launch::async, [&]() {
    is_loop_ = true;
    while (is_loop_) {
      std::unique_lock<std::mutex> lck(update_mtx_);
      update_cv_.wait(lck);
      if (!is_loop_) {
        break;
      }
      std::this_thread::sleep_for(std::chrono::seconds(5));
      if (--task_loop_ > 0) {
        int ret;
        if (!is_merge_) ret = start(last_task_id_, last_task_actions_, true);
        else ret = start(last_task_ids_, last_task_actions_, true);
        if (ret != 0) {
          LOG_ERROR("send task(%s:%u) error(%d)", last_task_id_.c_str(), task_loop_, ret);
        }
      }
    }
  });

  // 4 param
  if (PATH_ETC->is_ready()) {
    PATH_ETC->get_param(param_);
    if (sampling_) {
      sampling_->set_param(param_);
    }
    LOG_INFO("load all config ok");
  }
 
  // 5 load task and topology
  if (DB) {
    // 5.1 load task
    std::vector<task_t> tasks;
    // if (0 != DB->load_task(tasks)) {
    //   LOG_ERROR("load all task error");
    // }
    while (0 != DB->load_task(tasks)) {
      LOG_ERROR("load all task error");
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    LOG_INFO("load all task ok");
    for(auto &task : tasks) {
      order_task_[task.id] = task;
      order_task_[task.id].build_state = kBuildEnd;
    }
    node_id_ = DB->get_max_node_id();
    edge_id_ = DB->get_max_edge_id();
    kuqu_id_ = DB->get_max_kuqu_id();
    LOG_INFO("load max_node_id(%d) max_edge_id(%d) get_max_kuqu_id(%d) ok", node_id_, edge_id_, kuqu_id_);

    // 5.2 load topology
    topo_future_ = std::async(std::launch::async, [&, tasks](){
      try { 
        std::map<std::string, std::string> topology;
        // if (0 != DB->load_topology(topology)) {
        //   LOG_ERROR("load all topology map error");
        // }
        while (0 != DB->load_topology(topology)) {
          LOG_ERROR("load all topology map error");
          std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        LOG_INFO("load all topology map ok");

        LOG_INFO("load similar_angle_range(%.2f) max_steering_angle(%.2f) ok", 
          param_.path_plan.similar_angle_range, param_.path_plan.max_steering_angle);
        path_plan_ = std::make_shared<AmrPathFinder>(
          param_.path_plan.similar_angle_range, param_.path_plan.max_steering_angle);
        if (path_plan_) {
          is_load_graph_ok_ = path_plan_->loadTopologicalGraph(tasks, topology);
          LOG_INFO("load topolog graph ok(%d)", (int)is_load_graph_ok_);
        }
      } catch(...) {
        LOG_ERROR("load graph exception");
        is_load_graph_ok_ = false;
      }
    });

    // 5.3 load total data
    load_task_info();
  }

  // 6 sampling nodes and build task
  if (sampling_) {
    sampling_->on("edge_data", [&](const common::any &data)->int {
      //边数据
      auto task = common::any_cast<task_t>(data);
      fitting_future_ = std::async(std::launch::async, [&, task]() {
        //0 任务预置工作
        path_task_[task.id] = task;
        std::vector<edge_t> edges;
        if (order_task_.find(task.id) == order_task_.end()) {
          std::lock_guard<std::mutex> lck(task_mtx_);
          if (task.build_cmd == kTaskCmdContinue) {
            if (order_task_.find(task.orginal_id) != order_task_.end()) {
              order_task_[task.id] = order_task_[task.orginal_id];
              order_task_[task.id].id = task.id;
              order_task_[task.id].name = task.name;
              std::string last_edge_id;
              if (!order_task_[task.id].edges.empty()) {
                last_edge_id = order_task_[task.id].edges.back().id;
              }
              for (int i = 0; i < task.edges.size(); i++) {
                if (task.edges[i].id == last_edge_id) {
                  edges = std::vector<edge_t>(task.edges.begin() + i +1, task.edges.end());
                  break;
                }
              }
            }
          } else {
            edges = task.edges;
            order_task_[task.id] = task;
            order_task_[task.id].nodes.clear();
            order_task_[task.id].edges.clear();
            order_task_[task.id].actions.clear();
            if(!task.nodes.empty()) {
              order_task_[task.id].start_point = task.nodes.front();
            }
            order_task_[task.id].zone_id = task_state_.zone_id;
            order_task_[task.id].map_id = task_state_.map_id;
          }
        }
        order_task_[task.id].build_state = task.build_state;
        build_state_ = task.build_state;

        if (build_state_ == kBuildCancel) {
          std::lock_guard<std::mutex> lck(task_mtx_);
          order_task_.erase(order_task_.find(task.id));
          return 0;
        }

        //1、拟合路径
        for (auto &edge : edges) {
          //auto edge = task.edges.empty()? edge_t() : task.edges.back();
          std::vector<common::path_t> split_edges;
          split_edge(split_edges, edge.points, param_.sampling_reverse_min_distance);
          for (int k = 0; k < split_edges.size(); k++) {
            auto &raw_edge = split_edges[k];
            node_t start_pt, end_pt;
            if (!raw_edge.empty()) {
              start_pt = raw_edge.front();
              end_pt = raw_edge.back();
            }
            double sum = 0.0;
            double avg = 0.0;
            std::vector<Vector4d> raw_pts, new_pts;
            for(auto &node : raw_edge) {
              raw_pts.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
              sum += node.v;
            }
            avg = sum/raw_edge.size();
            try {
              if (path_fitting_ && !raw_pts.empty()) {
                new_pts = path_fitting_->CalPath(raw_pts, task.id);
              }
            } catch(std::exception &e) {
              LOG_ERROR("call fitting path exception: %s", e.what());
              return 2;
            } catch (...) {
              LOG_ERROR("call fitting path special exception");
              return 3;
            }
            int direction = 1;
            if (std::fabs(avg) < 0.001) {
              direction = 0;
            } else {
              direction = avg > 0 ? 1 : -1;
            }
            std::vector<node_t> new_nodes;
            for (int i = 0; i < new_pts.size(); i++) {
              auto &pt = new_pts[i];
              node_t node;
              auto time = common::get_now_time();
              node.id = std::to_string(get_node_id());
              node.name = node.id;
              node.timestamp = time;
              node.x = pt.x();
              node.y = pt.y();
              node.angle = pt.z();
              node.v = direction;
              if (i == 0 && k == 0) {
                node.node = edge.points.front().node;
              }
              if (i == new_pts.size()-1) {
                if (end_pt.type != kNodeAction) {
                  node.type = kNodeAction;
                  node.action = kNodeActionRest;
                } else {
                  node.type = end_pt.type;
                  node.action = end_pt.action;
                }
                if(build_state_ == kBuildEnd && k == split_edges.size()-1) {
                  node.node = kNodePosEnd;
                }
              }
              new_nodes.emplace_back(node);
            }
            if (new_nodes.size() >= 1) {
              edge_t new_edge;
              new_edge.start_point = new_nodes.front();
              new_edge.end_point = new_nodes.back();
              new_edge.id = std::to_string(get_edge_id());
              new_edge.name = new_edge.id;
              new_edge.points = new_nodes;
              new_edge.direction = direction;
              new_edge.length = common::distance(new_nodes);
              new_edge.avoid_level = order_task_[task.id].avoid_level;
              new_edge.velocity_level = order_task_[task.id].velocity_level;
              std::lock_guard<std::mutex> lck(task_mtx_);
              if (param_.path_fitting.second_fit) {
                fit_edges_.push_back(new_edge);
              } else {
                order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), new_nodes.begin(), new_nodes.end());
                order_task_[task.id].edges.emplace_back(new_edge);
              }

              if (new_edge.end_point.type == kNodeAction) {
                order_task_[task.id].actions.emplace_back(new_edge.end_point);
              }
              if (build_state_ == kBuildEnd && k == split_edges.size()-1) {
                order_task_[task.id].start_point = order_task_[task.id].nodes.front();
                order_task_[task.id].end_point = new_nodes.back();
              }
            }
          }
        }

        if (build_state_ != kBuildEnd) {
          return 0;
        }

        if (param_.path_fitting.second_fit) {
          std::cout << "second fit." << std::endl;
          //路径重新整合
          // std::vector<int> section_id;
          std::vector<std::pair<int, int>> sections;
          std::map<int, std::vector<node_t>> edge_nodes;
          int start_id = -1;
          bool refresh = false;
          //  1、寻找 起点/动作点 -> 下一动作点 的路径段集合
          for (const auto &edge : fit_edges_) {
            int id = std::stoi(edge.id);
            edge_nodes[id] = edge.points;
            if (start_id == -1 || refresh == true) start_id = id;
            if (edge.points.back().action == kNodeActionLoad ||
                edge.points.back().action == kNodeActionUnload) {
              refresh = true;
            }

            if (edge.points.back().action == kNodeActionLoad) {
              // section_id.push_back(id);
              refresh = true;
              sections.push_back(std::make_pair(start_id, id));
            } else {
              refresh = false;
            }
          }

          std::pair<int, int> last_search = std::make_pair(-1, -1);
          // 2、找到最终动作点为取货的路径段集合
          for (const auto &edge : fit_edges_) {
            if (sections.empty()) {
              std::vector<node_t> nodes = edge.points;
              order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), nodes.begin(), nodes.end());
              order_task_[task.id].edges.emplace_back(edge);

            } else {
              int id = std::stoi(edge.id);
              if (id >= last_search.first && id <= last_search.second) {
                continue;
              }

              bool rewrite = false;
              for (const auto &section : sections) {
                int start_id = section.first;
                int end_id = section.second;

                if (id >= start_id && id <= end_id) {
                  rewrite = true;
                  last_search = std::make_pair(start_id, end_id);
                  std::vector<edge_t> section_edges;
                  for (const auto &edge : fit_edges_) {
                    int edge_id = std::stoi(edge.id);
                    if (edge_id >= start_id && edge_id <= end_id) {
                      section_edges.push_back(edge);
                    }
                  }
                  // 调用接口
                  auto zh_path = path_fitting_->ZhengHePath(section_edges);
                  if (zh_path.empty()) {
                    rewrite = false;
                  } else {
                    std::vector<std::vector<node_t>> new_nodes;
                    for (int i = 0; i < zh_path.size(); i++) {
                      vector<Vector4d> path_nodes = zh_path[i];
                      std::vector<node_t> nodes;
                      for (int j = 0; j < path_nodes.size(); j++) {
                        node_t node;
                        auto time = common::get_now_time();
                        node.id = std::to_string(get_node_id());
                        node.name = node.id;
                        node.timestamp = time;
                        node.x = path_nodes[j].x();
                        node.y = path_nodes[j].y();
                        node.angle = path_nodes[j].z();
                        node.v = -1;
                        if (i == 0 && j == 0) {
                            node.node = section_edges.front().points.front().node;
                        }
                        if (i == nodes.size()-1 && j == path_nodes.size()-1) {
                            node.node = section_edges.back().points.back().node;
                        }
                        node.type = kNodeSampling;
                        node.action = kTaskStateNone;
                        nodes.emplace_back(node);
                      }
                      new_nodes.emplace_back(nodes);
                    }

                    std::vector<edge_t> new_edges;
                    for (int i = 0; i < new_nodes.size(); i++) {
                      edge_t et;
                      et.start_point = new_nodes[i].front();
                      et.end_point = new_nodes[i].back();
                      et.id = std::to_string(get_edge_id());
                      et.name = et.id;
                      et.points = new_nodes[i];
                      et.direction = -1;
                      et.avoid_level = section_edges.front().avoid_level;
                      et.velocity_level = section_edges.front().velocity_level;
                      new_edges.push_back(et);
                    }

                    for (const auto& edge : new_edges) {
                      order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), edge.points.begin(), edge.points.end());
                      order_task_[task.id].edges.emplace_back(edge);
                    }
                    // std::vector<node_t> nodes = new_edges.points;
                    // order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), nodes.begin(), nodes.end());
                    // order_task_[task.id].edges.emplace_back(new_edges);
                  }
                }
              }

              if (!rewrite) {
                std::vector<node_t> nodes = edge.points;
                order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), nodes.begin(), nodes.end());
                order_task_[task.id].edges.emplace_back(edge);
              }
            }

          }
        }
        fit_edges_.clear();

        //2、构建交管区域
        // if (build_state_ != kBuildEnd) {
        //   return 0;
        // }
        std::string id1 = task.id;
        if (order_task_.find(id1) == order_task_.end()) {
          return 0;
        }
        std::vector<Vector4d> line1;
        task_t &new_task = order_task_[id1];
        for (auto &node : new_task.nodes) {
          line1.push_back(Vector4d(node.x, node.y, node.angle, node.v));
        }
        for (auto &order : order_task_) {
          if (task.build_cmd == kTaskCmdOverride && order.first == task.orginal_id) {
            continue;
          }
          if ((new_task.map_id != order.second.map_id) && (new_task.zone_id != order.second.zone_id)) {
            continue;
          }
          if (traffic_mgr_ && new_task.id != order.first) {
            std::vector<Vector4d> line2;
            std::string id2 = order.first;
            auto max_traffic_id = DB->get_max_traffic_id();
            auto max_collison_id = DB->get_max_collision_id();

            if (order.second.kuqu_id == -1) {
              for(auto &node : order.second.nodes) {
                line2.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
              }
              auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id, max_traffic_id);
              for(auto &area : areas.traffic_area) {
                if (area.first == id1) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id1;
                    new_task.traffic.emplace_back(traffic);
                  }
                }
                if (area.first == id2) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id2;
                    order.second.traffic.emplace_back(traffic);
                  }
                }
              }

              for(auto &area : areas.rough_area) {
                if (area.first == id1) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id1;
                    new_task.collision.emplace_back(traffic);
                  }
                }
                if (area.first == id2) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id2;
                    order.second.collision.emplace_back(traffic);
                  }
                }
              }
            } else {
              for (auto &edge : order.second.edges) {
                for (const auto &node : edge.points) {
                  line2.push_back(Vector4d(node.x, node.y, node.angle, node.v));
                }
                auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id++, max_traffic_id++);
                for(auto &area : areas.traffic_area) {
                  if (area.first == id1) {
                    for(auto &item : area.second) {
                      traffic_t traffic;
                      transform(item, traffic);
                      traffic.task_id = id1;
                      new_task.traffic.emplace_back(traffic);
                    }
                  }
                  if (area.first == id2) {
                    for(auto &item : area.second) {
                      traffic_t traffic;
                      transform(item, traffic);
                      traffic.task_id = id2;
                      order.second.traffic.emplace_back(traffic);
                    }
                  }
                }

                for(auto &area : areas.rough_area) {
                  if (area.first == id1) {
                    for(auto &item : area.second) {
                      traffic_t traffic;
                      transform(item, traffic);
                      traffic.task_id = id1;
                      new_task.collision.emplace_back(traffic);
                    }
                  }
                  if (area.first == id2) {
                    for(auto &item : area.second) {
                      traffic_t traffic;
                      transform(item, traffic);
                      traffic.task_id = id2;
                      order.second.collision.emplace_back(traffic);
                    }
                  }
                }
              }
            }
          }
        }
        //3、构建拓扑地图
        std::async(std::launch::async, [&](){
          if (path_plan_) {
            std::vector<task_t> tasks;
            for(auto &item : order_task_) {
              if (task.build_cmd == kTaskCmdOverride && item.first == task.orginal_id) {
                continue;
              }
              tasks.emplace_back(item.second);
            }
            is_load_graph_ok_ = path_plan_->buildTopologicalGraph(tasks);
            //存储关联点
            topology_map_t pt_map = path_plan_->getPoint2TopologyMap();
            if (0 != DB->save_topology(pt_map)) {
              LOG_ERROR("save topology point error");
            }
          }
        });

        //4、存储数据库
        if (task.build_cmd == kTaskCmdOverride) {
          int ret = DB->del_task(task.orginal_id);
          LOG_INFO("delete task(%s) of db return %d", task.orginal_id.c_str(), ret);
          auto cit = order_task_.find(task.orginal_id);
          if (cit != order_task_.end()) {
            order_task_.erase(cit);
          }
        }
        if (0 != DB->save_task(new_task)) {
          LOG_ERROR("save task(%s) error", new_task.id.c_str());
        } else {
          for (auto &order : order_task_) {
            if (0 != DB->save_traffic(order.second.traffic)) {
              LOG_ERROR("save task(%s) traffic error", order.first.c_str());
            }
            if (0 != DB->save_collision(order.second.collision)) {
              LOG_ERROR("save task(%s) collision error", order.first.c_str());
            }
          }
        }
        LOG_INFO("save task(%s) success", new_task.id.c_str());
        return 0;
      });
      return 0;
    });

    sampling_->on("storage_data", [&](const common::any &data)->int {
      auto task = common::any_cast<task_t>(data);
      fitting_future_ = std::async(std::launch::async, [&, task]() {
        node_id_ = DB->get_max_node_id();
        edge_id_ = DB->get_max_edge_id();
        kuqu_id_ = DB->get_max_kuqu_id();
        LOG_INFO("load max_node_id(%d) max_edge_id(%d) get_max_kuqu_id(%d) ok", node_id_, edge_id_, kuqu_id_);

        if (order_task_.find(task.id) == order_task_.end()) {
          std::lock_guard<std::mutex> lck(task_mtx_);
          order_task_[task.id] = task;
          order_task_[task.id].nodes.clear();
          order_task_[task.id].edges.clear();
          order_task_[task.id].actions.clear();
          order_task_[task.id].zone_id = task_state_.zone_id;
          order_task_[task.id].map_id = task_state_.map_id;
          order_task_[task.id].kuqu_id = ++kuqu_id_;
        }       

        order_task_[task.id].build_state = task.build_state;
        build_state_ = task.build_state;

        if (build_state_ == kBuildCancel) {
          std::lock_guard<std::mutex> lck(task_mtx_);
          order_task_.erase(order_task_.find(task.id));
          return 0;
        } 

        kuqu_t kuqu = task.kuqu;
        kuqu.id = std::to_string(order_task_[task.id].kuqu_id);
        Storage storage;
        // 1、计算库位点
        int res = storage.CalKuQu(kuqu);
        if (res != 0) {
          return res;
        }
        // 2、计算探测点
        storage.CalDetect(kuqu);

        // 4 构建库区路线段(探测点->库区各列)
        std::vector<node_t> nodes;
        std::vector<edge_t> edges;
        for (const auto &enter : kuqu.detects) {
          int column = std::stoi(enter.id);
          Point edge_start{enter.x, enter.y};
          Point edge_end;
          int max_row = 0;
          for (const auto &storage : kuqu.storage) {
            if (storage.column == column) {
              if (storage.row > max_row) {
                edge_end = Point{storage.center.x, storage.center.y};
                max_row = storage.row;
              }
            }
          }
          // 进入路段
          double length = sqrt(pow(edge_start.x - edge_end.x, 2) + pow(edge_start.y - edge_end.y, 2));
          double angle = storage.CalAngle(edge_start, edge_end);
          std::vector<Point> edge_points = storage.sampleLineSegment(edge_start, edge_end, 0.05);
          std::vector<node_t> enter_nodes;
          std::vector<node_t> leave_nodes;
          for (int i = 0; i < edge_points.size(); i++) {
            node_t node;
            auto time = common::get_now_time();
            node.id = std::to_string(++node_id_);
            node.name = node.id;
            node.timestamp = time;
            node.x = edge_points[i].x;
            node.y = edge_points[i].y;
            node.angle = angle;
            node.v = -1;
            if (i == 0) {
              node.node = 1;
              node.type = kNodeSampling;
              node.action = kNodeActionNone;
            }
            if (i == edge_points.size()-1) {
              node.type = kNodeAction;
              node.action = kNodeActionRest;
              node.node = kNodePosEnd;
            }
            nodes.push_back(node);
            enter_nodes.push_back(node);
          }

          edge_t edge;
          edge.id = std::to_string(++edge_id_);
          edge.name = edge.id;
          edge.avoid_level = 1;
          edge.velocity_level = 1;
          edge.start_point = enter_nodes.front();
          edge.end_point = enter_nodes.back();
          edge.points = enter_nodes;
          edge.direction = -1;
          edge.length = length;
          edges.push_back(edge);

          // order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), nodes.begin(), nodes.end());
          // order_task_[task.id].edges.emplace_back(edge);

          // 离开路段
          // nodes.clear();
          for (int i = edge_points.size()-1; i >= 0; i--) {
            node_t node;
            auto time = common::get_now_time();
            node.id = std::to_string(++node_id_);
            node.name = node.id;
            node.timestamp = time;
            node.x = edge_points[i].x;
            node.y = edge_points[i].y;
            node.angle = angle;
            node.v = 1;
            if (i == 0) {
              node.type = kNodeAction;
              node.action = kNodeActionRest;
              node.node = kNodePosEnd;
            }
            if (i == edge_points.size()-1) {
              node.node = 1;
              node.type = kNodeSampling;
              node.action = kNodeActionNone;
            }
            nodes.push_back(node);
            leave_nodes.push_back(node);
          }

          edge.id = std::to_string(++edge_id_);
          edge.name = edge.id;
          edge.avoid_level = 1;
          edge.velocity_level = 1;
          edge.start_point = leave_nodes.front();
          edge.end_point = leave_nodes.back();
          edge.points = leave_nodes;
          edge.direction = 1;
          edge.length = length;
          edges.push_back(edge);

          // order_task_[task.id].nodes.insert(order_task_[task.id].nodes.end(), nodes.begin(), nodes.end());
          // order_task_[task.id].edges.emplace_back(edge);
        }

        order_task_[task.id].nodes = nodes;
        order_task_[task.id].edges = edges;
        order_task_[task.id].kuqu = kuqu;

        // 5 交管计算
        auto max_traffic_id = DB->get_max_traffic_id();
        auto max_collison_id = DB->get_max_collision_id();

        std::string id1 = task.id;
        if (order_task_.find(id1) == order_task_.end()) {
          return 0;
        }
        task_t &new_task = order_task_[task.id];

        int base = 0;
        for (const auto &edge : new_task.edges) {
          std::vector<Vector4d> line1;
          for (const auto &node : edge.points) {
            line1.push_back(Vector4d(node.x, node.y, node.angle, node.v));
          }
          for (auto &order : order_task_) {
            if ((new_task.map_id != order.second.map_id) && (new_task.zone_id != order.second.zone_id)) {
              continue;
            }
            if (traffic_mgr_ && new_task.id != order.first) {
              std::vector<Vector4d> line2;
              std::string id2 = order.first;
              for(auto &node : order.second.nodes) {
                line2.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
              }
              std::cout << "cal traffic: " << id1 << ", " << id2 << std::endl;

              auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id++, max_traffic_id++);
              for(auto &area : areas.traffic_area) {
                if (area.first == id1) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id1;
                    traffic.start_index = base;
                    traffic.end_index += base;
                    new_task.traffic.emplace_back(traffic);
                  }
                }
                if (area.first == id2) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id2;
                    order.second.traffic.emplace_back(traffic);
                  }
                }
              }

              for(auto &area : areas.rough_area) {
                if (area.first == id1) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id1;
                    traffic.start_index = base;
                    traffic.end_index += base;
                    new_task.collision.emplace_back(traffic);
                  }
                }
                if (area.first == id2) {
                  for(auto &item : area.second) {
                    traffic_t traffic;
                    transform(item, traffic);
                    traffic.task_id = id2;
                    order.second.collision.emplace_back(traffic);
                  }
                }
              }
            }
          }
          base += line1.size();
        }

        // 6 构建拓扑地图
        std::async(std::launch::async, [&](){
          if (path_plan_) {
            std::vector<task_t> tasks;
            for(auto &item : order_task_) {
              if (task.build_cmd == kTaskCmdOverride && item.first == task.orginal_id) {
                continue;
              }
              tasks.emplace_back(item.second);
            }
            is_load_graph_ok_ = path_plan_->buildTopologicalGraph(tasks);
            //存储关联点
            topology_map_t pt_map = path_plan_->getPoint2TopologyMap();
            if (0 != DB->save_topology(pt_map)) {
              LOG_ERROR("save topology point error");
            }
          }
        });

        // 7 存储数据库
        if (0 != DB->save_task(new_task)) {
          LOG_ERROR("save task(%s) error", new_task.id.c_str());
        } else {
          for (auto &order : order_task_) {
            if (0 != DB->save_traffic(order.second.traffic)) {
              LOG_ERROR("save task(%s) traffic error", order.first.c_str());
            }
            if (0 != DB->save_collision(order.second.collision)) {
              LOG_ERROR("save task(%s) collision error", order.first.c_str());
            }
          }
        }
        LOG_INFO("save task(%s) success", new_task.id.c_str());

      });
      return 0;
    });
  }
  return 0;
}

int PathTask::test_save(const std::string& send_id) {
  node_id_ = DB->get_max_node_id();
  edge_id_ = DB->get_max_edge_id();
  kuqu_id_ = DB->get_max_kuqu_id();
  LOG_INFO("load max_node_id(%d) max_edge_id(%d) get_max_kuqu_id(%d) ok", node_id_, edge_id_, kuqu_id_);
  LOG_INFO("task(%s) ready save ...", send_id.c_str());
  task_t task;
  if (0) {
    task.id = send_id;
    task.name = "test1";
    task.zone_id = "888888";
    task.map_id = "888888";

    Point start{-10, 0};
    Point end{10, 0};
    Storage storage;
    auto points = storage.sampleLineSegment(start, end, 0.05);
    std::vector<node_t> nodes;
    for (int i = 0; i<points.size(); i++) {
      node_t node;
      node.id = std::to_string(++node_id_);
      node.x = points[i].x;
      node.y = points[i].y;
      node.angle = 0;
      node.theta = 0;
      node.v = 1;
      node.name = node.id;
      nodes.push_back(node);      
    }

    std::vector<edge_t> edges;
    for (int i = 0; i < 1; i++) {
      edge_t edge;
      edge.id = std::to_string(++edge_id_);
      edge.name = edge.id;
      edge.start_point = nodes.front();
      edge.end_point = nodes.back();
      edge.points = nodes;
      edges.push_back(edge);
    }

    task.nodes = nodes;
    task.edges = edges;
    task.start_point = nodes.front();
    task.end_point = nodes.back();

    order_task_[task.id] = task;
    order_task_[task.id].nodes = task.nodes;
    order_task_[task.id].edges = task.edges;;
    order_task_[task.id].zone_id = "888888";
    order_task_[task.id].map_id = "888888";
    order_task_[task.id].kuqu_id = -1;
    
    if (0 != DB->save_task(task)) {
      LOG_ERROR("save task(%s) error", task.id.c_str());
    } else {
      for (auto &order : order_task_) {
        if (0 != DB->save_traffic(order.second.traffic)) {
          LOG_ERROR("save task(%s) traffic error", order.first.c_str());
        }
        if (0 != DB->save_collision(order.second.collision)) {
          LOG_ERROR("save task(%s) collision error", order.first.c_str());
        }
      }
    }
    LOG_INFO("task(%s) save finished ...", task.id.c_str());

    nodes.clear();
    edges.clear();
    task.nodes.clear();
    task.edges.clear();
    task.start_point = node_t();
    task.end_point = node_t();

    task.id = std::to_string(std::stoi(send_id)+1);
    task.name = "test2";
    task.zone_id = "888888";
    task.map_id = "888888";

    start = Point{0, -10};
    end = Point{0, 10};
    points = storage.sampleLineSegment(start, end, 0.05);
    for (int i = 0; i<points.size(); i++) {
      node_t node;
      node.id = std::to_string(++node_id_);
      node.x = points[i].x;
      node.y = points[i].y;
      node.angle = 1.57;
      node.theta = 1.57;
      node.v = 1;
      node.name = node.id;
      nodes.push_back(node);      
    }
    
    for (int i = 0; i < 1; i++) {
      edge_t edge;
      edge.id = std::to_string(++edge_id_);
      edge.name = edge.id;
      edge.start_point = nodes.front();
      edge.end_point = nodes.back();
      edge.points = nodes;
      edges.push_back(edge);
    }

    task.nodes = nodes;
    task.edges = edges;
    task.start_point = nodes.front();
    task.end_point = nodes.back();

    order_task_[task.id] = task;
    order_task_[task.id].nodes = task.nodes;
    order_task_[task.id].edges = task.edges;;
    order_task_[task.id].zone_id = "888888";
    order_task_[task.id].map_id = "888888";
    order_task_[task.id].kuqu_id = -1;
    
    // 5 交管计算
    std::string id1 = task.id;
    if (order_task_.find(id1) == order_task_.end()) {
      return 0;
    }
    std::vector<Vector4d> line1;
    task_t &new_task = task;
    for (auto &node : new_task.nodes) {
      line1.push_back(Vector4d(node.x, node.y, node.angle, node.v));
    }
    for (auto &order : order_task_) {
      if (new_task.map_id != order.second.map_id || new_task.zone_id != order.second.zone_id) {
        continue;
      }
      if (traffic_mgr_ && new_task.id != order.first) {
        std::vector<Vector4d> line2;
        std::string id2 = order.first;
        for(auto &node : order.second.nodes) {
          line2.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
        }
        auto max_traffic_id = DB->get_max_traffic_id();
        auto max_collison_id = DB->get_max_collision_id();
        auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id, max_traffic_id);
        for (auto &area : areas.traffic_area) {
          if (area.first == id1) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id1;
              new_task.traffic.emplace_back(traffic);
            }
          }
          if (area.first == id2) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id2;
              order.second.traffic.emplace_back(traffic);
            }
          }
        }

        for(auto &area : areas.rough_area) {
          if (area.first == id1) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id1;
              new_task.collision.emplace_back(traffic);
            }
          }
          if (area.first == id2) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id2;
              order.second.collision.emplace_back(traffic);
            }
          }
        }
      }
    }

    if (0 != DB->save_task(task)) {
      LOG_ERROR("save task(%s) error", task.id.c_str());
    } else {
      for (auto &order : order_task_) {
        LOG_WARN("ready save other task(%s) traffic", order.first.c_str());
        if (0 != DB->save_traffic(order.second.traffic)) {
          LOG_ERROR("save task(%s) traffic error", order.first.c_str());
        }
        if (0 != DB->save_collision(order.second.collision)) {
          LOG_ERROR("save task(%s) collision error", order.first.c_str());
        }
      }
    }
    LOG_INFO("task(%s) save finished ...", task.id.c_str());

  } else {
    task.id = send_id;
    task.name = "test_edge";
    task.zone_id = "888888";
    task.map_id = "888888";

    Storage storage;
    Point start{-5, -2.5};
    Point end{15, -2.5};
    auto points = storage.sampleLineSegment(start, end, 0.05);
    std::vector<node_t> nodes;
    for (int i = 0; i<points.size(); i++) {
      node_t node;
      node.id = std::to_string(++node_id_);
      node.x = points[i].x;
      node.y = points[i].y;
      node.angle = 0;
      node.theta = 0;
      node.v = 1;
      node.name = node.id;
      nodes.push_back(node);      
    }
    
    std::vector<edge_t> edges;
    for (int i = 0; i < 1; i++) {
      edge_t edge;
      edge.id = std::to_string(++edge_id_);
      edge.name = edge.id;
      edge.start_point = nodes.front();
      edge.end_point = nodes.back();
      edge.points = nodes;
      edges.push_back(edge);
    }

    task.nodes = nodes;
    task.edges = edges;
    task.start_point = nodes.front();
    task.end_point = nodes.back();

    order_task_[task.id] = task;
    order_task_[task.id].nodes = task.nodes;
    order_task_[task.id].edges = task.edges;;
    order_task_[task.id].zone_id = "888888";
    order_task_[task.id].map_id = "888888";
    order_task_[task.id].kuqu_id = -1;
    
    if (0 != DB->save_task(task)) {
      LOG_ERROR("save task(%s) error", task.id.c_str());
    } else {
      for (auto &order : order_task_) {
        if (0 != DB->save_traffic(order.second.traffic)) {
          LOG_ERROR("save task(%s) traffic error", order.first.c_str());
        }
        if (0 != DB->save_collision(order.second.collision)) {
          LOG_ERROR("save task(%s) collision error", order.first.c_str());
        }
      }
    }

    LOG_INFO("task(%s) save finished ...", task.id.c_str());

    nodes.clear();
    edges.clear();
    task.nodes.clear();
    task.edges.clear();
    task.start_point = node_t();
    task.end_point = node_t();

    task.id = std::to_string(std::stoi(send_id)+1);
    task.name = sampling_->get_kuqu_name();
    task.zone_id = "888888";
    task.map_id = "888888";

    node_t node;
    task.start_point = node;
    task.end_point = node;

    kuqu_t kuqu;
    std::vector<Point> kuqu_vertexs = {{10, 0}, {-0.5, -0.5}, {-0.5, 10}, {7, 13}, {8, 5}, {10, 5}};
    static int num = 0;
    for (const auto &node : kuqu_vertexs) {
      vertex_t vertex;
      vertex.id = std::to_string(num++);
      vertex.x = node.x;
      vertex.y = node.y;
      kuqu.vertexs.push_back(vertex);
    }

    kwsize_t kwInfo;
    kwInfo.width = 1.2;
    kwInfo.length = 1.0;
    kwInfo.rowspace = 0.3;
    kwInfo.columnspace = 0.5;
    kuqu.kwsize = kwInfo;  

    kuqu.direction = "0-1"; 
    task.kuqu_id = ++kuqu_id_;   
    kuqu.id = std::to_string(task.kuqu_id);
    kuqu.name = task.name;
    // task.kuqu = kuqu;

    {
      // kuqu_t kuqu = task.kuqu;
      // 1、计算库位点
      int res = storage.CalKuQu(kuqu);
      for (const auto &p : kuqu.storage) {
        std::cout << "kuwei: " << p.id << std::endl;
      }
      if (res != 0) {
        return res;
      }
      // 2、计算探测点
      storage.CalDetect(kuqu);

      // 4 构建库区路线段(所有沿线路径->库区各列)
      for (const auto &enter : kuqu.detects) {
        std::cout << "enter: " << enter.id << std::endl;
        int column = std::stoi(enter.id);
        Point edge_start{enter.x, enter.y};
        Point edge_end;
        int max_row = 0;
        for (const auto &storage : kuqu.storage) {
          if (storage.column == column) {
            if (storage.row > max_row) {
              edge_end = Point{storage.center.x, storage.center.y};
              max_row = storage.row;
            }
          }
        }
        // 进入路段
        edge_t edge;
        edge.id = std::to_string(++edge_id_);
        edge.name = edge.id;
        edge.avoid_level = 1;
        edge.velocity_level = 1;
        edge.column = std::stoi(enter.id);

        double length = sqrt(pow(edge_start.x - edge_end.x, 2) + pow(edge_start.y - edge_end.y, 2));
        double angle = storage.CalAngle(edge_start, edge_end);
        std::vector<Point> edge_points = storage.sampleLineSegment(edge_start, edge_end, 0.05);
        std::vector<node_t> enter_nodes;
        std::vector<node_t> leave_nodes;
        for (int i = 0; i < edge_points.size(); i++) {
          node_t node;
          auto time = common::get_now_time();
          node.id = std::to_string(++node_id_);
          node.name = node.id;
          node.timestamp = time;
          node.x = edge_points[i].x;
          node.y = edge_points[i].y;
          node.angle = angle;
          node.v = -1;
          if (i == 0) {
            node.node = 1;
            node.type = kNodeSampling;
            node.action = kNodeActionNone;
          }
          if (i == edge_points.size()-1) {
            node.type = kNodeAction;
            node.action = kNodeActionRest;
            node.node = kNodePosEnd;
          }
          nodes.push_back(node);
          enter_nodes.push_back(node);
        }

        edge.start_point = enter_nodes.front();
        edge.end_point = enter_nodes.back();
        edge.points = enter_nodes;
        edge.direction = -1;
        edge.length = length;
        edges.push_back(edge);

        // 离开路段
        // nodes.clear();
        for (int i = edge_points.size()-1; i >= 0; i--) {
          node_t node;
          auto time = common::get_now_time();
          node.id = std::to_string(++node_id_);
          node.name = node.id;
          node.timestamp = time;
          node.x = edge_points[i].x;
          node.y = edge_points[i].y;
          node.angle = angle;
          node.v = 1;
          if (i == 0) {
            node.action = kNodeActionRest;
            node.node = kNodePosEnd;
          }
          if (i == edge_points.size()-1) {
            node.node = 1;
            node.type = kNodeSampling;
            node.action = kNodeActionNone;
          }
          nodes.push_back(node);
          leave_nodes.push_back(node);
        }

        edge.id = std::to_string(++edge_id_);
        edge.name = edge.id;
        edge.avoid_level = 1;
        edge.velocity_level = 1;
        edge.column = std::stoi(enter.id);
        edge.start_point = leave_nodes.front();
        edge.end_point = leave_nodes.back();
        edge.points = leave_nodes;
        edge.direction = 1;
        edge.length = length;
        edges.push_back(edge);
      }

      task.nodes = nodes;
      task.edges = edges;
      task.start_point = nodes.front();
      task.end_point = nodes.back();
      task.kuqu = kuqu;
      for (const auto &edge : task.edges) {
        LOG_INFO("task(%s) edge(%s)", task.id.c_str(), edge.id.c_str());
      }

      order_task_[task.id] = task;
      order_task_[task.id].nodes = task.nodes;
      order_task_[task.id].edges = task.edges;;
      order_task_[task.id].zone_id = "888888";
      order_task_[task.id].map_id = "888888";
      order_task_[task.id].kuqu_id = task.kuqu_id;

      // 5 交管计算
      auto max_traffic_id = DB->get_max_traffic_id();
      auto max_collison_id = DB->get_max_collision_id();

      std::string id1 = task.id;
      if (order_task_.find(id1) == order_task_.end()) {
        return 0;
      }
      task_t &new_task = task;

      int base = 0;
      for (const auto &edge : new_task.edges) {
        std::vector<Vector4d> line1;
        for (const auto &node : edge.points) {
          line1.push_back(Vector4d(node.x, node.y, node.angle, node.v));
        }
        for (auto &order : order_task_) {
          if (new_task.map_id != order.second.map_id || new_task.zone_id != order.second.zone_id) {
            continue;
          }
          if (traffic_mgr_ && new_task.id != order.first) {
            std::vector<Vector4d> line2;
            std::string id2 = order.first;
            for(auto &node : order.second.nodes) {
              line2.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
            }
            std::cout << "cal traffic: " << id1 << ", " << id2 << std::endl;

            auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id++, max_traffic_id++);
            for(auto &area : areas.traffic_area) {
              if (area.first == id1) {
                for(auto &item : area.second) {
                  traffic_t traffic;
                  transform(item, traffic);
                  traffic.task_id = id1;
                  traffic.start_index = base;
                  traffic.end_index += base;
                  new_task.traffic.emplace_back(traffic);
                }
              }
              if (area.first == id2) {
                for(auto &item : area.second) {
                  traffic_t traffic;
                  transform(item, traffic);
                  traffic.task_id = id2;
                  order.second.traffic.emplace_back(traffic);
                }
              }
            }

            for(auto &area : areas.rough_area) {
              if (area.first == id1) {
                for(auto &item : area.second) {
                  traffic_t traffic;
                  transform(item, traffic);
                  traffic.task_id = id1;
                  traffic.start_index = base;
                  traffic.end_index += base;
                  new_task.collision.emplace_back(traffic);
                }
              }
              if (area.first == id2) {
                for(auto &item : area.second) {
                  traffic_t traffic;
                  transform(item, traffic);
                  traffic.task_id = id2;
                  order.second.collision.emplace_back(traffic);
                }
              }
            }
          }
        }
        base += line1.size();
      }

      if (0 != DB->save_task(task)) {
        LOG_ERROR("save task-kuqu(%s) error", (task.id).c_str());
      } else {
        for (auto &order : order_task_) {
          if (0 != DB->save_traffic(order.second.traffic)) {
            LOG_ERROR("save task(%s) traffic error", order.first.c_str());
          }
          if (0 != DB->save_collision(order.second.collision)) {
            LOG_ERROR("save task(%s) collision error", order.first.c_str());
          }
        }
      }

      LOG_INFO("task-kuqu(%s) save finished ...", (task.id).c_str());
    }

    return 0;
    
  }

  // if (0 != DB->save_task(task)) {
  //   LOG_ERROR("save task(%s) error", send_id.c_str());
  // }

  // LOG_INFO("task(%s) save finished ...", send_id.c_str());
  return 0;
}

int PathTask::test_del(const std::string& id) {
  int ret = 1;
  try {
    if (DB) {
      ret = DB->del_task(id);
      if (0 == ret) {
        std::lock_guard<std::mutex> lck(task_mtx_);
        auto cit = order_task_.find(id);
        if (cit != order_task_.end()) {
          order_task_.erase(cit);
        }
      }
      LOG_INFO("delete task(%s) return %d", id.c_str(), ret);

      // rebuild topology
      DB->delete_topology();
      std::vector<task_t> tasks; {
        std::lock_guard<std::mutex> lck(task_mtx_);
        for(auto &item : order_task_) {
          tasks.emplace_back(item.second);
        }
      }
      is_load_graph_ok_ = path_plan_->buildTopologicalGraph(tasks);
      topology_map_t pt_map = path_plan_->getPoint2TopologyMap();
      if (0 != DB->save_topology(pt_map)) {
        LOG_ERROR("save topology point error");
      } else {
        LOG_INFO("rebuild task topology success");
      }
    }
  } catch(std::exception &e) {
    LOG_INFO("delete task(%s) exception, %s", id.c_str(), e.what());
    ret = 2;
  }
  return ret;
}

int PathTask::deinit()
{
  is_loop_ = false;
  {
    std::lock_guard<std::mutex> lk(update_mtx_);
    update_cv_.notify_all();
  }
  return 0;
}

void PathTask::load_task_info()
{
  std::vector<task_info_t> datas;
  if (0 == DB->load_task_info(datas) && !datas.empty()) {
    bool is_find = false;
    for(auto &data : datas) {
      if (data.id == "task_info") {
        is_find = true;
        task_info_ = data;
      }
    }
    last_odom_ = task_info_.total_odom;
    if (!is_find || task_info_.create_odom_time.empty()) {
      task_info_.create_odom_time = common::get_current_time<std::string>();
    }
  } else {
    task_info_.create_odom_time = common::get_current_time<std::string>();
  }
  task_info_.current_odom_time = common::get_current_time<std::string>();
  task_info_.id = "task_info";
  LOG_INFO("last_odom_: %.3f, create_odom_time = %s, current_odom_time = %s",
    last_odom_, task_info_.create_odom_time.c_str(), task_info_.current_odom_time.c_str());
}

int16_t PathTask::cal_task(std::string &send_id)
{
  if (task_state_.operating_mode == false) {
    LOG_ERROR("task order(%s) can't cal task: manual mode", send_id.c_str());
    return 65535; 
  }

  if ((!task_state_.mapping && !task_state_.position_initialized) || task_state_.mapping) {
    LOG_ERROR("task order(%s) can't cal task: no location or mapping", send_id.c_str());
    return 65535; 
  } 

  if (task_state_.agv_state == "Error" || task_state_.agv_state == "Fault") {
    LOG_ERROR("task order(%s) can't cal task: error mode", send_id.c_str());
    return 65535; 
  }

  if (task_state_.agv_state != "Waiting") {
    LOG_ERROR("task order(%s) can't cal task: no waiting(%s) mode",
      send_id.c_str(), task_state_.agv_state.c_str());
    return 65535; 
  }

  std::vector<std::string> tasks;
  if (0 == DB->load_target_task_by_sn(std::stoi(send_id), tasks)) {
    if (tasks.size() != 1) {
      LOG_ERROR("send sn(%s) task num != 1(%d)", send_id.c_str(), tasks.size());
      return 65535;      
    }
  } else {
    LOG_ERROR("send sn(%s) can't find this task", send_id.c_str());
    return 65535;
  }

  std::string task_id(tasks.front());
  LOG_WARN("ready cal task(%s) grade", task_id.c_str());
  //1、先找到任务id
  if (order_task_.find(task_id) == order_task_.end()) {
    LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
    return 65535;
  }
  
  if (!path_plan_ || !is_load_graph_ok_) {
    LOG_ERROR("task order(%s) path_plan object is null or load graph failed(%d)",
      task_id.c_str(), (int)is_load_graph_ok_);
    return 65535;
  }

  task_t task;
  {
    std::lock_guard<std::mutex> lck(task_mtx_);
    task = order_task_[task_id];
  }
  std::vector<node_t> actions;
  pose_t curr_pose = pose_;
  actions = task.actions;

  int16_t grade = path_plan_->getTaskPathCost(curr_pose, actions, task_id);
  LOG_WARN("cal task(%s) exec grade(%d) finished.", task_id.c_str(), grade);
  return grade;
}

int PathTask::judge() {
  std::string send_id = "";
  std::vector<std::string> task_ids{};
  if (!is_merge_) return start(send_id, last_task_actions_, false, true);
  else return start(task_ids, last_task_actions_, false, true);
}

int PathTask::test_start(std::string &send_id)
{
  LOG_INFO("task(%s) start test ...", send_id.c_str());
  is_merge_ = false;
  //1 是否是继续任务
  std::string task_id(send_id);

  //2 判断任务重复下发
  task_t task;
  using namespace std::chrono;
  
  bool is_cross_floors = false;

  //4 下发任务
  order_future_ = std::async(std::launch::async, [&, task_id](){
    //1、先找到任务id
    if (order_task_.find(task_id) == order_task_.end()) {
      LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
      return (int)kTaskNotFound;
    }

    if (!path_plan_ || !is_load_graph_ok_) {
      LOG_ERROR("task order(%s) path_plan object is null or load graph failed(%d)",
        task_id.c_str(), (int)is_load_graph_ok_);
      return (int)kTaskLoadGraphError;
    }

    {
      std::lock_guard<std::mutex> lck(task_mtx_);
      task = order_task_[task_id];
    }
    std::vector<node_t> actions;
    // pose_t curr_pose = pose_;
    pose_t curr_pose;
    curr_pose.x = -0.336;
    curr_pose.y = -0.273;
    curr_pose.angle = -0.021;
    curr_pose.v = 0;

    task_t plan_task;
    plan_task.id = task_id;
    plan_task.avoid_level = task.avoid_level;
    plan_task.velocity_level = task.velocity_level;
    plan_task.loop = task.loop;

    actions = task.actions;
    
    std::stringstream str;
    str << "task_id = " << task_id;

    // 4.1 新下任务
    LOG_INFO("task(%s) path plan begin ...", task_id.c_str());
    std::vector<cotek::weighted_edge_t> paths;
    paths = path_plan_->getPath(curr_pose, actions, task_id, false);
    LOG_INFO("task(%s) path plan end", task_id.c_str());
    if (paths.empty()) {
      LOG_ERROR("task order(%s) path_plan failed(%d)", task_id.c_str(), (int)is_load_graph_ok_);
      return (int)kTaskPathPlanError;
    }

    //路径要转换为任务上已有的数据结构
    for (int i = 0; i < paths.size(); i++) {
      auto &path = paths[i];
      std::vector<node_t> nodes;
      if (0 != DB->load_point_by_edge_id(nodes, path.task_edge_id, path.start_index, path.end_index)) {
          LOG_ERROR("load point form db by edge_id(%s) (%d, %d) error", 
            path.task_edge_id.c_str(), path.start_index, path.end_index);
      }
      std::vector<task_t> tasks;
      if (0 != DB->load_task(path.task_edge_id, tasks)) {
          LOG_ERROR("load task form db by edge_id(%s) error", path.task_edge_id.c_str());
      }
      edge_t edge;
      edge.id = path.task_edge_id;
      int direction = 1;
      if (!nodes.empty()) {
        if (std::fabs(nodes.back().v) < 0.001) {
          direction = 0;
        } else {
          direction = nodes.back().v > 0 ? 1 : -1;
        }
        auto find_node = [&](const std::vector<node_t> &actions, const std::string &id) {
          for(auto &action : actions) {
            if (id == action.id) {
              return true;
            }
          }
          return false;
        };
        auto &snode = nodes.front();
        if (!find_node(actions, snode.id)) {
          snode.type = 0;
          snode.action = 0;
        }
        auto &enode = nodes.back();
        if (!find_node(actions, enode.id)) {
          enode.type = 0;
          enode.action = 0;
        }
        edge.start_point = snode;
        edge.end_point = enode;
        edge.start_index = path.start_index;
        edge.end_index = path.end_index;
        edge.direction = direction;
        edge.avoid_level = path.avoid_level;
        edge.velocity_level = path.velocity_level;
        // edge.avoid_level = !tasks.empty()? tasks.front().avoid_level : 1;
        // edge.velocity_level = !tasks.empty()? tasks.front().velocity_level : 1;
        edge.length = common::distance(nodes);
        if (i == 0) {
          plan_task.nodes.emplace_back(edge.start_point);
        }
        plan_task.nodes.emplace_back(edge.end_point);
        str << "\n\tedge" << path.task_edge_id << ": (" 
            << path.start_index << "(" << nodes.front().id 
            << ") -> " 
            << path.end_index << "(" << nodes.back().id << "))";
      }
      edge.points = nodes;
      plan_task.edges.emplace_back(edge);
    }
    if (!plan_task.nodes.empty()) {
      plan_task.start_point = plan_task.nodes.front();
      plan_task.end_point = plan_task.nodes.back();
    }
    plan_task.actions = actions;
    last_task_ = plan_task;
    window_popup_ = 0;
    std::stringstream ss;
    for (const auto &node : plan_task.nodes) {
      if (&node == &plan_task.nodes.front()) {
        ss << "\n\tnode: " << node.id;
      } else {
        ss << "->" << node.id;
      }
    }
    str << ss.str();
    LOG_INFO("new task: %s", str.str().c_str());

    //3、将任务转为decision_make任务
    emit("task_order", plan_task);

    LOG_INFO("task(%s) test finished ...", task_id.c_str());

    return (int)kTaskSuccess;
  });

  int ret = kTaskSuccess;
  switch (order_future_.wait_for(std::chrono::seconds(10)))
  {
  case std::future_status::ready:
    ret = order_future_.get();
    break;
  case std::future_status::timeout:
    ret = kTaskSendTimeout;
    break;
  default:
    ret = kTaskSendOtherError;
    break;
  }
  // LOG_INFO("send task(%s) loop(%d/%d) return = %d", merge_id.c_str(), 
  //   last_task_loop_-task_loop_+1, last_task_loop_, ret);
  return ret;
}

int PathTask::start(std::string &send_id, std::vector<std::string> &task_actions, 
                    bool is_self, bool is_continue)
{
  is_merge_ = false;
  //1 是否是继续任务
  std::string task_id(send_id);
  if (is_continue) {
    task_id = last_task_id_;
    task_actions = last_task_actions_;
  }
  send_id = task_id;

  //2 判断任务重复下发
  task_t task;
  using namespace std::chrono;
  if (duration_cast<seconds>(steady_clock::now() - task_time_).count() < 1 && 
      task_id == last_task_id_) {
    LOG_WARN("task order(%s) interval < 1s", task_id.c_str());
    return kTaskInterval;
  }
  task_time_ = steady_clock::now();

  //3 是否能下发任务
  if (task_state_.operating_mode == false) {
    LOG_ERROR("task order(%s) can't send task: manual mode", task_id.c_str());
    return task_code_t::kTaskManual;
  }

  if ((!task_state_.mapping && !task_state_.position_initialized) || task_state_.mapping) {
    LOG_ERROR("task order(%s) can't send task: no location or mapping", task_id.c_str());
    return task_code_t::kTaskNoLocation;
  } 

  if (task_state_.agv_state == "Error" || task_state_.agv_state == "Fault") {
    LOG_ERROR("task order(%s) can't send task: error mode", task_id.c_str());
    return task_code_t::kTaskError;
  }

  if (task_state_.agv_state != "Waiting") {
    LOG_ERROR("task order(%s) can't send task: no waiting(%s) mode",
      task_id.c_str(), task_state_.agv_state.c_str());
    return task_code_t::kTaskOtherState;
  }

  if (order_task_.find(task_id) == order_task_.end()) {
    LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
    return (int)kTaskNotFound;
  }
  if (task_actions.size() != 1) {
    LOG_ERROR("task order(%s) actions err.", task_id.c_str());
    return (int)kKuQuActError;
  } 

  std::string target_map = order_task_[task_id].zone_id;
  // if (DB->load_task_map(task_id, target_map) != 0) {
  //   LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
  //   return (int)kTaskNotFound;  
  // }
  bool is_kuqu_task = order_task_[task_id].kuqu_id != -1 ? true : false;
  if (is_kuqu_task) {
    if (task_actions.front() == "none") {
      LOG_ERROR("task order(%s) kuqu actions err.", task_id.c_str());
      return (int)kKuQuActError;
    }
  }

  bool is_cross_floors = false;
  std::string elevator_task = "";
  pose_t reloc_pose;
  elevator_task_.is_cross = false;
  if (target_map != task_state_.map_id) {
    // 跨楼层任务
    is_cross_floors = true;
    std::vector<std::string> tasks;
    // 查找当前地图中目标点为呼叫电梯的任务
    if (DB->load_target_task(task_state_.map_id, 9, tasks) != 0) {
      LOG_ERROR("load map(%s) task failed.", task_state_.map_id);
      return (int)kTaskNotFound;
    }
    if (tasks.size() < 1) {
      LOG_ERROR("task order(%s) can't find this task, size(%d)", task_id.c_str(), tasks.size());
      return (int)kElevatorNotFound;      
    } else {
      elevator_task = tasks[0];
    }

    tasks.clear();
    // 查找目标地图中目标点为呼叫电梯的任务
    if (DB->load_target_task(target_map, 10, tasks) != 0) {
      LOG_ERROR("load map(%s) task failed.", task_state_.map_id);
      return (int)kTaskNotFound;
    }
    if (tasks.size() < 1) {
      LOG_ERROR("task order(%s) can't find this task, size(%d)", task_id.c_str(), tasks.size());
      return (int)kElevatorNotFound;      
    }
    // 获取目标地图中电梯的点位信息
    std::vector<node_t> nodes;
    if (DB->load_target_node(tasks[0], 10, nodes) != 0) {
      LOG_ERROR("load task(%s) elevator node failed.", tasks[0]);
      return (int)kElevatorNotFound;      
    } else {
      reloc_pose.x = nodes[0].x;
      reloc_pose.y = nodes[0].y;
      reloc_pose.angle = nodes[0].angle;
      reloc_pose.v = nodes[0].v;
    }
    // 获取当前地图和目标地图的对应楼层信息
    std::string cur_floor, tar_floor;
    find_floor(task_state_.map_id, target_map, cur_floor, tar_floor);
    if (cur_floor == "8888" || tar_floor == "8888") {
      LOG_ERROR("find map(%s, %s) to floor(%s, %s) failed.",
                task_state_.map_id.c_str(), target_map.c_str(), cur_floor.c_str(), tar_floor.c_str());
      return (int)kFloorNotFound; 
    }
      
    elevator_task_.is_cross = true;
    elevator_task_.cur_map_id = task_state_.map_id;
    elevator_task_.tar_map_id = target_map;
    elevator_task_.cur_floor = cur_floor;
    elevator_task_.tar_floor = tar_floor;    
    elevator_task_.reloc_pose.x = reloc_pose.x;
    elevator_task_.reloc_pose.y = reloc_pose.y;
    elevator_task_.reloc_pose.v = reloc_pose.v;
    elevator_task_.reloc_pose.angle = reloc_pose.angle;
  }
  LOG_ERROR("cross_floors: %d, task(%s), pose(%.2f, %.2f, %f)", 
            is_cross_floors, elevator_task.c_str(), reloc_pose.x, reloc_pose.y, reloc_pose.angle);

  //4 下发任务
  order_future_ = std::async(std::launch::async, [&, task_id, is_kuqu_task, is_cross_floors, elevator_task, reloc_pose](){
    //1、先找到任务id
    if (order_task_.find(task_id) == order_task_.end()) {
      LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
      return (int)kTaskNotFound;
    }

    if (!path_plan_ || !is_load_graph_ok_) {
      LOG_ERROR("task order(%s) path_plan object is null or load graph failed(%d)",
        task_id.c_str(), (int)is_load_graph_ok_);
      return (int)kTaskLoadGraphError;
    }

    {
      std::lock_guard<std::mutex> lck(task_mtx_);
      task = order_task_[task_id];
    }
    std::vector<node_t> actions;
    pose_t curr_pose = pose_;
    task_t plan_task;
    plan_task.id = task_id;
    plan_task.avoid_level = task.avoid_level;
    plan_task.velocity_level = task.velocity_level;
    plan_task.loop = task.loop;
    if (!is_self) {
      task_loop_ = MAX_LOOP(task.loop);
      last_task_loop_ = task_loop_;
    }
    if (is_continue) {
      int find_index = -1;
      for (int i = 0; i < task.actions.size(); i++) {
        try {
          if (std::stol(task.actions[i].id) > std::stol(last_node_id_)) {
            find_index = i;
            break;
          }
          if (std::stol(task.actions[i].id) == std::stol(last_node_id_)) {
            if ((task.actions[i].action == 7 || 
                task.actions[i].action == 9 ||
                task.actions[i].action == 10)) {
              find_index = i;
              break;  
            }            
            auto dist = common::distance(task.actions[i].x, task.actions[i].y, 
                                         curr_pose.x, curr_pose.y);
            if (dist < 0.1) {
              find_index = i;
              break;
            }
          }
        } catch(std::exception &e) {
          LOG_ERROR("get task (%s) action exception, %s", 
            task_id.c_str(), e.what());
        }
      }
      for (int i = find_index; i < task.actions.size(); i++) {
        actions.push_back(task.actions[i]);
      }
    } else {
      actions = task.actions;
    }
    std::stringstream str;
    str << "task_id = " << task_id;

    // 4.1 新下任务
    LOG_INFO("task(%s) path plan begin ...", task_id.c_str());
    std::vector<cotek::weighted_edge_t> paths;
    std::pair<std::string, node_t> kuqu_start, kuqu_end;
    int kuqu_enter_index, kuqu_end_index;
    if (!is_cross_floors) {
      if (!is_kuqu_task) {
        paths = path_plan_->getPath(curr_pose, actions, task_id, is_continue);
      } else {
        // 库区任务特殊处理
        // paths = path_plan_->getPath(curr_pose, task_id, kuqu_start, kuqu_end, kuqu_enter_index, kuqu_end_index);
      }
      LOG_INFO("task(%s) path plan end", task_id.c_str());
      if (paths.empty()) {
        LOG_ERROR("task order(%s) path_plan failed(%d)", task_id.c_str(), (int)is_load_graph_ok_);
        return (int)kTaskPathPlanError;
      }
    } else {
      // paths = path_plan_->getPath(reloc_pose, actions, task_id, is_continue);
    }


    if (is_cross_floors) {
      if (order_task_.find(elevator_task) == order_task_.end()) {
        LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
        return (int)kTaskNotFound;
      }

      if (!path_plan_ || !is_load_graph_ok_) {
        LOG_ERROR("task order(%s) path_plan object is null or load graph failed(%d)",
          elevator_task.c_str(), (int)is_load_graph_ok_);
        return (int)kTaskLoadGraphError;
      }

      {
        std::lock_guard<std::mutex> lck(task_mtx_);
        task = order_task_[elevator_task];
      }
      actions.clear();
      plan_task.id = task_id;
      plan_task.avoid_level = task.avoid_level;
      plan_task.velocity_level = task.velocity_level;
      plan_task.loop = 1;
      // plan_task.loop = task.loop;
      if (!is_self) {
        task_loop_ = 2;
        last_task_loop_ = task_loop_;
      }
      if (is_continue) {
        int find_index = -1;
        for (int i = 0; i < task.actions.size(); i++) {
          try {
            if (std::stol(task.actions[i].id) > std::stol(last_node_id_)) {
              find_index = i;
              break;
            }

            if (std::stol(task.actions[i].id) == std::stol(last_node_id_)) {
              if ((task.actions[i].action == 7 || 
                  task.actions[i].action == 9 ||
                  task.actions[i].action == 10)) {
                find_index = i;
                break;  
              }            
              auto dist = common::distance(task.actions[i].x, task.actions[i].y, 
                                           curr_pose.x, curr_pose.y);
              if (dist < 0.1) {
                find_index = i;
                break;
              }
            }

          } catch(std::exception &e) {
            LOG_ERROR("get task (%s) action exception, %s", 
              elevator_task.c_str(), e.what());
          }
        }
        for (int i = find_index; i < task.actions.size(); i++) {
          actions.push_back(task.actions[i]);
        }
      } else {
        actions = task.actions;
      }
      // std::stringstream str;
      str << "elevator_task = " << elevator_task;
      // 4.1 新下任务
      LOG_INFO("task(%s) path plan begin ...", elevator_task.c_str());
      paths.clear();
      paths = path_plan_->getPath(curr_pose, actions, elevator_task, is_continue);
      LOG_INFO("task(%s) path plan end", elevator_task.c_str());
      if (paths.empty()) {
        LOG_ERROR("task order(%s) path_plan failed(%d)", elevator_task.c_str(), (int)is_load_graph_ok_);
        return (int)kTaskPathPlanError;
      }
    }

    // 判断规划的首条路线是否属于库区
    bool start_from_kuqu = false;
    bool start_from_this_kuqu = false;
    if (is_kuqu_task) {
      std::vector<task_t> tasks;
      if (0 != DB->load_task(paths.front().task_edge_id, tasks)) {
        LOG_ERROR("load task form db by edge_id(%s) error", paths.front().task_edge_id.c_str());
      }
      if (tasks.front().kuqu_id != -1) {
        start_from_kuqu = true;
        LOG_INFO("start from kuqu.");
      }
      if (start_from_kuqu && tasks.front().id == task_id) {
        start_from_this_kuqu = true;
        LOG_ERROR("agv at task kuqu.");
        return (int)kAtTaskKuQu;
      }
    }

    //路径要转换为任务上已有的数据结构
    for (int i = 0; i < paths.size(); i++) {
      auto &path = paths[i];
      std::vector<node_t> nodes;
      if (0 != DB->load_point_by_edge_id(nodes, path.task_edge_id, path.start_index, path.end_index)) {
          LOG_ERROR("load point form db by edge_id(%s) (%d, %d) error", 
            path.task_edge_id.c_str(), path.start_index, path.end_index);
      }
      // std::vector<task_t> tasks;
      // if (0 != DB->load_task(path.task_edge_id, tasks)) {
      //     LOG_ERROR("load task form db by edge_id(%s) error", path.task_edge_id.c_str());
      // }
      edge_t edge;
      edge.id = path.task_edge_id;
      int direction = 1;
      if (!nodes.empty()) {
        if (std::fabs(nodes.back().v) < 0.001) {
          direction = 0;
        } else {
          direction = nodes.back().v > 0 ? 1 : -1;
        }
        auto find_node = [&](const std::vector<node_t> &actions, const std::string &id) {
          for(auto &action : actions) {
            if (id == action.id) {
              return true;
            }
          }
          return false;
        };
        auto &snode = nodes.front();
        if (!find_node(actions, snode.id)) {
          snode.type = 0;
          snode.action = 0;
        }
        auto &enode = nodes.back();
        if (!find_node(actions, enode.id)) {
          enode.type = 0;
          enode.action = 0;
        }

        if (i == paths.size() - 1 && is_kuqu_task) {
          // 最终规划的到达库区的路径需要切割成两条边
          // 1、到达库区进入准备点
          std::vector<node_t> cut_nodes;
          for (const auto &node : nodes) {
            if (std::stoi(node.id) <= std::stoi(kuqu_start.second.id)) {
              cut_nodes.push_back(node);
            } else {
              break;
            }
          }
          edge.start_point = snode;
          edge.end_point = kuqu_start.second;
          edge.start_index = path.start_index;
          edge.end_index = kuqu_enter_index;
          edge.direction = direction;
          edge.avoid_level = path.avoid_level;
          edge.velocity_level = path.velocity_level;
          edge.length = common::distance(cut_nodes); 

          if (i == 0) {
            plan_task.nodes.emplace_back(edge.start_point);
          }
          plan_task.nodes.emplace_back(edge.end_point);

          edge.points = cut_nodes;
          plan_task.edges.emplace_back(edge);

          str << "\n\tedge" << path.task_edge_id << ": (" 
              << path.start_index << "(" << cut_nodes.front().id 
              << ") -> " 
              << path.end_index << "(" << cut_nodes.back().id << "))";

          // 2、库区进入点->库区终点
          cut_nodes.clear();
          for (const auto &node : nodes) {
            if (std::stoi(node.id) > std::stoi(kuqu_start.second.id)) {
              cut_nodes.push_back(node);
            }
          }
          edge.start_point = kuqu_start.second;
          edge.end_point = enode;
          edge.start_index = kuqu_enter_index;
          edge.end_index = path.end_index;
          edge.direction = direction;
          edge.avoid_level = path.avoid_level;
          edge.velocity_level = path.velocity_level;
          edge.length = common::distance(cut_nodes); 

          if (i == 0) {
            if (!start_from_kuqu) {
              if (std::stoi(paths[i].start_point_id) < std::stoi(kuqu_start.second.id) && 
                  std::stoi(paths[i].start_point_id) < std::stoi(kuqu_end.second.id)) {
                // 车辆处于外侧主干道中间启动进入库区
                edge.start_point.type = 1;
                if (task_actions.front() == "liftload") {
                  edge.start_point.action = 101; // 进入库区准备点--取货
                } 
                if (task_actions.front() == "unload") {
                  edge.start_point.action = 102; // 进入库区准备点--卸货
                }
              }
            }
          } else {
            if (path.task_edge_id == kuqu_start.first) {
              edge.start_point.type = 1;
              if (task_actions.front() == "liftload") {
                edge.start_point.action = 101; // 进入库区准备点--取货
              } 
              if (task_actions.front() == "unload") {
                edge.start_point.action = 102; // 进入库区准备点--卸货
              }     
            }
          }

          if (i == 0) {
            plan_task.nodes.emplace_back(edge.start_point);
          }
          plan_task.nodes.emplace_back(edge.end_point);

          edge.points = cut_nodes;
          plan_task.edges.emplace_back(edge);

          // 3、库区终点前矫正取货
          if (task_actions.front() == "liftload") {
            edge.start_point.action = 104;
            plan_task.edges.emplace_back(edge);
          }

          str << "\n\tedge" << path.task_edge_id << ": (" 
              << path.start_index << "(" << cut_nodes.front().id 
              << ") -> " 
              << path.end_index << "(" << cut_nodes.back().id << "))";

        } else {
          edge.start_point = snode;
          edge.end_point = enode;
          edge.start_index = path.start_index;
          edge.end_index = path.end_index;
          edge.direction = direction;
          edge.avoid_level = path.avoid_level;
          edge.velocity_level = path.velocity_level;
          edge.length = common::distance(nodes);

          if (is_kuqu_task) {
            if (i == 0) {
              if (start_from_kuqu) {
                // 从库区内启动去往另一库区
                // edge.special_point = snode;
                // edge.special_point.type = 1;
                // edge.special_point.action = 103; // 离开库区
                // edge.special_index = kuqu_enter_index;

                edge.start_point.type = 1;
                edge.start_point.action = 103; // 离开库区
              }
            }
          }

          if (i == 0) {
            plan_task.nodes.emplace_back(edge.start_point);
          }
          plan_task.nodes.emplace_back(edge.end_point);

          edge.points = nodes;
          plan_task.edges.emplace_back(edge);

          str << "\n\tedge" << path.task_edge_id << ": (" 
              << path.start_index << "(" << nodes.front().id 
              << ") -> " 
              << path.end_index << "(" << nodes.back().id << "))";
        }
      }

    }
    if (!plan_task.nodes.empty()) {
      plan_task.start_point = plan_task.nodes.front();
      plan_task.end_point = plan_task.nodes.back();
    }
    plan_task.actions = actions;
    plan_task.kuqu_id = order_task_[task_id].kuqu_id;
    plan_task.kuqu = order_task_[task_id].kuqu;
    
    last_task_ = plan_task;
    window_popup_ = 0;
    std::stringstream ss;
    for (const auto &node : plan_task.nodes) {
      if (&node == &plan_task.nodes.front()) {
        ss << "\n\tnode: " << node.id;
      } else {
        ss << "->" << node.id;
      }
    }
    str << ss.str();
    LOG_INFO("new task: %s", str.str().c_str());

    //3、将任务转为decision_make任务
    emit("task_order", plan_task);
    task_running_ = kRunningStart;

    task_result_.result_id = std::to_string(common::get_now_time());
    task_result_.task_id = task.id;
    task_result_.task_name = task.name;
    task_result_.begin_time = common::get_now_time();

    last_task_id_ = task_id;
    last_task_actions_.clear();
    last_task_actions_ = task_actions;
    last_end_point_ = plan_task.end_point;
    last_task_ids_.clear(); // 清空组合任务，防止判断出错
    exec_num_ = 0;

    return (int)kTaskSuccess;
  });

  int ret = kTaskSuccess;
  switch (order_future_.wait_for(std::chrono::seconds(10)))
  {
  case std::future_status::ready:
    ret = order_future_.get();
    break;
  case std::future_status::timeout:
    ret = kTaskSendTimeout;
    break;
  default:
    ret = kTaskSendOtherError;
    break;
  }
  LOG_INFO("send task(%s) loop(%d/%d) return = %d", task_id.c_str(), 
    last_task_loop_-task_loop_+1, last_task_loop_, ret);
  return ret;
}

int PathTask::start(std::vector<std::string> &task_ids, 
                    std::vector<std::string> &task_actions,
                    bool is_self, bool is_continue)
{
  is_merge_ = true;
  if (is_self) {
    exec_num_++;
  }
  if (!is_self && !is_continue) exec_num_ = 0;

  //1 是否是继续任务
  std::vector<std::string> left_tasks;
  std::vector<std::string> left_actions;
  std::string merge_id;
  for (int i=exec_num_; i<task_ids.size(); i++) {
    merge_id += task_ids[i];
    merge_id += " ";
    left_tasks.push_back(task_ids[i]);
    left_actions.push_back(task_actions[i]);
  }

  if (is_continue) {
    task_ids = last_task_ids_;
    task_actions = last_task_actions_;
    for (int i=exec_num_; i<task_ids.size(); i++) {
      merge_id += task_ids[i];
      merge_id += " ";
      left_tasks.push_back(task_ids[i]);
    }
    for (int i=exec_num_; i<task_actions.size(); i++) {
      left_actions.push_back(task_actions[i]);
    }
  }

  //2 判断任务重复下发
  using namespace std::chrono;
  if (duration_cast<seconds>(steady_clock::now() - task_time_).count() < 1) {
    LOG_WARN("task order(%s) interval < 1s", merge_id.c_str());
    return kTaskInterval;
  }
  task_time_ = steady_clock::now();

  //3 是否能下发任务
  if (task_state_.operating_mode == false) {
    LOG_ERROR("task order(%s) can't send task: manual mode", merge_id.c_str());
    return task_code_t::kTaskManual;
  }

  if ((!task_state_.mapping && !task_state_.position_initialized) || task_state_.mapping) {
    LOG_ERROR("task order(%s) can't send task: no location or mapping", merge_id.c_str());
    return task_code_t::kTaskNoLocation;
  } 

  if (task_state_.agv_state == "Error" || task_state_.agv_state == "Fault") {
    LOG_ERROR("task order(%s) can't send task: error mode", merge_id.c_str());
    return task_code_t::kTaskError;
  }

  if (task_state_.agv_state != "Waiting") {
    LOG_ERROR("task order(%s) can't send task: no waiting(%s) mode",
      merge_id.c_str(), task_state_.agv_state.c_str());
    return task_code_t::kTaskOtherState;
  }

  if (!path_plan_ || !is_load_graph_ok_) {
    LOG_ERROR("task order(%s) path_plan object is null or load graph failed(%d)",
      merge_id.c_str(), (int)is_load_graph_ok_);
    return (int)kTaskLoadGraphError;
  }

  if (!path_plan_->canCombineTasks(pose_, left_tasks)) {
    LOG_ERROR("merger task order(%s) path_plan failed.", merge_id.c_str());
    return (int)kMergeTaskError;
  }

  task_t task;
  //4 下发任务
  order_future_ = std::async(std::launch::async, [&, left_tasks, merge_id]() {
    std::string task_id = left_tasks[0];

    //1、先找到任务id
    if (order_task_.find(task_id) == order_task_.end()) {
      LOG_ERROR("task order(%s) can't find this task", task_id.c_str());
      return (int)kTaskNotFound;
    }

    if (!path_plan_ || !is_load_graph_ok_) {
      LOG_ERROR("task order(%s) path_plan object is null or load graph failed(%d)",
        task_id.c_str(), (int)is_load_graph_ok_);
      return (int)kTaskLoadGraphError;
    }

    {
      std::lock_guard<std::mutex> lck(task_mtx_);
      task = order_task_[task_id];
    }
    std::vector<node_t> actions;
    task_t plan_task;
    plan_task.id = task_id;
    // plan_task.id = merge_id;
    plan_task.avoid_level = task.avoid_level;
    plan_task.velocity_level = task.velocity_level;
    plan_task.loop = 1;
    if (!is_self) {
      task_loop_ = left_tasks.size();
      // last_task_loop_ = task_loop_;
    }
    last_task_loop_ = task_loop_;
    if (is_continue) {
      int find_index = -1;
      for (int i = 0; i < task.actions.size(); i++) {
        try {
          if (std::stol(task.actions[i].id) > std::stol(last_node_id_)) {
            find_index = i;
            break;
          }
          if (std::stol(task.actions[i].id) == std::stol(last_node_id_)) {
            if ((task.actions[i].action == 7 || 
                task.actions[i].action == 9 ||
                task.actions[i].action == 10)) {
              find_index = i;
              break;  
            }            
            auto dist = common::distance(task.actions[i].x, task.actions[i].y, 
                                         pose_.x, pose_.y);
            if (dist < 0.1) {
              find_index = i;
              break;
            }
          }
        } catch(std::exception &e) {
          LOG_ERROR("get task (%s) action exception, %s", 
            task_id.c_str(), e.what());
        }
      }
      for (int i = find_index; i < task.actions.size(); i++) {
        actions.push_back(task.actions[i]);
      }
    } else {
      actions = task.actions;
    }

    // 4.1 新下任务
    std::stringstream str;
    str << "task_id = " << task_id;
    LOG_INFO("task(%s) path plan begin ...", task_id.c_str());
    pose_t curr_pose = pose_;
    auto paths = path_plan_->getPath(curr_pose, actions, task_id, is_continue);
    LOG_INFO("task(%s) path plan end", task_id.c_str());
    if (paths.empty()) {
      LOG_ERROR("task order(%s) path_plan failed(%d)", merge_id.c_str(), (int)is_load_graph_ok_);
      return (int)kTaskPathPlanError;
    }

    //路径要转换为任务上已有的数据结构
    for(int i = 0; i < paths.size(); i++) {
      auto &path = paths[i];
      std::vector<node_t> nodes;
      if (0 != DB->load_point_by_edge_id(nodes, path.task_edge_id, path.start_index, path.end_index)) {
          LOG_ERROR("load point form db by edge_id(%s) (%d, %d) error", 
            path.task_edge_id.c_str(), path.start_index, path.end_index);
      }
      std::vector<task_t> tasks;
      if (0 != DB->load_task(path.task_edge_id, tasks)) {
          LOG_ERROR("load task form db by edge_id(%s) error", path.task_edge_id.c_str());
      }
      edge_t edge;
      edge.id = path.task_edge_id;
      int direction = 1;
      if (!nodes.empty()) {
        if (std::fabs(nodes.back().v) < 0.001) {
          direction = 0;
        } else {
          direction = nodes.back().v > 0 ? 1 : -1;
        }
        auto find_node = [&](const std::vector<node_t> &actions, const std::string &id) {
          for(auto &action : actions) {
            if (id == action.id) {
              return true;
            }
          }
          return false;
        };
        auto &snode = nodes.front();
        if (!find_node(actions, snode.id)) {
          snode.type = 0;
          snode.action = 0;
        }
        auto &enode = nodes.back();
        if (!find_node(actions, enode.id)) {
          enode.type = 0;
          enode.action = 0;
        }
        edge.start_point = snode;
        edge.end_point = enode;
        edge.start_index = path.start_index;
        edge.end_index = path.end_index;
        edge.direction = direction;
        edge.avoid_level = path.avoid_level;
        edge.velocity_level = path.velocity_level;
        // edge.avoid_level = !tasks.empty()? tasks.front().avoid_level : 1;
        // edge.velocity_level = !tasks.empty()? tasks.front().velocity_level : 1;
        edge.length = common::distance(nodes);
        if (i == 0) {
          plan_task.nodes.emplace_back(edge.start_point);
        }
        plan_task.nodes.emplace_back(edge.end_point);
        str << "\n\tedge" << path.task_edge_id << ": (" 
            << path.start_index << "(" << nodes.front().id 
            << ") -> " 
            << path.end_index << "(" << nodes.back().id << "))";
      }
      edge.points = nodes;
      plan_task.edges.emplace_back(edge);
    }
    if (!plan_task.nodes.empty()) {
      plan_task.start_point = plan_task.nodes.front();
      plan_task.end_point = plan_task.nodes.back();
    }
    plan_task.actions = actions;
    last_task_ = plan_task;
    window_popup_ = 0;
    std::stringstream ss;
    for (const auto &node : plan_task.nodes) {
      if (&node == &plan_task.nodes.front()) {
        ss << "\n\tnode: " << node.id;
      } else {
        ss << "->" << node.id;
      }
    }
    str << ss.str();
    LOG_INFO("new task: %s", str.str().c_str());

    //3、将任务转为decision_make任务
    emit("task_order", plan_task);
    task_running_ = kRunningStart;

    task_result_.result_id = std::to_string(common::get_now_time());
    // task_result_.task_id = merge_id;
    // task_result_.task_name = merge_id;
    task_result_.task_id = task.id;
    task_result_.task_name = task.name;
    task_result_.begin_time = common::get_now_time();

    last_task_ids_ = task_ids;
    last_task_actions_.clear();
    last_task_actions_ = task_actions;
    last_end_point_ = plan_task.end_point;
    // last_task_id_ = "";
    // last_task_id_ = merge_id;
    last_task_id_ = task_id;

    return (int)kTaskSuccess;
  });

  int ret = kTaskSuccess;
  switch (order_future_.wait_for(std::chrono::seconds(10)))
  {
  case std::future_status::ready:
    ret = order_future_.get();
    break;
  case std::future_status::timeout:
    ret = kTaskSendTimeout;
    break;
  default:
    ret = kTaskSendOtherError;
    break;
  }
  LOG_INFO("send task(%s) loop(%d/%d) return = %d", merge_id.c_str(), 
    last_task_loop_-task_loop_+1, last_task_loop_, ret);
  return ret;
}

int PathTask::stop(std::string &task_id)
{
  task_id = task_result_.task_id;
  //调用终止任务的结果
  window_popup_ = 0;
  task_running_ = kRunningEnd;
  task_result_.end_time = common::get_now_time();
  task_result_.result = kTaskResultCancel;
  task_result_.info = "manual cancel task";
  #ifdef SAVE_TASK
    save_task_result(task_result_);
  #endif
  auto ret = DB->save_task_info(task_result_.result);
  task_result_ = task_result_t();
  return 0;
}

void PathTask::running_handler()
{
  using namespace std::chrono;
  is_loop_ = true;
  bool is_press = false;
  bool last_mode = false;
  while (is_loop_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));

    if (!task_result_.task_id.empty() &&
        task_result_.begin_time > 0 &&
        task_result_.end_time == 0) {
      if (manual_mode_) {
        is_press = true;
      }

      if (is_press && !manual_mode_) {
        window_popup_ = 1;
        is_press = false;
      }
    }
    emit("maual_mode", window_popup_);

    last_mode = manual_mode_;
  }
}

void PathTask::path_handler()
{
  using namespace std::chrono;
  is_loop_ = true;
  std::vector<node_t> nodes;
  int info_count = 0;

  std::vector<task_info_t> task_info;
  int ret = DB->load_task_info(task_info);
  if (ret != 0) {
    LOG_WARN("load task info error");
  } else {
    for (const auto &info : task_info) {
      if (info.id == "task_info") {
        task_info_t data = info;
        data.current_task_count = 0;
        data.current_odom = 0;
        auto ret = DB->save_task_info(data);
      }
    }
  }

  while (is_loop_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // task info
    if (duration_cast<seconds>(steady_clock::now() - total_time_).count() > 10) {
      // std::vector<task_result_t> task_lists;
      // int ret = DB->load_task_result(task_lists);
      // if (ret != 0) {
      //   LOG_WARN("load task result error");
      // }
      // int error_cnt = 0;
      // int today_cnt = 0;
      // for (auto task : task_lists) {
      //   if (task.result != 0) {
      //     error_cnt++;
      //   }
      //   if (common::is_time_today(task.end_time)) {
      //     today_cnt++;
      //   }
      // }

      std::vector<task_info_t> task_info;
      int ret = DB->load_task_info(task_info);
      if (ret != 0) {
        LOG_WARN("load task info error");
      } else {
        for (const auto &info : task_info) {
          if (info.id == "task_info") {
            task_info_.current_task_count = info.current_task_count;
            task_info_.total_task_count = info.total_task_count;
            task_info_.error_task_count = info.error_task_count;
          }
        }
      }
      task_info_.total_odom = last_odom_ + task_info_.current_odom;
      // task_info_.current_task_count = today_cnt;
      // task_info_.total_task_count = task_lists.size();
      // task_info_.error_task_count = error_cnt;
      if (!task_info_.id.empty()) {
        int ret = DB->save_task_info(task_info_);
        if (ret != 0) {
          LOG_WARN("save task info error");
        }
      }

      total_time_ = steady_clock::now();
    }

    // task info
    if (info_count++ > 3) {
      task_list_t task_list;
      std::vector<task_t> tasks;
      for(auto &task : order_task_) {
        if (task.second.build_state == kBuildEnd) {
          tasks.push_back(task.second);
        }
      }
      std::sort(tasks.begin(), tasks.end(),
        [](const task_t &s1, const task_t &s2) {
          return s1.id < s2.id;
      });
      task_list.tasks = tasks;
      task_list.task_info = task_info_;
      emit("task_info", task_list);
      info_count = 0;
    }

    // path track
    if (task_running_ > 0) {
      if (task_running_ == kRunningStart) {
        nodes.clear();
        task_running_ = kRunning;
      }
      int task_loop = 1;
      if (!last_task_id_.empty()) {
        task_loop = last_task_loop_;
      }
      state_.task_loop = task_loop-task_loop_+1;
    } else {
      state_.task_loop = 0;
    }

    emit("path_state", state_);

    if (task_running_ <= kRunningEnd) {
      continue;
    }

    if (!nodes.empty()) {
      auto node = nodes.back();
      pose_t pose = pose_;
      double dist = common::distance(node.x, node.y, pose.x, pose.y);
      if (dist < param_.sampling_delta_distance) {
        continue;
      }
    }

    node_t curr_node;
    if (sampling_ && sampling_->get_node(curr_node)) {
      nodes.emplace_back(curr_node);
    }
    state_.task_id = last_task_id_;
    state_.points = nodes;
  }
}

void PathTask::set_task_state(const task_state_t &state)
{
  task_state_ = state;
  if (!task_state_.last_node_id.empty()) {
    last_node_id_ = task_state_.last_node_id;
  }
  if (state.order_id == last_task_id_ && 
     state.last_node_id == last_end_point_.id &&
     state.agv_state == "Finishing" && 
     state.operating_mode == true ) {
    task_state_t response(state);
    response.response_finish = true;
    task_result_.end_time = common::get_now_time();
    task_result_.result = kTaskResultOk;
    task_result_.info = "";
    #ifdef SAVE_TASK
      save_task_result(task_result_);
    #endif
    auto ret = DB->save_task_info(task_result_.result);
    task_result_ = task_result_t();
    emit("task_finish", response);
    task_running_ = kRunningEnd;
    LOG_INFO("task(%s) finish", last_task_id_.c_str());
    if (task_loop_-1 > 0) {
      std::lock_guard<std::mutex> lk(update_mtx_);
      update_cv_.notify_one();
    }
  }
}

int PathTask::set_task_info(const task_info_t &info)
{
  task_info_.current_odom    = info.current_odom;
  task_info_.total_duration  = info.total_duration;
  task_info_.avoid_duration  = info.avoid_duration;

  return 0;
}

int PathTask::get_task(const std::string &id, task_t &task)
{
  int ret = 1;
  if (DB) {
    ret = DB->load_task(id, task);
  }
  return ret;
}

int PathTask::get_task(std::vector<task_t> &tasks)
{
  int ret = 1;
  if (DB) {
    ret = DB->load_task(tasks);
  }
  return ret;
}

int PathTask::update_task(const task_t &task)
{
  if (!task_state_.order_id.empty() && 
    task_state_.order_id == task.id) {
    return 10;
  }

  int ret = 1;
  if (DB) {
    task_t temp_task;
    if (order_task_.find(task.id) != order_task_.end()) {
      temp_task = order_task_[task.id];
      temp_task.name = task.name;
      temp_task.avoid_level = task.avoid_level;
      temp_task.velocity_level = task.velocity_level;
      temp_task.loop = task.loop;
      temp_task.sn = task.sn;
      if (task.avoid_level !=0 && task.velocity_level !=0) {
        for (int i=0; i < temp_task.edges.size(); i++) {
          temp_task.edges[i].avoid_level = task.avoid_level;
          temp_task.edges[i].velocity_level = task.velocity_level;
          LOG_INFO("update edge(%s) avoid:%d, vel:%d", 
                  temp_task.edges[i].id.c_str(), task.avoid_level, task.velocity_level);
        }
      }
      ret = DB->update_task(temp_task);
      if (task.avoid_level !=0 && task.velocity_level !=0) {
        path_plan_->updataEdge(task);
      }
      if (0 == ret) {
        std::lock_guard<std::mutex> lck(task_mtx_);
        order_task_[task.id] = temp_task;
      }
      LOG_INFO("update task(%s) return %d", task.id.c_str(), ret);
    }
  }
  return ret;
}

int PathTask::update_edge(const edge_t &edge)
{
  int ret = 1;
  if (DB) {
    ret = DB->update_edge(edge);
    LOG_INFO("update edge(%s) return %d", edge.id.c_str(), ret);
  }

  {
    std::string task_id = "";
    for (const auto &task : order_task_) {
      for (const auto &ed : task.second.edges) {
        if (edge.id == ed.id) {
          task_id = task.first;
          break;
        }
      }
      if (task_id != "") break;
    } 

    if (task_id != "" && (order_task_.find(task_id) != order_task_.end())) {
      for (int i=0; i<order_task_[task_id].edges.size(); i++) {
        if (edge.id == order_task_[task_id].edges[i].id) {
          order_task_[task_id].edges[i].avoid_level = edge.avoid_level;
          order_task_[task_id].edges[i].velocity_level = edge.velocity_level;
          break;
        }
      }
      path_plan_->updataEdge(order_task_[task_id], edge.id, edge.velocity_level, edge.avoid_level);
    }
  }
  
  return ret;
}

int PathTask::update_point(const node_t &node)
{
  int ret = 1;
  if (DB) {
    ret = DB->update_point(node);
    LOG_INFO("update node(%s) return %d", node.id.c_str(), ret);
  }
  return ret;
}

int PathTask::updata_storage(const kuqu_t &kuqu, const std::string &task_id) {
  int ret = 1;

  if (order_task_.find(task_id) == order_task_.end()) {
    LOG_ERROR("cant find task(%s)", task_id.c_str());
    return ret;
  }

  kuqu_t old_kuqu = order_task_[task_id].kuqu;
  if (old_kuqu.name == kuqu.name && kuqu.direction == "") {
    LOG_INFO("kuwei name is same.");
    ret = 0;
  } else {
    if (DB) {
      ret = DB->update_storage_name(kuqu.name, task_id);
      if (0 == ret) {
        std::lock_guard<std::mutex> lck(task_mtx_);
        order_task_[task_id].name = kuqu.name;
      }
      LOG_INFO("update task(%s) storage name(%s) return %d", task_id.c_str(), kuqu.name.c_str(), ret);
    }
  }

  if (kuqu.direction == "") return ret;

  if ((old_kuqu.kwsize.width == kuqu.kwsize.width) &&
      (old_kuqu.kwsize.length == kuqu.kwsize.length) &&
      (old_kuqu.kwsize.rowspace == kuqu.kwsize.rowspace) &&
      (old_kuqu.kwsize.columnspace == kuqu.kwsize.columnspace)) {
    LOG_INFO("kuwei size is same.");
    return 0;    
  }

  // task_t old_task = order_task_[task_id];
  // task_t new_task = old_task;

  // 更新库位尺寸，整体全部更新
  kuqu_t new_kuqu;
  new_kuqu = kuqu;
  new_kuqu.id = old_kuqu.id;
  new_kuqu.normal = old_kuqu.normal;
  new_kuqu.vertexs = old_kuqu.vertexs;

  Storage storage;
  // 1、计算库位点
  int res = storage.CalKuQu(new_kuqu);
  if (res != 0) {
    return res;
  }
  // 2、计算探测点
  storage.CalDetect(new_kuqu);

  // 3、删除旧数据
  if (DB) {
    DB->del_point_of_task(task_id);
    DB->del_edge_of_task(task_id);
    DB->del_traffic_of_task(task_id);
    DB->del_collision_of_task(task_id);
    DB->delKuQuVertex(old_kuqu.id);
    DB->del_kuwei(old_kuqu.id);
  }

  node_id_ = DB->get_max_node_id();
  edge_id_ = DB->get_max_edge_id();
  kuqu_id_ = DB->get_max_kuqu_id();
  LOG_INFO("load max_node_id(%d) max_edge_id(%d) max_kuqu_id(%d) ok", node_id_, edge_id_, kuqu_id_);

  // 4 构建库区路线段(探测点->库区各列)
  std::vector<node_t> nodes;
  std::vector<edge_t> edges;
  for (const auto &enter : new_kuqu.detects) {
    int column = std::stoi(enter.id);
    Point edge_start{enter.x, enter.y};
    Point edge_end;
    int max_row = 0;
    for (const auto &storage : new_kuqu.storage) {
      if (storage.column == column) {
        if (storage.row > max_row) {
          edge_end = Point{storage.center.x, storage.center.y};
          max_row = storage.row;
        }
      }
    }
    // 进入路段
    double length = sqrt(pow(edge_start.x - edge_end.x, 2) + pow(edge_start.y - edge_end.y, 2));
    double angle = storage.CalAngle(edge_start, edge_end);
    std::vector<Point> edge_points = storage.sampleLineSegment(edge_start, edge_end, 0.05);
    std::vector<node_t> enter_nodes;
    std::vector<node_t> leave_nodes;
    for (int i = 0; i < edge_points.size(); i++) {
      node_t node;
      auto time = common::get_now_time();
      node.id = std::to_string(++node_id_);
      node.name = node.id;
      node.timestamp = time;
      node.x = edge_points[i].x;
      node.y = edge_points[i].y;
      node.angle = angle;
      node.v = -1;
      if (i == 0) {
        node.node = 1;
        node.type = kNodeSampling;
        node.action = kNodeActionNone;
      }
      if (i == edge_points.size()-1) {
        node.type = kNodeAction;
        node.action = kNodeActionRest;
        node.node = kNodePosEnd;
      }
      nodes.push_back(node);
      enter_nodes.push_back(node);
    }

    edge_t edge;
    edge.id = std::to_string(++edge_id_);
    edge.name = edge.id;
    edge.avoid_level = 1;
    edge.velocity_level = 1;
    edge.start_point = enter_nodes.front();
    edge.end_point = enter_nodes.back();
    edge.points = enter_nodes;
    edge.direction = -1;
    edge.length = length;
    edges.push_back(edge);

    // 离开路段
    // nodes.clear();
    for (int i = edge_points.size()-1; i >= 0; i--) {
      node_t node;
      auto time = common::get_now_time();
      node.id = std::to_string(++node_id_);
      node.name = node.id;
      node.timestamp = time;
      node.x = edge_points[i].x;
      node.y = edge_points[i].y;
      node.angle = angle;
      node.v = 1;
      if (i == 0) {
        node.type = kNodeAction;
        node.action = kNodeActionRest;
        node.node = kNodePosEnd;
      }
      if (i == edge_points.size()-1) {
        node.node = 1;
        node.type = kNodeSampling;
        node.action = kNodeActionNone;
      }
      nodes.push_back(node);
      leave_nodes.push_back(node);
    }

    edge.id = std::to_string(++edge_id_);
    edge.name = edge.id;
    edge.avoid_level = 1;
    edge.velocity_level = 1;
    edge.start_point = leave_nodes.front();
    edge.end_point = leave_nodes.back();
    edge.points = leave_nodes;
    edge.direction = 1;
    edge.length = length;
    edges.push_back(edge);
  }

  {
    DB->update_storage_size(new_kuqu);
    DB->update_task_index(task_id, nodes.front().id, nodes.back().id);
    // new_task.nodes = nodes;
    // new_task.edges = edges;
    // new_task.start_point = nodes.front();
    // new_task.end_point = nodes.back();
    // new_task.kuqu = new_kuqu;
    std::lock_guard<std::mutex> lck(task_mtx_);
    order_task_[task_id].nodes.clear();
    order_task_[task_id].edges.clear();
    order_task_[task_id].start_point = nodes.front();
    order_task_[task_id].end_point = nodes.back();
    order_task_[task_id].nodes = nodes;
    order_task_[task_id].edges = edges;
    order_task_[task_id].kuqu = new_kuqu;
  }


  // 5 交管计算
  auto max_traffic_id = DB->get_max_traffic_id();
  auto max_collison_id = DB->get_max_collision_id();

  std::string id1 = task_id;
  task_t &new_task = order_task_[task_id];

  int base = 0;
  for (const auto &edge : new_task.edges) {
    std::vector<Vector4d> line1;
    for (const auto &node : edge.points) {
      line1.push_back(Vector4d(node.x, node.y, node.angle, node.v));
    }
    for (auto &order : order_task_) {
      if ((new_task.map_id != order.second.map_id) && (new_task.zone_id != order.second.zone_id)) {
        continue;
      }
      if (traffic_mgr_ && new_task.id != order.first) {
        std::vector<Vector4d> line2;
        std::string id2 = order.first;
        for(auto &node : order.second.nodes) {
          line2.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
        }
        std::cout << "cal traffic: " << id1 << ", " << id2 << std::endl;

        auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id++, max_traffic_id++);
        for(auto &area : areas.traffic_area) {
          if (area.first == id1) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id1;
              traffic.start_index = base;
              traffic.end_index += base;
              new_task.traffic.emplace_back(traffic);
            }
          }
          if (area.first == id2) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id2;
              order.second.traffic.emplace_back(traffic);
            }
          }
        }

        for(auto &area : areas.rough_area) {
          if (area.first == id1) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id1;
              traffic.start_index = base;
              traffic.end_index += base;
              new_task.collision.emplace_back(traffic);
            }
          }
          if (area.first == id2) {
            for(auto &item : area.second) {
              traffic_t traffic;
              transform(item, traffic);
              traffic.task_id = id2;
              order.second.collision.emplace_back(traffic);
            }
          }
        }
      }
    }
    base += line1.size();
  }

  // 6 构建拓扑地图
  if (path_plan_) {
    std::vector<task_t> tasks;
    for(auto &item : order_task_) {
      tasks.emplace_back(item.second);
    }
    is_load_graph_ok_ = path_plan_->buildTopologicalGraph(tasks);
    //存储关联点
    topology_map_t pt_map = path_plan_->getPoint2TopologyMap();
    if (0 != DB->save_topology(pt_map)) {
      LOG_ERROR("save topology point error");
      return 2;
    }
  }

  // 7 存储数据库
  if (0 != DB->update_kuqu(new_task)) {
    LOG_ERROR("save task(%s) error", new_task.id.c_str());
  } else {
    for (auto &order : order_task_) {
      if (0 != DB->save_traffic(order.second.traffic)) {
        LOG_ERROR("save task(%s) traffic error", order.first.c_str());
      }
      if (0 != DB->save_collision(order.second.collision)) {
        LOG_ERROR("save task(%s) collision error", order.first.c_str());
      }
    }
  }
  LOG_INFO("save task(%s) success", new_task.id.c_str());

  return ret;
}

int PathTask::del_task(const std::string &id)
{
  if (!task_state_.order_id.empty() && 
    task_state_.order_id == id) {
    return 10;
  }

  int ret = 1;
  try {
    if (DB) {
      ret = DB->del_task(id);
      if (0 == ret) {
        std::lock_guard<std::mutex> lck(task_mtx_);
        auto cit = order_task_.find(id);
        if (cit != order_task_.end()) {
          order_task_.erase(cit);
        }
      }
      LOG_INFO("delete task(%s) return %d", id.c_str(), ret);

      // rebuild topology
      DB->delete_topology();
      std::vector<task_t> tasks; {
        std::lock_guard<std::mutex> lck(task_mtx_);
        for(auto &item : order_task_) {
          tasks.emplace_back(item.second);
        }
      }
      is_load_graph_ok_ = path_plan_->buildTopologicalGraph(tasks);
      topology_map_t pt_map = path_plan_->getPoint2TopologyMap();
      if (0 != DB->save_topology(pt_map)) {
        LOG_ERROR("save topology point error");
      } else {
        LOG_INFO("rebuild task topology success");
      }
    }
  } catch(std::exception &e) {
    LOG_INFO("delete task(%s) exception, %s", id.c_str(), e.what());
    ret = 2;
  }
  return ret;
}

int PathTask::save_task_result(const task_result_t &task)
{
  int ret = 1;
  try {
    if (DB) {
      ret = DB->save_task_result(task);
      if (0 == ret) {
        LOG_INFO("save task(%s) result(%s) ok", task.task_id.c_str(), task.result_id.c_str());
      } else {
        LOG_ERROR("save task(%s) result(%s) error", task.task_id.c_str(), task.result_id.c_str());
      }
    }
  } catch(std::exception &e) {
    LOG_INFO("save task result(%s) exception, %s", task.task_id.c_str(), e.what());
    ret = 2;
  }
  return ret;
}

int PathTask::load_point_by_edge_id(std::vector<node_t> &node, 
                                    const std::string &id, 
                                    const int &start_index, 
                                    const int &end_index, 
                                    const bool &find_index)
{
  int ret = 1;
  try {
    if (DB) {
      ret = DB->load_point_by_edge_id(node, id, start_index, end_index, find_index);
      if (0 == ret) {
        LOG_INFO("load edge(%s) points ok", id.c_str());
      } else {
        LOG_ERROR("load edge(%s) points error", id.c_str());
      }
    }
  } catch(std::exception &e) {
    LOG_INFO("load edge(%s) exception, %s", id.c_str());
    ret = 2;
  }
  return ret;
}

int PathTask::load_traffic(const std::string &id,
                           std::vector<traffic_t> &traffic)
{
  int ret = 1;
  try {
    if (DB) {
      ret = DB->load_traffic(id, traffic);
      if (0 == ret) {
        LOG_INFO("load edge(%s) traffic ok", id.c_str());
      } else {
        LOG_ERROR("load edge(%s) traffic error", id.c_str());
      }
    }
  } catch(std::exception &e) {
    LOG_INFO("load edge(%s) exception, %s", id.c_str());
    ret = 2;
  }
  return ret;
}

int PathTask::load_kuqu(const int32_t &id, kuqu_t &kuqu)
{
  int ret = 1;
  try {
    for (const auto &task : order_task_) {
      if (task.second.kuqu_id == id) {
        kuqu = task.second.kuqu;
        return 0;
      }
    }
    LOG_ERROR("edge(%d) not found", id);

  } catch(std::exception &e) {
    LOG_INFO("load kuqu(%d) exception", id);
    ret = 2;
  }
  return ret;
}

int PathTask::reset_db()
{
  int ret = 1;
  try {
    if (nullptr == DB) {
      return 1;
    }
    // 1 备份数据库
    ret = DB->backup_db();
    LOG_INFO("backup database return %d", ret);

    // 2 清空数据库
    ret = DB->reset_db();
    if (0 == ret) {
      LOG_INFO("reset database ok");
    } else {
      LOG_ERROR("reset database error(%d)", ret);
    }

    // 3 删除文件
    std::string map_dir = "/home/" + common::get_user_name() + "/config/map/";
    std::vector<std::string> files;
    files.push_back(map_dir + "merged_graph.dot");
    files.push_back(map_dir + "virtual_graph.dot");
    for(auto &file :files) {
      common::delete_file(file);
    }

    //4 清空缓冲
    order_task_.clear();
    task_info_ = task_info_t();
    load_task_info();

  } catch(std::exception &e) {
    LOG_INFO("reset database exception, %s", e.what());
    ret = 2;
  }
  return ret;
}

int PathTask::reset_task()
{
  int ret = 1;
  try {
    if (nullptr == DB) {
      return 1;
    }
    // 0、 备份数据库
    ret = DB->backup_db();
    LOG_INFO("backup database return %d", ret);

    // 1、 清空数据库
    ret = DB->reset_task();
    if (0 != ret) {
      LOG_ERROR("reset task db error(%d)", ret);
      return ret;
    }
    LOG_INFO("reset task db ok");

    // 2、 清空交管区和冲突区
    for (auto &task : order_task_) {
      task.second.traffic.clear();
      task.second.collision.clear();
    }
    LOG_INFO("reset task buffer ok");

    // 3、 重建交管区和冲突区
    auto cit = order_task_.begin();
    for ( ; cit != order_task_.end(); cit++) {
      std::string id1 = cit->first;
      std::vector<Vector4d> line1;
      for(auto &node : cit->second.nodes) {
        line1.push_back(Vector4d(node.x, node.y, node.angle, node.v));
      }
      for (auto cit2 = std::next(cit); cit2 != order_task_.end(); cit2++) {
        if (traffic_mgr_ && cit->second.id != cit2->first) {
          std::vector<Vector4d> line2;
          std::string id2 = cit2->first;
          for(auto &node : cit2->second.nodes) {
            line2.emplace_back(Vector4d(node.x, node.y, node.angle, node.v));
          }
          auto max_traffic_id = DB->get_max_traffic_id();
          auto max_collison_id = DB->get_max_collision_id();
          auto areas = traffic_mgr_->CalTraffic(line1, line2, id1, id2, max_collison_id, max_traffic_id);
          for(auto &area : areas.traffic_area) {
            if (area.first == id1) {
              for(auto &item : area.second) {
                traffic_t traffic;
                transform(item, traffic);
                traffic.task_id = id1;
                cit->second.traffic.emplace_back(traffic);
              }
            }
            if (area.first == id2) {
              for(auto &item : area.second) {
                traffic_t traffic;
                transform(item, traffic);
                traffic.task_id = id2;
                cit2->second.traffic.emplace_back(traffic);
              }
            }
          }

          for(auto &area : areas.rough_area) {
            if (area.first == id1) {
              for(auto &item : area.second) {
                traffic_t traffic;
                transform(item, traffic);
                traffic.task_id = id1;
                cit->second.collision.emplace_back(traffic);
              }
            }
            if (area.first == id2) {
              for(auto &item : area.second) {
                traffic_t traffic;
                transform(item, traffic);
                traffic.task_id = id2;
                cit2->second.collision.emplace_back(traffic);
              }
            }
          }
        }
      }
    }
    LOG_INFO("rebuild task traffic and collision ok");

    // 4、重建拓扑地图
    std::async(std::launch::async, [&](){
      if (path_plan_) {
        std::vector<task_t> tasks;
        for(auto &item : order_task_) {
          tasks.emplace_back(item.second);
        }
        is_load_graph_ok_ = path_plan_->buildTopologicalGraph(tasks);
        //存储关联点
        topology_map_t pt_map = path_plan_->getPoint2TopologyMap();
        if (0 != DB->save_topology(pt_map)) {
          LOG_ERROR("save topology point error");
        }
      }
    });
    LOG_INFO("rebuild task topological graph ok");

    // 5、存储交管区/冲突区/拓扑点到数据库
    for (auto &order : order_task_) {
      if (0 != DB->save_traffic(order.second.traffic)) {
        LOG_ERROR("save task(%s) traffic error", order.first.c_str());
      }
      if (0 != DB->save_collision(order.second.collision)) {
        LOG_ERROR("save task(%s) collision error", order.first.c_str());
      }
    }
    LOG_INFO("save task traffic and collision to db ok");
    LOG_INFO("reset task success");
  } catch(std::exception &e) {
    LOG_INFO("reset task exception, %s", e.what());
    ret = 10;
  }
  return ret;
}

} // namespace task
} // namespace cotek