#include "task/storage.h"
#include "cotek_common/log_porting.h"
#include "task/find_neibors.h"
#include "common/math.h"

namespace cotek {
namespace task {

// 计算单位向量
static Point normalize(const Point& v) {
    double len = std::sqrt(v.x * v.x + v.y * v.y);
    if (len == 0) return {0, 0};
    return {v.x / len, v.y / len};
}

static Point normalize(const Eigen::Vector2d& v) {
    double len = std::sqrt(v.x() * v.x() + v.y() * v.y());
    if (len == 0) return {0, 0};
    return {v.x() / len, v.y() / len};
}

int Storage::orientation(Point p, Point q, Point r) {
    double val = (q.y - p.y) * (r.x - q.x) - 
                 (q.x - p.x) * (r.y - q.y);
    if (fabs(val) < 1e-9) return 0; // 共线
    return (val > 0) ? 1 : 2;       // 顺时针 or 逆时针
}

bool Storage::onSegment(Point p, Point q, Point r) {
    return std::min(p.x, r.x) <= q.x && q.x <= std::max(p.x, r.x) &&
           std::min(p.y, r.y) <= q.y && q.y <= std::max(p.y, r.y);
}

bool Storage::doIntersect(Point p1, Point q1, Point p2, Point q2) {
    int o1 = orientation(p1, q1, p2);
    int o2 = orientation(p1, q1, q2);
    int o3 = orientation(p2, q2, p1);
    int o4 = orientation(p2, q2, q1);

    if (o1 != o2 && o3 != o4) return true;

    // 特殊情况：共线但重叠
    if (o1 == 0 && onSegment(p1, p2, q1)) return true;
    if (o2 == 0 && onSegment(p1, q2, q1)) return true;
    if (o3 == 0 && onSegment(p2, p1, q2)) return true;
    if (o4 == 0 && onSegment(p2, q1, q2)) return true;

    return false;
}

bool Storage::isSimplePolygon(const std::vector<Point>& polygon) {
    int n = polygon.size();
    for (int i = 0; i < n; ++i) {
        Point a1 = polygon[i];
        Point a2 = polygon[(i + 1) % n];  // 循环边

        for (int j = i + 1; j < n; ++j) {
            // 忽略相邻边或相同边
            if (j == i || (j + 1) % n == i || (i + 1) % n == j) continue;

            Point b1 = polygon[j];
            Point b2 = polygon[(j + 1) % n];

            if (doIntersect(a1, a2, b1, b2)) {
                return false; // 有相交，说明不是简单多边形
            }
        }
    }
    return true; // 所有边都不相交，是简单多边形
}

// 判断点是否在多边形内部（射线法）
bool Storage::isPointInPolygon(Point& p, const std::vector<Point>& polygon) {
    int n = polygon.size();
    int crossing_count = 0;

    for (int i = 0; i < n; ++i) {
        Point a = polygon[i];
        Point b = polygon[(i + 1) % n];

        // 确保 a.y <= b.y
        if (a.y > b.y) std::swap(a, b);

        // 判断射线是否穿过边
        if (p.y == a.y || p.y == b.y)
            p.y += 1e-10;  // 避免与顶点重合造成的问题

        if (p.y > a.y && p.y < b.y && 
            p.x < (b.x - a.x) * (p.y - a.y) / (b.y - a.y) + a.x) {
            crossing_count++;
        }
    }

    return crossing_count % 2 == 1;
}

// 计算边 AB 的朝向多边形内部的法向量
Eigen::Vector2d Storage::inwardNormal(const Point& A, const Point& B, const std::vector<Point>& polygon) {
    // 边向量
    Eigen::Vector2d v(B.x - A.x, B.y - A.y);

    // 两个可能的法向量（单位向量）
    Eigen::Vector2d n1(-v.y(), v.x());  // 顺时针 90°
    Eigen::Vector2d n2(v.y(), -v.x());  // 逆时针 90°
    n1.normalize();
    n2.normalize();

    // 边中点
    Point mid = Point{(A.x + B.x) / 2.0, (A.y + B.y) / 2.0};

    // 偏移一点测试哪个方向在多边形内
    double offset = 0.01;
    Point test1 = mid + Point{offset * n1.x(), offset * n1.y()};
    Point test2 = mid + Point{offset * n2.x(), offset * n2.y()};

    if (isPointInPolygon(test1, polygon))
        return n1;
    else
        return n2;
}

// 判断矩形是否完全在多边形内
bool Storage::isRectangleInsidePolygon(std::vector<Point>& rect, const std::vector<Point>& polygon) {
    for (auto& pt : rect) {
        if (!isPointInPolygon(pt, polygon)) {
            return false;  // 只要一个点在外面，就返回 false
        }
    }
    return true;
}

// 沿法向量方向移动一定距离
Point Storage::moveAlongNormal(const vertex_t& p, const Eigen::Vector2d& normal, double distance) {
    // 单位化法向量
    Eigen::Vector2d unit_normal = normal.normalized();

    // 计算新坐标
    Point new_point;
    new_point.x = p.x + unit_normal.x() * distance;
    new_point.y = p.y + unit_normal.y() * distance;

    return new_point;
}

int Storage::CalKuQu(kuqu_t &kuqu) {
  std::vector<Point> kuqu_vertexs;
  std::map<std::string, Point> vertex_map;

  for (const auto &vertex : kuqu.vertexs) {
    Point point;
    point.x = vertex.x;
    point.y = vertex.y;
    kuqu_vertexs.push_back(point);
    vertex_map[vertex.id] = point;
  }

  if (!isSimplePolygon(kuqu_vertexs)) {
    LOG_ERROR("not simple polygon.");
    return 1;
  }

  std::string direction = kuqu.direction;
  size_t pos = direction.find('-');
  std::string before = "";
  std::string after = "";
  if (pos != std::string::npos) {
    before = direction.substr(0, pos);
    after = direction.substr(pos + 1);
  } else {
    LOG_ERROR("No '-' found in the direction.");
    return 2;
  }    
  int dir1 = std::stoi(before);
  int dir2 = std::stoi(after);

  Eigen::Vector2d enter_edge = Eigen::Vector2d(kuqu_vertexs[dir2].x-kuqu_vertexs[dir1].x, 
                                               kuqu_vertexs[dir2].y-kuqu_vertexs[dir1].y);

  Point A = kuqu_vertexs[dir1];
  Point B = kuqu_vertexs[dir2];
  Eigen::Vector2d normal = inwardNormal(A, B, kuqu_vertexs);
  kuqu.normal = normal;

  double width = kuqu.kwsize.width;
  double length = kuqu.kwsize.length;
  double rowspace = kuqu.kwsize.rowspace;
  double columnspace = kuqu.kwsize.columnspace;

  Point dirAB = normalize(enter_edge);
  Point dirAC = normalize(normal);
  Point center = kuqu_vertexs[dir1] + dirAB * (columnspace + width/2.0) + dirAC * (rowspace + length/2.0);
  Point P1 = center + dirAB * (-width/2.0) + dirAC * (-length/2.0);
  Point P2 = center + dirAB * (width/2.0) + dirAC * (-length/2.0);
  Point P3 = center + dirAB * (width/2.0) + dirAC * (length/2.0);
  Point P4 = center + dirAB * (-width/2.0) + dirAC * (length/2.0);
  std::vector<Point> rect = {P1, P2, P3, P4};

  bool fit = isRectangleInsidePolygon(rect, kuqu_vertexs);
  if (!fit) {
    LOG_ERROR("this kuqu is not fit.");
    return 3;
  }

  storage_t storage;
  storage.id = "1-1";
  storage.kuqu = kuqu.id;
  storage.column = 1;
  storage.row = 1;
  storage.center.x = center.x;
  storage.center.y = center.y;

  for (const auto &p : rect) {
    vertex_t vertex;
    vertex.x = p.x;
    vertex.y = p.y;
    storage.vertexs.push_back(vertex);
  }
  kuqu.storage.push_back(storage);

  for (int m = 0; m < 20; m++) {
    // 列计算
    static int column = 0;
    static int row = 0;

    for (int n = 0; n < 20; n++) {
      // 行计算
      if (column == 0 && row == 0) row = 1;
      center = kuqu_vertexs[dir1] + 
                dirAB * ((columnspace + width/2.0) + (columnspace + width) * double(column))+ 
                dirAC * ((rowspace + length/2.0) + (rowspace + length) * double (row));  
      P1 = center + dirAB * (-width/2.0) + dirAC * (-length/2.0);
      P2 = center + dirAB * (width/2.0) + dirAC * (-length/2.0);
      P3 = center + dirAB * (width/2.0) + dirAC * (length/2.0);
      P4 = center + dirAB * (-width/2.0) + dirAC * (length/2.0);
      rect.clear();
      rect = {P1, P2, P3, P4};

      bool fit = isRectangleInsidePolygon(rect, kuqu_vertexs);
      if (!fit) {
        LOG_INFO("need change column.");
        break;
      }
      row++;    

      storage.id = std::to_string(column+1) + "-" + std::to_string(row);
      storage.column = column + 1;
      storage.row = row;
      storage.center.x = center.x;
      storage.center.y = center.y;

      for (const auto &p : rect) {
        vertex_t vertex;
        vertex.x = p.x;
        vertex.y = p.y;
        storage.vertexs.push_back(vertex);
      }
      kuqu.storage.push_back(storage);       
    }

    if (row == 0) {
      column = 0;
      LOG_INFO("cal kuqu finished.");
      break;
    } else {
      row = 0;
      column ++;
    }
  }

  return 0;
}

void Storage::CalDetect(kuqu_t &kuqu) {
  double distance = 3.0;

  std::map<int, std::vector<storage_t>> lie_kw;
  for (const auto &kw : kuqu.storage) {
    lie_kw[kw.column].push_back(kw);
  }

  Eigen::Vector2d reverse_normal(-kuqu.normal.x(), -kuqu.normal.y());

  for (const auto &lie : lie_kw) {
    Point new_p;
    if (lie.second.size() > 1) {
      storage_t start;
      storage_t end;
      int max_row = 1;
      for (const auto &storage : lie.second) {
        if (storage.row > max_row) {
          max_row = storage.row;
          end = storage;
        }
        if (storage.row == 1) {
          start = storage;
        }
      }

      new_p = moveAlongNormal(start.center, reverse_normal, distance);
      new_p.x = new_p.x;
      new_p.y = new_p.y;

    } else {
      new_p = moveAlongNormal(lie.second[0].center, reverse_normal, distance);
      new_p.x = new_p.x + lie.second[0].center.x;
      new_p.y = new_p.y + lie.second[0].center.y;
    }
    std::cout << "lie: " << lie.first << " | " << new_p.x << "," << new_p.y << std::endl;

    vertex_t detect;
    detect.id = std::to_string(lie.first);
    detect.x = new_p.x;
    detect.y = new_p.y;

    kuqu.detects.push_back(detect);
  }
}

std::vector<Point> Storage::sampleLineSegment(const Point& p1, const Point& p2, double step, bool include_end) {
    std::vector<Point> sampled_points;

    // 计算线段长度
    double dx = p2.x - p1.x;
    double dy = p2.y - p1.y;
    double length = std::sqrt(dx * dx + dy * dy);

    if (length == 0 || step <= 0) {
        sampled_points.push_back(p1); // 起点即终点或非法间隔
        return sampled_points;
    }

    // 计算采样个数（不包含终点）
    int num_samples = static_cast<int>(length / step);

    // 单位方向向量
    double dir_x = dx / length;
    double dir_y = dy / length;

    // 采样
    for (int i = 0; i <= num_samples; ++i) {
        double t = i * step;
        Point pt;
        pt.x = p1.x + dir_x * t;
        pt.y = p1.y + dir_y * t;
        sampled_points.push_back(pt);
    }

    // 可选添加终点（如果没被包含且要求包含）
    if (include_end) {
        Point last = sampled_points.back();
        if (std::hypot(p2.x - last.x, p2.y - last.y) > 1e-6) {
            sampled_points.push_back(p2);
        }
    }

    return sampled_points;
}

double Storage::CalAngle(const Point& p1, const Point& p2) {
  Vector2d v = Vector2d(p1.x - p2.x, p1.y - p2.y);
  double a = std::atan2(v.y(), v.x());
  a = common::normal_theta(a);
  return a;
}

}
}