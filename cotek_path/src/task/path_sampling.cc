#include "task/path_sampling.h"
#include "common/make_thread.h"
#include "common/time.h"
#include "common/math.h"
#include "db/path_db.h"
#include "cotek_common/log_porting.h"
#include <algorithm>

namespace cotek {
namespace task {

//#define PATH_DEBUG

static std::map<std::string, std::string> kLANG = {
  {"cn", "任务"},
  {"en", "task"}
};

static std::map<std::string, std::string> kKQLANG = {
  {"cn", "库区"},
  {"en", "storage"}
};

PathSampling::PathSampling()
{
  init();
}

PathSampling::~PathSampling()
{
  deinit();
}

int PathSampling::init()
{
  using namespace std::chrono;

  if (!thr_) {
    thr_ = std::move(common::make_thread("sample", [&]() {
      is_loop_ = true;
      bool is_extend = false;
      while (is_loop_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000/param_.sampling_frequency));
        int sample_state = sample_state_;
        emit("sample_state", sample_state);
        if (task_cmd_ <= kTaskCmdNone) {
          continue;
        }

        // publish state
        if (!is_extend) {
          state_.state = task_cmd_;
          state_.allow_extend = false;
        }
        {
          //std::lock_guard<std::mutex> lck(task_mtx_);
#ifndef PATH_DEBUG
          state_.points = task_.nodes;
#else   
          std::vector<node_t> temp_pts(state_.exists);
          temp_pts.insert(temp_pts.end(), task_.nodes.begin(), task_.nodes.end());
          state_.points = temp_pts;
#endif
        }
        state_.task_id = task_.id;
        emit("path_state", state_);

        if (task_state_ >= kStorageStateStart) {
          continue;
        }

        // is preContinue or preOverride
        //is_extend = false;
        if (task_cmd_ == kTaskCmdPreContinue && task_state_ == kTaskStateNone) {
          //2.判断车是否在当前路线终止点附近指定范围内，如果是切换状态为下一个状态
          if (!task_.nodes.empty()) {
            auto node = task_.nodes.back();
            auto pose = pose_;
            double dist = common::distance(node.x, node.y, pose.x, pose.y);
            if (dist <= param_.sampling_node_nearby_distance) {
              state_.state = task_cmd_ + 1;
              state_.allow_extend = true;
              is_extend = true;
            } else {
              is_extend = false;
            }
          } else {
            is_extend = false;
          }
        } else if (task_cmd_ == kTaskCmdPreOverride && task_state_ == kTaskStateNone) {
          state_.state = task_cmd_ + 1;
          state_.allow_extend = true;
          is_extend = true;
        } else if (task_cmd_ == kTaskCmdNew || task_cmd_ == kTaskCmdNone) {
          is_extend = false;
        }

        // start then sample point
        if (task_state_ != kTaskStateStart) {
          continue;
        }

        if (!task_.nodes.empty()) {
          auto node = task_.nodes.back();
          auto pose = pose_;
          double dist = common::distance(node.x, node.y, pose.x, pose.y);
          if (dist < param_.sampling_delta_distance) {
            continue;
          }
          double delta = (common::get_now_time() - node.timestamp)*1.0/1e6;
          if (delta < param_.sampling_delta_time*1000) {
            continue;
          }
        }

        node_t node;
        if (get_node(node)) {
          node.cmd = task_cmd_;
          {
            std::lock_guard<std::mutex> lck(task_mtx_);
            edge_.points.push_back(node);
            task_.nodes.push_back(node);
          }
        }
      }
    }));
  }
  return 0;
}

int PathSampling::deinit()
{
  is_loop_ = false;
  if (thr_ && thr_->joinable()) {
    thr_->join();
  }
  thr_ = nullptr;
  return 0;
}

void PathSampling::reset(bool is_reset)
{
  edge_ = edge_t();
  edge_start_ = false;
  task_state_ = kTaskStateNone;
  if (!is_reset) {
    task_cmd_ = kTaskCmdNone;
    task_ = task_t();
    return;
  }

  if (task_cmd_ == kTaskCmdContinue) {
    task_t task;
    std::string new_id = task_.id;
    std::string orginal_id = task_.orginal_id;
    std::string name = task_.name;
    if (0 != DB->load_task(orginal_id, task)) {
      LOG_ERROR("load task(%s) from db error", orginal_id.c_str());
      return;
    } else {
      LOG_INFO("load task(%s) from db ok", orginal_id.c_str());
    }
    {
      task_ = task;
      task_.id = new_id;
      task_.name = name;
      task_.build_cmd = task_cmd_;
      task_.orginal_id = orginal_id;
      state_.allow_extend = false;
      task_cmd_ = kTaskCmdPreContinue;
    }
  } else if(task_cmd_ == kTaskCmdOverride) {
    task_t task(task_);
    task_ = task_t();
    task_.id = task.id;
    task_.name = task.name;
    task_.orginal_id = task.orginal_id;
    state_.allow_extend = false;
    task_cmd_ = kTaskCmdPreOverride;
  } else {
    task_cmd_ = kTaskCmdNone;
    task_ = task_t();
  }
}

void PathSampling::set_vel(const vel_t &vel)
{
  std::lock_guard<std::mutex> lck(vel_mtx_);
  vel_ = vel;
  //printf("%.2f %.2f %.2f %.2f\n", pose_.x, pose_.y, pose_.angle, vel.v);
}

void PathSampling::set_pose(const pose_t &pose)
{
  std::lock_guard<std::mutex> lck(pose_mtx_);
  pose_ = pose;
  //printf("%.2f %.2f %.2f\n", pose.x, pose.y, pose.angle);
}

int PathSampling::set_task_cmd(int start, int cmd, const std::string &task_name, 
                               const kuqu_t &kuqu, const std::string &task_id)
{
  std::lock_guard<std::mutex> lck(task_mtx_);
  task_time_ = std::chrono::steady_clock::now();

  switch (start)
  {
    case kTaskStateNone:
    {
      if (cmd == kTaskCmdPreContinue || cmd == kTaskCmdPreOverride) {
        reset();
        //1.从数据库检索数据到task_
        task_t task;
        if (0 != DB->load_task(task_id, task)) {
          LOG_ERROR("load task(%s) from db error", task_id.c_str());
          return 1;
        } else {
          LOG_INFO("load task(%s) from db ok", task_id.c_str());
        }
        {
          if (cmd == kTaskCmdPreOverride) {
            task_.id = common::now_time();
            task_.name = task.name;
          } else {
            task_ = task;
            task_.id = common::now_time();
            task_.name = get_name();
          }
          task_.build_cmd = cmd;
          task_.orginal_id = task_id;
        }
      }
      break;
    }
    case kTaskStateStart:
    {
      node_id_ = DB->get_max_node_id();
      edge_id_ = DB->get_max_edge_id();
      kuqu_id_ = DB->get_max_kuqu_id();
      task_.build_cmd = cmd;
      if (cmd == kTaskCmdNew || cmd == kTaskCmdOverride) {
        if (cmd == kTaskCmdNew) {
          reset();
        }
#ifdef PATH_DEBUG
        load_exist_node(cmd, start, task_id);
#endif
        node_t node;
        if (get_node(node)) {
          // node
          node.cmd = cmd;
          node.action = kNodeActionNone;
          node.node = kNodePosStart;

          // edge
          get_edge(node, edge_, true);

          // task
          if (cmd != kTaskCmdOverride) {
            task_.id = common::now_time();
            task_.name = get_name();
          }
          task_.start_point = node;
          task_.nodes.push_back(node);
          task_.velocity_level = 1;
          task_.avoid_level = 1;
          task_.loop = 1;
        }
      } else if (cmd == kTaskCmdContinue) {
#if 0
        load_exist_node(cmd, start, task_id);
#endif
        get_edge(task_.end_point, edge_, true);
      }
      break;
    }
    case kTaskStateEndSave:
    {
      if (task_.id.empty()) {
        LOG_WARN("save task(%s) error, invalid id", task_.id.c_str());
        return 2;
      }
      if (task_.nodes.size() <= param_.sampling_node_min_number) {
        LOG_WARN("save task(%s) error, points size(%d) so short", task_.id.c_str(), task_.nodes.size());
        return 3;
      }

      static auto last_save_time = common::get_now_time() - 5001;
      if (common::get_now_time() - last_save_time < 5000) {
        LOG_WARN("save task(%s) error,the time interval is too short", task_.id.c_str());
        return 4;      
      }

      std::vector<std::string> ids;
      int index = 0;
      if (0 != DB->load_taskid(ids)) {
        LOG_ERROR("load all task name error");
        return 6;
      } else {
        for (const auto &id : ids) {
          if (id == task_.id) {
            LOG_ERROR("task(%s) already saved.", id.c_str());
            return 5;
          }
        }
      }

      last_save_time = common::get_now_time();

      task_state_ = start;
      node_t node;
      if (get_node(node)) {
        // node
        node.cmd = cmd;
        node.type = kNodeAction;
        node.action = kNodeActionRest;
        node.node = kNodePosEnd;

        // edge
        get_edge(node, edge_, false);

        // task
        task_.end_point = node;
        task_.nodes.push_back(node);
        task_.edges.push_back(edge_);
        task_.actions.push_back(node);
        task_.build_cmd = cmd;
        task_.build_state = kBuildEnd;

        auto ori_edges = task_.edges;
        // get all edge when save task
        std::vector<edge_t> edges;
        if (get_edge(task_.nodes, edges)) {
          task_.edges = edges;
        }
        if (cmd == kTaskCmdContinue) {
          for (int i=0; i < ori_edges.size(); i++) {
            task_.edges[i].id = ori_edges[i].id;
            task_.edges[i].name = ori_edges[i].name;
          }
        }

        emit("edge_data", task_);
        edge_start_ = false;
        task_cmd_ = cmd;
      }
      break;
    }
    case kTaskStateEndCancel:
    {
      task_state_ = start;
      if (!task_.id.empty()) {
        task_.build_state = kBuildCancel;
        //emit("edge_data", task_);
      }
      reset(true);
      break;
    }
    case kTaskStateCancelAction:
    {
      if (task_.id.empty()) {
        LOG_WARN("cancel task(%s) action error, invalid id", task_.id.c_str());
        return 2;
      }
      std::vector<node_t> nodes = task_.nodes;
      int action_node_index = -1;
      int action_edge_index = -1;
      for (int i = task_.nodes.size()-1; i >= 0; i--) {
        if (task_.nodes[i].type == 1 && task_.nodes[i].action > 0) {
          action_node_index = i;
          task_.nodes[i].type = 0;
          task_.nodes[i].action = 0;
          break;
        }
      }

      task_state_ = kTaskStateStart;
      task_cmd_ = cmd;

      break;
    }
    case kStorageStateStart:
    {
      node_id_ = DB->get_max_node_id();
      edge_id_ = DB->get_max_edge_id();
      kuqu_id_ = DB->get_max_kuqu_id();
      task_.build_cmd = cmd;
      reset();

      node_t node;
      // task
      task_.id = common::now_time();
      task_.name = get_kuqu_name();
      task_.start_point = node;
      task_.end_point = node;
      task_.velocity_level = 1;
      task_.avoid_level = 1;
      task_.loop = 1;
      task_.kuqu.name = task_.name;
      // task_.kuqu_id = kuqu_id_+1;

      break;
    }
    case kStorageStateSave:
    {
      if (task_.id.empty()) {
        LOG_WARN("save task(%s) error, invalid id", task_.id.c_str());
        return 2;
      }
      static auto last_save_time = common::get_now_time() - 5001;
      if (common::get_now_time() - last_save_time < 5000) {
        LOG_WARN("save task(%s) error,the time interval is too short", task_.id.c_str());
        return 4;      
      }

      std::vector<std::string> ids;
      if (0 != DB->load_taskid(ids)) {
        LOG_ERROR("load all task name error");
        return 6;
      } else {
        for (const auto &id : ids) {
          if (id == task_.id) {
            LOG_ERROR("task(%s) already saved.", id.c_str());
            return 5;
          }
        }
      }

      last_save_time = common::get_now_time();

      task_.build_cmd = cmd;
      task_.build_state = kBuildEnd;
      task_.kuqu = kuqu;

      emit("storage_data", task_);

      task_state_ = start;
      edge_start_ = false;
      task_cmd_ = cmd;
      break;
    }
    default:
      break;
  }

  if (start != kTaskStateEndCancel && start != kTaskStateCancelAction) {
    task_state_ = start;
    task_cmd_ = cmd;
  }
  sample_state_ = start;

  return 0;
}

int PathSampling::set_action_cmd(int action_cmd)
{
  std::lock_guard<std::mutex> lck(task_mtx_);
  using namespace std::chrono;
  if (duration_cast<milliseconds>(steady_clock::now() - action_time_).count() < 10) {
    return false;
  }

  action_time_ = std::chrono::steady_clock::now();

  if (task_cmd_ == kTaskCmdNew || 
      task_cmd_ == kTaskCmdContinue || 
      task_cmd_ == kTaskCmdOverride) {
    node_t node;
    if (get_node(node)) {
      // node
      node.cmd = task_cmd_;
      node.type = kNodeAction;
      node.action = action_cmd;

      // task
      task_.nodes.push_back(node);

      // edge
      if (edge_start_) {
        edge_.end_point = node;
        edge_.points.push_back(node);
        edge_.direction = node.v > -0.001? 1 : -1;
        // task
        task_.edges.push_back(edge_);
        task_.actions.push_back(node);
        task_.build_state = task_state_ == kTaskStateStart? kBuildStart : kBuildNone;
        //emit("edge_data", task_);
        edge_start_ = false;
      }

      // 1.结束完的路线又重新开始新边 2. 任务点是默认新边的起点
      {
        edge_ = edge_t();
        edge_.start_point = node;
        edge_.id = std::to_string(get_edge_id());
        edge_.name = edge_.id;
        edge_.points.push_back(node);
        edge_start_ = true;
      }
    }
  }
  return 0;
}

bool PathSampling::get_node(node_t &node)
{
  auto time = common::get_now_time();
  node.id = std::to_string(get_node_id());
  node.name = node.id;
  node.timestamp = time;
  node.x = pose_.x;
  node.y = pose_.y;
  node.angle = pose_.angle;
  node.v = vel_.v;
  node.theta = vel_.angle;
  node.type = 0;
  node.action = kNodeActionNone;
  return true;
}

bool PathSampling::get_edge(const std::vector<node_t> &nodes, std::vector<edge_t> &edges, int start_id)
{
  bool ret = true;

  edge_t edge;
  bool is_first = false;
  node_t last_node;
  for (int i = start_id; i < nodes.size(); i++) {
    const node_t &node = nodes[i];
    if (i == 0 || (node.type == 1 && node.action > 0) || i == nodes.size()-1) {
      if (!is_first) {
        edge = edge_t();
        edge.start_point = node;
        edge.id = std::to_string(get_edge_id());
        edge.name = edge.id;
        edge.points.push_back(node);
        is_first = true;
      } else {
        edge.end_point = node;
        edge.points.push_back(node);
        edge.direction = node.v > -0.001? 1 : -1;
        edges.push_back(edge);
        is_first = false;

        // new edge
        edge = edge_t();
        edge.start_point = node;
        edge.id = std::to_string(get_edge_id());
        edge.name = edge.id;
        edge.points.push_back(node);
        is_first = true;
      }
    } else {
      if (is_first) {
        edge.points.push_back(node);
      }
    }
  }

  return ret;
}

bool PathSampling::get_edge(const node_t &node, edge_t &edge, bool is_start)
{
  if (is_start) {
    edge_ = edge_t();
    edge_.id = std::to_string(get_edge_id());
    edge_.name = edge_.id;
    edge_.start_point = node;
  } else {
    edge_.end_point = node;
    edge_.direction = node.v > - 0.001? 1 : -1;
  }
  edge_.points.push_back(node);
  edge_start_ = is_start;
  return true;
}

std::string PathSampling::get_name()
{
  std::string find_str = "任务";
  if (kLANG.find(PATH_ETC->get_language()) != kLANG.end()) {
    find_str = kLANG[PATH_ETC->get_language()];
  }
  std::string task_name(find_str);
  std::vector<std::string> names;
  int index = 0;
  if (0 != DB->load_task(names)) {
    LOG_ERROR("load all task name error");
  } else {
    if (!names.empty()) {
      std::sort(names.begin(), names.end(), 
        [&](const std::string &s1, const std::string &s2) {
          if (s1.find(find_str) != std::string::npos && s2.find(find_str) == std::string::npos) {
            // 如果s1中包含"task"，而s2中不包含，则s1排在s2之后
            return false;
          } else if (s1.find(find_str) == std::string::npos && s2.find(find_str) != std::string::npos) {
            // 如果s1中不包含"task"，而s2中包含，则s2排在s1之后
            return true;
         } else if (s1.find(find_str) != std::string::npos && s2.find(find_str) != std::string::npos) {
            // 如果两个字符串都包含"task"，则比较数字
            int num1 = std::stoi(s1.substr(s1.find(find_str) + find_str.length()));
            int num2 = std::stoi(s2.substr(s2.find(find_str) + find_str.length()));
            return num1 < num2;
          } else {
            // 如果两个字符串都不包含"task"，则按照正常排序规则进行排序
            return s1 < s2;
          }
        }
      );
      std::string max_task = names.back();
      int pos = max_task.find(find_str);
      if (pos != std::string::npos) {
        index = atoi((max_task.substr(pos+find_str.length())).c_str());
      }
    }
  }
  return task_name += std::to_string(index+1);
}

std::string PathSampling::get_kuqu_name()
{
  std::string find_str = "库区";
  if (kKQLANG.find(PATH_ETC->get_language()) != kKQLANG.end()) {
    find_str = kKQLANG[PATH_ETC->get_language()];
  }
  std::string kuqu_name(find_str);
  std::vector<std::string> names;
  int index = 0;
  if (0 != DB->load_kuqu(names)) {
    LOG_ERROR("load all task name error");
  } else {
    if (!names.empty()) {
      std::sort(names.begin(), names.end(), 
        [&](const std::string &s1, const std::string &s2) {
          if (s1.find(find_str) != std::string::npos && s2.find(find_str) == std::string::npos) {
            // 如果s1中包含"task"，而s2中不包含，则s1排在s2之后
            return false;
          } else if (s1.find(find_str) == std::string::npos && s2.find(find_str) != std::string::npos) {
            // 如果s1中不包含"task"，而s2中包含，则s2排在s1之后
            return true;
         } else if (s1.find(find_str) != std::string::npos && s2.find(find_str) != std::string::npos) {
            // 如果两个字符串都包含"task"，则比较数字
            int num1 = std::stoi(s1.substr(s1.find(find_str) + find_str.length()));
            int num2 = std::stoi(s2.substr(s2.find(find_str) + find_str.length()));
            return num1 < num2;
          } else {
            // 如果两个字符串都不包含"task"，则按照正常排序规则进行排序
            return s1 < s2;
          }
        }
      );
      std::string max_task = names.back();
      int pos = max_task.find(find_str);
      if (pos != std::string::npos) {
        index = atoi((max_task.substr(pos+find_str.length())).c_str());
      }
    }
  }
  return kuqu_name += std::to_string(index+1);
}

void PathSampling::load_exist_node(int cmd, int state, const std::string &task_id)
{
  if (state == kTaskStateStart && (cmd == kTaskCmdNew ||  cmd == kTaskCmdContinue || cmd == kTaskCmdOverride)) {
    std::vector<task_t> tasks;
    if (0 == DB->load_task(tasks)) {
      state_.exists.clear();
      for (auto &task: tasks) {
        if (!task_id.empty() && task.id == task_id && (cmd == kTaskCmdContinue || cmd == kTaskCmdOverride)) {
          continue;
        }
        state_.exists.insert(state_.exists.end(), task.nodes.begin(), task.nodes.end());
      }
    }
  }
}

} // namespace task
} // namespace cotek