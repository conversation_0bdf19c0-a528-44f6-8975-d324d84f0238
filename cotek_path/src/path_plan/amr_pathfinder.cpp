//
// Created by o<PERSON> on 24-4-18.
//

#include "path_plan/amr_pathfinder.h"
// #include "amr_pathfinder.h"


bool AmrPathFinder::canCombineTasks(const cotek::pose_t& curr_pos, const std::vector<std::string>& task_ids){
    // 创建初始位置
    cotek::pose_t init_pos = curr_pos;
    // 当前任务对象
    cotek::task_t curr_task;
    // 首先在allTasks中找到该任务
    for (const auto& task_id : task_ids){
        bool found = false;
        for (const auto& task : allTasks) {
            if (task.id == task_id) {
                found = true;
                curr_task = task;
                break;
            }
        }
        if (!found) {
            LOG_ERROR_STREAM("task not found in path planner: " << task_id);
            return false;
        }

        // 取出action points 直接调用单任务的路径规划
        auto path = getPath(init_pos, curr_task.actions, task_id, false);
        if (path.empty()){
            LOG_WARN_STREAM("can't find path for task: " << task_id << " in init pos: " << init_pos.x << ", " << init_pos.y << ", " << init_pos.angle);
            return false;
        }
        // 将这个任务的最后一个点作为初始位置
        init_pos.x = curr_task.nodes.back().x;
        init_pos.y = curr_task.nodes.back().y;
        init_pos.angle = curr_task.nodes.back().angle;

    }
    return true;
}

// 控制路径代价范围为 0-65535
std::pair<int16_t, std::vector<cotek::weighted_edge_t>> AmrPathFinder::getPathAndCost(const cotek::pose_t &curr_pos, const std::string &target_id, double target_yaw)
{
    // 直接调用先前的路径规划
    auto path = getPath(curr_pos, target_id, target_yaw);
    // 如果路径为空，则返回65535
    if (path.empty()){
        return std::pair<int16_t, std::vector<cotek::weighted_edge_t>>(65535, path);
    }
    // 计算路径代价
    double total_weight = 0.0;
    for (const auto& edge : path){
        total_weight += edge.weight;
        LOG_INFO("task(%s) edge(%s) weight(%f)", edge.task_id.c_str(), edge.edge_id.c_str(), edge.weight);
    }
    int16_t cost = static_cast<int16_t>(total_weight * 10);

    return std::pair<int16_t, std::vector<cotek::weighted_edge_t>>(cost, path);
}


// 如果可以直接找到原路径，直接返回结果
std::pair<bool, std::vector<cotek::weighted_edge_t>> AmrPathFinder::couldUseOriginalTaskPath(
    const cotek::pose_t& curr_pos, std::vector<cotek::node_t> targets, 
    const std::string& task_id){
    std::vector<cotek::weighted_edge_t> res;

    auto neighborPoints = startLocator.getNeighborPoints(curr_pos.x, curr_pos.y, curr_pos.angle);

    if (neighborPoints.empty()){
        LOG_INFO_STREAM("neighbor points empty !");
    }

    // 如果这个任务存在任务点，去掉在第一个任务点之后的 neighbor，因为这些从这些neighbor开始往后走不能到达第一个任务点
    // if (-1 != taskFirstActionIndexMap[task_id]){
    //     // 使用迭代器遍历并移除不满足条件的元素
    //     for (auto it = neighborPoints.begin(); it != neighborPoints.end(); ) {
    //         if (it->taskIndex > taskFirstActionIndexMap[task_id]) {
    //             // 移除元素并更新迭代器
    //             it = neighborPoints.erase(it);
    //         } else {
    //             // 如果没有删除元素，才递增迭代器
    //             ++it;
    //         }
    //     }
    // }

    // 找到的neighbor的taskIndex不能大于第一个target的index位置
    if (taskActionWithIndexMap.find(task_id) != taskActionWithIndexMap.end()){
        int firstTargetIndex = std::numeric_limits<int>::max();
        const auto& taskActionsWithIndex = taskActionWithIndexMap[task_id];
        for (const auto& node : taskActionsWithIndex){
            if (node.id == targets[0].id){
                firstTargetIndex = node.index;
            }
        }

        // 如果是重规划，意味着有些action可能已经走过了，则找到上一个已经执行过的action index，在此之前的也需要丢弃
        int lastFinishedIndex = -1;
        if (targets[0].id != taskActionsWithIndex[0].id){
            for (int i = 1; i < taskActionsWithIndex.size(); ++i){
                if (targets[0].id == taskActionsWithIndex[i].id){
                    lastFinishedIndex = taskActionsWithIndex[i-1].index;
                    break;
                }
            }
        }

        // 使用迭代器遍历并移除不满足条件的元素
        for (auto it = neighborPoints.begin(); it != neighborPoints.end(); ) {
            if (it->taskIndex > firstTargetIndex || it->taskIndex < lastFinishedIndex) {
                // 移除元素并更新迭代器
                it = neighborPoints.erase(it);
            } else {
                // 如果没有删除元素，才递增迭代器
                ++it;
            }
        }
    }

    if (neighborPoints.empty()){
        LOG_INFO_STREAM("neighbor points empty after replan index check");
    }

    // 首先找到可能的neighbor, 周围的任务边
    // auto edgeAndVertex = findFirstEdgeAndVertex(graph, neighborPoints);

    for (auto& temp : neighborPoints){
        LOG_INFO_STREAM("path plan found neighbor point's edgeId: " << temp.edgeId
            << " taskId: " << temp.taskId);
        
        // 如果第一条边的task_id为当前id
        if (temp.taskId == task_id){
            // 直接拿到这条边，以及他在原任务后面的边，组成一条路径
            if(taskPathsMap.find(task_id) != taskPathsMap.end()){
                auto oriPath = taskPathsMap.at(task_id);

                // 使用 find_if 找到第一个 edge_task_id == target 的迭代器
                auto it = std::find_if(oriPath.begin(), oriPath.end(),
                [&](const cotek::weighted_edge_t& edge) {
                    return edge.task_edge_id == temp.edgeId;
                });

                if (it != oriPath.end()) {
                    // 如果找到了，就把之前的元素都删掉
                    oriPath.erase(oriPath.begin(), it);
                }
                return std::make_pair(true, oriPath);

            }
            LOG_ERROR_STREAM("can't find task path:"<<task_id);
        }
    }

    return std::make_pair(false, res);

}

// 获取当前位置执行任务的路径代价
uint16_t AmrPathFinder::getTaskPathCost(const cotek::pose_t& curr_pos, const std::vector<cotek::node_t> targets, const std::string& task_id){
    auto path = getPath(curr_pos, targets, task_id, false);
    // 如果路径为空，则返回无穷大
    if (path.empty()) return std::numeric_limits<uint16_t>::max();

    // 计算路径代价
    double total_weight = 0.0;
    for (const auto& edge : path){
        total_weight += edge.weight;
        LOG_INFO("task(%s) edge(%s) weight(%f)", edge.task_id.c_str(), edge.edge_id.c_str(), edge.weight);
    }
    uint16_t cost = static_cast<uint16_t>(total_weight * 10);

    return cost;
}

// 连续路径规划（中途多个要经过的点）
std::vector<cotek::weighted_edge_t> AmrPathFinder::getPath(const cotek::pose_t& curr_pos, std::vector<cotek::node_t> targets, const std::string& task_id, bool replan) {
    std::vector<cotek::weighted_edge_t> res;

    if (targets.empty()) {
        LOG_ERROR_STREAM( "path plan targets is empty" );
        return {};
    }

    // 如果可以找到原路线上的起点，则不用路径规划，直接返回结果
    // if (!replan) {
        bool noNeedPathPlan = false;
        std::tie(noNeedPathPlan, res) = couldUseOriginalTaskPath(curr_pos, targets, task_id);
        if (noNeedPathPlan){
            return res;
        }
    // }

    // 开始路径规划
    // 从当前位置到第一个目标点的路径规划
    auto firstPath = getPath(curr_pos, targets.front().id, targets.front().angle);
    if (firstPath.empty()) {
        LOG_ERROR_STREAM( "path plan can't reach first targets: "<< targets.front().id);
        return {};
    }
    res.insert(res.end(), firstPath.begin(), firstPath.end());

    // 路径规划到第一个target的拓扑点，但是可能到达这个位置的边的终点不是 target（因为有合并的情况）
    // 方案1: 这个时候已经很接近了，直接插入 target
    // 方案2：修改算法，保证最后一条边的 end_point_id 一定和 target 保持一致

    // 直接拿原任务上位于 该次路径规划第一个目标点以后的边拼接
    if(taskPathsMap.find(task_id) != taskPathsMap.end()){
        auto oriPath = taskPathsMap.at(task_id);
        // 找到这条边在路径上的位置，将这条边前面的内容丢弃
        for (int i = 0; i < oriPath.size(); ++i){
            if (oriPath[i].end_point_id == targets[0].id){
                oriPath.erase(oriPath.begin(), oriPath.begin()+i+1);
            }
        }

        res.insert(res.end(), oriPath.begin(), oriPath.end());
        // return std::make_pair(true, oriPath);

    }else{
        LOG_ERROR_STREAM( "path plan dont have this task" );
    }


#if 0
    // 从第一个目标点开始，规划到后续的每一个目标点
    for (size_t i = 0; i < targets.size() - 1; ++i) {
        auto startVertexId = point2TopologicalVertexId(targets[i].id).second;
        auto endVertexId = point2TopologicalVertexId(targets[i + 1].id).second;
        auto tempPath = getTaskPriorPath(startVertexId, endVertexId,
                                          radiansToDegrees(targets[i].angle),
                                          radiansToDegrees(targets[i + 1].angle),
                                          task_id);
        if (tempPath.empty()) {
            //LOG_INFO_STREAM( "从目标点 " << i+1 << " 到目标点 " << i+2 << " 的路径规划不通" );
            LOG_INFO_STREAM( "从拓扑点 " << startVertexId << " 到拓扑点 " << endVertexId << " 的路径规划不通" );
            return {};
        }
        // 可能需要根据实际情况调整合并路径的方式
        res.insert(res.end(), tempPath.begin(), tempPath.end());
    }
#endif

    return res;
}

std::vector<cotek::weighted_edge_t> AmrPathFinder::getPath(const std::string& s, const std::string& t, double s_yaw, double t_yaw, const std::string& task_id) {
    std::vector<cotek::weighted_edge_t> res;
    // 先将拟合点id转换为拓扑点id
    if (point2TopologyMap.empty()){
        LOG_INFO_STREAM( "point2TopologyMap is empty" );
    }  
    std::string Ts = point2TopologicalVertexId(s).second;  
    std::string Tt = point2TopologicalVertexId(t).second;  

    double min_source_diff = 361.0;
    double min_target_diff = 361.0;

    // 找到虚拟地图对应的起点以及终点
    Vertex source = std::numeric_limits<unsigned long>::max(), target = std::numeric_limits<unsigned long>::max();
    try {
        for (Vertex temp : map.at(s)){
            if (anglesAllowedTurn(s_yaw, virtualGraph[temp].heading) && angleDifference(s_yaw, virtualGraph[temp].heading) < min_source_diff){
                source = temp;
                min_source_diff = angleDifference(s_yaw, virtualGraph[temp].heading);
            }
        }
        for (Vertex temp : map.at(t)){
            if (anglesAllowedTurn(t_yaw, virtualGraph[temp].heading) && angleDifference(t_yaw, virtualGraph[temp].heading) < min_target_diff
                        && task_id == virtualGraph[temp].oriTask){
                target = temp;
                min_target_diff = angleDifference(t_yaw, virtualGraph[temp].heading);
            }
        }
    }catch (const std::out_of_range& e){
        LOG_ERROR_STREAM("vertex don't exist:"<<e.what());
        return res;
    }

    // 是否找到虚拟节点
    if (source == std::numeric_limits<unsigned long>::max()){
        LOG_ERROR_STREAM("can not find virtual node for point: " << s << "(topological node: " << Ts << ")");
        return res;
    }

    if (target == std::numeric_limits<unsigned long>::max()){
        LOG_ERROR_STREAM("can not find virtual node for point: " << t << "(topological node: " << Tt << ")");
        return res;
    }

    // 找到虚拟地图上最短路径
    //std::vector<Edge> path = modifiedDijkstra(virtualGraph, source, target);
    std::vector<Edge> path = Dijkstra(virtualGraph, source, target);

    // 输出结果，任务中边的id， 以及起始，终止位置索引/id
     return getOutputPath(path);
}

inline double getOppositeAngle(double angle) {
    double oppositeAngle = angle - 180;
    if (oppositeAngle < -180) {
        oppositeAngle += 360;
    }
    return oppositeAngle;
}

void AmrPathFinder::initVirtualGraph(const Graph &g) {
    // 先清空虚拟图
    virtualGraph.clear();
    map.clear();

    // 通过迭代器遍历每一个边, 创建所有虚拟节点
    auto ei = boost::edges(g);
    for (auto edgeIt = ei.first; edgeIt != ei.second; ++edgeIt){
        // 获取当前边的属性
        //const EdgeData& edgeData = boost::get(boost::edge_bundle, g)[*edgeIt];
        const EdgeData& edgeData = g[*edgeIt];

        // 对这条边 source 和 target 可能的朝向添加虚拟节点，以及 oriTask
        addVirtualVertex(edgeData.source_id, edgeData.source_yaw, edgeData.task_id);
        addVirtualVertex(edgeData.source_id, getOppositeAngle(edgeData.source_yaw), edgeData.task_id);
        addVirtualVertex(edgeData.target_id, edgeData.target_yaw, edgeData.task_id);
        addVirtualVertex(edgeData.target_id, getOppositeAngle(edgeData.target_yaw), edgeData.task_id);

    }

    // 连接虚拟边
    for (auto edgeIt = ei.first; edgeIt != ei.second; ++edgeIt){
        // 获取当前边的属性
        const EdgeData& edgeData = boost::get(boost::edge_bundle, g)[*edgeIt];

        // 找到沿该边前进后退所对应的 target virtualNode
        Vertex forwardTarget = std::numeric_limits<unsigned long>::max(); //初始化为最大值
        Vertex backwardTarget = std::numeric_limits<unsigned long>::max();
        for (Vertex temp : map[edgeData.target_id]){
            if (anglesSimilar(virtualGraph[temp].heading, edgeData.target_yaw)){
                backwardTarget = temp;
            }else if(anglesSimilar(virtualGraph[temp].heading, getOppositeAngle(edgeData.target_yaw))){
                forwardTarget = temp;
            }
        }
        if (forwardTarget == std::numeric_limits<unsigned long>::max() || backwardTarget == std::numeric_limits<unsigned long>::max() ){
            LOG_ERROR_STREAM( "building virtual graph: can not find target virtual node for edge:" << edgeData.id);
        }

        // 连接虚拟边
        for (Vertex s : map[edgeData.source_id]){
            if (edgeData.move_type == 0 || edgeData.move_type == 2){ // 这条边可以前进
                if(anglesAllowedTurn(virtualGraph[s].heading, edgeData.source_yaw)){
                    Edge ve = boost::add_edge(s, forwardTarget, virtualGraph).first;
                    virtualGraph[ve].weight = edgeData.weight;
                    virtualGraph[ve].ori_id = edgeData.id;
                    virtualGraph[ve].task_id = edgeData.task_id;
                    virtualGraph[ve].task_edge_id = edgeData.task_edge_id;

                    // todo: 属于task_edge中的index
                    virtualGraph[ve].source_id = edgeData.source_id;
                    virtualGraph[ve].target_id = edgeData.target_id;
                    virtualGraph[ve].start_index = edgeData.start_index;
                    virtualGraph[ve].end_index = edgeData.end_index;
                    virtualGraph[ve].velocity_level = edgeData.velocity_level;
                    virtualGraph[ve].avoid_level = edgeData.avoid_level;
                    virtualGraph[ve].ori_target_id = edgeData.ori_target_id;

                }
            }

            if(edgeData.move_type == 1 || edgeData.move_type == 2){ // 这条边可以倒车
                if(anglesAllowedTurn(virtualGraph[s].heading, getOppositeAngle(edgeData.source_yaw))){
                    Edge ve = boost::add_edge(s, backwardTarget, virtualGraph).first;
                    virtualGraph[ve].weight = edgeData.weight;
                    virtualGraph[ve].ori_id = edgeData.id;
                    virtualGraph[ve].task_id = edgeData.task_id;
                    virtualGraph[ve].task_edge_id = edgeData.task_edge_id;

                    // todo: 属于task_edge中的index
                    virtualGraph[ve].source_id = edgeData.source_id;
                    virtualGraph[ve].target_id = edgeData.target_id;
                    virtualGraph[ve].start_index = edgeData.start_index;
                    virtualGraph[ve].end_index = edgeData.end_index;
                    virtualGraph[ve].velocity_level = edgeData.velocity_level;
                    virtualGraph[ve].avoid_level = edgeData.avoid_level;
                    virtualGraph[ve].ori_target_id = edgeData.ori_target_id;

                }
            }

        }

    }

}

void AmrPathFinder::addVirtualVertex(const std::string &oriId, double heading, const std::string& oriTask) {
    // 判断该节点是否已经存在，如果存在则不添加
    if(map.find(oriId) != map.end()){
        for (Vertex v : map[oriId]){
            if (anglesSimilar(virtualGraph[v].heading, heading)){
                return;
            }
        }

        //添加到 map 和 virtual graph
        Vertex v = boost::add_vertex(virtualGraph);
        virtualGraph[v].heading = heading;
        virtualGraph[v].id = oriId+"-"+std::to_string(static_cast<int>(heading));
        virtualGraph[v].oriTask = oriTask;
        virtualGraph[v].vertex_type = 2; // 虚拟节点
        //
        map[oriId].insert(v);
    }else{
        // map 里面还不存在，直接添加
        Vertex v = boost::add_vertex(virtualGraph);
        virtualGraph[v].heading = heading;
        virtualGraph[v].id = oriId+"-"+std::to_string(static_cast<int>(heading));
        virtualGraph[v].oriTask = oriTask;
        virtualGraph[v].vertex_type = 2; // 虚拟节点
        //
        map[oriId].insert(v);
    }
}

inline bool AmrPathFinder::anglesSimilar(double angle1, double angle2) const {
    // 计算两个角度的差值并取绝对值
    double diff = std::abs(angle1 - angle2);

    // 如果差值大于180，那么需要通过360减去差值来得到真正的最小差值
    if (diff > 180) {
        diff = 360 - diff;
    }

    // 判断差值是否在范围内
    return diff <= similarAngleRange;
}

bool AmrPathFinder::anglesAllowedTurn(double angle1, double angle2) const {
    // 计算两个角度的差值并取绝对值
    double diff = std::abs(angle1 - angle2);

    // 如果差值大于180，那么需要通过360减去差值来得到真正的最小差值
    if (diff > 180) {
        diff = 360 - diff;
    }

    // 判断差值是否在范围内
    return diff <= maxSteeringAngle;
}

// 函数用于计算两个已经在[-180, 180]范围内的角度之间的差异
double AmrPathFinder::angleDifference(double angle1, double angle2) {
    double diff = angle1 - angle2;
    
    // 调整差值使其在[-180, 180]范围内
    if (diff > 180.0) {
        diff -= 360.0;
    } else if (diff < -180.0) {
        diff += 360.0;
    }
    
    // 返回差值的绝对值，确保差异为正数
    return std::fabs(diff);
}

std::vector<cotek::weighted_edge_t> AmrPathFinder::getOutputPath(const std::vector<Edge> &path) {
    std::vector<cotek::weighted_edge_t> res;
    res.reserve(path.size());
    for (const auto& e : path){
        res.emplace_back(std::move(cotek::weighted_edge_t{
                virtualGraph[e].task_id,
                virtualGraph[e].id,
                virtualGraph[e].task_edge_id,
                virtualGraph[e].source_id,
                virtualGraph[e].target_id,
                virtualGraph[e].weight,
                virtualGraph[e].start_index,
                virtualGraph[e].end_index,
                virtualGraph[e].velocity_level,
                virtualGraph[e].avoid_level
        }));
    }
    return res;

}

// 静态路径规划
std::vector<cotek::weighted_edge_t> AmrPathFinder::getStaticPath(const std::string &s, const std::string &t) {
    // 根据节点id找到 vertex descriptor
    auto path = Dijkstra(graph, find_vertex_by_id(graph, s), find_vertex_by_id(graph, t));

    return getOutputPath(path);
}

Vertex AmrPathFinder::find_vertex_by_id(const Graph &g, const std::string &id) {
    std::pair<vertex_iterator, vertex_iterator> vi = vertices(g);
    for (vertex_iterator it = vi.first; it != vi.second; ++it) {
        if(g[*it].id == id) {
            return *it; // Return the vertex_descriptor for the matching id
        }
    }
    throw std::runtime_error("Vertex with the given id not found!");
}

double AmrPathFinder::getPathWeight(const std::vector<cotek::weighted_edge_t> &edges) {
    return std::accumulate(edges.begin(), edges.end(), 0.0,
                           [](double sum, const cotek::weighted_edge_t& edge) {
                               return sum + edge.weight;
                           });
}


// 这里需要去除一条拓扑边上的重复点，由于不知道
std::vector<std::pair<cotek::weighted_edge_t, VertexData>>
AmrPathFinder::findFirstEdgeAndVertex(const Graph &g, const std::vector<QueryPoint> &points) {
    std::vector<std::pair<cotek::weighted_edge_t, VertexData>> res;

    // 转换成map<拓扑边id，离边target最近的点>
//    std::unordered_map<std::string, QueryPoint> map_t;
//    for (const auto& point : points) {
//        map_t[point.edgeId] = point; // 如果edgeId不是唯一的，这将覆盖具有相同edgeId的先前点
//    }

    EdgeIterator ei, ei_end;
    for (boost::tie(ei, ei_end) = edges(graph); ei != ei_end; ++ei) {
        Edge e = *ei;
        const EdgeData& edgeData = graph[e];

        // 循环判断点是不是在这条边上
        for (const auto& point : points){
            if (point.edgeId != edgeData.task_edge_id || point.index < edgeData.start_index ||
                    point.index > edgeData.end_index){
                continue;
            }
            // 点在这条边上，记录下结果
            // 构建从这个点到拓扑点的小半条边
            // todo;这里需要重新设置数据
            double weight = edgeData.weight*(edgeData.end_index-point.index)/(edgeData.end_index-edgeData.start_index);
            cotek::weighted_edge_t temp;
            temp.task_edge_id = edgeData.task_edge_id;
            temp.weight = weight;
            temp.start_index = point.index;
            temp.end_index = edgeData.end_index;
            temp.task_id = edgeData.task_id;
            temp.velocity_level = edgeData.velocity_level;
            temp.avoid_level = edgeData.avoid_level;

            // 记录起点id以及朝向，借用这个数据结构
            VertexData tempData;
            tempData.id = edgeData.target_id;
            tempData.heading = getOppositeAngle(edgeData.target_yaw);

            res.emplace_back(temp, tempData);
        }

    }

    return res;
}

std::vector<cotek::weighted_edge_t> AmrPathFinder::getPath(cotek::pose_t s, const std::string &t, double t_yaw) {
    auto neighborPoints = startLocator.getNeighborPoints(s.x, s.y, s.angle);
    //将终点拟合点转换为拓扑点
    if (point2TopologyMap.empty()){
        LOG_INFO_STREAM( "point2TopologyMap is empty");
    }    
    std::string Tt = point2TopologicalVertexId(t).second;  

    // 用于记录最短路径
    std::pair<double, std::vector<cotek::weighted_edge_t>> shortestPath;
    shortestPath.first = std::numeric_limits<double>::max();

    // 首先找到可能的neighbor到拓扑点之间的一小段路径，以及路径规划起点
    auto edgeAndVertex = findFirstEdgeAndVertex(graph, neighborPoints);
    // 多个点尝试，找到最短路径
    for (const auto& temp : edgeAndVertex){
        // 如果路径规划起点等于终点，且满足朝向要求，则不用路径规划，
        if(temp.second.id == Tt 
            && (anglesAllowedTurn(temp.second.heading, radiansToDegrees(t_yaw)) || anglesAllowedTurn(getOppositeAngle(temp.second.heading), radiansToDegrees(t_yaw))) // 倒车到这个边的终点
            && temp.first.weight<shortestPath.first){

                shortestPath.first = temp.first.weight;
                shortestPath.second = {temp.first};
        }

        // 首先找到当前点到拓扑点之间的一小段路径
        // 调用指定朝向的路径规划
        // auto path = getTaskPriorPath(temp.second.id, Tt, temp.second.heading, radiansToDegrees(t_yaw), taskId);
        auto path = getPathWithTargetId(temp.second.id, Tt, temp.second.heading, radiansToDegrees(t_yaw), t);

        if(path.empty()) continue;
        if (getPathWeight(path)+temp.first.weight < shortestPath.first){
            // 加入路径规划起点之前的一小段路
            path.insert(path.begin(), temp.first);

            // 更新shortestPath
            shortestPath.first = getPathWeight(path)+temp.first.weight;
            shortestPath.second = path;
        }
    }

    if(shortestPath.second.empty()){
        LOG_INFO_STREAM("No path from pos: (" << s.x << s.y << ")" << "to target"
          << t << "(Topo node" << Tt << ")");
    }
    return shortestPath.second;

}

void AmrPathFinder::exportVirtualGraph (){
    std::string path = "/home/" + common::get_user_name() + "/config/map/virtual_graph.dot";
    std::ofstream dotfile;
    dotfile.open(path);

    boost::write_graphviz(dotfile, virtualGraph,
            // 顶点属性的写入函数
                          [&](std::ostream& out, const Graph::vertex_descriptor& v) {
                              //out << "[node_id=\"" << g[v].id <<
                              out << "[label=\"" << v/*virtualGraph[v].id*/ << "-"<<virtualGraph[v].id<<
                                  "\", x=\"" << virtualGraph[v].x <<
                                  "\", y=\"" << virtualGraph[v].y <<
                                  "\",vertex_type=\"" << virtualGraph[v].vertex_type <<
                                  "\",ori_task=\"" << virtualGraph[v].oriTask <<
                                  "\",heading=\"" << virtualGraph[v].heading <<
                                  "\"]";
                          },
            // 边属性的写入函数
                          [&](std::ostream& out, const Graph::edge_descriptor& e) {
                              out << "[weight=\"" << virtualGraph[e].weight <<
                                  "\", source_id=\"" << virtualGraph[e].source_id <<
                                  "\", target_id=\"" << virtualGraph[e].target_id <<
                                  "\", id=\"" << virtualGraph[e].id <<
                                  "\", task_id=\"" << virtualGraph[e].task_id <<
                                  "\", move_type=\"" << virtualGraph[e].move_type <<
                                  "\", source_yaw=\"" << virtualGraph[e].source_yaw <<
                                  "\", target_yaw=\"" << virtualGraph[e].target_yaw <<
                                  "\", task_edge_id=\"" << virtualGraph[e].task_edge_id <<
                                  "\", start_index=\"" << virtualGraph[e].start_index <<
                                  "\", end_index=\"" << virtualGraph[e].end_index <<
                                  "\", ori_target_id=\"" << virtualGraph[e].ori_target_id <<
                                  "\", velocity_level=\"" << virtualGraph[e].velocity_level <<
                                  "\", avoid_level=\"" << virtualGraph[e].avoid_level <<
                                  "\"]";
                          });

}

// 这里的点都是拓扑点，输入点要先找到对应的拓扑点
std::vector<cotek::weighted_edge_t>
AmrPathFinder::getTaskPriorPath(const std::string &s, const std::string &t, double s_yaw, double t_yaw,
                                const std::string &taskId) {
    std::vector<cotek::weighted_edge_t> res;

    // closest to source angle and target angle
    // 最近的 virtual vertex 离source 和 target 的最小角度
    double min_source_diff = 360.0;

    // 找到虚拟地图对应的起点
    Vertex source = std::numeric_limits<unsigned long>::max();
    try {
        for (Vertex temp : map.at(s)){
            if (anglesAllowedTurn(s_yaw, virtualGraph[temp].heading) && angleDifference(s_yaw, virtualGraph[temp].heading) < min_source_diff){
                source = temp;
                min_source_diff = angleDifference(s_yaw, virtualGraph[temp].heading);
            }
        }
    }catch (const std::out_of_range& e){
        LOG_ERROR_STREAM("source vertex doesn't exist:"<<e.what());
        return res;
    }

    // 检查是否找到起点虚拟节点
    if (source == std::numeric_limits<unsigned long>::max()){
        LOG_ERROR_STREAM("can not find source virtual vertex");
        return res;
    }

    // 存储所有可能的目标点，按角度差排序
    std::vector<std::pair<Vertex, double>> possible_targets;
    try {
        for (Vertex temp : map.at(t)){
            double angle_diff = angleDifference(t_yaw, virtualGraph[temp].heading);
            if (anglesAllowedTurn(t_yaw, virtualGraph[temp].heading) 
                && angle_diff <= 30.0
                && taskId == virtualGraph[temp].oriTask
            ){
                possible_targets.push_back({temp, angle_diff});
            }
        }
    }catch (const std::out_of_range& e){
        LOG_ERROR_STREAM("target vertex doesn't exist:"<<e.what());
        return res;
    }

    // 按角度差排序
    std::sort(possible_targets.begin(), possible_targets.end(),
              [](const auto& a, const auto& b) { return a.second < b.second; });

    // 如果没有找到任何可能的目标点
    if (possible_targets.empty()){
        LOG_ERROR_STREAM("can not find any valid target virtual vertex");
        return res;
    }

    // 依次尝试每个目标点进行路径规划
    for (const auto& target_pair : possible_targets) {
        Vertex target = target_pair.first;
        std::vector<Edge> path = modifiedDijkstra(virtualGraph, source, target, taskId);
        
        if (!path.empty()) {
            // 找到有效路径，返回结果
            return getOutputPath(path);
        }
    }

    // 所有可能的目标点都尝试过，但都没找到路径
    // LOG_ERROR_STREAM("no valid path found for any target virtual vertex, from "<< s << "to" << t );
    return res;
}

// 这里的点都是拓扑点，输入点要先找到对应的拓扑点
std::vector<cotek::weighted_edge_t>
AmrPathFinder::getPathWithTargetId(const std::string &s, const std::string &t, double s_yaw, double t_yaw,
                                const std::string &targetId) {
    std::vector<cotek::weighted_edge_t> res;

    // closest to source angle and target angle
    // 最近的 virtual vertex 离source 和 target 的最小角度
    double min_source_diff = 360.0;

    // 找到虚拟地图对应的起点
    Vertex source = std::numeric_limits<unsigned long>::max();
    try {
        for (Vertex temp : map.at(s)){
            if (anglesAllowedTurn(s_yaw, virtualGraph[temp].heading) && angleDifference(s_yaw, virtualGraph[temp].heading) < min_source_diff){
                source = temp;
                min_source_diff = angleDifference(s_yaw, virtualGraph[temp].heading);
            }
        }
    }catch (const std::out_of_range& e){
        LOG_ERROR_STREAM("source vertex doesn't exist:"<<e.what());
        return res;
    }

    // 检查是否找到起点虚拟节点
    if (source == std::numeric_limits<unsigned long>::max()){
        LOG_ERROR_STREAM("can not find source virtual vertex");
        return res;
    }

    // 存储所有可能的目标点，按角度差排序
    std::vector<std::pair<Vertex, double>> possible_targets;
    try {
        for (Vertex temp : map.at(t)){
            double angle_diff = angleDifference(t_yaw, virtualGraph[temp].heading);
            if (anglesAllowedTurn(t_yaw, virtualGraph[temp].heading) 
                && angle_diff <= 30.0
                // && taskId == virtualGraph[temp].oriTask
            ){
                possible_targets.push_back({temp, angle_diff});
            }
        }
    }catch (const std::out_of_range& e){
        LOG_ERROR_STREAM("target vertex doesn't exist:"<<e.what());
        return res;
    }

    // 按角度差排序
    std::sort(possible_targets.begin(), possible_targets.end(),
              [](const auto& a, const auto& b) { return a.second < b.second; });

    // 如果没有找到任何可能的目标点
    if (possible_targets.empty()){
        LOG_ERROR_STREAM("can not find any valid target virtual vertex");
        return res;
    }

    // 依次尝试每个目标点进行路径规划
    for (const auto& target_pair : possible_targets) {
        Vertex target = target_pair.first;
        std::vector<Edge> path = dijkstraWithTargetId(virtualGraph, source, target, targetId);
        
        if (!path.empty()) {
            // 找到有效路径，返回结果
            return getOutputPath(path);
        }
    }

    // 所有可能的目标点都尝试过，但都没找到路径
    // LOG_ERROR_STREAM("no valid path found for any target virtual vertex, from "<< s << "to" << t );
    return res;
}

// 计算任务连接关系
void AmrPathFinder::initTaskConnectionsMap(const std::vector<cotek::task_t>& tasks) {
    for (const auto& task : tasks) {
        std::vector<std::string> nextTasks;

        // 构造当前任务的终点位置
        cotek::pose_t endPos;
        endPos.x = task.end_point.x;
        endPos.y = task.end_point.y;
        endPos.angle = task.end_point.angle;

        // 遍历所有其他任务，检查是否可以连接
        for (const auto& otherTask : tasks) {
            // 跳过自己
            if (task.id == otherTask.id) {
                continue;
            }

            // 尝试从当前任务终点到其他任务的路径规划
            auto path = getPath(endPos, otherTask.actions, otherTask.id, false);
            if (!path.empty()) {
                // 如果能够规划出路径，说明可以连接
                nextTasks.push_back(otherTask.id);
                LOG_INFO_STREAM("Task " << task.id << " can connect to task " << otherTask.id);
            }

        }

        // 存储连接关系
        taskNextTasksMap[task.id] = nextTasks;
        LOG_INFO_STREAM("Task " << task.id << " has " << nextTasks.size() << " possible next tasks");
    }
}

// 获取指定任务终点能够执行的下一个任务列表
std::vector<std::string> AmrPathFinder::getNextTasks(const std::string& task_id) const {
    auto it = taskNextTasksMap.find(task_id);
    if (it != taskNextTasksMap.end()) {
        return it->second;
    }
    return {};
}

// 判断是否为库区任务
bool AmrPathFinder::isKuquTask(const std::string& task_id) const {
    for (const auto& task : allTasks) {
        if (task.id == task_id && task.kuqu_id != -1) {
            return true;
        }
    }
    return false;
}

// 获取库区任务的路径
std::vector<cotek::weighted_edge_t> AmrPathFinder::getKuquPath(const cotek::pose_t& curr_pos, const std::string& kuqu_id) {
    std::vector<cotek::weighted_edge_t> result;

    // 查找库区绑定信息
    auto binding_it = kuqu_bindings.find(kuqu_id);
    if (binding_it == kuqu_bindings.end() || binding_it->second.empty()) {
        LOG_ERROR_STREAM("Kuqu binding not found for kuqu_id: " << kuqu_id);
        return result;
    }

    const auto& bindings = binding_it->second;

    // 选择最优的绑定（这里简单选择第一个，实际可以根据距离等因素选择）
    const auto& best_binding = bindings[0];

    // 首先规划到库区终点节点
    auto path_to_kuqu_end = getPath(curr_pos, best_binding.end_node_id, 0.0);
    if (path_to_kuqu_end.empty()) {
        LOG_ERROR_STREAM("Cannot find path to kuqu end node: " << best_binding.end_node_id);
        return result;
    }

    // 添加到库区终点的路径
    result.insert(result.end(), path_to_kuqu_end.begin(), path_to_kuqu_end.end());

    // 添加库区内部的切割后边
    for (const auto& kuqu_edge : best_binding.kuqu_edges) {
        if (!kuqu_edge.cut_points.empty()) {
            cotek::weighted_edge_t edge;
            edge.task_id = kuqu_edge.task_id;
            edge.task_edge_id = kuqu_edge.edge_id;
            edge.edge_id = kuqu_edge.edge_id + "_cut";
            edge.start_point_id = kuqu_edge.cut_points.front().id;
            edge.end_point_id = kuqu_edge.cut_points.back().id;
            edge.start_index = 0;
            edge.end_index = kuqu_edge.cut_points.size() - 1;

            // 计算切割后边的权重
            double weight = 0.0;
            for (size_t i = 0; i < kuqu_edge.cut_points.size() - 1; ++i) {
                const auto& p1 = kuqu_edge.cut_points[i];
                const auto& p2 = kuqu_edge.cut_points[i + 1];
                weight += std::sqrt(std::pow(p2.x - p1.x, 2) + std::pow(p2.y - p1.y, 2));
            }
            edge.weight = weight;

            result.push_back(edge);
        }
    }

    LOG_INFO_STREAM("Generated kuqu path for kuqu_id " << kuqu_id << " with " << result.size()
                    << " edges using binding to topology edge " << best_binding.topology_edge_id);
    return result;
}

// 获取库区所有终点节点ID
std::vector<std::string> AmrPathFinder::getKuquEndNodes(const std::string& kuqu_id) const {
    std::vector<std::string> end_nodes;
    auto binding_it = kuqu_bindings.find(kuqu_id);
    if (binding_it != kuqu_bindings.end()) {
        for (const auto& binding : binding_it->second) {
            end_nodes.push_back(binding.end_node_id);
        }
    }
    return end_nodes;
}

// 获取库区所有绑定关系
std::vector<GraphBuilder::KuquBinding> AmrPathFinder::getKuquBindings(const std::string& kuqu_id) const {
    auto binding_it = kuqu_bindings.find(kuqu_id);
    if (binding_it != kuqu_bindings.end()) {
        return binding_it->second;
    }
    return {};
}
