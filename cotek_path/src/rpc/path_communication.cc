#include "rpc/path_communication.h"
#include "ros/ros_node.h"
#include "cotek_common/log_porting.h"
#include "var/var.h"
#include <std_msgs/String.h>
#include <nav_msgs/Odometry.h>
#include <tf/transform_broadcaster.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Int16.h>
#include <std_msgs/Int32.h>
#include "cotek_msgs/move_feedback.h"
#include "cotek_msgs/agv_position.h"
#include "cotek_msgs/std_cmd.h"
#include "cotek_msgs/task_cmd.h"
#include "cotek_msgs/task_update.h"
#include "cotek_msgs/edge_update.h"
#include "cotek_msgs/node_update.h"
#include "cotek_msgs/storage_update.h"
#include "cotek_msgs/action_command.h"
#include "cotek_msgs/learning_state.h"
#include "cotek_msgs/learning_edge.h"
#include "cotek_msgs/order.h"
#include "cotek_msgs/task_order.h"
#include "cotek_msgs/update_event.h"
#include "cotek_msgs/instant_action.h"
#include "cotek_msgs/task_info.h"
#include "cotek_msgs/odom_info.h"
#include "cotek_msgs/task_cal.h"
#include "cotek_msgs/edge_get.h"
#include "cotek_msgs/traffic_get.h"
#include "cotek_msgs/kuqu_get.h"
#include "cotek_msgs/kuqu.h"
#include "cotek_msgs/kuwei.h"
#include "cotek_common/cotek_topic_name.h"
#include "task/path_sampling.h"
#include "task/path_task.h"
#include "var/const.h"
#include "etc/path_etc.h"
#include "db/path_db.h"

namespace cotek {
namespace rpc {

static constexpr char kMoveCmdTopic[]       = "/moveCmd";
static constexpr char kMoveFeedbackTopic[]  = "/moveFeedback";
static constexpr char kMoveOdomTopic[]      = "/odom";
static constexpr char kPathStateTopic[]     = "/learningState";
static constexpr char kSampleStateTopic[]   = "/sampleState";
static constexpr char kTaskDataTopic[]      = "/task";
static constexpr char kTaskCmdTopic[]       = "/taskCmd";
static constexpr char kTaskUpdateTopic[]    = "/taskUpdate";
static constexpr char kStorageUpdateTopic[] = "/storageUpdate";
static constexpr char kEdgeUpdateTopic[]    = "/edgeUpdate";
static constexpr char kNodeUpdateTopic[]    = "/nodeUpdate";
static constexpr char kTaskOrderTopic[]     = "/taskOrder";
static constexpr char kActionCmdTopic[]     = "/actionCommand";
static constexpr char kOrderTopic[]         = "/order";
static constexpr char kAgvPositionTopic[]   = "/agvPosition";
static constexpr char kTaskEvent[]          = "/updateEvent";
static constexpr char kManualTopic[]        = "/maualMode";
static constexpr char kResetDBTopic[]       = "/resetDB";
static constexpr char kResetTaskTopic[]     = "/resetTask";
static constexpr char kLanguageTopic[]      = "/language";

static constexpr char kStartPause[]         = "startPause";
static constexpr char kStopPause[]          = "stopPause";
static constexpr char kCancelOrder[]        = "cancelOrder";

static constexpr float kMoveSpeed[] = {0.5, 0.5, 0.8, 1.2};
static constexpr int kAvoidLevel[]  = {23, 22, 23, 24};
static const char* kActionType[]    = {"",              // 0
                                       "limitLoad",     // 1
                                       "limitUnLoad",   // 2
                                       "nav",           // 3
                                       "confirm",       // 4
                                       "charge",        // 5
                                       "lieUnLoad",     // 6
                                       "openDoor",      // 7
                                       "closeDoor",     // 8
                                       "callDT",        // 9
                                       "takeDT",        // 10
                                       "leaveDT",       // 11
                                       "switchMap"};
static const char* kStartCmdError[] = {
  "task cmd success",
  "preExtend/preOverride load task from db by task id error",
  "task id invalid",
  "task size so short, must be greater than 10 points",
  "",
}; 

static const char* kStartTaskError[] = {
  "send task order success",
  "send task order's interval < 1s",                // 1: < 1s
  "send task order error: manual mode",             // 2: manual
  "send task order error: no location",             // 3: no location
  "send task order error: robot is error",          // 4: error mode
  "send task order error: not waiting",             // 5: other not support
  "",
  "",
  "",
  "",
  "send task order error: id invalid",              // 10: no find task
  "send task order error: path_plan object null or load graph failed", // 11: load graph failed
  "send task order error: path plan failed",        // 12
  "send task order error: path plan timeout 10s",   // 13
  "send task order error: path plan unknown error", // 14
};

static const char kStartTaskInfo[] = "please move robot to start position";

static void transform(const node_t &data, cotek_msgs::learning_point &msg) {
  msg.id         = data.id;
  msg.name       = data.name;
  msg.timestamp  = data.timestamp;
  msg.x          = data.x;
  msg.y          = data.y;
  msg.angle      = data.angle;
  msg.theta      = data.theta;
  msg.v          = data.v;
  msg.cmd        = data.cmd;
  msg.action     = data.action;
  msg.type       = data.type;
  msg.node       = data.node;
}

static void transform(const learning_state_t &data, cotek_msgs::learning_state &msg) {
  msg.state = data.state;
  msg.allow_extend = data.allow_extend;
  for(auto &item : data.points) {
    cotek_msgs::learning_point pt;
    transform(item, pt);
    msg.points.emplace_back(pt);
  }
  msg.task_id = data.task_id;
  msg.task_loop = data.task_loop;
}

static void transform(const node_t &data, cotek_msgs::end_point &msg) {
  msg.node_id             = data.id;
  msg.node_position.x     = data.x;
  msg.node_position.y     = data.y;
  msg.node_position.theta = data.theta;
  cotek_msgs::action action;
  action.action_id        = data.id;
  action.action_type      = kActionType[data.type == 1? data.action : 0];
  msg.actions.emplace_back(action);
}

static void transform(const node_t &data, cotek_msgs::end_point &msg, const bool &has_lie) {
  msg.node_id             = data.id;
  msg.node_position.x     = data.x;
  msg.node_position.y     = data.y;
  msg.node_position.theta = data.theta;
  cotek_msgs::action action;
  action.action_id        = data.id;
  if (!has_lie) {
    action.action_type    = kActionType[data.type == 1? data.action : 0];
  } else {
    action.action_type    = "limitUnLoad";
  }
  msg.actions.emplace_back(action);
}

static void transform(const node_t &data, cotek_msgs::node &msg, int seq, const std::string &last_id,
                      const bool &has_lie, const bool &is_end, const elevator_task_t& ele_task, 
                      const bool &has_call) {
  msg.node_id                 = data.id;
  msg.sequence_id             = seq;
  msg.node_position.x         = data.x;
  msg.node_position.y         = data.y;
  msg.node_position.theta     = data.theta;
  if (data.type == 1 && data.action != 3 && data.action != 6) {
    cotek_msgs::action action;
    action.action_id          = data.id;
    action.action_type        = kActionType[data.type == 1? data.action : 0];
    action.action_description = last_id + "-" + data.id;
    if (action.action_type == "openDoor") {
      action.action_value_list.push_back("1");
    }
    if (action.action_type == "closeDoor") {
      action.action_value_list.push_back("-1");
    }
    if (action.action_type == "callDT") {
      action.action_value_list.push_back(ele_task.cur_floor);
    }
    if (action.action_type == "takeDT") {
      action.action_value_list.push_back(ele_task.tar_floor);
    } 
    if (action.action_type == "takeDT") {
      if (has_call) msg.actions.emplace_back(action);
    } else {
      msg.actions.emplace_back(action);
    }
  }
  if (data.type == 1 && data.action == 3 && has_lie && is_end) {
    cotek_msgs::action action;
    action.action_id          = data.id;
    action.action_description = last_id + "-" + data.id;
    action.action_type = "limitUnLoad";
    msg.actions.emplace_back(action);
  }
}

static void transform(const edge_t &data, cotek_msgs::edge &msg, int seq) {

  std::vector<double> path_speed{0.5, 0.5, 0.8, 1.2};
  std::vector<int> path_avoid{23, 22, 23, 24};
  etc::path_param_t param;
  if (PATH_ETC->is_ready() && PATH_ETC->get_param(param)) {
    path_speed = param.path_speed;
    path_avoid = param.path_avoid;
  }
  msg.edge_id = data.id;
  msg.sequence_id = seq;
  cotek_msgs::end_point ep;
  transform(data.end_point, ep);
  msg.start_node_id = data.start_point.id;
  msg.end_node_id   = data.end_point.id;
  msg.direction     = std::to_string(data.direction);
  msg.max_speed     = path_speed[data.velocity_level];
  msg.avoid_map     = path_avoid[data.avoid_level];
  msg.start_index   = data.start_index;
  msg.end_index     = data.end_index;
  msg.length        = data.length;
}

static void transform(const edge_t &data, cotek_msgs::learning_edge &msg) {
  msg.id = data.id;
  msg.name = data.name;
  msg.velocity_level = data.velocity_level;
  msg.avoid_level = data.avoid_level;
  for (const auto &point : data.points) {
    cotek_msgs::point_2d pt;
    pt.x = point.x;
    pt.y = point.y;
    msg.points.push_back(pt);
  }
}

static void transform(const task_t &data, cotek_msgs::order &msg, const elevator_task_t& ele_task) {
  msg.order_id        = data.id;
  msg.task_id         = data.id;
  msg.order_update_id = 1;
  msg.zone_set_id     = "";
  msg.kuqu_id         = data.kuqu_id;
  
  bool has_lie_action = false;
  for (int index = data.nodes.size()-1; index >=0; index--) {
    auto &node = data.nodes[index];
    if (node.action == 6) {
      has_lie_action = true;
      break;
    }
  }

  cotek_msgs::end_point ep;
  transform(data.end_point, ep, has_lie_action);
  msg.end_point = ep;

  bool hascall = false;
  for (int index = 0; index < data.nodes.size(); index++) {
    bool is_end = index == data.nodes.size()-1 ? true : false; 
    auto &node = data.nodes[index];
    std::string last_id = index == 0 ? "" : data.nodes[index-1].id;
    cotek_msgs::node pt;
    if (node.action == 9) hascall = true;
    transform(node, pt, index+1, last_id, has_lie_action, is_end, ele_task, hascall);
    msg.nodes.emplace_back(pt);
  }

  std::vector<std::string> actions;
  bool insert = false;
  bool not_lie_start = false;
  bool take = false;
  for (int index = 0; index < data.edges.size(); index++) {
    auto &edge = data.edges[index];
    cotek_msgs::edge line;
    transform(edge, line, index+1);
#if 1
    auto node_cb = [&](const node_t &node, const bool &not_lie_start, 
                       const bool &is_end, const std::string &last_id = "", const bool &insert) {
      cotek_msgs::action action;
      if (node.type == 1) {
        if (insert && take) {
          action.action_id   = node.id;
          action.action_type = "initPosition";
          // todo: give target map & reloc point
          action.action_value_list.push_back("888888");
          action.action_value_list.push_back(ele_task.tar_map_id);
          action.action_value_list.push_back(std::to_string(ele_task.reloc_pose.x));
          action.action_value_list.push_back(std::to_string(ele_task.reloc_pose.y));
          action.action_value_list.push_back(std::to_string(ele_task.reloc_pose.angle));
          line.actions.emplace_back(action);
          take = false;
        }
      }
      if (node.type == 1 && node.action != 3 && node.action < 100) {
        action.action_id          = node.id;
        action.action_type        = kActionType[node.type == 1? node.action : 0];

        if (action.action_type == "limitLoad") {
          action.action_type = "forkvan";
          line.actions.emplace_back(action);
        }
        if (action.action_type == "limitUnLoad") {
          action.action_type = "forkdownvan";
          line.actions.emplace_back(action);
        }
        if (action.action_type == "lieUnLoad" && not_lie_start) {
          action.action_type = "trackColumnDown";
          line.actions.emplace_back(action);          
        }       
        if (action.action_type == "takeDT") {
          take = true;
        } 
        actions.push_back(action.action_type);

        //action.action_description = last_id + "-" + data.id;
        //line.actions.emplace_back(action);
      }
      if (node.type == 1 && node.action == 101) {
        action.action_id          = node.id;
        action.action_type        = "kuquLoadEnter";
        line.actions.emplace_back(action);  
        actions.push_back(action.action_type);
      }
      if (node.type == 1 && node.action == 102) {
        action.action_id          = node.id;
        action.action_type        = "kuquUnLoadEnter";
        line.actions.emplace_back(action);  
        actions.push_back(action.action_type);
      }
      if (node.type == 1 && node.action == 103) {
        action.action_id          = node.id;
        action.action_type        = "kuquLeave";
        line.actions.emplace_back(action);  
        actions.push_back(action.action_type);
      }
      if (node.type == 1 && node.action == 104) {
        action.action_id          = node.id;
        action.action_type        = "stablizer";
        line.actions.emplace_back(action);  
        actions.push_back(action.action_type);
      }
    };

    if (!insert) {
      for (const auto &action : actions) {
        if (action == "callDT") {
          insert = true;
          break;
        }
      }
    }

    bool is_end = index == index < data.nodes.size()-1 ? true : false; 
    node_cb(edge.start_point, false, is_end, "", insert);
    node_cb(edge.end_point, not_lie_start, is_end, "", insert);
    if (edge.end_point.type == 1 && edge.end_point.action == 6 && not_lie_start == false) {
      not_lie_start = true;
    }
    // if (not_lie_start) not_lie_start = false;
#endif
    msg.edges.emplace_back(line);
  }
}

static void transform(const task_t &data, cotek_msgs::learning_task &msg) {
  msg.task_id         = data.id;
  msg.task_name       = data.name;
  msg.start_point     = data.start_point.id;
  msg.end_point       = data.end_point.id;
  msg.velocity_level  = data.velocity_level; 
  msg.avoid_level     = data.avoid_level;
  msg.loop            = data.loop;
  msg.zone_id         = data.zone_id;
  msg.map_id          = data.map_id;
  msg.sn              = data.sn;
  msg.kuqu_id         = data.kuqu_id;

  for (int index = 0; index < data.nodes.size(); index++) {
    auto &node = data.nodes[index];
    cotek_msgs::learning_point pt;
    transform(node, pt);
    msg.points.emplace_back(pt);
  }

  for (int index = 0; index < data.actions.size(); index++) {
    auto &node = data.actions[index];
    cotek_msgs::learning_point pt;
    transform(node, pt);
    msg.actions.emplace_back(pt);
  }

  for (int index = 0; index < data.edges.size(); index++) {
    auto &edge = data.edges[index];
    cotek_msgs::learning_edge ed;
    transform(edge, ed);
    msg.edges.emplace_back(ed);
  }

  if (data.kuqu_id != -1) {
    msg.kuqu_name = data.kuqu.name;
    msg.kuqu_dir = data.kuqu.direction;
    msg.width = data.kuqu.kwsize.width;
    msg.length = data.kuqu.kwsize.length;
    msg.rowspace = data.kuqu.kwsize.rowspace;
    msg.columnspace = data.kuqu.kwsize.columnspace;
    for (int index = 0; index < data.kuqu.vertexs.size(); index++) {
      cotek_msgs::vertex vertex;
      vertex.id = data.kuqu.vertexs[index].id;
      vertex.x = data.kuqu.vertexs[index].x;
      vertex.y = data.kuqu.vertexs[index].y;
      msg.vertexs.emplace_back(vertex);
    }
    for (int index = 0; index < data.kuqu.storage.size(); index++) {
      cotek_msgs::vertex vertex;
      vertex.id = data.kuqu.storage[index].center.id;
      vertex.x = data.kuqu.storage[index].center.x;
      vertex.y = data.kuqu.storage[index].center.y;
      msg.kuwei.emplace_back(vertex);
    }    
  }
}

static void transform(const cotek_msgs::task_info &info, task_info_t &task_info) {
  task_info.current_odom_time   = info.current_odom_time;
  task_info.create_odom_time    = info.create_odom_time;
  task_info.current_odom        = info.current_odom;
  task_info.total_odom          = info.total_odom;
  task_info.total_duration      = info.total_duration; 
  task_info.avoid_duration      = info.avoid_duration; 
  task_info.current_task_count  = info.current_task_count;
  task_info.total_task_count    = info.total_task_count;
  task_info.error_task_count    = info.error_task_count;
}

static void transform(const task_info_t &info, cotek_msgs::task_info &task_info) {
  task_info.current_odom_time   = info.current_odom_time;
  task_info.create_odom_time    = info.create_odom_time;
  task_info.current_odom        = info.current_odom;
  task_info.total_odom          = info.total_odom;
  task_info.total_duration      = info.total_duration; 
  task_info.avoid_duration      = info.avoid_duration; 
  task_info.current_task_count  = info.current_task_count;
  task_info.total_task_count    = info.total_task_count;
  task_info.error_task_count    = info.error_task_count;
}

static void transform(const std::vector<task_t> &tasks, cotek_msgs::task_info &task_info) {
  for (auto &task : tasks) {
    cotek_msgs::learning_task task_msg;
    transform(task, task_msg);
    task_info.tasks.emplace_back(task_msg);
  }
}

static void transform(const task_list_t &tasks, cotek_msgs::task_info &task_info) {
  transform(tasks.tasks, task_info);
  transform(tasks.task_info, task_info);
}

PathCommuication::PathCommuication()
{

}

PathCommuication::~PathCommuication()
{
  deinit();
}

int PathCommuication::init(SamplingPtr sampling, TaskPtr task)
{
  path_sampling_ = sampling;
  path_task_ = task;

  using namespace ::cotek_msgs;
  ros_handle_ = ROS_NODE->node_handle();
  if (nullptr == ros_handle_) {
    LOG_ERROR("current ros node object is null");
    return false;
  }

  ros_pubs_[kPathStateTopic] = std::make_shared<::ros::Publisher>(
    ros_handle_->advertise<learning_state>(kPathStateTopic, 20));
  ros_pubs_[kSampleStateTopic] = std::make_shared<::ros::Publisher>(
    ros_handle_->advertise<std_msgs::Int32>(kSampleStateTopic, 20));
  ros_pubs_[cotek_topic::kTaskInfoTopic] = std::make_shared<::ros::Publisher>(
    ros_handle_->advertise<task_info>(cotek_topic::kTaskInfoTopic, 20));
  ros_pubs_[kOrderTopic] = std::make_shared<::ros::Publisher>(
    ros_handle_->advertise<cotek_msgs::order>(kOrderTopic, 20));
  ros_pubs_[cotek_topic::kTaskInstanActionTopic] = std::make_shared<::ros::Publisher>(
    ros_handle_->advertise<cotek_msgs::instant_action>(cotek_topic::kTaskInstanActionTopic, 20));
  ros_pubs_[kManualTopic] = std::make_shared<::ros::Publisher>(
        ros_handle_->advertise<std_msgs::Int32>(kManualTopic, 20));
  //1.地图接口 数据库保存和检索
  //2.重定位接口 数据库保存和检索

  //3.任务接口
  //3.1 编辑任务/删除任务
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<task_update::Request, task_update::Response>(
        kTaskUpdateTopic,
        [&](task_update::Request &req, task_update::Response &res) {
          LOG_INFO("task_update(%d): id=%s, name=%s, velocity=%d, safety=%d, loop=%d, sn=%d",
            req.update, req.task_id.c_str(), req.task_name.c_str(), req.velocity_level, 
            req.avoid_level, req.loop, req.sn);
          if (path_task_) {
            task_t task;
            task.id = req.task_id;
            task.name = req.task_name;
            task.avoid_level = req.avoid_level;
            task.velocity_level = req.velocity_level;
            task.loop = req.loop;
            task.sn = req.sn;
            int ret = 0;
            if (req.update == 0) {
              ret = path_task_->update_task(task);
            } else {
              ret = path_task_->del_task(task.id);
            }
            res.code = ret;
            res.msg = (ret == 0)? "update task success" : "update task error";
          } else {
            res.code = 1000;
            res.msg = "update task failed";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<storage_update::Request, storage_update::Response>(
        kStorageUpdateTopic,
        [&](storage_update::Request &req, storage_update::Response &res) {
          LOG_INFO("storage update.");
          if (path_task_) {
            kuqu_t kuqu;

            kwsize_t kwInfo;
            kwInfo.width = req.width;
            kwInfo.length = req.length;
            kwInfo.rowspace = req.rowspace;
            kwInfo.columnspace = req.columnspace;
            kuqu.kwsize = kwInfo;  

            kuqu.direction = req.direction;   
            kuqu.name = req.kuqu_name;
                  
            int ret = 0;
            ret = path_task_->updata_storage(kuqu, req.task_id);

            res.code = ret;
            res.msg = (ret == 0)? "update storage success" : "update storage error";
          } else {
            res.code = 1000;
            res.msg = "update storage failed";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<edge_update::Request, edge_update::Response>(
        kEdgeUpdateTopic,
        [&](edge_update::Request &req, edge_update::Response &res) {
          LOG_INFO("edges update.");
          if (path_task_) {
            int size = req.id.size();
            int ret = 0;

            for (int i=0; i<size; i++) {
              edge_t edge;
              edge.id = req.id[i];
              edge.velocity_level = req.speed[i];
              edge.avoid_level = req.safety[i];

              ret = path_task_->update_edge(edge);
              if (ret != 0) break;
            }

            res.code = ret;
            res.msg = (ret == 0)? "update edge success" : "update edge error";
          } else {
            res.code = 1000;
            res.msg = "update edge failed";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<node_update::Request, node_update::Response>(
        kNodeUpdateTopic,
        [&](node_update::Request &req, node_update::Response &res) {
          LOG_INFO("nodes update");
          if (path_task_) {
            int size = req.id.size();
            int ret = 0;

            for (int i=0; i<size; i++) {
              node_t node;
              node.id = req.id[i];
              node.action = req.type[i];

              ret = path_task_->update_point(node);
              if (ret != 0) break;
            }

            res.code = ret;
            res.msg = (ret == 0)? "update node success" : "update node error";
          } else {
            res.code = 1000;
            res.msg = "update node failed";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  //3.3 设置示教开始/结束接口（携带新建/续建/覆盖）
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<task_cmd::Request, task_cmd::Response>(
        kTaskCmdTopic,
        [&](task_cmd::Request &req, task_cmd::Response &res) {
          LOG_INFO("task_cmd: id=%s, name=%s, cmd=%d, start=%d", 
            req.task_id.c_str(), req.task_name.c_str(), req.cmd, req.start);
          if (path_sampling_) {
            kuqu_t kuqu;
            for (const auto &node : req.vertexs) {
              vertex_t vertex;
              vertex.id = node.id;
              vertex.x = node.x;
              vertex.y = node.y;
              kuqu.vertexs.push_back(vertex);
            }

            kwsize_t kwInfo;
            kwInfo.width = req.width;
            kwInfo.length = req.length;
            kwInfo.rowspace = req.rowspace;
            kwInfo.columnspace = req.columnspace;
            kuqu.kwsize = kwInfo;  

            kuqu.direction = req.direction;          

            int ret = path_sampling_->set_task_cmd(req.start, req.cmd, req.task_name, kuqu, req.task_id);
            res.code = ret;
            
            std::string lang = PATH_ETC->get_language();
            if (kCmdCode[lang].find(ret) != kCmdCode[lang].end()) {
              res.msg = kCmdCode[lang][ret];
            } else {
              res.msg = std::to_string(ret);
            }
          } else {
            res.code = 1000;
            res.msg = "task cmd error";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  //3.4 设置示教动作接口
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<action_command::Request, action_command::Response>(
        kActionCmdTopic,
        [&](action_command::Request &req, action_command::Response &res) {
          LOG_INFO("action_cmd: cmd=%d", req.cmd);
          if (path_sampling_) {
            path_sampling_->set_action_cmd(req.cmd);
            res.code = 0;
            res.msg = "action cmd success";
          } else {
            res.code = 1000;
            res.msg = "action cmd error";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  //3.5 定时发送状态数据
  if (path_sampling_) {
    path_sampling_->on("path_state", [&](const common::any &data) {
        //发送采集数据
        learning_state msg;
        auto state = common::any_cast<learning_state_t>(data);
        transform(state, msg);
        ros_pubs_[kPathStateTopic]->publish(msg);
    });

    path_sampling_->on("sample_state", [&](const common::any &data) {
        //发送采集数据
        std_msgs::Int32 msg;
        msg.data = common::any_cast<int32_t>(data);
        ros_pubs_[kSampleStateTopic]->publish(msg);
    });
  }

  //3.6 发送任务数据到决策节点
  if (path_task_) {
    path_task_->on("path_state", [&](const common::any &data) {
        //发送采集数据
        learning_state msg;
        auto state = common::any_cast<learning_state_t>(data);
        transform(state, msg);
        ros_pubs_[kPathStateTopic]->publish(msg);
    });
    path_task_->on("task_order", [&](const common::any &data) {
        //发送订单数据
        cotek_msgs::order order;
        auto task = common::any_cast<task_t>(data);
        auto elevator_task = path_task_->get_elevator_task();
        transform(task, order, elevator_task);
        ros_pubs_[kOrderTopic]->publish(order);
    });

    path_task_->on("task_finish", [&](const common::any &data) {
        //发送任务完成数据
        auto state = common::any_cast<task_state_t>(data);
        cotek_msgs::instant_action instant_action;
        cotek_msgs::action action;
        action.action_type = "finishResponse";
        action.action_value_list.emplace_back(state.order_id);
        action.action_value_list.emplace_back(state.task_id);
        instant_action.actions.emplace_back(action);
        ros_pubs_[cotek_topic::kTaskInstanActionTopic]->publish(instant_action);
    });

    path_task_->on("task_info", [&](const common::any &data) {
        //发送采集数据
        task_info msg;
        auto tasks = common::any_cast<task_list_t>(data);
        transform(tasks, msg);
        ros_pubs_[cotek_topic::kTaskInfoTopic]->publish(msg);
    });

    path_task_->on("maual_mode", [&](const common::any &data) {
        //是否弹窗
        std_msgs::Int32 msg;
        auto popup = common::any_cast<int>(data);
        msg.data = popup;
        ros_pubs_[kManualTopic]->publish(msg);
    });
  }

  //3.7 接收任务
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<task_order::Request, task_order::Response>(
      kTaskOrderTopic,
      [&](task_order::Request &req, task_order::Response &res) {
        std::string merge_id;
        for (auto id : req.task_ids) {
          merge_id += id + " ";
        }
        LOG_INFO("task_order: task_id=%s, task_ids=%d, merge_id=%s, sn=%s, cmd=%d", 
                 req.task_id.c_str(), req.task_ids.size(), merge_id.c_str(), req.sn.c_str(), req.cmd);
        bool ret = true;
        static const int kCancelTask = 2;
        static const int kContinueTask = 1;
        try {
          if (path_task_) {
            int ret = 0;
            std::string task_id;
            if (req.sn != "") {
              std::vector<std::string> tasks;
              int sn = std::stoi(req.sn);
              if (0 == DB->load_target_task_by_sn(sn, tasks)) {
                if (tasks.size() != 1) {
                  LOG_ERROR("send sn(%s) task num != 1(%d)", req.sn.c_str(), tasks.size());
                  ret = 1;     
                } else {
                  task_id = tasks.front();
                  std::vector<std::string> actions;
                  if (req.cmd != kCancelTask) {
                    ret = path_task_->start(task_id, actions, false, req.cmd);
                  } else { 
                    ret = path_task_->stop(task_id);
                  }
                }
              } else {
                LOG_ERROR("send sn(%s) can't find this task", req.sn.c_str());
                ret = 1;
              }
            } else {
              if (req.task_ids.size() < 2) {
                if (req.task_ids.size() == 1) task_id = req.task_ids[0];
                else task_id = req.task_id;
                LOG_INFO("task_order: task_id=%s", task_id.c_str());

                // task_id = req.task_id;
                if (req.cmd != kCancelTask && req.cmd != kContinueTask) {
                  ret = path_task_->start(task_id, req.actions, false, req.cmd);
                } else if (req.cmd == kContinueTask) {
                  ret = path_task_->judge();
                } else { 
                  ret = path_task_->stop(task_id);
                }
              } else {
                if (req.cmd != kCancelTask && req.cmd != kContinueTask) {
                  ret = path_task_->start(req.task_ids, req.actions, false, req.cmd);
                } else if (req.cmd == kContinueTask) { 
                  ret = path_task_->judge();
                } else { 
                  ret = path_task_->stop(task_id);
                }
              }
            }

            res.data = task_id;
            res.code = ret;
            std::string lang = PATH_ETC->get_language();
            if (kTaskCode[lang].find(ret) != kTaskCode[lang].end()) {
              res.msg = kTaskCode[lang][ret];
            } else {
              res.msg = std::to_string(ret);
            }
          } else {
            throw std::runtime_error("task object is null");
          }
        } catch(std::exception &e) {
            LOG_ERROR("path task order exception: %s", e.what());
            res.code = 1000;
            res.msg = "path task order error";
            ret = false;
        }
        return ret;
      },
      ::ros::VoidConstPtr())));

  // ros_subs_.emplace_back(
  //   std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<std_msgs::String>(
  //     kTaskOrderTopic, 20, [&](const std_msgs::String::ConstPtr &data) {
  //       if (path_task_ && data) {
  //         try {
  //           if (path_task_) {
  //             std::string task_id(data->data);
  //             int ret = path_task_->start(task_id);
  //             LOG_INFO("path task order ret = %d", ret);
  //           } else {
  //             LOG_ERROR("path task order object is null");
  //           }
  //         } catch(std::exception &e) {
  //           LOG_ERROR("path task order exception: %s", e.what());
  //         }
  //       }
  //     })));

  // 3.8 任务结束
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<cotek_msgs::instant_action>(
        cotek_topic::kTaskInstanActionTopic, 20, [&](const cotek_msgs::instant_action::ConstPtr &data) {
          for (auto &action : data->actions) {
            if (action.action_type == kStartPause) {
              // HandleStartPause(action);
            }

            if (action.action_type == kStopPause) {
              // HandleStopPause(action);
            }

            if (action.action_type == kCancelOrder) {
              std::string task_id;
              if (path_task_) {
                int ret = path_task_->stop(task_id);
                LOG_INFO("cancel path task order ret = %d", ret);
              }
            }
          }
        })));
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<cotek_msgs::update_event>(
      kTaskEvent, 20, [&](const cotek_msgs::update_event::ConstPtr &data) {
        if (path_task_ && data) {
          try {
            if (path_task_) {
              task_state_t state;
              state.time = data->time;
              state.order_id = data->order_id;
              state.task_id = data->task_id;
              state.last_node_id = data->last_node_id;
              state.agv_state = data->agv_state;
              state.operating_mode = data->operating_mode == "AUTOMATIC";

              state.mapping = data->agv_position.mapping;
              state.position_initialized = data->agv_position.position_initialized;
              state.localization_score = data->agv_position.localization_score;
              state.x = data->agv_position.x;
              state.y = data->agv_position.y;
              state.zone_id = data->agv_position.zone_id;
              state.map_id = data->agv_position.map_id;
              path_task_->set_task_state(state);
            } else {
              LOG_ERROR("path task state object is null");
            }
          } catch(std::exception &e) {
            LOG_ERROR("path task state exception: %s", e.what());
          }
        }
      })));
  // 3.9 任务统计信息
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<cotek_msgs::odom_info>(
      cotek_topic::kGlobalOdomInfoTopic, 20, [&](const cotek_msgs::odom_info::ConstPtr &data) {
        if (path_task_ && data) {
          try {
            task_info_t task_info;
            task_info.current_odom    = data->odom;
            task_info.total_duration  = data->total_duration;
            task_info.avoid_duration  = data->avoid_duration;
            path_task_->set_task_info(task_info);
          } catch(std::exception &e) {
            LOG_ERROR("set task info exception: %s", e.what());
          }
        }
      })));

  //3.10 任务执行代价计算
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<task_cal::Request, task_cal::Response>(
      "taskCal",
      [&](task_cal::Request &req, task_cal::Response &res) {
        LOG_INFO("cal task_order: task_id=%s", req.task_id.c_str());
        bool ret = true;
        try {
          if (path_task_) {
            int16_t grade = path_task_->cal_task(req.task_id);
            res.data = grade;

          } else {
            throw std::runtime_error("task object is null");
          }
        } catch(std::exception &e) {
            LOG_ERROR("path task order exception: %s", e.what());
            res.data = 65535;
        }
        return ret;
      },
      ::ros::VoidConstPtr())));

  //3.11 获取任务边点集->导航
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<edge_get::Request, edge_get::Response>(
      "edgeGet",
      [&](edge_get::Request &req, edge_get::Response &res) {
        bool ret = true;
        try {
          if (path_task_) {
            std::vector<cotek::node_t> node;
            if (path_task_->load_point_by_edge_id(
                  node, req.edge_id, req.start_index, req.end_index, req.find_index) == 0) {
              for (const auto& n : node) {
                cotek_msgs::learning_point point;
                point.id = n.id;
                point.name = n.name;
                point.v = n.v;
                point.node = n.node;
                point.cmd = n.cmd;
                point.action = n.action;
                point.type = n.type;
                point.index = n.index;
                point.x = n.x;
                point.y = n.y;
                point.theta = n.theta;
                point.angle = n.angle;
                point.timestamp = n.timestamp;
                res.points.push_back(point);
              }
              return ret;
            }
          } else {
            throw std::runtime_error("task object is null");
          }
        } catch(std::exception &e) {
            LOG_ERROR("path task order exception: %s", e.what());
        }
        return ret;
      },
      ::ros::VoidConstPtr())));

  //3.12 获取对应边上的交管信息->导航
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<traffic_get::Request, traffic_get::Response>(
      "trafficGet",
      [&](traffic_get::Request &req, traffic_get::Response &res) {
        bool ret = true;
        try {
          if (path_task_) {
            std::vector<cotek::traffic_t> traffic;
            if (path_task_->load_traffic(req.edge_id, traffic) == 0) {
              for (const auto& tr : traffic) {
                cotek_msgs::learning_traffic traffic;
                traffic.id = tr.id;
                traffic.edge_id = tr.edge_id;
                traffic.task_id = tr.task_id;
                traffic.start_index = tr.start_index;
                traffic.end_index = tr.end_index;
                res.info.push_back(traffic);
              }
              return ret;
            }
          } else {
            throw std::runtime_error("task object is null");
          }
        } catch(std::exception &e) {
            LOG_ERROR("path task order exception: %s", e.what());
        }
        return ret;
      },
      ::ros::VoidConstPtr())));

  //3.12 获取对应库区信息->导航
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<kuqu_get::Request, kuqu_get::Response>(
      "kuquGet",
      [&](kuqu_get::Request &req, kuqu_get::Response &res) {
        bool ret = true;
        try {
          if (path_task_) {
            cotek::kuqu_t kuqu;
            if (path_task_->load_kuqu(req.kuqu_id, kuqu) == 0) {
              cotek_msgs::kuqu msg;
              msg.kuqu_id = std::stoi(kuqu.id);
              msg.normal_x = kuqu.normal.x();
              msg.normal_y = kuqu.normal.y();
              for (const auto &storage : kuqu.storage) {
                cotek_msgs::kuwei kuwei;
                kuwei.id = storage.id;
                kuwei.row = storage.row;
                kuwei.column = storage.column;
                kuwei.x = storage.center.x;
                kuwei.y = storage.center.y;
                msg.kuwei.push_back(kuwei); 
              }
              for (const auto &detect : kuqu.detects) {
                cotek_msgs::vertex vertex;
                vertex.id = detect.id;
                vertex.x = detect.x;
                vertex.y = detect.y;
                msg.detect.push_back(vertex);
              }
              res.info = msg;
              
              return ret;
            }
          } else {
            throw std::runtime_error("task object is null");
          }
        } catch(std::exception &e) {
            LOG_ERROR("path task order exception: %s", e.what());
        }
        return ret;
      },
      ::ros::VoidConstPtr())));

  //4.其他接口（订阅小车反馈数据）
  //4.1 move feedback
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<move_feedback>(
      kMoveFeedbackTopic, 20, [&](const move_feedback::ConstPtr &data) {
        if (path_sampling_ && data) {
          cotek::vel_t cmd;
          cmd.v = data->velocity;
          cmd.w = data->omega;
          cmd.angle = data->angle;
          path_sampling_->set_vel(cmd);
        }
      })));

  //4.2 odom
#if 0
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<nav_msgs::Odometry>(
      kMoveOdomTopic, 20, [&](const nav_msgs::Odometry::ConstPtr &data) {
        if (path_sampling_ && data) {
          odom_t msg;
          msg.vel.v = data->twist.twist.linear.x;
          msg.vel.w = data->twist.twist.angular.z;
          msg.pose.x = data->pose.pose.position.x;
          msg.pose.y = data->pose.pose.position.y;
          msg.pose.angle = tf::getYaw(data->pose.pose.orientation);
          //path_sampling_->set_pose(msg);
          //path_task_->set_pose(msg);
        }
      })));
#endif

  // agv_position
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<agv_position>(
      kAgvPositionTopic, 20, [&](const agv_position::ConstPtr &data) {
        if (path_sampling_ && data) {
          pose_t msg;
          msg.x = data->x;
          msg.y = data->y;
          msg.angle = data->theta;
          path_sampling_->set_pose(msg);
          path_task_->set_pose(msg);
        }
      })));

  // manual
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<std_msgs::Int32>(
      "manual", 20, [&](const std_msgs::Int32::ConstPtr &data) {
        if (path_task_ && data) {
          path_task_->set_manual(data->data);
        }
      })));
  
  // language
  ros_subs_.emplace_back(
    std::make_shared<::ros::Subscriber>(ros_handle_->subscribe<std_msgs::String>(
      kLanguageTopic, 10, [&](const std_msgs::String::ConstPtr &data) {
        if (PATH_ETC && data) {
          std::string language = PATH_ETC->get_language();
          if (language != data->data) {
            PATH_ETC->set_language(data->data);
            LOG_INFO("language changed: %s", data->data.c_str());
          }
        }
      })));

  // reset database
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<std_cmd::Request, std_cmd::Response>(
        kResetDBTopic,
        [&](std_cmd::Request &req, std_cmd::Response &res) {
          LOG_INFO("reset_db: cmd=%d", req.cmd);
          if (path_task_) {
            int ret = path_task_->reset_db();
            res.code = ret;
            res.msg = (ret == 0)? "reset db success" : "reset db failed";
          } else {
            res.code = 1000;
            res.msg = "reset db error";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  // reset traffic and collision
  ros_srvs_.push_back(std::make_shared<::ros::ServiceServer>(
    ros_handle_->advertiseService<std_cmd::Request, std_cmd::Response>(
        kResetTaskTopic,
        [&](std_cmd::Request &req, std_cmd::Response &res) {
          LOG_INFO("reset_task: cmd=%d", req.cmd);
          if (path_task_) {
            int ret = path_task_->reset_task();
            res.code = ret;
            res.msg = (ret == 0)? "reset task success" : "reset task failed";
          } else {
            res.code = 1000;
            res.msg = "reset db error";
          }
          return true;
        },
        ::ros::VoidConstPtr())));

  return 0;
}

int PathCommuication::deinit()
{
  return 0;
}

} // namespace rpc
} // namespace cotek