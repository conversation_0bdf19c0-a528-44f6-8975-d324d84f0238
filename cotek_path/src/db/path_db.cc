#include "db/path_db.h"
#include <memory>
#include <tuple>
#include <algorithm>
#include <fstream>
#include "common/chcwd.h"
#include "common/time.h"
#include "db/path_data.h"
#include "cotek_common/db/cotek_db.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include <unordered_set>
#include <thread>

namespace cotek {
namespace db {

using json = nlohmann::json;
using namespace ::sqlite_orm;

static constexpr char kPathDB[] = "cotek_path.db";
static constexpr char kPathKuQu[] = "kuqu.json";

//#define LOG_ERROR printf

void transform(const edge_t &edge, db_edge_t &data) {
  data.id             = edge.id;
  data.name           = edge.name;
  data.start_point    = edge.start_point.id;
  data.end_point      = edge.end_point.id;
  data.direction      = edge.direction;
  data.avoid_level    = edge.avoid_level;
  data.velocity_level = edge.velocity_level;
  data.length         = edge.length;
  data.column         = edge.column;
}

void transform(const db_edge_t &data, edge_t &edge) {
  edge.id             = data.id;
  edge.name           = data.name;
  edge.start_point.id = data.start_point;
  edge.end_point.id   = data.end_point;
  edge.direction      = data.direction;
  edge.avoid_level    = data.avoid_level;
  edge.velocity_level = data.velocity_level;
  edge.length         = data.length;
}

void transform(const traffic_t &data, db_traffic_t &db) {
  if (data.poly.size() != 2) {
    return;
  }
  db.task_id          = data.task_id;
  db.traffic_id       = data.id;
  db.x1               = data.poly[0].x;
  db.y1               = data.poly[0].y;
  db.theta1           = data.poly[0].angle;
  db.x2               = data.poly[1].x;
  db.y2               = data.poly[1].y;
  db.theta2           = data.poly[1].angle;
  db.traffic          = data.traffic;
  db.start_index      = data.start_index;
  db.end_index        = data.end_index;
}

void transform(const db_traffic_t &db, traffic_t &data) {
  data.task_id        = db.task_id;
  data.id             = db.traffic_id;
  data.traffic        = db.traffic;
  pose_t pt1, pt2;
  pt1.x               = db.x1;
  pt1.y               = db.y1;
  pt1.angle           = db.theta1;
  pt2.x               = db.x2;
  pt2.y               = db.y2;
  pt2.angle           = db.theta2;
  data.poly.clear();
  data.poly.emplace_back(pt1);
  data.poly.emplace_back(pt2);
  data.start_index    = db.start_index;
  data.end_index      = db.end_index;
}

void transform(const task_t &task, db_task_t &data) {
  data.id             = task.id;
  data.name           = task.name;
  data.priority       = task.priority;
  data.start_point    = task.start_point.id;
  data.end_point      = task.end_point.id;
  data.velocity_level = task.velocity_level;
  data.avoid_level    = task.avoid_level;
  data.loop           = task.loop;
  data.zone_id        = task.zone_id;
  data.map_id         = task.map_id;
  data.sn             = task.sn;
  data.kuqu           = task.kuqu_id;
}

void transform(const db_task_t &task, task_t &data) {
  data.id             = task.id;
  data.name           = task.name;
  data.priority       = task.priority;
  data.start_point.id = task.start_point;
  data.end_point.id   = task.end_point;
  data.velocity_level = task.velocity_level;
  data.avoid_level    = task.avoid_level;
  data.loop           = task.loop;
  data.zone_id        = task.zone_id;
  data.map_id         = task.map_id;
  data.sn             = task.sn;
  data.kuqu_id        = task.kuqu;
}

void transform(const db_kuqu_t &db, kuqu_t &data) {
  data.id = db.kuqu_id;
  data.name = db.name;
  data.direction = db.direction;
  data.normal = Eigen::Vector2d(db.normal_x, db.normal_y);
  data.kwsize.width = db.width;
  data.kwsize.length = db.length;
  data.kwsize.rowspace = db.rowspace;
  data.kwsize.columnspace = db.columnspace;
  // data.start_kuwei = db.start_kuwei;
  // data.end_kuwei = db.end_kuwei;
}

void transform(const kuqu_t &data, db_kuqu_t &db) {
  db.kuqu_id = data.id;
  db.name = data.name;
  db.direction = data.direction;
  db.normal_x = data.normal.x();
  db.normal_y = data.normal.y();
  db.width = data.kwsize.width;
  db.length = data.kwsize.length;
  db.rowspace = data.kwsize.rowspace;
  db.columnspace = data.kwsize.columnspace;
  // db.start_kuwei = data.start_kuwei;
  // db.end_kuwei = data.end_kuwei;
}

void transform(const db_storage_t &db, storage_t &data) {
  data.id = db.kuwei_id;
  data.kuqu = db.kuqu;
  data.row = db.row;
  data.column = db.column;
  data.center = vertex_t{"", db.center_x, db.center_y};
  data.vertexs.push_back(vertex_t{"", db.x1, db.y1});
  data.vertexs.push_back(vertex_t{"", db.x2, db.y2});
  data.vertexs.push_back(vertex_t{"", db.x3, db.y3});
  data.vertexs.push_back(vertex_t{"", db.x4, db.y4});
}

void transform(const storage_t &data, db_storage_t &db) {
  db.kuwei_id = data.id;
  db.kuqu = data.kuqu;
  db.row = data.row;
  db.column = data.column; 
  db.center_x = data.center.x;
  db.center_y = data.center.y;
  db.x1 = data.vertexs[0].x;
  db.y1 = data.vertexs[0].y;
  db.x2 = data.vertexs[1].x;
  db.y2 = data.vertexs[1].y;
  db.x3 = data.vertexs[2].x;
  db.y3 = data.vertexs[2].y;
  db.x4 = data.vertexs[3].x;
  db.y4 = data.vertexs[3].y;
}

bool saveKuquVertex(const kuqu_t &data) {
  std::string content = "/home/" + common::get_user_name() + "/config/map/" + kPathKuQu;

  // 打开输入文件
  std::ifstream input_file(content);
  if (!input_file.is_open()) {
    LOG_ERROR_STREAM("Failed to open " << content);
    return 1;
  }

  // 解析 JSON
  json root;
  input_file >> root;
  input_file.close(); // 读取完成后关闭

  // 删除已有相同 ID 的对象（如存在）
  if (root.contains("list") && root["list"].is_array()) {
    for (size_t i = 0; i < root["list"].size(); ++i) {
      if (root["list"][i].contains("id") && root["list"][i]["id"] == data.id) {
        root["list"].erase(i);
        LOG_INFO("kuqu(%s) already exists. overwrite it.", data.id.c_str());
        // break; // 如果 ID 唯一，删一次即可
      }
    }
  }

  // 构造要添加的新 polygon 对象
  json new_polygon;
  json vertex = json::array();
  json detect = json::array();
  for (const auto &p : data.vertexs) {
    vertex.push_back({{"id", p.id}, {"x", p.x}, {"y", p.y}});
  }
  for (const auto &p : data.detects) {
    detect.push_back({{"column", p.id}, {"x", p.x}, {"y", p.y}});
  }
  new_polygon["id"] = data.id;
  new_polygon["vertex"] = vertex;
  new_polygon["detect"] = detect;

  // 添加到 list 中
  root["list"].push_back(new_polygon);

  // 保存到输出文件
  std::ofstream output_file(content);
  if (!output_file.is_open()) {
    LOG_ERROR_STREAM("Failed to open " << content);
    return 1;
  }

  output_file << root.dump(4);  // 使用缩进格式
  output_file.close();

  LOG_INFO("save kuqu(%s) vertexs finished.", data.id.c_str());
  return 0;
}

bool loadKuquVertex(const std::string &id, kuqu_t &kuqu) {
  std::string content = "/home/" + common::get_user_name() + "/config/map/" + kPathKuQu;

  // 打开输入文件
  std::ifstream input_file(content);
  if (!input_file.is_open()) {
    LOG_ERROR_STREAM("Failed to open " << content);
    return 1;
  }

  nlohmann::ordered_json json = nlohmann::ordered_json::parse(input_file);  
  input_file.close();

  if (json.contains("list")) {
    for (const auto& item : json["list"]) {
      if (item["id"] == id) {
        // 获取 vertex 数组
        std::vector<vertex_t> vertexs;
        std::vector<vertex_t> detects;
        const auto& points = item["vertex"];
        for (const auto& point : points) {
          vertex_t vertex;
          vertex.id = point["id"].get<std::string>();
          vertex.x = point["x"].get<double>();
          vertex.y = point["y"].get<double>();
          vertexs.push_back(vertex);
        }
        const auto& datas = item["detect"];
        for (const auto& data : datas) {
          vertex_t vertex;
          vertex.id = data["column"].get<std::string>();
          vertex.x = data["x"].get<double>();
          vertex.y = data["y"].get<double>();
          detects.push_back(vertex);
        }
        kuqu.detects = detects;
        kuqu.vertexs = vertexs;

        break; // 找到即退出
      }
    }
  }

  LOG_INFO("load kuqu(%s) vertexs finished.", id.c_str());  
  return 0;
}

bool PathDB::delKuQuVertex(const std::string &id) {
  std::string content = "/home/" + common::get_user_name() + "/config/map/" + kPathKuQu;

  // 打开输入文件
  std::ifstream input_file(content);
  if (!input_file.is_open()) {
    LOG_ERROR_STREAM("Failed to open " << content);
    return 1;
  }
  nlohmann::ordered_json json = nlohmann::ordered_json::parse(input_file);  
  input_file.close();

  // 删除对应 id 的对象
  auto& list = json["list"];
  for (auto it = list.begin(); it != list.end(); ) {
    if ((*it)["id"] == id) {
      it = list.erase(it);  // 删除并返回下一个迭代器
    } else {
      ++it;
    }
  }  

  // 保存到输出文件
  std::ofstream output_file(content);
  if (!output_file.is_open()) {
    LOG_ERROR_STREAM("Failed to open " << content);
    return 1;
  }

  output_file << json.dump(4);  // 使用缩进格式
  output_file.close();

  LOG_INFO("del kuqu(%s) vertexs finished.", id.c_str());  
  return 0;
}

using Storage = decltype(common::init_storage(""));
static std::unique_ptr<Storage> storage_{nullptr};

PathDB::PathDB()
{ 
  init();
}

PathDB::~PathDB()
{ 
  deinit();
}

int PathDB::init()
{
  try {
    std::string path = "/home/" + common::get_user_name() + "/config/map/" + kPathDB;
    storage_ = std::make_unique<Storage>(common::init_storage(path));
    storage_->sync_schema();
    storage_->pragma.journal_mode(journal_mode::WAL);
    storage_->pragma.busy_timeout(5000);
    is_ready_ = true;
  } catch (std::exception &e) {
    LOG_ERROR("create db exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::deinit()
{ 
  return 0;
}

int PathDB::save_task(const task_t &task)
{
  int ret = 0;
  try {
    db_task_t data;
    transform(task, data);
    if (storage_ && is_ready_) {
      //存储任务表
      storage_->replace<db_task_t>(data);
      //存储所有点
      bool is_ret = true;
      ret = save_point(task.nodes, task.id);
      if (0 != ret) {
        LOG_ERROR("save task(%s) point error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储所有边
      ret = save_edge(task.edges, task.id);
      if (0 != ret) {
        LOG_ERROR("save task(%s) edge error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储所有交管
      ret = save_traffic(task.traffic);
      if (0 != ret) {
        LOG_ERROR("save task(%s) traffic error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储所有碰撞区
      ret = save_collision(task.collision);
      if (0 != ret) {
        LOG_ERROR("save task(%s) collision error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储库区
      if (task.kuqu_id != -1) {
        ret = save_kuqu(task.kuqu);
        if (0 != ret) {
          LOG_ERROR("save task(%s) kuqu error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }
        ret = saveKuquVertex(task.kuqu);
        if (0 != ret) {
          LOG_ERROR("save task(%s) kuqu json error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }
        for (const auto &kuwei : task.kuqu.storage) {
          std::this_thread::sleep_for(std::chrono::milliseconds(50));
          ret = save_kuwei(kuwei);
          if (0 != ret) {
            LOG_ERROR("save task(%s) kuwei error: %d", task.id.c_str(), ret);
            is_ret &= false;
          }
        }
      }
      ret = is_ret? 0 : 1;
    }
  } catch (std::exception &e) {
    LOG_ERROR("save task(%s) exception: %s", task.id.c_str(), e.what());
    ret = 2;
  }

  return ret;
}

int PathDB::update_kuqu(const task_t &task)
{
  int ret = 0;
  try {
    db_task_t data;
    transform(task, data);
    if (storage_ && is_ready_) {
      //存储所有点
      bool is_ret = true;
      ret = save_point(task.nodes, task.id);
      if (0 != ret) {
        LOG_ERROR("save task(%s) point error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储所有边
      ret = save_edge(task.edges, task.id);
      if (0 != ret) {
        LOG_ERROR("save task(%s) edge error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储所有交管
      ret = save_traffic(task.traffic);
      if (0 != ret) {
        LOG_ERROR("save task(%s) traffic error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储所有碰撞区
      ret = save_collision(task.collision);
      if (0 != ret) {
        LOG_ERROR("save task(%s) collision error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //存储库区
      if (task.kuqu_id != -1) {
        ret = saveKuquVertex(task.kuqu);
        if (0 != ret) {
          LOG_ERROR("save task(%s) kuqu json error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }
        for (const auto &kuwei : task.kuqu.storage) {
          std::this_thread::sleep_for(std::chrono::milliseconds(50));
          ret = save_kuwei(kuwei);
          if (0 != ret) {
            LOG_ERROR("save task(%s) kuwei error: %d", task.id.c_str(), ret);
            is_ret &= false;
          }
        }
      }
      ret = is_ret? 0 : 1;
    }
  } catch (std::exception &e) {
    LOG_ERROR("save task(%s) exception: %s", task.id.c_str(), e.what());
    ret = 2;
  }

  return ret;
}

int PathDB::load_task_map(const std::string &id, std::string &map_id)
{
  int ret = 0;
  try {
    auto db_tasks = storage_->get_all<db_task_t>(where(c(&db_task_t::id) == id));
    if (1 == !db_tasks.size()) {
      LOG_ERROR("can't get task(%s) from db", id.c_str());
      return 1;
    }    
    map_id = db_tasks.front().map_id;
  } catch (std::exception &e) {
    LOG_ERROR("load task (%s) map exception: %s", id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::load_kuqu(const std::string &task_id, kuqu_t &kuqu)
{
  try {
    if (storage_ && is_ready_) {
      // 1 查询任务
      auto db_tasks = storage_->get_all<db_task_t>(where(c(&db_task_t::id) == task_id));
      if (1 == !db_tasks.size()) {
        LOG_ERROR("can't get task(%s) from db", task_id.c_str());
        return 1;
      }
      auto &db_task = db_tasks.front();
      task_t task;
      transform(db_task, task);
      std::string kuqu_id = std::to_string(task.kuqu_id);
      LOG_INFO("load task(%s) kuqu(%s)", task_id.c_str(), kuqu_id.c_str());

      // 2 查询库区
      auto db_kuqu = storage_->get_all<db_kuqu_t>(where(c(&db_kuqu_t::kuqu_id) == kuqu_id));
      if (1 == !db_kuqu.size()) {
        LOG_ERROR("can't get kuqu(%s) from db", kuqu_id.c_str());
        return 1;
      }
      LOG_INFO("load task(%s) kuqu(%s) finished.", task_id.c_str(), kuqu_id.c_str());

      transform(db_kuqu.front(), kuqu);

      // 3 查询库位
      int ret = load_storage_by_kuqu_id(kuqu.storage, kuqu_id);
      if (0 != ret) {
        LOG_ERROR("load storage from kuqu(%s) error", kuqu_id.c_str());
      }
      ret = loadKuquVertex(kuqu_id, kuqu);
      if (0 != ret) {
        LOG_ERROR("load storage from kuqu(%s) error", kuqu_id.c_str());
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load task(%s) kuqu exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_storage_by_kuqu_id(std::vector<storage_t> &storage, const std::string &kuqu_id)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_storage_t> storages;
      storages = storage_->select(object<db_storage_t>(),
                                  where(c(&db_storage_t::kuqu) == kuqu_id));

      for (const auto &db : storages) {
        storage_t kuwei;
        transform(db, kuwei);
        storage.push_back(kuwei);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load storage by kuqu id(%s) exception: %s", kuqu_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_kuqu(const kuqu_t &data)
{
  try {
    if (storage_ && is_ready_) {
      db_kuqu_t db_data;
      transform(data, db_data);
      // storage_->replace<db_kuqu_t>(db_data);
      if (storage_->get_all<db_kuqu_t>(
          where(c(&db_kuqu_t::kuqu_id) == data.id)).empty()) {
        storage_->insert(db_data);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("save kuqu(%s) exception: %s", data.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_kuwei(const storage_t &data)
{
  try {
    if (storage_ && is_ready_) {
      LOG_ERROR("ready save kuwei(%s)", data.id.c_str());
      db_storage_t db_data;
      transform(data, db_data);
      if (storage_->get_all<db_storage_t>(
          where(c(&db_storage_t::kuqu) == data.kuqu and
                c(&db_storage_t::kuwei_id) == data.id)).empty()) {
        storage_->insert(db_data);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("save kuwei(%s) exception: %s", data.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_task(const std::string &id, task_t &task)
{
  int ret = 0;
  try {
    if (storage_ && is_ready_) {
      // 1 查询任务
      auto db_tasks = storage_->get_all<db_task_t>(where(c(&db_task_t::id) == id));
      if (1 == !db_tasks.size()) {
        LOG_ERROR("can't get task(%s) from db", id.c_str());
        return 1;
      }
      auto &db_task = db_tasks.front();
      transform(db_task, task);

      //2 查询任务的起点和终点
      auto sp = storage_->get_all<node_t>(where(c(&node_t::id) == db_task.start_point));
      auto ep = storage_->get_all<node_t>(where(c(&node_t::id) == db_task.end_point));
      if (!sp.empty()) {
        task.start_point = sp.front();
      }
      if (!ep.empty()) {
        task.end_point = ep.front();
      }

      //3.1 查询所有点
      bool is_ret = true;
      int ret = load_point_by_task_id(task.nodes, task.id);
      if (0 != ret) {
        LOG_ERROR("load task(%s) point error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }
      //3.2 查询所有任务点
      ret = load_point_by_task_id(task.actions, task.id, 1);
      if (0 != ret) {
        LOG_ERROR("load task(%s) action point error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }

      //4 查询所有边
      ret = load_edge(task.edges, task.id);
      if (0 != ret) {
        LOG_ERROR("load task(%s) edge error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }

      //5 查询交管
      ret = load_traffic(task.traffic, task.id);
      if (0 != ret) {
        LOG_ERROR("load task(%s) traffic error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }

      //6 查询冲突区
      ret = load_collision(task.collision, task.id);
      if (0 != ret) {
        LOG_ERROR("load task(%s) collision error: %d", task.id.c_str(), ret);
        is_ret &= false;
      }

      if (task.kuqu_id != -1) {
        // 7 查询库区
        ret = load_kuqu(task.id, task.kuqu);
        if (0 != ret) {
          LOG_ERROR("load task(%s) kuqu error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }
      }

      ret = is_ret? 0 : 2;
    }
  } catch (std::exception &e) {
    LOG_ERROR("get task(%s) from db exception: %s", id.c_str(), e.what());
    ret = 3;
  }
  return ret;
}

int PathDB::load_task(const std::string &edge_id, std::vector<task_t> &tasks)
{
  int ret = 0;
  try {
    auto db_tasks = storage_->select(object<db_task_t>(),
          where(c(&db_task_edge_t::edge_id) == edge_id and
                c(&db_task_edge_t::task_id) == &db_task_t::id),
          order_by(&db_task_edge_t::id).asc());
    for(auto &db_task: db_tasks) {
      task_t task;
      transform(db_task, task);
      tasks.emplace_back(task);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load task by edge_id(%s) exception: %s", edge_id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::load_task(std::vector<task_t> &tasks)
{
  int ret = 0;
  std::string task_id;
  try {
    bool is_ret = true;
    if (storage_ && is_ready_) {
      // 1 查询任务
      auto db_tasks = storage_->get_all<db_task_t, std::vector<db_task_t>>();
      LOG_INFO("load task size: %d", db_tasks.size());
      for (auto &db_task : db_tasks) {
        task_t task;
        transform(db_task, task);
        task_id = task.id;
        LOG_INFO("load task(%s)", task_id.c_str());

        //2 查询任务的起点和终点
        auto sp = storage_->get_all<node_t>(where(c(&node_t::id) == db_task.start_point));
        auto ep = storage_->get_all<node_t>(where(c(&node_t::id) == db_task.end_point));
        if (!sp.empty()) {
          task.start_point = sp.front();
        }
        if (!ep.empty()) {
          task.end_point = ep.front();
        }

        //3.1 查询所有点
        int ret = load_point_by_task_id(task.nodes, task.id);
        if (0 != ret) {
          LOG_ERROR("load task(%s) point error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }

        //3.2 查询所有任务点
        ret = load_point_by_task_id(task.actions, task.id, 1);
        if (0 != ret) {
          LOG_ERROR("load task(%s) action point error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }

        //4 查询所有边
        ret = load_edge(task.edges, task.id);
        if (0 != ret) {
          LOG_ERROR("load task(%s) edge error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }

        //5 查询交管
        ret = load_traffic(task.traffic, task.id);
        if (0 != ret) {
          LOG_ERROR("load task(%s) traffic error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }

        //6 查询冲突区
        ret = load_collision(task.collision, task.id);
        if (0 != ret) {
          LOG_ERROR("load task(%s) collision error: %d", task.id.c_str(), ret);
          is_ret &= false;
        }

        if (task.kuqu_id != -1) {
          // 7 查询库区
          ret = load_kuqu(task.id, task.kuqu);
          if (0 != ret) {
            LOG_ERROR("load task(%s) kuqu error: %d", task.id.c_str(), ret);
            is_ret &= false;
          }
        }

        tasks.emplace_back(task);
      }
    }
    ret = is_ret? 0 : 1;
  } catch (std::exception &e) {
    LOG_ERROR("laod task(%s) exception: %s", task_id.c_str(), e.what());
    ret = 2;
  }
  return ret;
}

int PathDB::load_task(std::vector<std::string> &task_names)
{
  try {
    auto tasks = storage_->get_all<db_task_t>(order_by(&db_task_t::name).asc());
    for(auto &task: tasks) {
      task_names.emplace_back(task.name);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load all task name exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_kuqu(std::vector<std::string> &kuqu_names)
{
  try {
    auto kuqus = storage_->get_all<db_kuqu_t>(order_by(&db_kuqu_t::name).asc());
    for(auto &kuqu: kuqus) {
      kuqu_names.emplace_back(kuqu.name);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load all kuqu name exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_taskid(std::vector<std::string> &task_ids)
{
  try {
    auto tasks = storage_->get_all<db_task_t>(order_by(&db_task_t::name).asc());
    for(auto &task: tasks) {
      task_ids.emplace_back(task.id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load all task name exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::update_task(const task_t &task)
{
  int ret = 0;
  try {
    auto tasks = storage_->get_all<db_task_t>(order_by(&db_task_t::name).asc());
    for (const auto& tk : tasks) {
      if (tk.sn == task.sn && tk.sn != -1 && tk.id != task.id) {
        LOG_ERROR("update task(%s) failed, sn(%d) has same", task.id.c_str(), task.sn);
        return 1;        
      }
    }

    db_task_t data;
    transform(task, data);
    if (storage_ && is_ready_) {
      //存储任务表
      storage_->replace<db_task_t>(data);
    }

    if (task.avoid_level != 0 && task.velocity_level != 0) {
      std::vector<edge_t> edges;
      auto ret = load_edge(edges, task.id);
      if (ret == 0) {
        for (const auto &edge : edges) {
          db_edge_t db_edge;
          transform(edge, db_edge);

          db_edge.avoid_level = task.avoid_level;
          db_edge.velocity_level = task.velocity_level;
          
          if (storage_ && is_ready_) {
            //存储任务表
            storage_->replace<db_edge_t>(db_edge);
          }    
        }
      }
    }

  } catch (std::exception &e) {
    LOG_ERROR("update task(%s) exception: %s", task.id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::update_edge(const edge_t &edge)
{
  int ret = 0;
  try {
    std::vector<db_edge_t> edges;
    edges = storage_->select(object<db_edge_t>(),
                             where(c(&db_edge_t::id) == edge.id));

    for (const auto& ed : edges) {
      db_edge_t path = ed;
      path.avoid_level = edge.avoid_level;
      path.velocity_level = edge.velocity_level;

      if (storage_ && is_ready_) {
        //存储任务表
        storage_->replace<db_edge_t>(path);
      }      
    }
  } catch (std::exception &e) {
    LOG_ERROR("update edge(%s) exception: %s", edge.id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::update_point(const node_t &node)
{
  int ret = 0;
  try {
    std::vector<db_node_t> nodes;
    nodes = storage_->select(object<db_node_t>(),
                             where(c(&db_node_t::id) == node.id));

    for (const auto& nd : nodes) {
      db_node_t point = nd;
      point.action = node.action;

      if (storage_ && is_ready_) {
        //存储任务表
        storage_->replace<db_node_t>(point);
      }      
    }
  } catch (std::exception &e) {
    LOG_ERROR("update edge(%s) exception: %s", node.id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::update_storage_name(const std::string &name, const std::string &task_id)
{
  int ret = 0;
  try {
    std::vector<db_task_t> tasks;
    tasks = storage_->select(object<db_task_t>(),
                             where(c(&db_task_t::id) == task_id));

    int kuqu_id = 0;
    for (const auto& db_task : tasks) {
      db_task_t task = db_task;
      task.name = name;
      kuqu_id = task.kuqu;

      if (storage_ && is_ready_) {
        //存储任务表
        storage_->replace<db_task_t>(task);
      }      
    }

    if (kuqu_id != 0) {
      std::vector<db_kuqu_t> kuqus;
      kuqus = storage_->select(object<db_kuqu_t>(),
                               where(c(&db_kuqu_t::kuqu_id) == kuqu_id));

      for (const auto& db_kuqu : kuqus) {
        db_kuqu_t kuqu = db_kuqu;
        kuqu.name = name;

        if (storage_ && is_ready_) {
          //存储任务表
          storage_->replace<db_kuqu_t>(kuqu);
        }      
      }      
    }

    LOG_INFO("update task(%s) kuqu name(%s) finished.", task_id.c_str(), name.c_str());
  } catch (std::exception &e) {
    LOG_ERROR("update task(%s) kuqu name(%s) exception: %s", task_id.c_str(), name.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::update_storage_size(const kuqu_t &kuqu)
{
  int ret = 0;
  try {
    std::vector<db_kuqu_t> kuqus;
    kuqus = storage_->select(object<db_kuqu_t>(),
                              where(c(&db_kuqu_t::kuqu_id) == kuqu.id));

    for (const auto& db_kuqu : kuqus) {
      db_kuqu_t data = db_kuqu;
      data.width = kuqu.kwsize.width;
      data.length = kuqu.kwsize.length;
      data.rowspace = kuqu.kwsize.rowspace;
      data.columnspace = kuqu.kwsize.columnspace;

      if (storage_ && is_ready_) {
        //存储任务表
        storage_->replace<db_kuqu_t>(data);
      }      
    }      

    LOG_INFO("update kuqu(%s) size finished.", kuqu.id.c_str());
  } catch (std::exception &e) {
    LOG_ERROR("update kuqu(%s) size exception: %s", kuqu.id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::update_task_index(const std::string &task_id, const std::string &start, const std::string &end)
{
  int ret = 0;
  try {
    std::vector<db_task_t> tasks;
    tasks = storage_->select(object<db_task_t>(),
                             where(c(&db_task_t::id) == task_id));

    for (const auto& db_task : tasks) {
      db_task_t task = db_task;
      task.start_point = start;
      task.end_point = end;

      if (storage_ && is_ready_) {
        //存储任务表
        storage_->replace<db_task_t>(task);
      }      
    }      

    LOG_INFO("update task(%s) points index finished.", task_id.c_str());
  } catch (std::exception &e) {
    LOG_ERROR("update task(%s) points index exception: %s", task_id.c_str(), e.what());
    ret = 1;
  }
  return ret;
}

int PathDB::del_task(const std::string &id)
{
  try {
    if (storage_ && is_ready_) {
      //1 先移除任务所在点： 任务-点关联表
      del_point_of_task(id);
      //2 移除任务所在的边： 任务-边关联表
      del_edge_of_task(id);
      //3 移除任务所在的交管： 任务-交管关联表
      del_traffic_of_task(id);
      //4 移除任务所在的冲突区： 任务-冲突区关联表
      del_collision_of_task(id);
      //5 移除库区表
      std::string kuqu_id;
      del_kuqu(id, kuqu_id);
      delKuQuVertex(kuqu_id);
      //6 再移除任务表
      storage_->remove<db_task_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove task(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::del_kuqu(const std::string &id, std::string &kuqu_id) 
{
  try {
    if (storage_ && is_ready_) {
      // 1 查询任务
      auto db_tasks = storage_->get_all<db_task_t>(where(c(&db_task_t::id) == id));
      if (1 == !db_tasks.size()) {
        LOG_ERROR("can't get task(%s) from db", id.c_str());
        return 1;
      }
      auto &db_task = db_tasks.front();
      task_t task;
      transform(db_task, task);

      if (task.kuqu_id == -1) return 0;
      kuqu_id = std::to_string(task.kuqu_id);

      storage_->remove_all<db_kuqu_t>(where(c(&db_kuqu_t::kuqu_id) == kuqu_id));
      storage_->remove_all<db_storage_t>(where(c(&db_storage_t::kuqu) == kuqu_id));
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove_all task(%s)'s kuqu exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::del_kuwei(const std::string &kuqu_id) 
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove_all<db_storage_t>(where(c(&db_storage_t::kuqu) == kuqu_id));
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove_all task(%s)'s kuwei exception: %s", kuqu_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::del_point_of_task(const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      //任务的所有点
#if 1
      auto nodes = storage_->select(object<node_t>(),
          where(c(&node_t::id) == &db_task_node_t::node_id and c(&db_task_node_t::task_id) == task_id), 
                  order_by(&db_task_node_t::id).asc());
      storage_->transaction([&]() {
        for(auto &node: nodes) {
          auto count = storage_->count<db_task_node_t>(where(c(&db_task_node_t::node_id) == node.id));
          if (count <= 1) {
            storage_->remove_all<node_t>(where(c(&node_t::id) == node.id));
          }
        }
        return true;
      });
#else
      storage_->remove_all<db_node_t>(where(in(&db_node_t::id,
                                      select(&db_task_node_t::node_id,
                                        where(c(&db_task_node_t::task_id) == task_id)))));
#endif
      //任务与点的关联表
      storage_->remove_all<db_task_node_t>(where(c(&db_task_node_t::task_id) == task_id));

      //移除拓扑点
      storage_->remove_all<topology_node_t>(where(in(&topology_node_t::origin_id,
                                      select(&db_task_node_t::node_id,
                                        where(c(&db_task_node_t::task_id) == task_id)))));
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove_all task(%s)'s node exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::del_edge_of_task(const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {

#if 1
      auto edges = storage_->select(object<db_task_edge_t>(),
                    where(c(&db_task_edge_t::task_id) == task_id),
                          order_by(&db_task_edge_t::id).asc());
      storage_->transaction([&]() {
        for(auto &edge: edges) {
          auto count = storage_->count<db_task_edge_t>(where(c(&db_task_edge_t::edge_id) == edge.edge_id));
          if (count <= 1) {
            //移除边
            storage_->remove_all<db_edge_t>(where(c(&db_edge_t::id) == edge.edge_id));
            //移除边与点的关联表
            storage_->remove_all<db_edge_node_t>(where(c(&db_edge_node_t::edge_id) == edge.edge_id));
          }
        }
        return true;
      });

#else
      //边与点的关联表
      storage_->remove_all<db_edge_node_t>(where(in(&db_edge_node_t::edge_id,
                                            select(&db_task_edge_t::edge_id,
                                              where(c(&db_task_edge_t::task_id) == task_id)))));

      //任务上的所有边
      storage_->remove_all<db_edge_t>(where(in(&db_edge_t::id,
                                      select(&db_task_edge_t::edge_id,
                                        where(c(&db_task_edge_t::task_id) == task_id)))));
#endif
      //任务与边的关联表
      storage_->remove_all<db_task_edge_t>(where(c(&db_task_edge_t::task_id) == task_id));
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove_all task(%s)'s edge exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::del_traffic_of_task(const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
#if 1
      storage_->remove_all<db_traffic_t>(where(in(&db_traffic_t::traffic_id,
                                          select(&db_traffic_t::traffic_id,
                                            where(c(&db_traffic_t::task_id) == task_id)))));
      storage_->remove_all<db_task_traffic_t>(where(in(&db_task_traffic_t::traffic_id,
                                          select(&db_task_traffic_t::traffic_id,
                                            where(c(&db_task_traffic_t::task_id) == task_id)))));
#else
      storage_->remove_all<db_traffic_t>(where(c(&db_traffic_t::task_id) == task_id));
      storage_->remove_all<db_task_traffic_t>(where(c(&db_task_traffic_t::task_id) == task_id));
#endif
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove_all task(%s)'s traffic exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::del_collision_of_task(const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove_all<db_collision_t>(where(in(column<db_collision_t>(&db_collision_t::traffic_id),
                                           select(column<db_collision_t>(&db_collision_t::traffic_id),
                                            where(column<db_collision_t>(&db_collision_t::task_id) == task_id)))));
      //storage_->remove_all<db_collision_t>(where(column<db_collision_t>(&db_collision_t::task_id) == task_id));
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove_all task(%s)'s collision exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_point(const node_t &data)
{
  try {
    if (storage_ && is_ready_) {
      storage_->replace<node_t>(data);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save node(%s) exception: %s", data.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_point(const std::vector<node_t> &nodes, const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_task_node_t> db_data = storage_->get_all<db_task_node_t>();
      std::vector<std::string> db_tasks;
      for (const auto &data : db_data) {
        db_tasks.push_back(data.task_id);
      }
      std::unordered_set<std::string> unique_set(db_tasks.begin(), db_tasks.end());
      for (const auto &data : unique_set) {
        LOG_INFO("db task node, task(%s)", data.c_str());
      }

      storage_->transaction([&]() {
        for(auto &pt: nodes) {
          // if (storage_->get_all<node_t>(where(c(&node_t::id) == pt.id)).empty()) {
          //   storage_->replace<node_t>(pt);
          // }
          storage_->replace<node_t>(pt);
          
          if (!task_id.empty()) {
            //存储任务与点的关联表
            db_task_node_t db_data;
            db_data.task_id = task_id;
            db_data.node_id = pt.id;
            db_data.id = -1;

            if (unique_set.find(task_id) != unique_set.end()) {
              if (storage_->get_all<db_task_node_t>(
                  where(c(&db_task_node_t::task_id) == task_id and
                        c(&db_task_node_t::node_id) == pt.id)).empty()) {
                storage_->insert<db_task_node_t>(db_data);
              }
            } else {
              storage_->insert<db_task_node_t>(db_data);
            }

            // if (storage_->get_all<db_task_node_t>(
            //     where(c(&db_task_node_t::task_id) == task_id and
            //           c(&db_task_node_t::node_id) == pt.id)).empty()) {
            //   storage_->insert<db_task_node_t>(db_data);
            // }
            // storage_->replace<db_task_node_t>(db_data);
          }
        }
        return true;
      });
    }
  } catch (std::exception &e) {
    LOG_ERROR("save node exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_target_task(const std::string& map_id, const int& action_type, std::vector<std::string> &tasks)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<node_t> nodes;

      auto db_tasks = storage_->get_all<db_task_t, std::vector<db_task_t>>();
      for (const auto& task : db_tasks) {
        if (map_id != task.map_id) continue;

        if (load_point_by_task_id(nodes, task.id, -1, action_type) == 0) {
          if (nodes.size() == 1) {
            tasks.push_back(task.id);
          }
        }
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load target action(%d) task exception: %s", action_type, e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_target_task_by_sn(const int& sn, std::vector<std::string> &tasks)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_task_t> tks;

      tks = storage_->select(object<db_task_t>(),
                             where(c(&db_task_t::sn) == sn));

      for (const auto& task : tks) {
        tasks.push_back(task.id);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load target sn(%d) task exception: %s", sn, e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_target_node(const std::string& task_id, const int& action_type, std::vector<node_t> &nodes)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<node_t> t_nodes;

      t_nodes.clear();
      if (load_point_by_task_id(t_nodes, task_id, -1, action_type) == 0) {
        if (t_nodes.size() == 1) {
          nodes.push_back(t_nodes[0]);
        }
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load target action(%d) task exception: %s", action_type, e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_point(const std::string &id, node_t &node)
{
  try {
    if (storage_ && is_ready_) {
      node = storage_->get<node_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load point(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_point(std::vector<node_t> &nodes)
{
  try {
    if (storage_ && is_ready_) {
      nodes = storage_->get_all<node_t, std::vector<node_t>>(order_by(&node_t::id).asc());
    }
  } catch (std::exception &e) {
    LOG_ERROR("load point exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_point_by_task_id(std::vector<node_t> &nodes, const std::string &task_id, int point_type, int point_action)
{
  try {
    if (storage_ && is_ready_) {
      if (task_id.empty()) {
        nodes = storage_->select(object<node_t>(),
          where(c(&node_t::id) == &db_task_node_t::node_id), order_by(&db_task_node_t::id).asc());
      } else {

#if 0 // just for test
        auto sql = storage_->prepare(select(columns(asterisk<node_t>()),
          where(c(&node_t::id) == &db_task_node_t::node_id and c(&db_task_node_t::task_id) == task_id), 
                order_by(&db_task_node_t::id).asc()));
        std::cout << storage_->dump(sql) << std::endl;
        auto rows = storage_->execute(sql);
        for(auto& row: rows) {
          std::cout << get<0>(row) << '\t' << get<1>(row) << '\t' << get<2>(row) << '\t' << get<3>(row) << std::endl;
        }
#else 
        if (point_type == -1 && point_action == -1) {
          nodes = storage_->select(object<node_t>(),
            where(c(&node_t::id) == &db_task_node_t::node_id and 
                  c(&db_task_node_t::task_id) == task_id), 
                  order_by(&db_task_node_t::id).asc());
        } else if (point_action != -1) {
          nodes = storage_->select(object<node_t>(),
            where(c(&node_t::id) == &db_task_node_t::node_id and 
                  c(&node_t::action) == point_action and 
                  c(&db_task_node_t::task_id) == task_id), 
                  order_by(&db_task_node_t::id).asc());          
        } else {
          nodes = storage_->select(object<node_t>(),
            where(c(&node_t::id) == &db_task_node_t::node_id and 
                  c(&node_t::type) == point_type and 
                  c(&db_task_node_t::task_id) == task_id), 
                  order_by(&db_task_node_t::id).asc());
        }
#endif
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load point by task id(%s) exception: %s\n", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_point_by_edge_id(std::vector<node_t> &nodes, const std::string &edge_id, int start, int end, bool find_index)
{
  try {
    if (storage_ && is_ready_) {
      if (edge_id.empty()) {
        nodes = storage_->select(object<node_t>(),
          where(c(&node_t::id) == &db_edge_node_t::node_id), order_by(&db_edge_node_t::id).asc());
      } else {
        if (start >= 0 && end >= 0) {
          nodes = storage_->select(object<node_t>(),
            where(c(&node_t::id) == &db_edge_node_t::node_id and c(&db_edge_node_t::edge_id) == edge_id),
                  order_by(&db_edge_node_t::id).asc(), limit(start, end-start+1));
        } else {
          nodes = storage_->select(object<node_t>(),
            where(c(&node_t::id) == &db_edge_node_t::node_id and c(&db_edge_node_t::edge_id) == edge_id),
                  order_by(&db_edge_node_t::id).asc());
        }
        if (find_index) {
          auto db_nodes = storage_->select(object<db_task_node_t>(),
                      from<db_task_node_t>(),
                      where(in(&db_task_node_t::task_id,
                            select(&db_task_edge_t::task_id,
                                    where(c(&db_task_edge_t::edge_id) == edge_id)))),
                      order_by(&db_task_node_t::id).asc());
          for (auto &node : nodes) {
            for(int i = 0; i < db_nodes.size(); i++) {
              if (node.id == db_nodes[i].node_id) {
                node.index = i;
                break;
              }
            }
          }
        }
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load point by edge id(%s) exception: %s", edge_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_point(const std::string &id)
{
  try {
    if (storage_ && is_ready_) {
        storage_->remove_all<node_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove point(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_point_by_edge_id(const std::string &edge_id)
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove<db_edge_t>(edge_id);
      storage_->remove_all<db_edge_node_t>(
        where(c(&db_edge_t::id) == &db_edge_node_t::edge_id and c(&db_edge_t::id) == edge_id));
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove edge(%s) exception: %s", edge_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_point_by_task_id(const std::string &task_id)
{
  return 0;
}

int PathDB::save_edge(const edge_t &edge, const std::string &task_id)
{
  try {
    db_edge_t edge_data;
    transform(edge, edge_data);
    if (storage_ && is_ready_) {
      //1 先存储边的数据
      storage_->replace(edge_data);
      //2 存储边的起点和终点
      // if (storage_->get_all<node_t>(where(c(&node_t::id) == edge.start_point.id)).empty()) {
      //   storage_->replace<node_t>(edge.start_point);
      // }
      // if (storage_->get_all<node_t>(where(c(&node_t::id) == edge.end_point.id)).empty()) {
      //   storage_->replace<node_t>(edge.end_point);
      // }
      storage_->replace<node_t>(edge.start_point);
      storage_->replace<node_t>(edge.end_point);

      //3 存储边上的所有点到关联表、采集点表
      storage_->transaction([&] {
        for(auto &pt: edge.points) {
            // if (storage_->get_all<node_t>(where(c(&node_t::id) == pt.id)).empty()) {
            //   storage_->replace<node_t>(pt);
            // }
            storage_->replace<node_t>(pt);

            db_edge_node_t insert_data;
            insert_data.edge_id = edge_data.id;
            insert_data.node_id = pt.id;
            insert_data.id = -1;

            if (storage_->get_all<db_edge_node_t>(
                where(c(&db_edge_node_t::edge_id) == edge.id and
                      c(&db_edge_node_t::node_id) == pt.id)).empty()) {
              storage_->insert<db_edge_node_t>(insert_data);
            }
            // storage_->replace<db_edge_node_t>(insert_data);
        }
        return true;
      });

      // 4 存储任务与边的关联表
      if (!task_id.empty()) {
        db_task_edge_t db_data;
        db_data.task_id = task_id;
        db_data.edge_id = edge.id;
        db_data.id = -1;
        if (storage_->get_all<db_task_edge_t>(
            where(c(&db_task_edge_t::task_id) == task_id and
                  c(&db_task_edge_t::edge_id) == edge.id)).empty()) {
          storage_->insert<db_task_edge_t>(db_data);
        }
        // storage_->replace<db_task_edge_t>(db_data);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("save edge(%s) exception: %s", edge.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_edge(const std::vector<edge_t> &edges, const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_edge_node_t> db_data = storage_->get_all<db_edge_node_t>();
      std::vector<std::string> db_edges;
      for (const auto &data : db_data) {
        db_edges.push_back(data.edge_id);
      }
      std::unordered_set<std::string> unique_set(db_edges.begin(), db_edges.end());
      // for (const auto &data : unique_set) {
      //   LOG_INFO("db edge node, edge(%s)", data.c_str());
      // }

      storage_->transaction([&]() {
        for (auto &edge: edges) {
          db_edge_t edge_data;
          transform(edge, edge_data);

          storage_->replace(edge_data);

          storage_->replace<node_t>(edge.start_point);
          storage_->replace<node_t>(edge.end_point);

          for (auto &pt: edge.points) {
            storage_->replace<node_t>(pt);

            db_edge_node_t insert_data;
            insert_data.edge_id = edge_data.id;
            insert_data.node_id = pt.id;
            insert_data.id = -1;

            if (unique_set.find(edge_data.id) != unique_set.end()) {
              // if (storage_->get_all<db_edge_node_t>(
              //     where(c(&db_edge_node_t::edge_id) == edge.id and
              //           c(&db_edge_node_t::node_id) == pt.id)).empty()) {
              //   storage_->insert<db_edge_node_t>(insert_data);
              // }
            } else {
              storage_->insert<db_edge_node_t>(insert_data);
            }
          }

          // 4 存储任务与边的关联表
          if (!task_id.empty()) {
            db_task_edge_t db_data;
            db_data.task_id = task_id;
            db_data.edge_id = edge.id;
            db_data.id = -1;
            if (storage_->get_all<db_task_edge_t>(
                where(c(&db_task_edge_t::task_id) == task_id and
                      c(&db_task_edge_t::edge_id) == edge.id)).empty()) {
              storage_->insert<db_task_edge_t>(db_data);
            }
            // storage_->replace<db_task_edge_t>(db_data);
          }

          unique_set.insert(edge_data.id);
        }
        return true;
      });
    }
    // for(auto &edge : edges) {
    //   save_edge(edge, task_id);
    // }
  } catch (std::exception &e) {
    LOG_ERROR("save edge of task(%s) exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_edge(const std::string &id, edge_t &edge)
{
  try {
    if (storage_ && is_ready_) {
      auto db_edge = storage_->get<db_edge_t>(id);
      transform(db_edge, edge);
      auto sp = storage_->get_all<node_t>(where(c(&node_t::id) == db_edge.start_point));
      auto ep = storage_->get_all<node_t>(where(c(&node_t::id) == db_edge.end_point));
      if (!sp.empty()) {
        edge.start_point = sp.front();
      }
      if (!ep.empty()) {
        edge.end_point = ep.front();
      }
      int ret = load_point_by_edge_id(edge.points, edge.id);
      if (0 != ret) {
        LOG_ERROR("load point from edge(%s)error", edge.id.c_str());
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load edge(%s) exception: %s", edge.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_edge(std::vector<edge_t> &edges, const std::string &task_id, int start, int end)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_edge_t> db_edges;
      if (task_id.empty()) {
        db_edges = storage_->select(object<db_edge_t>(),
          where(c(&db_edge_t::id) == &db_task_edge_t::edge_id), order_by(&db_task_edge_t::id).asc());
      } else {
        if (start >= 0 && end >= 0) {
          db_edges = storage_->select(object<db_edge_t>(),
              where(c(&db_edge_t::id) == &db_task_edge_t::edge_id and
                    c(&db_task_edge_t::task_id) == task_id and
                    c(&db_task_edge_t::edge_id) == &db_edge_node_t::edge_id and
                    c(&db_edge_node_t::node_id) == &db_task_node_t::node_id and
                    c(&db_task_node_t::task_id) == task_id),
                    order_by(&db_task_node_t::id).asc(), limit(start, end-start));
        } else {
            db_edges = storage_->select(object<db_edge_t>(),
              where(c(&db_edge_t::id) == &db_task_edge_t::edge_id and c(&db_task_edge_t::task_id) == task_id),
                order_by(&db_task_edge_t::id).asc());
        }
      }
      edges.clear();
      for(auto &db_edge : db_edges) {
        edge_t edge;
        transform(db_edge, edge);
        auto sp = storage_->get_all<node_t>(where(c(&node_t::id) == db_edge.start_point));
        auto ep = storage_->get_all<node_t>(where(c(&node_t::id) == db_edge.end_point));
        if (!sp.empty()) {
          edge.start_point = sp.front();
        }
        if (!ep.empty()) {
          edge.end_point = ep.front();
        }
        int ret = load_point_by_edge_id(edge.points, edge.id);
        if (0 != ret) {
          LOG_ERROR("load point from edge(%s)error", edge.id.c_str());
        }
        edges.emplace_back(edge);
      }

    }
  } catch (std::exception &e) {
    LOG_ERROR("load edge by task id(%s) exception: %s\n", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_edge(const std::string &edge_id, const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      if (task_id.empty()) {
        storage_->remove<db_edge_t>(edge_id);
        storage_->remove_all<db_edge_node_t>(where(c(&db_edge_t::id) == &db_edge_node_t::edge_id and c(&db_edge_t::id) == edge_id));
      } else {
        storage_->remove_all<db_task_edge_t>(
          where(c(&db_edge_t::id) == &db_task_edge_t::edge_id and 
                c(&db_task_edge_t::task_id) == task_id and
                c(&db_task_edge_t::edge_id) == edge_id));
        storage_->remove_all<db_edge_node_t>(where(c(&db_traffic_t::task_id) == task_id and c(&db_edge_t::id) == edge_id));
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove edge(%s) exception: %s", edge_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_traffic(const traffic_t &traffic)
{
  try {
    if (storage_ && is_ready_) {
      //存储交管表
      db_traffic_t db_traffic;
      transform(traffic, db_traffic);
      if (storage_->get_all<db_traffic_t>(
          where(c(&db_traffic_t::task_id) == traffic.task_id and
                c(&db_traffic_t::traffic_id) == traffic.id)).empty()) {
        storage_->insert(db_traffic);
      }
      // storage_->replace<db_traffic_t>(db_traffic);

      //存储任务与交管的关联表
      db_task_traffic_t db_data;
      db_data.task_id = traffic.task_id;
      db_data.traffic_id = traffic.id;
      db_data.id = -1;
      if (storage_->get_all<db_task_traffic_t>(
          where(c(&db_task_traffic_t::task_id) == traffic.task_id and
                c(&db_task_traffic_t::traffic_id) == traffic.id)).empty()) {
        storage_->insert<db_task_traffic_t>(db_data);
      }
      // storage_->replace<db_task_traffic_t>(db_data);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save traffic(%d) exception: %s", traffic.id, e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_traffic(const std::vector<traffic_t> &traffics)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_traffic_t> db_traffic_data = storage_->get_all<db_traffic_t>();
      std::vector<std::string> db_traffic_tasks;
      std::vector<uint64_t> db_traffic_ids;
      for (const auto &data : db_traffic_data) {
        db_traffic_tasks.push_back(data.task_id);
        db_traffic_ids.push_back(data.traffic_id);
      }
      std::unordered_set<std::string> unique_traffic_tasks(db_traffic_tasks.begin(), db_traffic_tasks.end());
      std::unordered_set<uint64_t> unique_traffic_ids(db_traffic_ids.begin(), db_traffic_ids.end());

      std::vector<db_task_traffic_t> db_tk_traffic_data = storage_->get_all<db_task_traffic_t>();
      std::vector<std::string> db_tk_traffic_tasks;
      std::vector<uint64_t> db_tk_traffic_ids;
      for (const auto &data : db_tk_traffic_data) {
        db_tk_traffic_tasks.push_back(data.task_id);
        db_tk_traffic_ids.push_back(data.traffic_id);
      }
      std::unordered_set<std::string> unique_tk_traffic_tasks(db_tk_traffic_tasks.begin(), db_tk_traffic_tasks.end());
      std::unordered_set<uint64_t> unique_tk_traffic_ids(db_tk_traffic_ids.begin(), db_tk_traffic_ids.end());

      storage_->transaction([&]() {
        for (auto &traffic : traffics) {
          //存储交管表
          db_traffic_t db_traffic;
          transform(traffic, db_traffic);

          if (unique_traffic_tasks.find(traffic.task_id) == unique_traffic_tasks.end()) {
            storage_->insert<db_traffic_t>(db_traffic);
          } else if (unique_traffic_ids.find(traffic.id) == unique_traffic_ids.end()) {
            storage_->insert<db_traffic_t>(db_traffic);
          } else {
            if (storage_->get_all<db_traffic_t>(
                where(c(&db_traffic_t::task_id) == traffic.task_id and
                      c(&db_traffic_t::traffic_id) == traffic.id)).empty()) {
              storage_->insert<db_traffic_t>(db_traffic);
            }
          }

          //存储任务与交管的关联表
          db_task_traffic_t db_data;
          db_data.task_id = traffic.task_id;
          db_data.traffic_id = traffic.id;
          db_data.id = -1;
          if (unique_tk_traffic_tasks.find(traffic.task_id) == unique_tk_traffic_tasks.end()) {
            storage_->insert<db_task_traffic_t>(db_data);
          } else if (unique_tk_traffic_ids.find(traffic.id) == unique_tk_traffic_ids.end()) {
            storage_->insert<db_task_traffic_t>(db_data);
          } else {
            if (storage_->get_all<db_task_traffic_t>(
                where(c(&db_task_traffic_t::task_id) == traffic.task_id and
                      c(&db_task_traffic_t::traffic_id) == traffic.id)).empty()) {
              storage_->insert<db_task_traffic_t>(db_data);
            }
          }

          unique_traffic_tasks.insert(traffic.task_id);
          unique_traffic_ids.insert(traffic.id);
          unique_tk_traffic_tasks.insert(traffic.task_id);
          unique_tk_traffic_ids.insert(traffic.id);
        }
        return true;
      });
    }

    // for(auto &traffic : traffics) {
    //   save_traffic(traffic);
    // }
  } catch (std::exception &e) {
    LOG_ERROR("save traffic exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_traffic(const std::string &traffic_id, traffic_t &traffic)
{
  try {
    if (storage_ && is_ready_) {
      auto db_traffic = storage_->get<db_traffic_t>(traffic_id);
      transform(db_traffic, traffic);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load traffic by traffic id(%s) exception: %s", traffic_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_traffic(const std::string &edge_id, std::vector<traffic_t> &traffics)
{
   try {
    if (storage_ && is_ready_) {
      std::vector<db_traffic_t> db_traffics;
      if (edge_id.empty()) {
        db_traffics = storage_->select(object<db_traffic_t>(),
          where(c(&db_traffic_t::traffic_id) == &db_task_traffic_t::traffic_id), order_by(&db_task_traffic_t::id).asc());
      } else {
        db_traffics = storage_->select(object<db_traffic_t>(),
          from<db_traffic_t>(),
          where(in(&db_traffic_t::task_id, select(&db_task_edge_t::task_id, where(c(&db_task_edge_t::edge_id) == edge_id)))));
      }
      traffics.clear();
      for(auto &db_traffic : db_traffics) {
        traffic_t traffic;
        transform(db_traffic, traffic);
        traffics.emplace_back(traffic);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load traffic by edge id(%s) exception: %s", edge_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_traffic(std::vector<traffic_t> &traffics, const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_traffic_t> db_traffics;
      if (task_id.empty()) {
        db_traffics = storage_->select(object<db_traffic_t>(),
          where(c(&db_traffic_t::traffic_id) == &db_task_traffic_t::traffic_id), order_by(&db_task_traffic_t::id).asc());
      } else {
        db_traffics = storage_->select(object<db_traffic_t>(),
          where(c(&db_traffic_t::traffic_id) == &db_task_traffic_t::traffic_id and c(&db_task_traffic_t::task_id) == task_id),
                order_by(&db_task_traffic_t::id).asc());
      }
      traffics.clear();
      for(auto &db_traffic : db_traffics) {
        traffic_t traffic;
        transform(db_traffic, traffic);
        traffics.emplace_back(traffic);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load traffic by task id(%s) exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_traffic(const std::string &task_id, const std::string &traffic_id)
{
  try {
    if (storage_ && is_ready_) {
      if(!traffic_id.empty()) {
        storage_->remove_all<db_traffic_t>(where(in(&db_traffic_t::traffic_id,
                                                 select(&db_traffic_t::traffic_id,
                                                  where(c(&db_traffic_t::task_id) == task_id and
                                                        c(&db_traffic_t::traffic_id) == traffic_id)))));

        storage_->remove_all<db_task_traffic_t>(where(in(&db_task_traffic_t::traffic_id,
                                                 select(&db_task_traffic_t::traffic_id,
                                                  where(c(&db_task_traffic_t::task_id) == task_id and
                                                        c(&db_task_traffic_t::traffic_id) == traffic_id)))));
      } else {
        storage_->remove_all<db_traffic_t>(where(in(&db_traffic_t::traffic_id,
                                          select(&db_traffic_t::traffic_id,
                                            where(c(&db_traffic_t::task_id) == task_id)))));
        storage_->remove_all<db_task_traffic_t>(where(in(&db_task_traffic_t::traffic_id,
                                          select(&db_task_traffic_t::traffic_id,
                                            where(c(&db_task_traffic_t::task_id) == task_id)))));
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove traffic(%s) exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::get_max_traffic_id()
{
  try {
    if (storage_ && is_ready_) {
      auto ptr = storage_->max(&db_traffic_t::traffic_id);
      if (ptr) {
        return *ptr;
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load max traffic id exception: %s", e.what());
    return 0;
  }
  return 0;
}

int PathDB::get_max_collision_id()
{
  try {
    if (storage_ && is_ready_) {
      auto db_collisions = storage_->select(object<db_collision_t>(),
        order_by(column<db_collision_t>(&db_collision_t::traffic_id)).asc());
      if (!db_collisions.empty()) {
        return db_collisions.back().traffic_id;
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load max collision id exception: %s", e.what());
    return 0;
  }
  return 0;
}

int64_t PathDB::get_max_node_id()
{
  int64_t node_id = 0;
  try {
    if (storage_ && is_ready_) {
      auto nodes = storage_->select(object<db_node_t>(),
        order_by(&db_node_t::id).asc());
      std::sort(nodes.begin(), nodes.end(),
        [](const db_node_t &n1, const db_node_t &n2) {
          return std::stol(n1.id) < std::stol(n2.id);
        }
      );

      if (!nodes.empty()) {
        node_id = atol(nodes.back().id.c_str());
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load max node id exception: %s", e.what());
    node_id = 0;
  }
  return node_id;
}

int64_t PathDB::get_max_edge_id()
{
  int64_t edge_id = 0;
  try {
    if (storage_ && is_ready_) {
      auto edges = storage_->select(object<db_edge_t>(),
        order_by(&db_edge_t::id).asc());
      std::sort(edges.begin(), edges.end(),
        [](const db_edge_t &n1, const db_edge_t &n2) {
          return std::stol(n1.id) < std::stol(n2.id);
        }
      );
      if (!edges.empty()) {
        edge_id = atol(edges.back().id.c_str());
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load max edge id exception: %s", e.what());
    edge_id = 0;
  }
  return edge_id;
}

int64_t PathDB::get_max_kuqu_id()
{
  int64_t kuqu_id = 0;
  try {
    if (storage_ && is_ready_) {
      auto kuqus = storage_->select(object<db_kuqu_t>(),
        order_by(&db_kuqu_t::id).asc());
      std::sort(kuqus.begin(), kuqus.end(),
        [](const db_kuqu_t &n1, const db_kuqu_t &n2) {
          return n1.id < n2.id;
        }
      );
      if (!kuqus.empty()) {
        kuqu_id = kuqus.back().id;
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load max kuqu id exception: %s", e.what());
    kuqu_id = 0;
  }
  return kuqu_id;
}

int PathDB::save_collision(const traffic_t &collision)
{
  try {
    if (storage_ && is_ready_) {
      db_collision_t db_collision;
      transform(collision, db_collision);
      if (storage_->get_all<db_collision_t>(
          where(column<db_collision_t>(&db_collision_t::task_id) == collision.task_id and
                column<db_collision_t>(&db_collision_t::traffic_id) == collision.id)).empty()) {
        storage_->insert(db_collision);
      }
      // storage_->replace<db_collision_t>(db_collision);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save collision(%d) exception: %s", collision.id, e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_collision(const std::vector<traffic_t> &collisions)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_collision_t> db_collision_data = storage_->get_all<db_collision_t>();
      std::vector<std::string> db_collision_tasks;
      std::vector<uint64_t> db_collision_ids;
      for (const auto &data : db_collision_data) {
        db_collision_tasks.push_back(data.task_id);
        db_collision_ids.push_back(data.traffic_id);
      }
      std::unordered_set<std::string> unique_collision_tasks(db_collision_tasks.begin(), db_collision_tasks.end());
      std::unordered_set<uint64_t> unique_collision_ids(db_collision_ids.begin(), db_collision_ids.end());

      storage_->transaction([&]() {
        for (auto &collision : collisions) {
          db_collision_t db_collision;
          transform(collision, db_collision);

          if (unique_collision_tasks.find(collision.task_id) == unique_collision_tasks.end()) {
            storage_->insert<db_collision_t>(db_collision);
          } else if (unique_collision_ids.find(collision.id) == unique_collision_ids.end()) {
            storage_->insert<db_collision_t>(db_collision);
          } else {
            if (storage_->get_all<db_collision_t>(
                  where(column<db_collision_t>(&db_collision_t::task_id) == collision.task_id and
                        column<db_collision_t>(&db_collision_t::traffic_id) == collision.id)).empty()) {
              storage_->insert(db_collision);
            }
          }

          unique_collision_tasks.insert(collision.task_id);
          unique_collision_ids.insert(collision.id);
        }
        return true;
      });
    }

    // for(auto &collision : collisions) {
    //   save_collision(collision);
    // }
  } catch (std::exception &e) {
    LOG_ERROR("save collision exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_collision(const std::string &collision_id, traffic_t &collision)
{
  try {
    if (storage_ && is_ready_) {
      auto db_collision = storage_->get<db_collision_t>(collision_id);
      transform(db_collision, collision);
    }
  } catch (std::exception &e) {
    LOG_ERROR("load collision by collision id(%s) exception: %s", collision_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_collision(std::vector<traffic_t> &collisions, const std::string &task_id)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<db_collision_t> db_collisions;
      if (task_id.empty()) {
        db_collisions = storage_->select(object<db_collision_t>(),
          order_by(column<db_collision_t>(&db_collision_t::id)).asc());
      } else {
#if 0 // just for test
        auto sql = storage_->prepare(select(object<db_collision_t>(),
          where(column<db_collision_t>(&db_collision_t::task_id) == task_id),
          order_by(column<db_collision_t>(&db_collision_t::id)).asc()));
        std::cout << storage_->dump(sql) << std::endl;
        auto rows = storage_->execute(sql);
#else 
        db_collisions = storage_->select(object<db_collision_t>(),
          where(column<db_collision_t>(&db_collision_t::task_id) == task_id),
          order_by(column<db_collision_t>(&db_collision_t::id)).asc());
#endif
      }
      collisions.clear();
      for(auto &db_collision : db_collisions) {
        traffic_t collision;
        transform(db_collision, collision);
        collisions.emplace_back(collision);
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("load collision by task id(%s) exception: %s", task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_collision(const std::string &task_id, const std::string &collision_id)
{
  try {
    if (storage_ && is_ready_) {
      if(!collision_id.empty()) {
        storage_->remove_all<db_collision_t>(
          where(column<db_collision_t>(&db_collision_t::task_id) == task_id and 
                column<db_collision_t>(&db_collision_t::traffic_id) == collision_id));
      } else {
        storage_->remove_all<db_collision_t>(where(column<db_collision_t>(&db_collision_t::task_id) == task_id));
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("remove collision(%s), task(%s) exception: %s", collision_id.c_str(), task_id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_task_result(const task_result_t &data)
{
  try {
    if (storage_ && is_ready_) {
      storage_->replace(data);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save task result(%s) exception: %s\n", data.result_id.c_str(),
              e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_task_result(const std::string &id, task_result_t &data)
{
  try {
    if (storage_ && is_ready_) {
      data = storage_->get<task_result_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("get task result(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_task_result(std::vector<task_result_t> &datas)
{
  try {
    if (storage_ && is_ready_) {
      datas = storage_->get_all<task_result_t, std::vector<task_result_t>>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("get all task result exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_task_info(const task_info_t &data)
{
  try {
    if (storage_ && is_ready_) {
      storage_->replace(data);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save task info exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_task_info(const int &result)
{
  try {
    if (storage_ && is_ready_) {
      std::vector<task_info_t> datas = storage_->get_all<task_info_t, std::vector<task_info_t>>();
      for (auto &data : datas) {
        if (data.id == "task_info") {
          data.current_task_count++;
          data.total_task_count++;
          if (result != 0) data.error_task_count++;
          storage_->replace(data);
        }
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("save task info exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_task_info(std::vector<task_info_t> &data)
{
  try {
    if (storage_ && is_ready_) {
      data = storage_->get_all<task_info_t, std::vector<task_info_t>>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("get all task info exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_map(const map_t &data)
{
  try {
    if (storage_ && is_ready_) {
      storage_->replace(data);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save map(%s) exception: %s\n", data.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_map(const std::string &id, map_t &data)
{
  try {
    if (storage_ && is_ready_) {
      data = storage_->get<map_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("get map(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_map(std::vector<map_t> &datas)
{
  try {
    if (storage_ && is_ready_) {
      datas = storage_->get_all<map_t, std::vector<map_t>>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("get all map exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::save_relocation(const relocation_t &data)
{
  try {
    if (storage_ && is_ready_) {
      storage_->replace(data);
    }
  } catch (std::exception &e) {
    LOG_ERROR("save relocation(%s) exception: %s", data.id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_relocation(const std::string &id, relocation_t &data)
{
  try {
    if (storage_ && is_ready_) {
      data = storage_->get<relocation_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("get relocation(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_relocation(std::vector<relocation_t> &datas)
{
  try {
    if (storage_ && is_ready_) {
      datas = storage_->get_all<relocation_t, std::vector<relocation_t>>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("get all relocation exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_relocation(const std::string &id)
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove_all<relocation_t>(id);
    }
  } catch (std::exception &e) {
    LOG_ERROR("delete all relocation exception: %s", e.what());
    return 1;
  }
  return 0;
}


int PathDB::save_topology(const topology_map_t &datas)
{
  try {
    if (storage_ && is_ready_) {
      return storage_->transaction([&, datas]() {
       for (auto &data : datas) {
          auto fittingPoint_id = data.first;
          topology_node_t tp;
          tp.id = -1;
          tp.topology_id = data.second;
          tp.origin_id = data.first;
          if (storage_->get_all<topology_node_t>(
            where(c(&topology_node_t::topology_id) == data.second and 
                      c(&topology_node_t::origin_id) == data.first)).empty()) {
            storage_->insert(tp);
          }
        }
        return true;;
      })? 0 : 1;
    }
  } catch (std::exception &e) {
    LOG_ERROR("save topology point exception: %s", e.what());
    return 2;
  }
  return 0;
}

int PathDB::load_topology(const std::string &id, std::vector<std::string> &datas)
{
  try {
    if (storage_ && is_ready_) {
      datas = storage_->select(&topology_node_t::topology_id, where(c(&topology_node_t::origin_id) == id));
    }
  } catch (std::exception &e) {
    LOG_ERROR("get topology(%s) exception: %s", id.c_str(), e.what());
    return 1;
  }
  return 0;
}

int PathDB::load_topology(std::map<std::string, std::string> &datas)
{
  try {
    if (storage_ && is_ready_) {
      auto db_datas = storage_->get_all<topology_node_t, std::vector<topology_node_t>>();
      datas.clear();
      for (auto &db_data : db_datas) {
        datas[db_data.origin_id] = db_data.topology_id;
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR("get all topology exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::delete_topology(const std::string &id)
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove_all<topology_node_t>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("delete topology exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::reset_task()
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove_all<db_task_traffic_t>();
      storage_->remove_all<db_traffic_t>();
      storage_->remove_all<db_collision_t>();
      storage_->remove_all<topology_node_t>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("reset task exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::reset_db()
{
  try {
    if (storage_ && is_ready_) {
      storage_->remove_all<db_task_node_t>();
      storage_->remove_all<db_task_edge_t>();
      storage_->remove_all<db_task_traffic_t>();
      storage_->remove_all<db_edge_node_t>();
      storage_->remove_all<node_t>();
      storage_->remove_all<db_edge_t>();
      storage_->remove_all<db_task_t>();
      storage_->remove_all<db_traffic_t>();
      storage_->remove_all<db_collision_t>();
      storage_->remove_all<task_result_t>();
      storage_->remove_all<task_info_t>();
      storage_->remove_all<map_t>();
      storage_->remove_all<relocation_t>();
      storage_->remove_all<topology_node_t>();
    }
  } catch (std::exception &e) {
    LOG_ERROR("reset db exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::backup_db()
{
  try {
    std::string db_dir = "/home/" + common::get_user_name() + "/config/map/"; 
    std::string db_path =  db_dir + kPathDB;
    std::string backup_dir = "/home/" + common::get_user_name() + "/config/backup";
    std::string backup_path = backup_dir + "/cotek_path_" + common::get_current_systime() + ".db";
    if (!common::is_dir_exist(backup_dir)) {
      common::create_dir(backup_dir);
    }
#if 0
    if (std::rename(db_path.c_str(), backup_path.c_str())) {
      LOG_ERROR("backup db failed, file = %s", db_path.c_str());
    }
#else
    if (storage_ && is_ready_) {
      storage_->backup_to(backup_path);
    }
#endif

    init();

  } catch (std::exception &e) {
    LOG_ERROR("backup db exception: %s", e.what());
    return 1;
  }
  return 0;
}

int PathDB::backup_db(const std::string& src_db_path, const std::string& dest_db_path)
{
  try {
    // 打开原始数据库
    sqlite3* src_db = nullptr;
    if (sqlite3_open(src_db_path.c_str(), &src_db) != SQLITE_OK) {
        LOG_ERROR_STREAM("can't open source database: " << src_db_path);
        return 1;
    }

    // 打开目标数据库（备份）
    sqlite3* dest_db = nullptr;
    if (sqlite3_open(dest_db_path.c_str(), &dest_db) != SQLITE_OK) {
        LOG_ERROR_STREAM("can't open dest database: " << dest_db_path);
        sqlite3_close(src_db);
        return 2;
    }

    // 使用ATTACH语句附加备份数据库
    const char* attachSql = "ATTACH DATABASE ? AS backup";
    sqlite3_stmt* stmt;
    sqlite3_prepare_v2(src_db, attachSql, -1, &stmt, 0);
    sqlite3_bind_text(stmt, 1, dest_db_path.c_str(), -1, SQLITE_STATIC);
    if (sqlite3_step(stmt) != SQLITE_DONE) {
      LOG_ERROR_STREAM("sqlite ATTACH failed: " << sqlite3_errmsg(src_db));
      sqlite3_finalize(stmt);
      sqlite3_close(src_db);
      sqlite3_close(dest_db);
      return 3;
    }
    sqlite3_finalize(stmt);

    // 执行备份操作
    const char* backupSql = "BACKUP TO backup";
    if (sqlite3_exec(src_db, backupSql, nullptr, nullptr, nullptr) != SQLITE_OK) {
      LOG_ERROR_STREAM("backup databse failed: " << sqlite3_errmsg(src_db));
    } else {
      LOG_INFO_STREAM("backup databse success: " << dest_db_path);
    }

    // 关闭数据库连接
    sqlite3_close(src_db);
    sqlite3_close(dest_db);

  } catch (std::exception &e) {
    LOG_ERROR_STREAM("backup database exception: " << e.what());
    return 4;
  }
  return 0;
}

}  // namespace db
}  // namespace cotek
