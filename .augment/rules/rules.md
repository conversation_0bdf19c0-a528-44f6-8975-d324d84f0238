---
type: "agent_requested"
description: "Example description"
---

- Single Responsibility Principle.Open-Closed Principle
-Liskov Substitution Principle-Interface Segregation Principle
-Dependency Inversion Principle
-Encourages <PERSON> to write straightforward uncomplicated solutions
-Avoids over-engineering and unnecessary complexity
-Results in more readable and maintainablecode
-Prevents <PERSON> from adding speculative features
-Focuses on implementing only what's currently needed
-Reduces code bloat and maintenance overhead