/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef DECISION_MAKER_INCLUDE_DECISION_MAKER_STEP_H_
#define DECISION_MAKER_INCLUDE_DECISION_MAKER_STEP_H_

#include <cstdint>
#include <deque>
#include <string>

#include "cotek_common/geometry/cotek_geometry.h"
#include "cotek_msgs/agv_actionGoal.h"
#include "cotek_msgs/track_pathGoal.h"
#include "decision_maker/decision_config.h"
#include "decision_maker/decision_data.h"

namespace decision_maker {

using AgvTaskOperationType = dispacth::AgvTaskOperationType;
using NaviType = dispacth::NaviType;
using StepType = dispacth::StepType;
using PathType = dispacth::PathType;
using MoveType = dispacth::MoveType;

using Point = cotek_geometry::Point;
using Pose = cotek_geometry::Pose;

struct AdjustSpeedPose {
  bool need_adjust;
  double adjust_speed;
  Pose adjust_pose;
  AdjustSpeedPose()
      : need_adjust(false),
        adjust_speed(0.0),
        adjust_pose(Pose(88888., 88888., 0)) {}
};

struct HighPrecisionPose {
  bool need;
  Pose high_precision_pose;
  HighPrecisionPose()
      : need(false), high_precision_pose(Pose(88888., 88888., 0)) {}
};

struct ActionGoal {
  ActionState action_state;
  GoalType g_type{GoalType::NONE};
  cotek_msgs::track_pathGoal t_goal;
  cotek_msgs::agv_actionGoal a_goal;
  bool operator==(const ActionGoal &goal) const {
    return g_type == goal.g_type &&
           t_goal.motion_type == goal.t_goal.motion_type &&
           t_goal.end_id == goal.t_goal.end_id &&
           a_goal.action_type == goal.a_goal.action_type &&
           a_goal.action_value == goal.a_goal.action_value;
  }
};

using ActionGoals = std::deque<ActionGoal>;

struct FixPose {
  bool vaild{false};
  Pose pose{Pose(88888., 88888., 0.)};
};

class Step {
 public:
  /**
   * \brief default constuctor of step
   */
  Step()
      : sequence_id_(0),
        node_id_("0"),
        step_type_(StepType::NONE),
        path_type_(PathType::NONE),
        point_(Point(0.0, 0.0)),
        point_yaw_(0.0),
        speed_(0),
        odom_(0),
        avoid_map_(0),
        adjust_speed_pose_(AdjustSpeedPose()),

        // TODO(@ssh)
        navi_type_(NaviType::FREE),
        move_type_(MoveType::NONE),
        point_id_(0),
        control_point0_(Point(0.0, 0.0)),
        control_point1_(Point(0.0, 0.0)),
        current_sec_id_(0),
        target_sec_id_(0),
        switch_point_id_(0),
        switch_point_(Point(0.0, 0.0)),
        switch_point_yaw_(0),
        append_(false),
        insert_rotate_goal_cnt_(0) {}

  /**
   * \brief default setter of private variable
   */
  // 3.0
  inline void set_sequence_id(int32_t id) { sequence_id_ = id; }
  inline void set_node_id(std::string node_id) { node_id_ = node_id; }

  inline void set_start_node(const Node &node) { start_node_ = node; }
  inline void set_node(const Node &node) { node_ = node; }
  inline void set_next_node(const Node &node) { next_node_ = node; }
  inline void set_final_end_node(const Node &node) { final_end_node_ = node; }
  inline void set_edge(const Edge &edge) { edge_ = edge; }
  inline void set_next_edge(const Edge &edge) { next_edge_ = edge; }
  void insert_action_goal(const ActionGoal &goal);

  inline void set_target_speed(const double &speed) { target_speed_ = speed; }
  inline void set_fix_pose(const FixPose &pose) { fix_pose_ = pose; }

  inline void set_point(const Point &pt) { point_ = pt; }
  inline void set_append(const bool &append) { append_ = append; }
  inline void set_point_yaw(const float &yaw) { point_yaw_ = yaw; }
  inline void set_step_type(const StepType &type) { step_type_ = type; }
  inline void set_max_speed(const double &speed) { speed_ = speed; }
  inline void set_odom(const double &odom) { odom_ = odom; }
  inline void set_avoid_map(const int32_t &code) { avoid_map_ = code; }
  inline void set_avoid_strategy(const std::vector<std::string> &strategy) {
    avoid_strategy_ = strategy;
  }
  inline void set_adjust_speed_pose(const AdjustSpeedPose &pose) {
    adjust_speed_pose_ = pose;
  }
  inline void set_start_pose(const Pose &pose) { start_pose_ = pose; }
  inline void set_high_precision_pose(const HighPrecisionPose &pose) {
    high_precision_pose_ = pose;
  }
  inline void set_remain_distance(const double &dist) {
    remain_distance_ = dist;
  }

  inline void set_navi_type(NaviType type) { navi_type_ = type; }

  inline void set_path_type(PathType type) {
    if (static_cast<int>(type) > static_cast<int>(path_type_))
      path_type_ = type;
  }

  void set_control_point(const Point &pt0, const Point &pt1) {
    control_point0_ = pt0;
    control_point1_ = pt1;
  }

  void set_order_id(const std::string &id) { order_id_ = id; }

  void set_task_id(const std::string &id) { task_id_ = id; }

  void set_kuqu_id(const int32_t &id) { kuqu_id_ = id; }

  // TODO(@ssh)

  // 2.0

  inline void set_current_sec_id(int32_t id) { current_sec_id_ = id; }
  inline void set_target_sec_id(int32_t id) { target_sec_id_ = id; }
  inline void set_switch_point_id(int32_t id) { switch_point_id_ = id; }
  inline void set_switch_point(const Point &pt) { switch_point_ = pt; }
  inline void set_switch_point_yaw(int32_t id) { switch_point_yaw_ = id; }

  /**
   * \brief default getter of private variable
   */
  // 3.0

  inline std::string order_id() const { return order_id_; }
  inline std::string task_id() const { return task_id_; }
  inline int32_t kuqu_id() const { return kuqu_id_; }
  inline int32_t sequence_id() const { return node_.sequence_id; }
  inline std::string node_id() const { return node_.node_id; }
  inline Node start_node() const { return start_node_; }
  inline Node node() const { return node_; }
  inline Node next_node() const { return next_node_; }
  inline Node end_node() const { return final_end_node_; }

  inline double target_speed() const { return target_speed_; }
  inline FixPose fix_pose() const { return fix_pose_; }

  inline Edge edge() const { return edge_; }
  inline Edge next_edge() const { return next_edge_; }
  inline bool append() const { return append_; }
  inline ActionGoals action_goals() const { return action_goals_; }

  inline const Point point() const {
    return Point(node_.node_position.x, node_.node_position.y);
  }
  inline int32_t start_index() const { return edge_.start_index; }
  inline int32_t end_index() const { return edge_.end_index; }
  inline double node_theta() const { return node_.node_position.theta; }
  inline double point_yaw() const { return edge_.orientation; }
  inline StepType step_type() const { return step_type_; }
  inline PathType path_type() const { return path_type_; }
  inline double odom() const { return edge_.length; }
  inline double speed() const { return edge_.max_speed; }
  inline int32_t avoid_map() const { return edge_.avoid_map; }
  inline std::vector<std::string> avoid_strategy() const {
    return edge_.avoid_strategy;
  }
  inline AdjustSpeedPose adjust_speed_pose() const {
    return adjust_speed_pose_;
  }
  inline Pose start_pose() const { return start_pose_; }
  inline HighPrecisionPose high_precision_pose() const {
    return high_precision_pose_;
  }
  inline double remain_distance() const { return remain_distance_; }

  // TODO(@ssh)
  inline std::string action_type() const { return action_.action_type; }
  inline Action action() const { return action_; }

  inline const Point control_point_0() const { return control_point0_; }
  inline const Point control_point_1() const { return control_point1_; }

  // 2.0
  inline NaviType navi_type() const { return navi_type_; }
  inline MoveType move_type() const { return move_type_; }

  inline int32_t current_sec_id() const { return current_sec_id_; }
  inline int32_t target_sec_id() const { return target_sec_id_; }
  inline int32_t switch_point_id() const { return switch_point_id_; }
  inline const Point &switch_point() const { return switch_point_; }
  inline float switch_point_yaw() const { return switch_point_yaw_; }

  inline std::vector<std::string> operation_value() const {
    return action_.value_list;
  }

  // 3.0

  bool FirstPlan();
  bool SecondPlan();
  bool hasOperationGoal() const;
  bool hasMoveOperationGoal() const;

  int GetDirection() const;

  int GetNextDirection() const;

  bool hasGoal(const ActionGoal &target_goal) const;

  bool hasTargetAMoveGoal(const navigation::MotionType &type) const;

  bool hasTargetActionGoal(const AgvTaskOperationType &type) const;
  bool hasTargetActionGoal(const int &type) const;
  ActionGoal GetTargetActionGoal(const AgvTaskOperationType &type) const;

  void SetActionState(const std::string &id, const std::string &status);

  std::string StepToString();

 private:
  ActionGoal CreatStartGoal();

  std::vector<ActionGoal> CreatNormalGoals(const Action &action);

  ActionGoal CreatEndGoal();
  ActionGoal CreatTrafficUnlockEndoal();

  bool NeedRotateGoal(ActionGoal &goal);

  // 3.0 原子任务
  std::string order_id_;
  std::string task_id_;
  int32_t kuqu_id_;
  int32_t sequence_id_{0};  //序列号id
  std::string node_id_;
  Node start_node_;
  Node node_;
  Node next_node_;
  Node final_end_node_;
  Edge edge_;
  Edge next_edge_;

  double target_speed_{0.};
  FixPose fix_pose_;

  StepType step_type_{StepType::NONE};
  PathType path_type_{PathType::NONE};
  ActionGoals action_goals_;

  Point point_;
  double point_yaw_{0.};
  double speed_{0.};
  double odom_{0.};
  int32_t avoid_map_{0};
  std::vector<std::string> avoid_strategy_;
  AdjustSpeedPose adjust_speed_pose_;
  Pose start_pose_;
  double remain_distance_{0.};
  HighPrecisionPose high_precision_pose_;

  // TODO(@ssh)
  Action action_;

  // 2.0

  NaviType navi_type_{NaviType::NONE};  // 3.0不需要 通过action实施
  MoveType move_type_{MoveType::NONE};

  int32_t point_id_;
  Point control_point0_;
  Point control_point1_;
  int current_sec_id_;
  int target_sec_id_;
  int switch_point_id_;

  Point switch_point_;
  float switch_point_yaw_;

  double operation_value_{};

  bool append_{false};

  uint8_t insert_rotate_goal_cnt_{0};
};

struct OperationStep {
  bool vaild;
  Step step;
  OperationStep() : vaild(false) {}
};

}  // namespace decision_maker

#endif  // DECISION_MAKER_INCLUDE_DECISION_MAKER_STEP_H_
