/**
 * Copyright (c) 202306 CoTEK Inc. All rights reserved.
 */
#ifndef __DECISION_MAKER__DECISION_DATA__H__
#define __DECISION_MAKER__DECISION_DATA__H__

#include <cstdint>
#include <map>
#include <mutex>
#include <string>
#include <vector>

#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/cotek_protocal.h"
#include "cotek_common/geometry/cotek_geometry.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/node_diagnostic_manager.h"
#include "cotek_msgs/error.h"

namespace decision_maker {

using Json = nlohmann::ordered_json;
using DecisionMakerNodeStatus = cotek_diagnostic::DecisionMakerNodeStatus;
using NodeStatusManager =
    cotek_diagnostic::NodeStatusManager<DecisionMakerNodeStatus>;

namespace instantAction {

constexpr char kStartPause[] = "startPause";
constexpr char kStopPause[] = "stopPause";
constexpr char kCancelOrder[] = "cancelOrder";
constexpr char kFinishResponse[] = "finishResponse";
constexpr char kRelocation[] = "initPosition";
constexpr char kEmergencyStop[] = "emergencyStop";
constexpr char kaudioControl[] = "audioControl";
constexpr char kManualControl[] = "manualControl";
constexpr char kClearData[] = "clearData";
constexpr char kTrafficUnlock[] = "trafficUnlock";
constexpr char kRemoteControl[] = "remoteControl";

}  // namespace instantAction

enum class AudioType3 : uint8_t {
  NONE = 0,
  BUMP_FAULT = 1,            // (防撞条触发)报警音
  FORWARD = 2,               // 前进运行
  BACKWARD = 3,              // 倒车（倒车请注意)
  OBSTACLE_WARN = 4,         // 减速避障(嘟嘟嘟提示音)
  OBSTACLE_STOP = 5,         // 停车避障(请移除障碍物)
  LOW_BATTERY = 6,           // 低电量(低电量请注意)
  POWER_ON_INIT = 7,         // 自检中请等待(自检中请等待)
  NETWORK_TIMEOUT = 8,       // 离线(机器人已离线)
  TASK_PAUSE = 9,            // 任务暂停(等待调度指令)
  LOCALIZER_ERROR = 10,      // 定位失败(定位失败)
  OFF_THE_TRACK = 11,        // 出轨(出轨请注意)
  PALLET_DETECT_ERROR = 12,  // 库位检测失败(库位检测失败)
  WARING_PERSON = 13,        // 识别到行人(行人请注意)
  INIT_FAILED = 14,          // 自检失败(自检失败,请注意)
  ROBOT_ERROR = 15,          // 故障(机器人故障)
  CHARGE_ERROR = 16,         // 充电失败(充电失败)   #调度下发
  TASK_ERROR = 17,           // 任务出错(任务出错)
  LOSE_LOAD = 18,            // 掉货(货物掉落请注意)
  INIT_SUCCEED = 19,         // 自检成功或重定位成功(准备就绪)
  NO_IN_WORK_AREA = 20,  // 不在工作区域告警(非工作区域请注意)  #调度下发
  TURN_LEFT_WARNING = 21,   // 左转(左转请注意)
  TURN_RIGHT_WARNING = 22,  // 右转(左转请注意)
  PICK_UP_CALL = 23,        // 请取货(请取货)
  UP_SHELF_LOSS = 24,       // 向上整定失败(托盘丢失)
  ROUTE_PLAN_ERROR = 25,  // 路径规划失败(路径规划失败)  #调度下发
  CHECK_WINDOW = 26,  // XP2手动切换到自动跳弹(弹窗请确认)  #调度下发
  RELOCATION_SUCCEED = 27,   // 重定位成功
  DOWN_TAG_LOSS_ERROR = 28,  // 丢码(向下二维码丢失)
  UP_TAG_LOSS_ERROR = 29,    // 丢码(向上二维码丢失)
  LOCALIZER_LOSE = 30,       // 定位丢失(定位丢失)
  WARING_OCCUPATION =
      31,  // 手动时驶入授权占点(驶入授权占点区域 请驶离)  #调度下发
  DOCKING_TIME_OUT = 32,  // 对接超时
  WARING_STORAGE = 33,  // 库位分配异常(库位分配异常)  #调度下发
  ACTION_TIME_OUT = 34,     // ACTION超时
  EMERGENCY_STOP = 35,      // 急停处于按下状态
  STABILIZE_TIME_OUT = 36,  // 整定超时
  PALLET_LIMIT_ERROR = 37,  // 货物超板(货物超板请注意)

  PLEASE_CONFIRM_MANUALLY = 38,  // 人工请确认(人工请确认)
  STOP_BACKWARD = 39,            // 禁止倒车请确认(禁止倒车请确认)
  HANDRAIL_ERROR = 40,           // 踏板错误
  TRAFFIC_CONTROL = 41,          // 车辆交管
  LEARNING_WARN = 42,            // 示教注意提醒
  WAIT_OPENDOOR = 43,            // 等待电梯/自动门开门
  WAIT_CLOSEDOOR = 44,           // 等待电梯/自动门关门
  CALL_ELEVATOR = 45,            // 呼叫电梯中
  

  AVOID_LASER0_OBSTACLE = 50,  // 0号激光避障
  AVOID_LASER1_OBSTACLE = 51,  // 1号激光避障
  AVOID_LASER2_OBSTACLE = 52,  // 2号激光避障
  AVOID_LASER3_OBSTACLE = 53,  // 3号激光避障
  AVOID_LASER4_OBSTACLE = 54,  // 4号激光避障
  AVOID_LASER5_OBSTACLE = 55,  // 5号激光避障

  AVOID_CAMERA0_OBSTACLE = 60,  // 0号相机避障
  AVOID_CAMERA1_OBSTACLE = 61,  // 1号相机避障
  AVOID_CAMERA2_OBSTACLE = 62,  // 2号相机避障
  AVOID_CAMERA3_OBSTACLE = 63,  // 3号相机避障
  AVOID_CAMERA4_OBSTACLE = 64,  // 4号相机避障
  AVOID_CAMERA5_OBSTACLE = 65,  // 5号相机避障

  NAVI_LASER0_OBSTACLE = 70,  // 导航0号激光避障
  NAVI_LASER1_OBSTACLE = 71,  // 导航1号激光避障
  NAVI_LASER2_OBSTACLE = 72,  // 导航2号激光避障
  NAVI_LASER3_OBSTACLE = 73,  // 导航3号激光避障

  BACK_LIGHT1_AVOID = 81,  // 后置1号光电避障
  BACK_LIGHT2_AVOID = 82,  // 后置2号光电避障

  ULTRASONIC_AVOID = 90,  // 超声波避障

  LASER0_DATA_ERROR = 100,  // 激光数据出错(0号导航激光积灰，请擦拭激光)
  LASER0_ERROR = 101,  // 激光数据出错(0号导航激光出错，请检查)
  LASER1_DATA_ERROR = 102,  // 激光数据出错(1号导航激光积灰，请擦拭激光)
  LASER1_ERROR = 103,  // 激光数据出错(1号导航激光出错，请检查)
  LASER2_DATA_ERROR = 104,  // 激光数据出错(2号导航激光积灰，请擦拭激光)
  LASER2_ERROR = 105,  // 激光数据出错(2号导航激光出错，请检查)

};
struct AtomicPoint {
  double x{0.0};
  double y{0.0};
  double z{0.0};
  double weight{0.};
  double theta{0.};
  double angle{0.};
};

struct PathState {
  dispacth::PathType path_type{dispacth::PathType::NONE};
  float percent{0.};
};

struct Velocity {
  float velocity{0.0};
  float omega{0.0};
};

struct NodePoint : public AtomicPoint {
  double allowed_deviation_xy{0.0};
  double allowed_deviation_theta{0.0};
  std::string map_id;
  std::string map_description;
};

struct Trajectory {
  double degree{0.0};
  std::vector<double> knot;
  std::vector<AtomicPoint> control_points;
};

struct Action {
  std::string action_type;
  std::string action_id;
  std::string action_description;
  std::string blocking_type;
  std::vector<std::string> value_list;
  std::string mix_conditions;
};

struct StepAction {
  std::string action_type;
  std::string action_id;
  std::string action_description;
  std::string blocking_type;
  std::vector<std::string> value_list;
  std::string mix_conditions;
};

enum NodeType { kNodeNormal, kNodeStart, kNodeEnd, kEdge };

struct Node {
  uint32_t node_type{kNodeNormal};  //节点类型
  std::string node_id;              // 节点编号
  int32_t sequence_id{0};           // 节点序列编号
  std::string node_description;
  NodePoint node_position;      // 节点位姿
  bool released{false};         // 授权
  std::vector<Action> actions;  // 节点动作列表
};

struct Edge {
  std::string edge_id;            // 边id
  int32_t sequence_id{0};         //边序列号
  std::string edge_descripition;  //边描述
  bool released{false};           //是否授权
  std::string start_node_id;      //开始节点
  std::string end_node_id;        //目标节点
  float max_height{0.0};          //最大高度
  float min_height{0.0};          //最小高度
  float max_speed{0.0};           //最大速度
  float orientation{0.0};         //全局坐标系朝向角 单位: rad
  std::string orientation_type;   //固定字符GLOBAL
  std::string direction;
  bool rotation_allowed{false};             //是否运行原地旋转
  float max_rotation_speed{0.0};            //最大旋转速度
  Trajectory trajectory;                    //曲线控制点
  float length{0.0};                        //路径长度
  std::vector<Action> actions;              //节点动作列表
  int32_t avoid_map{0};                     //避障地图
  std::vector<std::string> avoid_strategy;  //传感器避障策略
  std::string mix_conditions;
  int start_index;
  int end_index;
};

struct ActionState {
  std::string action_id;
  std::string action_type;
  std::string action_description;
  std::string action_status{
      "WAITING"};  // WAITING INITIALIZING RUNNING PAUSED FINISHED FAILED
  bool operator==(const ActionState &state) {
    return action_id == state.action_id && action_type == state.action_type &&
           action_status == state.action_status;
  }
};

enum ActionType { kActionStartNode, kActionNode, kActionEdge, kActionEndNode };

struct ActionStep {
  uint32_t type{kActionNode};
  Node node;
  Edge edge;
};
enum class GoalType : uint32_t {
  NONE = 0,
  TRACK_PATH_GOAL = 1,
  ACTION_GOAL = 2,
  TRACK_PATH_ACTION_GOAL = 3,
  END = 4,
  OPEN_LOOP_CAN_BREAK = 5,
  OPEN_LOOP_NOT_BREAK = 6,
  END_NO_FINISH = 7
};

struct Order {
  std::string order_id;        //工单号
  std::string task_id;         //任务号
  int32_t kuqu_id;
  int32_t order_update_id{0};  // 工单更新id
  std::string zone_set_id;
  Node end_point;
  std::vector<Node> nodes;
  std::vector<Edge> edges;
};

struct InstantAction {
  std::vector<Action> actions;
};

struct ForkliftLogicOption {
  double load_distance_offset{0.0};  // 米
  double unload_distance_offset{0.0};
  double charge_distance_offset{0.0};

  bool enable_auto_fork_up_down{false};
  double auto_fork_up_margin{0.0};
  double auto_fork_down_margin{0.0};

  bool enable_lose_pallet_check{false};
  uint32_t klost_weighing_threshold{0};
};

struct JackUpLogicOption {};

// 逻辑参数
struct LogicOption {
  AgvType agv_type{AgvType::UNKONW};
  bool enable_record_tf{false};
  int volume{90};
  double need_rotate_theta{0.0};

  ForkliftLogicOption fork_lift_logic_option;
  JackUpLogicOption jack_up_logic_option;
};

using AgvErrorLevel = common::AgvErrorLevel;
using Pose = cotek_geometry::Pose;

enum class StepFinishState : uint8_t {
  DONE_AND_SUCCEED = 1,
  DONE_BUT_FAILED = 2,
  NOT_DONE = 3,
  END_STEP_FINISH = 4,
  END_STEP_NO_FINISH = 5,
  DONE_STEP_TO_OPERATION = 6,
  DONE_STEP_TO_END = 7,
  GOAL_ERROR = 200

};

enum class GoalFinishState : uint8_t {
  FAILED = 0,
  SUCCEEDED = 1,
  SUCCEEDED_TO_OPERATION = 2,
  SUCCEEDED_TO_END = 3
};

enum class ErrorType : uint16_t {
  NORMAL = 0,
  TASK_SEQUENCE_NUMBER_ERROR = 1,
  TASK_ORDER_ID_ERROR = 2,
  SEQUENCE_ID_ERROR = 3,
  TASK_TYPE_ERROR = 4,
  DONGLE_DIED = 5,
  LOST_GOODS = 6
  // 警告(自行增加)
};

enum class TaskControlType : int32_t {
  NONE = 0,
  STOP_RECOVER = 1,
  PAUSE_STOP = 2,
  EMERGENCY_STOP = 3,
  SINGLE_TASK_START = 4,
  SINGLE_TASK_STOP = 5,
  TASK_CANCEL = 11,
  TRAFFIC_UNLOCK = 21,
  REMOTE_CONTROL = 22,
  CANCEL_REMOTE_CONTROL = 23
};

enum class LoadStateType : int32_t { UNKONW = 0, ON_LOAD = 1, NO_LOAD = 2 };

// enum class AgvErrorLevel : uint16_t {
//   NONE = 0,
//   WAITING = 1,
//   ERROR = 2,
//   FATAL = 3
// };

enum class ResponseType : int32_t {
  NONE = 0,
  TASK_RECEIVED = 1,
  TASK_ABNORMAL = 2,
  CONTROL_RECEIVED = 3
  // FINISH_RESPONSE = 4,
};

enum class StateErrorType {
  NONE = 0,
  CHARGEERROR = 1,
  FORKERROR = 2,
  FAULT = 3,
  LOST_PALLET = 4
};

enum class DirectionType : uint8_t {
  NONE = 0,
  FORWARD = 1,
  TURN_LEFT = 2,
  TURN_RIGHT = 3,
  BACKWARD = 4,
};
enum class AgvErrorType : uint32_t {
  NONE = 0,
  DRIVER_NOT_READY = 1,
  BUMP = (1 << 1),
  LOCALIZER_ERROR = (1 << 2),
  SLAM_DELAY_ERROR = (1 << 3),
  SENSOR_TIME_OUT = (1 << 4),
  HANDRAIL = (1 << 6),
  NO_RECOVER_FATAL = (1 << 30)

};

struct TaskEndPoint {
  TaskEndPoint() : sequence_id(1), sequence_num(0), point_id(0) {}

  int sequence_id{1};  // 由1开始
  int sequence_num{0};
  int point_id{0};
  Pose end_pose;
};

struct TaskOdomInfo {
  TaskOdomInfo()
      : consume_time(0.), odom(0.), total_duration(0.), avoid_duration(0.) {}

  std::string order_id;
  ros::Time start_time;
  double consume_time{0.};
  double odom{0.0};
  double total_duration{0.0};
  double avoid_duration{0.0};
};

struct OdomInfo {
  OdomInfo() : odom(0.), total_duration(0.), avoid_duration(0.) {}

  double odom{0.0};
  double total_duration{0.0};
  double avoid_duration{0.0};
};

struct SafetyState {
  bool avoid_overtime{false};
  AvoidLevel type{AvoidLevel::NONE};
  AvoidSpeedLevel level{AvoidSpeedLevel::FREE};
};

struct LedType {
  CorningLedType corning_led_type{CorningLedType::LIGHT_OFF};
  ThreeColorLedType three_color_led_type{ThreeColorLedType::GREEN_ON};
};

struct FinishResponseType {
  std::string order_id;
  std::string task_id;
  bool finish_state{false};

  explicit FinishResponseType(const std::string &order = "",
                              const std::string &task = "", bool state = false)
      : order_id(order), task_id(task), finish_state(state) {}
};

struct TaskControlAudio {
  AudioType3 audio_type{AudioType3::NONE};
  bool enable{false};
  uint8_t level{0};
  TaskControlAudio() : audio_type(AudioType3::NONE), enable(false), level(0) {}
};

enum class PowerOnState : uint8_t { NONE = 0, DOING = 1, DONE = 2, FAILED = 3 };
// 满足触发条件时，超时会将数据清零
class TimedManagerState {
 public:
  TimedManagerState() : is_trigger_(false), state_(0) {}
  TimedManagerState(const TimedManagerState &) = delete;
  TimedManagerState &operator=(const TimedManagerState &) = delete;

  void set(const bool &trigger, const int &state, const ros::Time &time) {
    std::unique_lock<std::mutex> lock(mutex_);
    is_trigger_ = trigger;
    state_ = state;
    start_time_ = time;
  }

  void set_trigger(const bool &trigger) {
    std::unique_lock<std::mutex> lock(mutex_);
    is_trigger_ = trigger;
  }

  void set_state(const int &state) {
    std::unique_lock<std::mutex> lock(mutex_);
    state_ = state;
  }

  void set_start_time(const ros::Time &time) {
    std::unique_lock<std::mutex> lock(mutex_);
    start_time_ = time;
  }

  inline const bool is_trigger() { return is_trigger_; }

  inline const uint8_t state() { return state_; }

  inline const ros::Time start_time() { return start_time_; }

 private:
  std::mutex mutex_;
  ros::Time start_time_;
  bool is_trigger_;
  uint8_t state_;
};

struct PalletCenter {
  ros::Time time;
  Pose pallet_center_pose;
  double height;
  int id;
  PalletCenter() : id(0), height(0.) {}
};

struct Event {
  // 控制信号
  TaskControlType task_control{TaskControlType::NONE};
  // 调度控制音频信号
  TaskControlAudio task_audio;
  // 手自动
  bool manual{false};
  // 急停信号
  bool emergency_stop{false};
  // 是否充电
  bool charging{false};
  // 音频时间管理信号 语音&&清除故障使用
  std::shared_ptr<TimedManagerState> re_location_state_ptr{nullptr};

  std::shared_ptr<TimedManagerState> power_on_state_ptr{nullptr};
  // 结束任务响应
  FinishResponseType task_finish_response;

  void ResetTaskFinish() { task_finish_response = FinishResponseType(); }

  // 开启掉货检测 抬货之后触发
  bool enable_lose_pallet_check{false};
  // 手自动切换后关闭掉货检测
  bool close_check_weight{false};
  // 检测叉腿上升或下降状态
  bool check_fork_state{false};
  // 货物重量
  int load_weight{0};
  // 货物重量基准
  int load_weight_base{0};
  // 叉腿高度
  int forklift_height{0};
  // 托盘中心位姿
  PalletCenter pallet_center;

  ForkStateType pallet_fork_state;
  // 载货状态
  LoadStateType heap_load_state{LoadStateType::UNKONW};
  LoadStateType load_state{LoadStateType::UNKONW};
  // 二维码id
  uint32_t qr_num_{0};

  // 错误等级
  AgvErrorLevel agv_error_level{AgvErrorLevel::NONE};
  // 错误来源类型
  uint32_t agv_error_type{0};
  cotek_msgs::error agv_error_key;

  bool traffic_unlock_finish{false};
  ros::Time traffic_unlock_finish_time;
};

struct IndicatorType {
  bool finish_request{false};
  bool finish_location_request{false};
  bool bummp_error{false};
  bool handrail_error{false};
  bool pederstrain_waring{false};
  bool avoid_sensor_timeout_error{false};
  bool lost_landmark_error{false};
  bool localizer_match_error{false};
  bool localizer_init_error{false};
  bool laser_error{false};
  bool laser_lost_error{false};
  bool shelf_loss_error{false};
  bool down_tag_loss_error{false};
  bool up_tag_loss_error{false};
  bool down_qr_camera_error{false};
  bool up_qr_camera_error{false};
  bool get_velocity_error{false};
  bool docking_time_out_error{false};
  bool stabilize_time_out_error{false};
  bool action_time_out_error{false};
  bool navi_error{false};
  bool stop_backward_error{false};
  bool embedded_driver_error{false};
  bool pallet_detect_error{false};
  bool switch_audio_type{false};
  bool dongle_alive_state_{false};
  bool up_weight_check_error{false};
  bool pallet_limit_error{false};
  bool down_weight_check_error{false};
  bool slam_pose_delay_error{false};
  DirectionType direction_type{DirectionType::NONE};
  StateErrorType error_code{StateErrorType::NONE};

  int switch_section_id{0};  // 将要切换区域id

  std::string current_zone_id;  // 当前所在地图
  std::string current_map_id;   //当前所在区域
};

struct AudioStateType {
  AudioType3 type{AudioType3::NONE};
  uint8_t level{90};
  uint32_t repeat{0};
};

struct ReportType {
  // 3.0
  std::string order_id;
  std::string task_id;
  int32_t order_update_id;
  std::string zone_id;  // 地图id
  std::string map_id;
  std::string last_node_id;
  int32_t last_sequence_id;
  Node end_node;

  std::string agv_status;  // 状态机状态
  std::string operation_mode;

  bool driving{false};
  bool paused{false};

  std::vector<Node> node_states;
  std::vector<Edge> edge_states;
  std::vector<ActionState> action_states;

  bool new_base_request{false};
  double distance_since_last_node{0.};

  OdomInfo global_odom_info;
  cotek_msgs::error agv_error_key;

  // 2.0
  // task_manager current_step sequenceId
  int task_sequence{0};  // 当前正在处理的任务指令的序列号

  // safety setting
  int avoid_map{0};  // 目前先用调度发下来的避障区域
  std::vector<std::string> avoid_strategy;  // 避障策略

  int switch_section_id{0};  // 当前切换区域

  // task end
  TaskEndPoint task_end_point;  // 当前任务终点属性
  double remain_distance{0.0};  // 当前任务与终点距离

  // audio
  bool audio_control{false};
  int audio_type{0};
};

}  // namespace decision_maker

#endif  // __DECISION_MAKER__DECISION_DATA__H__
