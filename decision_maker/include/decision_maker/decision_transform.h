/**
 * Copyright (c) 202306 CoTEK Inc. All rights reserved.
 */
#ifndef __DECISION_MAKER__DECISION_TRANSFORM__H__
#define __DECISION_MAKER__DECISION_TRANSFORM__H__

#include <cstdint>
#include <map>
#include <string>
#include <vector>

#include "cotek_msgs/end_point.h"
#include "cotek_msgs/order.h"
#include "cotek_msgs/update_event.h"
#include "decision_data.h"

namespace decision_maker {

inline void transform(const cotek_msgs::control_point &data, AtomicPoint &msg) {
  msg.x = data.x;
  msg.y = data.y;
  msg.weight = data.weight;
}

inline void transform(const cotek_msgs::node_position &data, NodePoint &msg) {
  msg.x = data.x;
  msg.y = data.y;
  msg.theta = data.theta;
  msg.map_id = data.map_id;
  msg.map_description = data.map_description;
  msg.allowed_deviation_xy = data.allowed_deviation_xy;
  msg.allowed_deviation_theta = data.allowed_deviation_theta;
}

inline void transform(const NodePoint &msg, cotek_msgs::node_position &data) {
  data.x = msg.x;
  data.y = msg.y;
  data.theta = msg.theta;
  data.map_id = msg.map_id;
  data.map_description = msg.map_description;
  data.allowed_deviation_xy = msg.allowed_deviation_xy;
  data.allowed_deviation_theta = msg.allowed_deviation_theta;
}

inline void transform(const cotek_msgs::action &data, Action &msg) {
  msg.action_id = data.action_id;
  msg.action_type = data.action_type;
  msg.action_description = data.action_description;
  msg.blocking_type = data.blocking_type;
  msg.value_list = data.action_value_list;
  msg.mix_conditions = data.mix_conditions;
}

inline void transform(const Action &msg, cotek_msgs::action &data) {
  data.action_id = msg.action_id;
  data.action_type = msg.action_type;
  data.action_description = msg.action_description;
  data.blocking_type = msg.blocking_type;
  data.action_value_list = msg.value_list;
  data.mix_conditions = msg.mix_conditions;
}

inline void transform(const cotek_msgs::node &data, Node &msg) {
  msg.sequence_id = data.sequence_id;
  msg.node_id = data.node_id;
  msg.node_description = data.node_description;
  msg.released = data.released;
  transform(data.node_position, msg.node_position);
  for (auto &action : data.actions) {
    Action _action;
    transform(action, _action);
    msg.actions.emplace_back(_action);
  }
}

inline void transform(const Node &msg, cotek_msgs::node &data) {
  data.sequence_id = msg.sequence_id;
  data.node_id = msg.node_id;
  data.node_description = msg.node_description;
  data.released = msg.released;
  transform(msg.node_position, data.node_position);
  for (auto &action : msg.actions) {
    cotek_msgs::action _action;
    transform(action, _action);
    data.actions.emplace_back(_action);
  }
}

inline void transform(const Node &msg, cotek_msgs::end_point &data) {
  data.node_id = msg.node_id;
  data.node_description = msg.node_description;
  transform(msg.node_position, data.node_position);
  for (auto &action : msg.actions) {
    cotek_msgs::action _action;
    transform(action, _action);
    data.actions.emplace_back(_action);
  }
}

inline void transform(const Node &msg, cotek_msgs::node_state &data) {
  data.sequence_id = msg.sequence_id;
  data.node_id = msg.node_id;
  data.node_description = msg.node_description;
  data.released = msg.released;
  for (auto &action : msg.actions) {
    cotek_msgs::action _action;
    transform(action, _action);
    data.actions.emplace_back(_action);
  }
  transform(msg.node_position, data.node_position);
}

inline void transform(const cotek_msgs::trajectory &data, Trajectory &msg) {
  msg.degree = data.degree;
  msg.knot = data.knot_vector;
  for (auto &pt : data.control_points) {
    AtomicPoint _pt;
    transform(pt, _pt);
    msg.control_points.emplace_back(_pt);
  }
}

inline void transform(const cotek_msgs::edge &data, Edge &msg) {
  msg.edge_id = data.edge_id;                      // 边id
  msg.sequence_id = data.sequence_id;              // 边序列号
  msg.edge_descripition = data.edge_descripition;  // 边描述
  msg.released = data.released;                    // 是否授权
  msg.start_node_id = data.start_node_id;          // 开始节点
  msg.end_node_id = data.end_node_id;              // 目标节点
  msg.max_speed = data.max_speed;                  // 最大速度
  msg.max_height = data.max_height;                // 最大高度
  msg.min_height = data.min_height;                // 最小高度
  msg.orientation = data.orientation;  // 全局坐标系朝向角 单位: rad
  msg.orientation_type = data.orientation_type;  // 固定字符GLOBAL
  msg.direction = data.direction;
  msg.rotation_allowed = data.rotation_allowed;      // 是否运行原地旋转
  msg.max_rotation_speed = data.max_rotation_speed;  // 最大旋转速度
  transform(data.trajectory, msg.trajectory);        // 曲线控制点
  msg.length = data.length;                          // 路径长度
  for (auto &action : data.actions) {
    ;  // 节点动作列表
    Action _action;
    transform(action, _action);
    msg.actions.emplace_back(_action);
  }
  msg.avoid_map = data.avoid_map;             // 避障地图
  msg.avoid_strategy = data.avoid_strategys;  // 传感器避障策略
  msg.mix_conditions = data.mix_conditions;   // 自定义完成条件
  msg.start_index = data.start_index;
  msg.end_index = data.end_index;
}

inline void transform(const Edge &msg, cotek_msgs::edge_state &data) {
  data.edge_id = msg.edge_id;                      // 边id
  data.sequence_id = msg.sequence_id;              // 边序列号
  data.edge_descripition = msg.edge_descripition;  // 边描述
  data.released = msg.released;                    // 是否授权
  data.orientation = msg.orientation;              // 路径朝向
  // transform(data.trajectory, msg.trajectory)    ;  // 曲线控制点
  data.avoid_map = msg.avoid_map;             // 避障地图
  data.avoid_strategys = msg.avoid_strategy;  // 传感器避障策略
  // data.mix_conditions     = msg.mix_conditions    ;  // 自定义完成条件
}

inline void transform(const cotek_msgs::end_point &data, Node &msg) {
  msg.node_type = NodeType::kNodeEnd;
  msg.node_id = data.node_id;                        //终点节点id
  msg.node_description = data.node_description;      //终点节点描述符
  transform(data.node_position, msg.node_position);  //任务终点位姿
  for (auto &action : data.actions) {                //任务终点动作列表
    Action _action;
    transform(action, _action);
    msg.actions.emplace_back(_action);
  }
}

inline void transform(const cotek_msgs::order &data, Order &msg) {
  msg.order_id = data.order_id;
  msg.task_id = data.task_id;
  msg.kuqu_id = data.kuqu_id;
  msg.order_update_id = data.order_update_id;
  msg.zone_set_id = data.zone_set_id;
  for (auto &node : data.nodes) {
    Node _node;
    transform(node, _node);
    msg.nodes.emplace_back(_node);
  }

  for (auto &edge : data.edges) {
    Edge _edge;
    transform(edge, _edge);
    msg.edges.emplace_back(_edge);
  }

  transform(data.end_point, msg.end_point);
}

}  // namespace decision_maker

#endif  // __DECISION_MAKER__DECISION_TRANSFORM__H__
