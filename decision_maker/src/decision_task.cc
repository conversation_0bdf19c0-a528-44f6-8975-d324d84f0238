/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "decision_maker/decision_task.h"

#include <cstdint>
#include <exception>

#include "cotek_common/log_porting.h"
#include "cotek_common/math.h"
#include "decision_maker/decision_config.h"
#include "decision_maker/decision_data.h"
#include "decision_maker/decision_transform.h"
#include "eigen3/Eigen/Dense"
#include "step.h"

namespace decision_maker {
// 3.0
std::vector<Node> DecisionTask::GetRemainNodeStates() {
  std::vector<Node> nodes;
  if (task_ptr_ && is_doing_task_) {
    for (auto iter = iter_; iter != task_ptr_->end(); iter++) {
      nodes.push_back((*iter).node());
    }
  }
  return nodes;
}
std::vector<Edge> DecisionTask::GetRemainEdgeStates() {
  std::vector<Edge> edges;
  if (task_ptr_ && is_doing_task_) {
    for (auto iter = iter_; iter != task_ptr_->end(); iter++) {
      edges.push_back((*iter).edge());
    }
  }
  return edges;
}

std::vector<ActionState> DecisionTask::GetActionStates() {
  std::vector<ActionState> action_states;
  if (task_ptr_ && is_doing_task_) {
    for (auto iter = task_ptr_->begin(); iter != iter_; iter++) {
      for (auto&& action : (*iter).action_goals()) {
        if (!action.action_state.action_id.empty()) {
          action.action_state.action_status = "FINISHED";
          action_states.push_back(action.action_state);
        }
      }
    }
  }
  return action_states;
}

bool DecisionTask::TaskAvailable() {
  if (task_ptr_ != nullptr && is_doing_task_) {
    return true;
  }
  return false;
}

bool DecisionTask::StartTask() {
  if (is_doing_task_ || task_ptr_->empty()) {
    return false;
  }
  current_order_id_ = task_ptr_->order_id();
  iter_ = task_ptr_->begin();
  current_step_ = next_step_ = *iter_;
  next_next_step_ = iter_ == task_ptr_->end() - 1 ? *iter_ : *(iter_ + 1);
  return true;
}

void DecisionTask::FreshStep() {
  current_step_ = (iter_ == task_ptr_->begin()) ? *iter_ : *(iter_ - 1);
  next_step_ = *(iter_);
  next_next_step_ = (iter_ == task_ptr_->end() - 1) ? *iter_ : *(iter_ + 1);
}

void DecisionTask::Moveforward() {
  current_step_ = next_step_;
  next_step_ = iter_ == task_ptr_->end() - 1 ? *iter_ : *(++iter_);
  next_next_step_ = iter_ == task_ptr_->end() - 1 ? *iter_ : *(iter_ + 1);
  LOG_WARN("[task_manager] Step Moveforward");
}

void DecisionTask::MoveToOperation() {
  auto iter_temp = std::find_if(iter_, task_ptr_->end(), [=](const Step& it) {
    return it.hasOperationGoal();
  });
  iter_ = iter_temp == task_ptr_->end() ? task_ptr_->end() - 1 : iter_temp;
  current_step_ = iter_ == task_ptr_->begin() ? *iter_ : *(iter_ - 1);
  next_step_ = *iter_;
  next_next_step_ = iter_ == task_ptr_->end() - 1 ? *iter_ : *(iter_ + 1);

  LOG_WARN("[task_manager] Step Move to operation");
}

void DecisionTask::MoveToEnd() {
  auto iter_temp = std::find_if(iter_, task_ptr_->end(), [=](Step it) {
    return it.sequence_id() == final_end_node_.sequence_id;
  });
  iter_ = iter_temp == task_ptr_->end() ? task_ptr_->end() - 1 : iter_temp;
  current_step_ = iter_ == task_ptr_->begin() ? *iter_ : *(iter_ - 1);
  next_step_ = *iter_;
  next_next_step_ = iter_ == task_ptr_->end() - 1 ? *iter_ : *(iter_ + 1);

  LOG_WARN("[task_manager] Step Move to end");
}

DirectionType DecisionTask::ComputeStepDirection(const Step& current_step,
                                                 const Step& next_step) const {
  // compute direction for forward od backward
  bool is_forward = false;
  if (next_step.path_type() == PathType::B_SPLINE_CURVES) {
    Eigen::Vector2d start_oritentation(std::cos(current_step.point_yaw()),
                                       std::sin(current_step.point_yaw()));
    Eigen::Vector2d start_to_end(
        next_step.control_point_0().x() - current_step.point().x(),
        next_step.control_point_0().y() - current_step.point().y());
    is_forward = start_oritentation.dot(start_to_end) > 0. ? true : false;
  } else {
    Eigen::Vector2d start_oritentation(std::cos(current_step.point_yaw()),
                                       std::sin(current_step.point_yaw()));
    Eigen::Vector2d start_to_end(
        next_step.point().x() - current_step.point().x(),
        next_step.point().y() - current_step.point().y());
    is_forward = start_oritentation.dot(start_to_end) > 0. ? true : false;
  }

  if (!is_forward) return DirectionType::BACKWARD;
  if (next_step.path_type() == PathType::B_SPLINE_CURVES) {
    Eigen::Vector3d current_to_next;
    Eigen::Vector3d current;
    current_to_next << next_step.point().x() - current_step.point().x(),
        next_step.point().y() - current_step.point().y(), 0;
    current << std::cos(current_step.point_yaw()),
        std::sin(current_step.point_yaw()), 0;
    return current.cross(current_to_next)[2] > 0 ? DirectionType::TURN_LEFT
                                                 : DirectionType::TURN_RIGHT;
  }
  return DirectionType::FORWARD;
}

DirectionType DecisionTask::NextStepDirection() const {
  return ComputeStepDirection(current_step_, next_step_);
}

DirectionType DecisionTask::NextNextStepDirection() const {
  return ComputeStepDirection(next_step_, next_next_step_);
}

bool DecisionTask::IsCurrentStepTerminal() const {
  if (nullptr != task_ptr_ && !task_ptr_->empty() &&
      current_step_.sequence_id() == task_ptr_->back().sequence_id() &&
      current_step_.node_id() != final_end_node_.node_id && !task_cancle_) {
    return true;
  }
  return false;
}

bool DecisionTask::IsCurrentStepTerminal(const Step& current_step,
                                         const Step& next_step) const {
  if (current_step.sequence_id() == next_step.sequence_id()) {
    return true;
  }
  return false;
}

bool DecisionTask::IsCurrentStepOpreation(const Step& current_step,
                                          const Step& next_step) const {
  bool ret = false;

  if (current_step.hasOperationGoal()) {
    ret = true;
  }
  return ret;
}

bool DecisionTask::IsNextStepCurve(const Step& current_step,
                                   const Step& next_step) const {
  return next_step.path_type() == PathType::B_SPLINE_CURVES;
}

bool DecisionTask::IsVelocityReserve(const Step& before_step,
                                     const Step& current_step,
                                     const Step& next_step) const {
  Eigen::Vector2d current_to_next;
  Eigen::Vector2d next_to_next_next;

  if (before_step.sequence_id() == current_step.sequence_id() ||
      current_step.sequence_id() == next_step.sequence_id()) {
    return false;
  }

  int cur_dir = current_step.GetDirection();

  int next_dir = next_step.GetDirection();

  if (cur_dir * next_dir < 0) {
    LOG_INFO_STREAM("Velocity reverse!!! "
                    << current_step.edge().edge_id << " dir: " << cur_dir << " "
                    << next_step.edge().edge_id << " dir: " << next_dir);
    return true;
  }
  return false;
}

bool DecisionTask::IsNextStepTerminal() const {
  if (nullptr != task_ptr_ && !task_ptr_->empty() &&
      next_step_.sequence_id() == task_ptr_->back().sequence_id() &&
      next_step_.node_id() != final_end_node_.node_id) {
    return true;
  }
  return false;
}

bool DecisionTask::IsNextStepFinal() const {
  if (nullptr == task_ptr_ || task_ptr_->empty() ||
      iter_ != task_ptr_->end() - 1) {
    return false;
  }

  return next_step_.node_id() == final_end_node_.node_id;
}

// 叉车类原地旋转使用
bool DecisionTask::IsNextLineNeedRotation() const {
  if (current_step_.path_type() == PathType::STRAIGHT_LINE &&
      next_step_.path_type() == PathType::STRAIGHT_LINE &&
      std::fabs(angles::normalize_angle(current_step_.point_yaw() -
                                        next_step_.point_yaw())) >=
          option_.need_rotate_theta) {
    // LOG_WARN("[NextStep Need Rotation] current_step_:%f  next_step_:%f",
    //          current_step_.point_yaw(), next_step_.point_yaw());
    return true;
  }
  return false;
}

bool DecisionTask::IsNextStepRotation() const {
  if (std::fabs(angles::normalize_angle(current_step_.point_yaw() -
                                        next_step_.point_yaw())) <
      option_.need_rotate_theta) {
    return false;
  } else {
    LOG_WARN("[NextStep Need Rotation] current_step_:%f  next_step_:%f",
             current_step_.point_yaw(), next_step_.point_yaw());
    return true;
  }
}

bool DecisionTask::IsNextNextStepRotation() const {
  if (std::fabs(angles::normalize_angle(next_step_.point_yaw() -
                                        next_next_step_.point_yaw())) <
      option_.need_rotate_theta) {
    return false;
  } else {
    LOG_WARN("[NextNextStep Need Rotation] next_step_:%f  next_next_step_:%f",
             next_step_.point_yaw(), next_next_step_.point_yaw());
    return true;
  }
}

bool DecisionTask::IsNextNextStepOperation() const {
  return next_next_step_.hasOperationGoal();
}

bool DecisionTask::IsNextStepMoveOperation() const {
  return next_step_.hasMoveOperationGoal();
}

bool DecisionTask::IsNextNextStepCharge() const {
  return next_next_step_.hasTargetActionGoal(AgvTaskOperationType::CHARGE);
}

bool DecisionTask::IsNextNextStepCurve() const {
  return next_next_step_.path_type() == PathType::B_SPLINE_CURVES;
}

bool DecisionTask::IsNextNextStepFinal() const {
  if (nullptr == task_ptr_ || task_ptr_->empty() ||
      (task_ptr_->size() > 2 && iter_ != task_ptr_->end() - 2)) {
    return false;
  }

  return next_next_step_.node_id() == final_end_node_.node_id;
}

AdjustSpeedPose DecisionTask::IsNextStepAdjustSpeed(
    const Step& cur_step, const Step& next_step,
    const Step& next_next_step) const {
  AdjustSpeedPose adjust_speed_pose;

  double angle_diff = std::fabs(angles::normalize_angle(
      next_step.point_yaw() - next_next_step.point_yaw()));

  if ((angle_diff > option_.need_rotate_theta &&
       next_next_step.path_type() != PathType::B_SPLINE_CURVES) ||
      next_next_step.hasTargetAMoveGoal(
          navigation::MotionType::FORK_PALLET_STABLIZER) ||
      next_step.hasOperationGoal() || next_step.sequence_id() == 1 ||
      next_step.node_id() == final_end_node_.node_id ||
      next_step.node_id() == next_next_step.node_id()) {
    adjust_speed_pose.need_adjust = true;
    adjust_speed_pose.adjust_speed = 0.0;
    adjust_speed_pose.adjust_pose =
        Pose(next_step.point(), next_step.point_yaw());
    return adjust_speed_pose;
  }
  // 斜线减速
  if (angle_diff > 0.2 * option_.need_rotate_theta &&
      next_next_step.path_type() != PathType::B_SPLINE_CURVES) {
    adjust_speed_pose.need_adjust = true;
    adjust_speed_pose.adjust_speed = next_next_step.speed();
    adjust_speed_pose.adjust_pose =
        Pose(next_step.point(), next_step.point_yaw());
    return adjust_speed_pose;
  }

  if (next_next_step.path_type() == PathType::B_SPLINE_CURVES) {
    adjust_speed_pose.need_adjust = true;
    adjust_speed_pose.adjust_speed = next_next_step.speed();
    adjust_speed_pose.adjust_pose =
        Pose(next_step.point(), next_step.point_yaw());
    return adjust_speed_pose;
  }
  return adjust_speed_pose;
}

HighPrecisionPose DecisionTask::IsNextStepHighPrecision(
    const Step& cur_step, const Step& next_step,
    const Step& next_next_step) const {
  HighPrecisionPose high_precision_pose;
  // 抬货 卸货 充电 需要高精度
  if (next_step.hasTargetActionGoal(AgvTaskOperationType::UP) ||
      next_step.hasTargetActionGoal(AgvTaskOperationType::DOWN) ||
      next_step.hasTargetActionGoal(AgvTaskOperationType::CHARGE) ||
      next_step.hasTargetActionGoal(AgvTaskOperationType::FORK_HEIGHT_MOVE) ||
      next_step.hasTargetActionGoal(10) /* TODO@(ssh) 临时用10代替预取货 */) {
    high_precision_pose.need = true;
    high_precision_pose.high_precision_pose =
        Pose(next_step.point(), next_step.point_yaw());
    return high_precision_pose;
  }
  return high_precision_pose;
}

bool DecisionTask::IsVelocityReserve() const {
  Eigen::Vector2d current_to_next;
  Eigen::Vector2d next_to_next_next;
  current_to_next << next_step_.point().x() - current_step_.point().x(),
      next_step_.point().y() - current_step_.point().y();
  next_to_next_next << next_next_step_.point().x() - next_step_.point().x(),
      next_next_step_.point().y() - next_step_.point().y();
  auto cur_to_next_dist =
      std::hypot(next_step_.point().x() - current_step_.point().x(),
                 next_step_.point().y() - current_step_.point().y());
  auto next_to_next_next_dist =
      std::hypot(next_next_step_.point().x() - next_step_.point().x(),
                 next_next_step_.point().y() - next_step_.point().y());

  return current_to_next.dot(next_to_next_next) /
                     (cur_to_next_dist * next_to_next_next_dist) >
                 -0.5
             ? false
             : true;
}

bool DecisionTask::FinishTask() {
  if (task_ptr_) {
    is_doing_task_ = false;
    task_cancle_ = false;
    delay_task_cancle_ = false;
    task_cancle_step_cnt_ = 0;
    task_ptr_ = nullptr;
    pause_task_ = false;
    LOG_INFO("[Decision]: FinishTask !!!");
    return true;
  }
  return false;
}

bool DecisionTask::RecoverTask() {
  if (task_ptr_ == nullptr || task_ptr_->empty()) {
    LOG_WARN("NO Task Recover");
    return false;
  }
  LOG_INFO("Task Recover");
  pause_task_ = false;
  return true;
}

bool DecisionTask::PauseTask() {
  if (task_ptr_ == nullptr || task_ptr_->empty()) {
    LOG_WARN("NO Task Pause");
    return false;
  }
  LOG_INFO("Task Pause");
  pause_task_ = true;
  return true;
}

bool DecisionTask::CancleTask() {
  if (task_ptr_ == nullptr || task_ptr_->empty()) {
    LOG_WARN("NO Task Cancle");
    return false;
  }
#if 0 // xp1 屏蔽距离判断
  // 仅剩余路径超过一定阈值时，才允许取消工单，不然会急刹车
  if (NextStep().remain_distance() < 3.0) {
    delay_task_cancle_ = true;
    LOG_WARN("Remain distance not enough. delay cancle order!!!");
    return true;
  }
#endif

  task_cancle_ = true;
  task_cancle_step_cnt_ = 0;
  return true;
}

bool DecisionTask::AddTaskData(const Order& data) {
  if (is_doing_task_) {
    return false;
  }
  task_ptr_ = std::make_shared<Task>(data.order_id, data.order_update_id);

  if (AddSteps(task_ptr_, false, &iter_, data) && StartTask()) {
    is_doing_task_ = true;
    order_ = data;
    return true;
  }
  task_ptr_ = nullptr;
  return false;
}

bool DecisionTask::MergeTaskData(const Order& data) {
  if (!is_doing_task_ || !task_ptr_) {
    return false;
  }
  return AddSteps(task_ptr_, true, &iter_, data);
}

bool DecisionTask::AddTaskData(
    const cotek_msgs::task_request::ConstPtr& task_req) {
  if (is_doing_task_) {
    return false;
  }
  // 当前无任务，新增任务
  task_ptr_ = std::make_shared<Task>(task_req->order_id, task_req->sequence_num,
                                     task_req->step_size);

  if (AddSteps(task_ptr_, false, &iter_, task_req) && StartTask()) {
    is_doing_task_ = true;
    return true;
  }
  return false;
}

bool DecisionTask::MergeTaskData(
    const cotek_msgs::task_request::ConstPtr& task_req, bool add_new_task) {
  // 有任务及首任务非起点任务时 视为拼接
  if (is_doing_task_ &&
      StepType::START_POINT !=
          static_cast<StepType>(task_req->step_list[0].step_type)) {
    // 拼接任务： 任务order id 需和上一次id相同(重复一个节点)
    // eg: 原任务: 1 2 3 4 5 6 7 8 9
    //     新任务:           6 7 8 9 10 11 12
    // erase之后： 1 2 3 4 5 6
    // 最终任务：   1 2 3 4 5 6 7 8 9 10 11 12
    return AddSteps(task_ptr_, true, &iter_, task_req);
  }

  if (add_new_task) {
    // 暂停状态下能够新增任务
    task_ptr_ = std::make_shared<Task>(
        task_req->order_id, task_req->sequence_num, task_req->step_size);
    is_doing_task_ = false;
    if (AddSteps(task_ptr_, false, &iter_, task_req) && StartTask()) {
      is_doing_task_ = true;
      return true;
    }
  }
  return false;
}

bool DecisionTask::AddSteps(
    std::shared_ptr<Task> task, bool append, step_list_t::iterator* cur,
    const cotek_msgs::task_request::ConstPtr& task_req) {
  return true;
}

bool DecisionTask::AddSteps(std::shared_ptr<Task> task, bool append,
                            step_list_t::iterator* cur, const Order& task_req) {
  try {
    // task末尾节点与task_req头节点相同才可拼接
    LOG_WARN_COND(append, "append task!!!");
    LOG_WARN_COND(!append, "add new task!!!");
    if (append) {
      // append task
      if (task_req.order_update_id != task->order_update_id() + 1) {
        LOG_ERROR("append task error, sequence num need be one by one.");
        LOG_ERROR("reveived sequence num :%d", task_req.order_update_id);
        return false;
      }

      if (task_req.order_id != task->order_id()) {
        LOG_ERROR("append task error, order id need be different.");
        node_status_mangager_ptr_->SetNodeStatus(
            DecisionMakerNodeStatus::TASK_ORDER_ID_ERROR);
        return false;
      }

      // 从当前节点往后找
      auto append_iter = std::find_if(*cur, task->end(), [=](Step it) {
        return it.sequence_id() == task_req.nodes.at(0).sequence_id;
      });

      if (append_iter == task->end()) {
        LOG_ERROR("can not find the suquence id, can't append task");
        node_status_mangager_ptr_->SetNodeStatus(
            DecisionMakerNodeStatus::TASK_SEQUENCE_NUMBER_ERROR);
        return false;
      }

      if (append_iter->node_id() != task_req.nodes.at(0).node_id) {
        LOG_ERROR("node_id not match last node_id, can't append task");
        node_status_mangager_ptr_->SetNodeStatus(
            DecisionMakerNodeStatus::TASK_TYPE_ERROR);
        return false;
      }

      int offset = *cur - task->begin();
      task->erase(append_iter + 1, task->end());
      // 防止越界
      task->reserve(append_iter - task->begin() + task_req.nodes.size() + 10);
      // iter重定向 !!!
      *cur = task->begin() + offset;
      (*cur)->set_append(true);
    }

    // new task
    // 新任务首个点必须为起点，且首个序列号为1
    if (!append) {
      if (task_req.order_update_id != 1 || task_req.nodes.size() <= 1 ||
          1 != task_req.nodes.at(0).sequence_id ||
          task_req.end_point.node_id.empty()) {
        node_status_mangager_ptr_->SetNodeStatus(
            DecisionMakerNodeStatus::TASK_TYPE_ERROR);
        LOG_ERROR(
            "Start task error!!!  order_update_id: %d, sequence_id: %d, size: "
            "%d ",
            task_req.order_update_id, task_req.nodes.at(0).sequence_id,
            task_req.nodes.size());
        return false;
      }
    }

    int it = append ? 1 : 0;

    final_end_node_ = task_req.end_point;

    // 起点step只有node没有edge
    if (!append) {
      Step step;
      step.set_order_id(task_req.order_id);
      step.set_task_id(task_req.task_id);
      step.set_kuqu_id(task_req.kuqu_id);
      step.set_node(task_req.nodes.at(it));

      // 添加下一段node与edge 用于导航提前规划
      int next_it = (it == (task_req.nodes.size() - 1)) ? it : it + 1;
      if (next_it > it) {
        step.set_next_node(task_req.nodes.at(next_it));
        step.set_next_edge(task_req.edges.at(next_it - 1));
      }

      if (!step.FirstPlan()) return false;
      task->push_back(step);
      it++;
    }

    // 拼接时，拼接前的最后一个点添加下一段
    if (append && task_req.nodes.size() > 1) {
      auto append_iter = std::find_if(*cur, task->end(), [=](Step it) {
        return it.sequence_id() == task_req.nodes.at(0).sequence_id;
      });

      if (append_iter != task->end()) {
        append_iter->set_next_node(task_req.nodes.at(1));
        append_iter->set_next_edge(task_req.edges.at(0));
      }
    }

    while (it < task_req.nodes.size()) {
      Step step;
      step.set_order_id(task_req.order_id);
      step.set_task_id(task_req.task_id);
      step.set_kuqu_id(task_req.kuqu_id);
      step.set_start_node(task_req.nodes.at(it - 1));
      step.set_node(task_req.nodes.at(it));
      step.set_final_end_node(task_req.end_point);
      auto edge = task_req.edges.at(it - 1);
      //  当调度未发送edge的action时 默认移动action
      if (edge.actions.empty()) {
        Action action;
        action.action_type = "nav";
        edge.actions.push_back(action);
      }
      step.set_edge(edge);

      // 添加下一段node与edge 用于导航提前规划
      int next_it = (it == (task_req.nodes.size() - 1)) ? it : it + 1;
      if (next_it > it) {
        step.set_next_node(task_req.nodes.at(next_it));
        step.set_next_edge(next_it - 1 < task_req.edges.size()? task_req.edges.at(next_it - 1): Edge());
      }

      if (!step.FirstPlan()) return false;

      task->push_back(step);
      it++;
    }

    // 提前规划 寻找特殊位置
    for (step_list_t::iterator iter = append ? *cur : task->begin();
         iter != task->end(); iter++) {
      // 任务串起点
      (*iter).set_start_pose(
          Pose((*task->begin()).point(), (*task->begin()).point_yaw()));
      // 添加取货前必须确保叉腿下降
      // (*iter).insert_action_goal(
      //     ComputeMustDownGoal(iter, task, task_req.end_point, option_));
      (*iter).insert_action_goal(InsertDownGoal(iter, task, option_));
      // 任务串调节速度点
      (*iter).set_adjust_speed_pose(FindAdjustSpeedPose(iter, task));
      // 任务串高精度点
      (*iter).set_high_precision_pose(FindHighPrecisionPose(iter, task));
      // 到达当前任务终点距离
      (*iter).set_remain_distance(ComputeRemainDistance(iter, task));
      // 根据任务调节路线目标速度
      (*iter).set_target_speed(ComputeTargetSpeed(iter, task));
      // 根据后轮中心位移 调节终点位置
      (*iter).set_fix_pose(ComputeFixPose(iter, task));
      // 添加叉腿自动升降
      if (option_.fork_lift_logic_option.enable_auto_fork_up_down) {
        (*iter).insert_action_goal(
            ComputeAutoUpDownGoal(iter, task, task_req.end_point, option_));
      }

      if (!(*iter).SecondPlan()) return false;
    }
    // 拼接后，刷新step属性
    if (append) FreshStep();

    set_order_id(task_req.order_id);
    set_order_update_id(task_req.order_update_id);
    task->set_order_update_id(task_req.order_update_id);
    set_task_id(task_req.task_id);

    final_end_node_ = task_req.end_point;

    LOG_INFO_STREAM(TaskToString());
  } catch (std::exception& ex) {
    LOG_ERROR(ex.what());
    return false;
  }

  return true;
}

AdjustSpeedPose DecisionTask::FindAdjustSpeedPose(
    const step_list_t::iterator& iter, const std::shared_ptr<Task> task) {
  step_list_t::iterator iter_tmp = iter;
  AdjustSpeedPose adjust_speed_pose;
  while (iter_tmp != task->end() - 1) {
    Step cur_step = iter_tmp == task->begin() ? *iter_tmp : *(iter_tmp - 1);
    Step next_step = *iter_tmp;
    Step next_next_step =
        iter_tmp == task->end() - 1 ? *iter_tmp : *(++iter_tmp);
    adjust_speed_pose =
        IsNextStepAdjustSpeed(cur_step, next_step, next_next_step);
    if (adjust_speed_pose.need_adjust) {
      return adjust_speed_pose;
    }
  }
  Step end_step = *iter_tmp;
  adjust_speed_pose.need_adjust = true;
  adjust_speed_pose.adjust_speed = 0.0;
  adjust_speed_pose.adjust_pose = Pose(end_step.point(), end_step.point_yaw());
  return adjust_speed_pose;
}

HighPrecisionPose DecisionTask::FindHighPrecisionPose(
    const step_list_t::iterator& iter, const std::shared_ptr<Task> task) {
  step_list_t::iterator iter_tmp = iter;
  HighPrecisionPose high_precision_pose;
  while (iter_tmp != task->end() - 1) {
    Step cur_step = *iter_tmp;
    Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(++iter_tmp);
    Step next_next_step =
        iter_tmp == task->end() - 1 ? *iter_tmp : *(iter_tmp + 1);
    high_precision_pose =
        IsNextStepHighPrecision(cur_step, next_step, next_next_step);
    if (high_precision_pose.need) {
      return high_precision_pose;
    }
  }
  // end_step
  Step end_step = *iter_tmp;

  high_precision_pose = IsNextStepHighPrecision(end_step, end_step, end_step);

  return high_precision_pose;
}

NextBsplineStepPair DecisionTask::FindNextBsplineStep() {
  NextBsplineStepPair next_b_step;
  step_list_t::iterator iter_tmp = iter_;
  if (task_ptr_ == nullptr) return next_b_step;

  while (iter_tmp != task_ptr_->end() - 1) {
    Step cur_step = *iter_tmp;
    Step next_step =
        iter_tmp == task_ptr_->end() - 1 ? *iter_tmp : *(++iter_tmp);
    Step next_next_step =
        iter_tmp == task_ptr_->end() - 1 ? *iter_tmp : *(iter_tmp + 1);

    if (next_step.path_type() == PathType::B_SPLINE_CURVES) {
      next_b_step.vaild = true;
      next_b_step.first_step = cur_step;
      next_b_step.second_step = next_step;
      return next_b_step;
    }
  }
  next_b_step.vaild = false;
  return next_b_step;
}

DirectionType DecisionTask::NextBsplineDirection() {
  NextBsplineStepPair next_b_step = FindNextBsplineStep();

  if (!next_b_step.vaild) return DirectionType::NONE;
  // 提前3m计算曲线转弯方向
  if (math::GetDistance(current_step_.point(), next_b_step.first_step.point()) >
      3.0)
    return DirectionType::NONE;

  return ComputeStepDirection(next_b_step.first_step, next_b_step.second_step);
}

double DecisionTask::ComputeRemainDistance(const step_list_t::iterator& iter,
                                           const std::shared_ptr<Task> task) {
  step_list_t::iterator iter_tmp = iter;
  double distance = 0;
  while (iter_tmp != task->end() - 1) {
    Step cur_step = *iter_tmp;
    Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(++iter_tmp);
    distance += math::GetDistance(cur_step.point(), next_step.point());
  }
  return distance;
}

double DecisionTask::ComputeTargetSpeed(const step_list_t::iterator& iter,
                                        const std::shared_ptr<Task> task) {
  step_list_t::iterator iter_tmp = iter;
  double target = 0.0;
  Step before_step = iter_tmp == task->begin() ? *iter_tmp : *(iter_tmp - 1);
  Step current_step = *iter_tmp;
  Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(iter_tmp + 1);

  if (IsCurrentStepTerminal(current_step, next_step) ||
      IsCurrentStepOpreation(current_step, next_step) ||
      IsVelocityReserve(before_step, current_step, next_step)) {
    target = 0;
  } else if (IsNextStepCurve(current_step, next_step)) {
    target = next_step.speed();
  } else {
    target = current_step.speed();
  }

  return target;
}

static Point GetOffsetPoint(const Point& start, const Point& end,
                            const double& yaw,
                            const double& load_distance_offset,
                            const bool& relative_dir) {
  Point ret_point;
  // 目标点随各类动作进行补偿
  Eigen::Vector2d start2end(end.x() - start.x(), end.y() - start.y());
  Eigen::Vector2d current_yaw(std::cos(yaw), std::sin(yaw));
  auto forward = start2end.dot(current_yaw) >= 0 ? 1. : -1.;
  forward = relative_dir ? forward : 1.;
  float dis12 = sqrt(pow(end.x() - start.x(), 2) + pow(end.y() - start.y(), 2));
  // 起点与终点同一点
  if (math::NearZero(dis12)) {
    ret_point.set(end.x(), end.y());
  } else {
    float cos = (end.x() - start.x()) / dis12;
    float sin = (end.y() - start.y()) / dis12;
    float ex = end.x() + forward * load_distance_offset * cos;
    float ey = end.y() + forward * load_distance_offset * sin;
    ret_point.set(ex, ey);
  }
  LOG_INFO("[actual_enx_point]: (%f, %f)", ret_point.x(), ret_point.y());
  return ret_point;
}

FixPose DecisionTask::ComputeFixPose(const step_list_t::iterator& iter,
                                     const std::shared_ptr<Task> task) {
  step_list_t::iterator iter_tmp = iter;
  double target = 0.0;
  Step before_step = iter_tmp == task->begin() ? *iter_tmp : *(iter_tmp - 1);
  Step current_step = *iter_tmp;
  Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(iter_tmp + 1);

  FixPose fix_pose;
  if (current_step.hasTargetActionGoal(AgvTaskOperationType::UP)) {
    fix_pose.pose.set_point(GetOffsetPoint(
        before_step.point(), current_step.point(),
        current_step.edge().orientation,
        option_.fork_lift_logic_option.load_distance_offset, false));
    fix_pose.vaild = true;
  }
  if (current_step.hasTargetActionGoal(AgvTaskOperationType::DOWN)) {
    fix_pose.pose.set_point(GetOffsetPoint(
        before_step.point(), current_step.point(),
        current_step.edge().orientation,
        option_.fork_lift_logic_option.unload_distance_offset, true));
    fix_pose.vaild = true;
  }
  if (current_step.hasTargetActionGoal(AgvTaskOperationType::CHARGE)) {
    fix_pose.pose.set_point(GetOffsetPoint(
        before_step.point(), current_step.point(),
        current_step.edge().orientation,
        option_.fork_lift_logic_option.charge_distance_offset, false));
    fix_pose.vaild = true;
  }
  return fix_pose;
}

static bool NodehasTargetAction(const Node& node, const std::string& type) {
  for (auto& action : node.actions) {
    if (action.action_type == type) return true;
  }
  return false;
}

static ActionGoal CreateOpenLoopUpGoal() {
  ActionGoal goal;
  goal.action_state.action_type = "openLoopUp";
  goal.g_type = GoalType::OPEN_LOOP_CAN_BREAK;
  goal.a_goal.action_type = static_cast<int>(AgvTaskOperationType::OPENLOOP_UP);
  return goal;
}

static ActionGoal CreateOpenLoopDownGoal() {
  ActionGoal goal;
  goal.action_state.action_type = "openLoopDown";
  goal.g_type = GoalType::OPEN_LOOP_CAN_BREAK;
  goal.a_goal.action_type =
      static_cast<int>(AgvTaskOperationType::OPENLOOP_DOWN);
  return goal;
}

static ActionGoal CreateUpGoal() {
  ActionGoal goal;
  goal.action_state.action_type = "limitLoad";
  goal.g_type = GoalType::ACTION_GOAL;
  goal.a_goal.action_type = static_cast<int>(AgvTaskOperationType::UP);
  return goal;
}

static ActionGoal CreateDownGoal() {
  ActionGoal goal;
  goal.action_state.action_type = "limitUnload";
  goal.g_type = GoalType::ACTION_GOAL;
  goal.a_goal.action_type = static_cast<int>(AgvTaskOperationType::DOWN);
  return goal;
}

ActionGoal DecisionTask::ComputeAutoUpDownGoal(
    const step_list_t::iterator& iter, const std::shared_ptr<Task> task,
    const Node& end_node, const LogicOption& option) {
  // 一次任务规划仅下发一次抬升和下降
  static bool up_goal = true;
  static bool down_goal = true;
  ActionGoal goal;
  step_list_t::iterator iter_tmp = iter;
  Step before_step = iter_tmp == task->begin() ? *iter_tmp : *(iter_tmp - 1);
  Step current_step = *iter_tmp;
  Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(iter_tmp + 1);

  bool end_node_down = NodehasTargetAction(end_node, "limitLoad") ||
                       NodehasTargetAction(end_node, "charge") ||
                       NodehasTargetAction(end_node, "syncBackward") ||
                       NodehasTargetAction(end_node, "preLoad");
  double dist_to_end = math::GetDistance(
      current_step.point(),
      Point(end_node.node_position.x, end_node.node_position.y));
  double dist_to_start =
      math::GetDistance(current_step.point(), current_step.start_pose());

  // 当前位置距离起点超过阈值生成抬升任务
  if (up_goal &&
      dist_to_start > option.fork_lift_logic_option.auto_fork_up_margin) {
    // 抬升任务需远离取货终点
    if (end_node_down &&
        dist_to_end < option.fork_lift_logic_option.auto_fork_down_margin) {
      // empty
    } else {
      goal = CreateOpenLoopUpGoal();
      up_goal = false;
    }
  }

  // 终点需要下降 && 当前位置接近终点阈值 &&  下降位置也需远离起点
  if (down_goal && end_node_down &&
      (dist_to_end < option.fork_lift_logic_option.auto_fork_down_margin) &&
      (dist_to_start > 1.5) &&
      (!current_step.hasTargetActionGoal(AgvTaskOperationType::UP)) &&
      (!next_step.hasTargetActionGoal(AgvTaskOperationType::UP))) {
    goal = CreateOpenLoopDownGoal();
    down_goal = false;
    up_goal = false;
  }

  if (iter == task->end() - 1) {
    up_goal = true;
    down_goal = true;
  }

  return goal;
}

ActionGoal DecisionTask::InsertDownGoal(const step_list_t::iterator& iter,
                                        const std::shared_ptr<Task> task,
                                        const LogicOption& option) {
  ActionGoal goal;
  step_list_t::iterator iter_tmp = iter;
  Step before_step = iter_tmp == task->begin() ? *iter_tmp : *(iter_tmp - 1);
  Step current_step = *iter_tmp;
  Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(iter_tmp + 1);

  if (next_step.hasTargetActionGoal(AgvTaskOperationType::UP) &&
      current_step.sequence_id() != next_step.sequence_id()) {
    LOG_INFO_STREAM_THROTTLE(1, "insert must down goal!!!");
    goal = CreateDownGoal();
  }

  return goal;
}

ActionGoal DecisionTask::ComputeMustDownGoal(const step_list_t::iterator& iter,
                                             const std::shared_ptr<Task> task,
                                             const Node& end_node,
                                             const LogicOption& option) {
  ActionGoal goal;
  step_list_t::iterator iter_tmp = iter;
  Step before_step = iter_tmp == task->begin() ? *iter_tmp : *(iter_tmp - 1);
  Step current_step = *iter_tmp;
  Step next_step = iter_tmp == task->end() - 1 ? *iter_tmp : *(iter_tmp + 1);

  bool end_node_down = NodehasTargetAction(end_node, "limitLoad") ||
                       NodehasTargetAction(end_node, "charge") ||
                       NodehasTargetAction(end_node, "syncBackward") ||
                       NodehasTargetAction(end_node, "preLoad");

  double dist_from_start_to_end = math::GetDistance(
      current_step.start_pose(),
      Point(end_node.node_position.x, end_node.node_position.y));

  if (end_node_down && dist_from_start_to_end < 6.0 &&
      next_step.hasTargetActionGoal(AgvTaskOperationType::UP) &&
      current_step.sequence_id() != next_step.sequence_id()) {
    LOG_INFO_STREAM_THROTTLE(1, "insert must down goal!!!");
    goal = CreateDownGoal();
  }

  if (end_node_down &&
      (option_.fork_lift_logic_option.auto_fork_down_margin < 0.01) &&
      next_step.hasTargetActionGoal(AgvTaskOperationType::UP) &&
      current_step.sequence_id() != next_step.sequence_id()) {
    LOG_INFO_STREAM_THROTTLE(1, "insert must down goal!!!");
    goal = CreateDownGoal();
  }

  // TODO(@ssh) 临时采用数字
  if (end_node_down && dist_from_start_to_end < 6.0 &&
      next_step.hasTargetActionGoal(10) &&
      current_step.sequence_id() != next_step.sequence_id()) {
    LOG_INFO_STREAM_THROTTLE(1, "insert must down goal!!!");
    goal = CreateDownGoal();
  }
  return goal;
}

bool DecisionTask::IsEndStepFinal() const {
  if (!task_ptr_) return false;

  return task_ptr_->back().node_id() == final_end_node_.node_id;
}

}  // namespace decision_maker
