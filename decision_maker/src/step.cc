/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "decision_maker/step.h"

#include <cstdint>
#include <sstream>
#include <string>

#include "angles/angles.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/geometry/pose.h"
#include "cotek_common/math.h"
#include "decision_data.h"
#include "decision_maker/data_manager.h"

namespace decision_maker {

using MotionType = navigation::MotionType;

static const std::map<PathType, std::string> kPathString = {
    {PathType::POINT, "Point"},
    {PathType::STRAIGHT_LINE, "Line"},
    {PathType::B_SPLINE_CURVES, "Curve"}};

static std::string GetPathString(const PathType &path) {
  std::string ret;
  if (kPathString.count(path) > 0) {
    return kPathString.at(path);
  }
  return ret;
}

ActionGoal Step::CreatStartGoal() {
  ActionGoal goal;
  goal.g_type = GoalType::TRACK_PATH_GOAL;
  goal.t_goal.motion_type = static_cast<int32_t>(MotionType::START_POINT);
  goal.t_goal.navi_type = static_cast<int32_t>(NaviType::FREE);
  goal.t_goal.path_type = static_cast<int32_t>(PathType::POINT);
  goal.t_goal.end_id = std::stoi(node_id());
  goal.t_goal.end_x = point().x();
  goal.t_goal.end_y = point().y();
  goal.t_goal.end_yaw = math::Rad2Deg(node_theta());
  goal.t_goal.max_speed = speed();
  goal.t_goal.start_index = 0;
  goal.t_goal.end_index = 0;
  return goal;
}

ActionGoal Step::CreatEndGoal() {
  ActionGoal goal;
  goal.g_type = GoalType::END;
  goal.t_goal.navi_type = static_cast<int32_t>(NaviType::FREE);
  goal.t_goal.path_type = static_cast<int32_t>(PathType::POINT);
  goal.t_goal.end_id = std::stoi(node_id());
  goal.t_goal.end_x = point().x();
  goal.t_goal.end_y = point().y();
  goal.t_goal.end_yaw = math::Rad2Deg(edge().orientation);
  goal.t_goal.max_speed = speed();
  goal.t_goal.start_index = 0;
  goal.t_goal.end_index = 0;
  return goal;
}

ActionGoal Step::CreatTrafficUnlockEndoal() {
  ActionGoal goal;
  goal.g_type = GoalType::END_NO_FINISH;
  goal.t_goal.navi_type = static_cast<int32_t>(NaviType::FREE);
  goal.t_goal.path_type = static_cast<int32_t>(PathType::POINT);
  goal.t_goal.end_id = std::stoi(node_id());
  goal.t_goal.end_x = point().x();
  goal.t_goal.end_y = point().y();
  goal.t_goal.end_yaw = math::Rad2Deg(edge().orientation);
  goal.t_goal.max_speed = speed();
  return goal;
}

std::vector<ActionGoal> Step::CreatNormalGoals(const Action &action) {
  ActionGoal goal;
  goal.action_state.action_id = action.action_id;
  goal.action_state.action_type = action.action_type;
  goal.action_state.action_description = action.action_description;

  goal.g_type =
      static_cast<GoalType>(DecisionConfig::GetGoalType(action.action_type));

  goal.t_goal.order_id = order_id_;
  goal.t_goal.task_id = task_id_;
  goal.t_goal.kuqu_id = kuqu_id_;

  goal.a_goal.order_id = order_id_;

  if (static_cast<uint8_t>(goal.g_type) & 0B01) {
    goal.t_goal.motion_type = DecisionConfig::GetMotionType(action.action_type);
    goal.t_goal.navi_type = static_cast<int32_t>(NaviType::FREE);

    if (edge_.trajectory.control_points.size() > 1) {
      goal.t_goal.path_type = static_cast<int32_t>(PathType::B_SPLINE_CURVES);
      goal.t_goal.c1_x = edge_.trajectory.control_points.at(0).x;
      goal.t_goal.c1_y = edge_.trajectory.control_points.at(0).y;
      goal.t_goal.c2_x = edge_.trajectory.control_points.at(1).x;
      goal.t_goal.c2_y = edge_.trajectory.control_points.at(1).y;

      set_control_point(Point(edge_.trajectory.control_points.at(0).x,
                              edge_.trajectory.control_points.at(0).y),
                        Point(edge_.trajectory.control_points.at(1).x,
                              edge_.trajectory.control_points.at(1).y));
    } else {
      goal.t_goal.dir = atoi(edge_.direction.c_str());
      goal.t_goal.edge_id = edge_.edge_id;
      goal.t_goal.start_index = edge_.start_index;
      goal.t_goal.end_index = edge_.end_index;
      goal.t_goal.length = edge_.length;
      if (edge_.end_index != 0) {
        goal.t_goal.path_type = static_cast<int32_t>(PathType::FITTING_LINE);
      } else {
        goal.t_goal.path_type = DecisionConfig::GetPathType(action.action_type);
      }
    }

    // 单点任务认定为路径类型为point
    if (!edge_.start_node_id.empty() &&
        edge_.start_node_id == edge_.end_node_id) {
      goal.t_goal.path_type = static_cast<int32_t>(PathType::POINT);
    }

    set_path_type(static_cast<PathType>(goal.t_goal.path_type));

    goal.t_goal.end_id = std::stoi(node_id());
    goal.t_goal.end_x = point().x();
    goal.t_goal.end_y = point().y();

    if (goal.t_goal.motion_type == static_cast<int>(MotionType::FORK_ROTATE)) {
      goal.t_goal.end_yaw = std::stoi(action.value_list[0]);
    } else {
      goal.t_goal.end_yaw = math::Rad2Deg(edge().orientation);
    }

    if (goal.t_goal.motion_type == static_cast<int>(MotionType::SWITCH_MAP)) {
      Pose target_pose;
      target_pose.set(std::stod(action.value_list.at(2)),
                      std::stod(action.value_list.at(3)),
                      std::stod(action.value_list.at(4)));
      goal.t_goal.end_x = target_pose.x();
      goal.t_goal.end_y = target_pose.y();
      goal.t_goal.end_yaw = target_pose.yaw();
    }

    goal.t_goal.action_value = action.value_list;
    goal.t_goal.max_speed = edge().max_speed;
    goal.t_goal.target_speed = edge().max_speed;
    goal.t_goal.length = edge().length;
    goal.t_goal.mix_condition = action.mix_conditions;

    // next
    if (next_node_.sequence_id != 0) {
      goal.t_goal.next_end_id = std::stoi(next_node_.node_id);
      goal.t_goal.next_end_x = next_node_.node_position.x;
      goal.t_goal.next_end_y = next_node_.node_position.y;
      goal.t_goal.next_end_yaw = math::Rad2Deg(next_edge_.orientation);
      if (next_edge_.trajectory.control_points.size() > 1) {
        goal.t_goal.next_path_type =
            static_cast<int32_t>(PathType::B_SPLINE_CURVES);
        goal.t_goal.next_c1_x = next_edge_.trajectory.control_points.at(0).x;
        goal.t_goal.next_c1_y = next_edge_.trajectory.control_points.at(0).y;
        goal.t_goal.next_c2_x = next_edge_.trajectory.control_points.at(1).x;
        goal.t_goal.next_c2_y = next_edge_.trajectory.control_points.at(1).y;
      } else {
        goal.t_goal.next_edge_id = next_edge_.edge_id;
        goal.t_goal.next_start_index = next_edge_.start_index;
        goal.t_goal.next_end_index = next_edge_.end_index;
        if (next_edge_.end_index != 0) {
          goal.t_goal.next_path_type = static_cast<int32_t>(PathType::FITTING_LINE);
        } else {
          goal.t_goal.next_path_type =
              static_cast<int32_t>(PathType::STRAIGHT_LINE);        
        }

      }
    }
  }

  std::vector<ActionGoal> goals;

  if (static_cast<uint8_t>(goal.g_type) & 0B10) {
    goal.a_goal.mix_condition = action.mix_conditions;

    // goal.a_goal.action_type =
    //     DecisionConfig::GetAtomicActionType(action.action_type);
    // goal.a_goal.action_value = action.value_list;

    if (1) {
      if (action.action_type.find("DT") == std::string::npos) {
        goal.a_goal.action_type =
            DecisionConfig::GetAtomicActionType(action.action_type);
        goal.a_goal.action_value = action.value_list;
      } else {
        if (action.action_type == "callDT") {
          goal.a_goal.action_type =
              DecisionConfig::GetAtomicActionType(action.action_type);
          goal.a_goal.action_value = action.value_list;
          goals.push_back(goal);

          goal.a_goal.action_type =
              DecisionConfig::GetAtomicActionType("controlDT");
          goal.a_goal.action_value.clear();
          goal.a_goal.action_value.push_back("1");
          goal.action_state.action_type = "controlDT";
          // goals.push_back(goal);
        }

        if (action.action_type == "takeDT") {
          goal.a_goal.action_type =
              DecisionConfig::GetAtomicActionType("controlDT");
          goal.a_goal.action_value.clear();
          goal.a_goal.action_value.push_back("-1");
          goal.action_state.action_type = "controlDT";
          goals.push_back(goal);

          goal.a_goal.action_type =
              DecisionConfig::GetAtomicActionType("callDT");
          goal.a_goal.action_value.clear();
          goal.a_goal.action_value.push_back(action.value_list.at(0));
          goal.action_state.action_type = "callDT";
          goals.push_back(goal);

          goal.a_goal.action_type =
              DecisionConfig::GetAtomicActionType("controlDT");
          goal.a_goal.action_value.clear();
          goal.a_goal.action_value.push_back("1");
          goal.action_state.action_type = "controlDT";
          // goals.push_back(goal);  

        }

        if (action.action_type == "leaveDT") {
          goal.a_goal.action_type =
              DecisionConfig::GetAtomicActionType("controlDT");
          goal.a_goal.action_value.clear();
          goal.a_goal.action_value.push_back("-1");
          // goals.push_back(goal);
        }
      }
    }

  }

  ActionGoal temp_goal = goal;
  // 运动前判定是否插入原地旋转
  if (NeedRotateGoal(temp_goal)) {
    goals.push_back(temp_goal);
  }
  goals.push_back(goal);

  return goals;
}

bool Step::NeedRotateGoal(ActionGoal &goal) {
  // 需确保定位
  VaildPose current_pose = DataManager::Instance().robot_pose();
  if (!current_pose.vaild) {
    return false;
  }
  // 仅移动任务才能生成原地旋转
  if (!(static_cast<uint8_t>(goal.g_type) & 0B01)) {
    return false;
  }
  // 路径允许原地旋转&&路线角度与当前定位角度超过阈值
  // 一个step仅允许插入一个原地旋转
  if (edge_.rotation_allowed && (insert_rotate_goal_cnt_ == 0) &&
      (std::fabs((angles::normalize_angle(edge_.orientation) -
                  angles::normalize_angle(current_pose.pose.theta()))) > 1.0)) {
    goal.action_state.action_id = "fork_rotate";
    goal.action_state.action_type = "fork_rotate";
    goal.t_goal.motion_type = static_cast<int>(MotionType::FORK_ROTATE);
    goal.t_goal.path_type = static_cast<int>(PathType::POINT);
    goal.t_goal.end_yaw = math::Rad2Deg(edge().orientation);
    insert_rotate_goal_cnt_++;
    LOG_INFO("Insert rotate goal!!!");
    return true;
  }
  return false;
}

void Step::insert_action_goal(const ActionGoal &goal) {
  if (goal.g_type == GoalType::NONE) return;
  if (hasGoal(goal)) return;
  action_goals_.push_back(goal);
}

bool Step::FirstPlan() {
  bool ret = false;
  if (sequence_id() == 0) return ret;

  if (sequence_id() == 1) {
    auto &&goal = CreatStartGoal();
#if 1  //debug simple xp1
    action_goals_.push_back(goal);
#endif
  }

  for (auto &edge_action : edge_.actions) {
    auto &&goals = CreatNormalGoals(edge_action);

    for (auto &goal : goals) {
      action_goals_.push_back(goal);
    }
  }

  for (auto &node_action : node_.actions) {
    auto &&goals = CreatNormalGoals(node_action);

    for (auto &goal : goals) {
      action_goals_.push_back(goal);
    }
  }

  if (node_id() == final_end_node_.node_id) {
    auto &&goal = order_id() == "CoTEK-9999" ? CreatTrafficUnlockEndoal()
                                             : CreatEndGoal();
    action_goals_.push_back(goal);
  }

  return true;
}

// TODO（@ssh 后续特殊任务点此处添加
bool Step::SecondPlan() {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<uint8_t>(goal.g_type) & 0B01) {
      goal.t_goal.target_speed = target_speed();
      goal.t_goal.length = edge_.length;

      goal.t_goal.start_pose_x = start_pose().x();
      goal.t_goal.start_pose_y = start_pose().y();
      goal.t_goal.start_pose_yaw = start_pose().yaw();

      goal.t_goal.adjust_speed_pose_x = adjust_speed_pose().adjust_pose.x();
      goal.t_goal.adjust_speed_pose_y = adjust_speed_pose().adjust_pose.y();
      goal.t_goal.adjust_speed_pose_yaw = adjust_speed_pose().adjust_pose.yaw();
      goal.t_goal.adjust_speed = adjust_speed_pose().adjust_speed;

      goal.t_goal.high_precision_pose_x =
          high_precision_pose().high_precision_pose.x();
      goal.t_goal.high_precision_pose_y =
          high_precision_pose().high_precision_pose.y();
      goal.t_goal.high_precision_pose_yaw =
          high_precision_pose().high_precision_pose.yaw();

      goal.t_goal.end_x =
          fix_pose().vaild ? fix_pose().pose.x() : goal.t_goal.end_x;
      goal.t_goal.end_y =
          fix_pose().vaild ? fix_pose().pose.y() : goal.t_goal.end_y;

      // TODO(@ssh) 如果下段为取货，则修改为取货控制器
      if (goal.t_goal.motion_type == static_cast<int>(MotionType::LINE) &&
          (hasTargetActionGoal(AgvTaskOperationType::UP) ||
           hasTargetActionGoal(10))) {
        goal.t_goal.motion_type = static_cast<int>(MotionType::FORKVAN);
        LOG_WARN("FORK_VAN controler!!");
      }

      // // TODO(@ssh)当曲线两个控制点很近时，执行原地旋转
      // bool is_line =
      //     Point(goal.t_goal.c1_x, goal.t_goal.c1_y) == Point(0.0, 0.0) &
      //     Point(goal.t_goal.c1_x, goal.t_goal.c1_y) == Point(0.0, 0.0);

      // if (!is_line &&
      //     math::GetDistance(Point(goal.t_goal.c1_x, goal.t_goal.c1_y),
      //                       Point(goal.t_goal.c2_x, goal.t_goal.c2_y)) < 0.05) {
      //   goal.t_goal.motion_type = static_cast<int>(MotionType::FORK_ROTATE);
      //   LOG_WARN("FORK_ROTATE controler!!");
      // }

      // next
      if (next_node_.sequence_id != 0) {
        goal.t_goal.next_end_id = std::stoi(next_node_.node_id);
        goal.t_goal.next_end_x = next_node_.node_position.x;
        goal.t_goal.next_end_y = next_node_.node_position.y;
        goal.t_goal.next_end_yaw = math::Rad2Deg(next_edge_.orientation);
        if (next_edge_.trajectory.control_points.size() > 1) {
          goal.t_goal.next_path_type =
              static_cast<int32_t>(PathType::B_SPLINE_CURVES);
          goal.t_goal.next_c1_x = next_edge_.trajectory.control_points.at(0).x;
          goal.t_goal.next_c1_y = next_edge_.trajectory.control_points.at(0).y;
          goal.t_goal.next_c2_x = next_edge_.trajectory.control_points.at(1).x;
          goal.t_goal.next_c2_y = next_edge_.trajectory.control_points.at(1).y;
        } else {
          goal.t_goal.next_edge_id = next_edge_.edge_id;
          goal.t_goal.next_start_index = next_edge_.start_index;
          goal.t_goal.next_end_index = next_edge_.end_index;
          if (next_edge_.end_index != 0) {
            goal.t_goal.next_path_type = static_cast<int32_t>(PathType::FITTING_LINE);
          } else {
            goal.t_goal.next_path_type =
                static_cast<int32_t>(PathType::STRAIGHT_LINE);        
          }
        }
        // 连续两段方向相反曲线 取消下一段
        if (goal.t_goal.path_type ==
                static_cast<int32_t>(PathType::B_SPLINE_CURVES) &&
            goal.t_goal.next_path_type ==
                static_cast<int32_t>(PathType::B_SPLINE_CURVES) &&
            (GetDirection() * GetNextDirection() < 0)) {
          LOG_WARN("Cancle continus next step!!!");
          goal.t_goal.next_end_id = std::stoi("0");
        }

        // 上一段为倒车直线下一段为前进曲线 取消连续性
        if (goal.t_goal.path_type ==
                static_cast<int32_t>(PathType::STRAIGHT_LINE) &&
            goal.t_goal.next_path_type ==
                static_cast<int32_t>(PathType::B_SPLINE_CURVES) &&
            (GetDirection() < 0. && GetNextDirection() > 0.)) {
          LOG_WARN("Cancle continus next step!!!");
          goal.t_goal.next_end_id = std::stoi("0");
        }
      }
    }
  }
  return true;
}

int Step::GetDirection() const {
  int dir = 0;
  // 存在边且不是单点边
  if (!edge_.edge_id.empty() && edge_.start_node_id != edge_.end_node_id) {
    // 曲线计算朝向
    if (edge_.trajectory.control_points.size() > 1) {
      // 判断前进或后退
      Eigen::Vector3d end_oritentation(std::cos(edge_.orientation),
                                       std::sin(edge_.orientation), 0);
      Eigen::Vector3d second_control_to_end(
          node_.node_position.x - edge_.trajectory.control_points.at(0).x,
          node_.node_position.y - edge_.trajectory.control_points.at(0).y, 0);

      dir = end_oritentation.dot(second_control_to_end) > 0. ? 1 : -1;
    } else /*计算直线边朝向*/ {
      // 判断前进或后退
      Eigen::Vector2d start_oritentation(std::cos(edge_.orientation),
                                         std::sin(edge_.orientation));
      Eigen::Vector2d start_to_end(
          node_.node_position.x - start_node_.node_position.x,
          node_.node_position.y - start_node_.node_position.y);
      dir = start_oritentation.dot(start_to_end) > 0. ? 1 : -1;
    }
  }
  return dir;
}

int Step::GetNextDirection() const {
  int dir = 0;
  // 存在下一段边
  if (!next_edge_.edge_id.empty() && !next_node_.node_id.empty()) {
    // 曲线计算朝向
    if (next_edge_.trajectory.control_points.size() > 1) {
      // 判断前进或后退
      Eigen::Vector3d end_oritentation(std::cos(next_edge_.orientation),
                                       std::sin(next_edge_.orientation), 0);
      Eigen::Vector3d second_control_to_end(
          next_node_.node_position.x -
              next_edge_.trajectory.control_points.at(0).x,
          next_node_.node_position.y -
              next_edge_.trajectory.control_points.at(0).y,
          0);

      dir = end_oritentation.dot(second_control_to_end) > 0. ? 1 : -1;
    } else /*计算直线边朝向*/ {
      // 判断前进或后退
      Eigen::Vector2d start_oritentation(std::cos(next_edge_.orientation),
                                         std::sin(next_edge_.orientation));
      Eigen::Vector2d start_to_end(
          next_node_.node_position.x - node_.node_position.x,
          next_node_.node_position.y - node_.node_position.y);
      dir = start_oritentation.dot(start_to_end) > 0. ? 1 : -1;
    }
  }
  return dir;
}

bool Step::hasOperationGoal() const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<int32_t>(goal.g_type) & 0B10) {
      ret = true;
    }
  }
  return ret;
}

bool Step::hasMoveOperationGoal() const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<int32_t>(goal.g_type) ==
        static_cast<int>(StepType::MOVE_OPERATION)) {
      ret = true;
    }
  }
  return ret;
}

bool Step::hasGoal(const ActionGoal &target_goal) const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (goal == target_goal) {
      ret = true;
    }
  }
  return ret;
}

bool Step::hasTargetAMoveGoal(const navigation::MotionType &type) const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<int32_t>(goal.g_type) & 0B01 &&
        goal.t_goal.motion_type == static_cast<int>(type)) {
      ret = true;
    }
  }
  return ret;
}

bool Step::hasTargetActionGoal(const AgvTaskOperationType &type) const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<int32_t>(goal.g_type) & 0B10 &&
        goal.a_goal.action_type == static_cast<int>(type)) {
      ret = true;
    }
  }
  return ret;
}

bool Step::hasTargetActionGoal(const int &type) const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<int32_t>(goal.g_type) & 0B10 &&
        goal.a_goal.action_type == static_cast<int>(type)) {
      ret = true;
    }
  }
  return ret;
}

ActionGoal Step::GetTargetActionGoal(const AgvTaskOperationType &type) const {
  bool ret = false;
  for (auto &goal : action_goals_) {
    if (static_cast<int32_t>(goal.g_type) & 0B10 &&
        goal.a_goal.action_type == static_cast<int>(type)) {
      return goal;
    }
  }
  return ActionGoal();
}

void Step::SetActionState(const std::string &id, const std::string &status) {
  for (auto &goal : action_goals_) {
    if (goal.action_state.action_id != "9999" &&
        goal.action_state.action_id == id) {
      goal.action_state.action_status = status;
    }
  }
}

std::string Step::StepToString() {
  std::stringstream ss;
  ss << "{ "
     << "nodeId:" << node_id() << ", "
     << "sequenceId: " << sequence_id() << ", "
     << "edgeId: " << edge().edge_id << ", "
     << "pathType: " << GetPathString(path_type()) << ", "
     << "nodePose: (" << node_.node_position.x << "," << node_.node_position.y
     << "," << edge_.orientation << "), "
     << "fixPose: (" << fix_pose_.pose.x() << "," << fix_pose_.pose.y() << "), "
     << "avoidMap: " << avoid_map() << ", "
     << "maxSpeed: " << speed() << ", "
     << "targetSpeed: " << target_speed() << ", "
     << "length: " << odom() << ", "
     << "remainDistance: " << remain_distance() << ", "
     << "adjustPose: (" << adjust_speed_pose().adjust_pose.x() << ","
     << adjust_speed_pose().adjust_pose.y() << ")"
     << ", "
     << "adjustSpeed: (" << adjust_speed_pose().adjust_speed << ","
     << "highPercisePose: (" << high_precision_pose().high_precision_pose.x()
     << "," << high_precision_pose().high_precision_pose.y() << ")"
     << ", "
     << "start_indx: " << start_index()
     << ", "
     << "end_index: " << end_index()
     << ", "
     << "action: [ ";

  for (auto &goal : action_goals_) {
    ss << "{ actionId: " << goal.action_state.action_id << ", "
       << "actionType: " << goal.action_state.action_type << ", "
       << "action_description: " << goal.action_state.action_description << "}";
  }
  ss << " ] "
     << " } " << std::endl;

  return ss.str();
}

}  // namespace decision_maker
