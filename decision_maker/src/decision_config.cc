/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "decision_config.h"

#include <cstdint>
#include <exception>
#include <map>
#include <regex>
#include <string>
#include <tuple>

namespace decision_maker {

#define CFG BasicConfigHelper::Instance()

/*ation_id, move_type*/
static std::map<std::string, int> kGoalType = {{"limitLoad", 2},
                                               {"limitUnLoad", 2},
                                               {"heightLoad", 2},
                                               {"heightUnLoad", 2},
                                               {"forkHeightMove", 2},
                                               {"forkLateralMove", 2},
                                               {"forkSideMove", 2},
                                               {"forkTiltMove", 2},
                                               {"forkLateralMoveWithAdjust", 2},
                                               {"palletDetectWithAdjust", 2},
                                               {"shelfPoseDetect", 2},
                                               {"palletBackLimit", 2},

                                               {"palletNoMove", 2},
                                               {"palletZero", 2},
                                               {"palletRotate", 2},
                                               {"palletCorrect", 2},

                                               {"checkUpWeight", 2},
                                               {"checkDownWeight", 2},
                                               {"checkStatus", 2},
                                               {"delay", 2},
                                               {"charge", 2},
                                               {"operatorIo", 2},
                                               {"checkIo", 2},

                                               {"confirm", 2},
                                               {"openDoor", 2},
                                               {"closeDoor", 2},
                                               {"callDT", 2},
                                               {"takeDT", 2},
                                               {"leaveDT", 2},

                                               {"preLoad", 2},
                                               {"rest", 2},
                                               {"syncForward", 1},
                                               {"syncBackward", 1},
                                               {"trafficUnLock", 1},

                                               {"palletDetect", 1},
                                               {"stablizer", 1},
                                               {"highPrecisionStablizer", 1},
                                               {"unloadDetect", 1},
                                               {"upStablizer", 1},
                                               {"downStablizer", 1},
                                               {"upStablizer", 1},
                                               {"freeMove", 1},
                                               {"forkRotate", 1},
                                               {"jackRotate", 1},
                                               {"chargeMove", 1},
                                               {"forbidUnload", 1},
                                               {"distAdjustWithGoal", 1},
                                               {"trackColumnUp", 1},
                                               {"trackColumnDown", 1},
                                               {"palletAdjustMove", 1},
                                               {"rotateAdjustMove", 1},
                                               {"jackAdjsutMove", 1},
                                               {"initPosition", 1},
                                               {"forkvan", 1},
                                               {"forkdownvan", 1},
                                               {"kuquLoadEnter", 1},
                                               {"kuquUnLoadEnter", 1},
                                               {"kuquLeave", 1},
                                               {"moveWithUpDown", 3},
                                               {"DIY1", 1},
                                               {"DIY2", 1},

                                               {"nav", 1},
                                               {"action", 2},
                                               {"nav_action", 3},
                                               {"", 0}};

static std::map<std::string, int> kAtomicActionType = {
    {"rest", 1},
    {"preLoad", 10},
    {"limitLoad", 11},
    {"limitUnLoad", 12},
    {"heightLoad", 11},
    {"heightUnLoad", 12},
    {"forkHeightMove", 101},
    {"moveWithUpDown", 101},
    {"forkLateralMove", 102},
    {"forkSideMove", 104},
    {"forkTiltMove", 103},
    {"forkLateralMoveWithAdjust", 105},
    {"palletDetectWithAdjust", 908},
    {"palletBackLimit", 906},
    {"unloadDetect", 907},
    {"shelfPoseDetect", 909},
    {"palletNoMove", 201},
    {"palletZero", 202},
    {"palletRotate", 204},
    {"palletCorrect", 203},
    {"checkUpWeight", 51},
    {"checkDownWeight", 52},
    {"charge", 13},
    {"delay", 14},
    {"operatorIo", 5},
    {"checkIo", -10},
    {"checkStatus", -5},
    {"DIY1", -11},
    {"DIY2", -12},

    {"confirm", 910},
    {"openDoor", 5},
    {"closeDoor", 5},
    {"callDT", 3},
    {"takeDT", 4},
    {"leaveDT", 4},
    {"controlDT", 4},

    {"nav", -100},
    {"action", -101},
    {"nav_action", -102},
    {"", 0}};

static std::map<std::string, int> kMotionType = {{"upStablizer", 12},
                                                 {"downStablizer", 13},
                                                 {"zDownStablizer", 17},
                                                 {"palletDetect", 30},
                                                 {"stablizer", 8},
                                                 {"highPrecisionStablizer", 34},
                                                 {"freeMove", 2},
                                                 {"forkRotate", 18},
                                                 {"moveWithUpDown", 2},
                                                 {"jackRotate", 10},
                                                 {"chargeMove", 11},
                                                 {"trafficUnLock", 16},
                                                 {"syncForward", 14},
                                                 {"syncBackward", 14},
                                                 {"forbidUnload", 9},
                                                 {"distAdjustWithGoal", 33},
                                                 {"trackColumnUp", 6},
                                                 {"trackColumnDown", 6},
                                                 {"palletAdjustMove", 31},
                                                 {"rotateAdjustMove", 23},
                                                 {"initPosition", 20},
                                                 {"jackAdjsutMove", 22},
                                                 {"forkvan", 5},
                                                 {"forkdownvan", 35},
                                                 {"kuquLoadEnter", 36},
                                                 {"kuquUnLoadEnter", 37},
                                                 {"kuquLeave", 38},
                                                 {"nav", 2},
                                                 {"", 0}};

static std::map<std::string, int> kPathType = {{"upStablizer", 2},
                                               {"downStablizer", 2},
                                               {"zDownStablizer", 2},
                                               {"palletDetect", 2},
                                               {"trafficUnLock", 2},
                                               {"syncForward", 2},
                                               {"syncBackward", 2},
                                               {"stablizer", 2},
                                               {"highPrecisionStablizer", 2},
                                               {"freeMove", 2},
                                               {"forkRotate", 1},
                                               {"moveWithUpDown", 2},
                                               {"jackRotate", 1},
                                               {"chargeMove", 2},
                                               {"forbidUnload", 2},
                                               {"distAdjustWithGoal", 2},
                                               {"trackColumnUp", 2},
                                               {"trackColumnDown", 2},
                                               {"palletAdjustMove", 2},
                                               {"initPosition", 1},
                                               {"rotateAdjustMove", 1},
                                               {"jackAdjsutMove", 2},
                                               {"nav", 2},
                                               {"", 0}};

#if 0
static std::tuple<std::string, int, int> kAtomicActionType = {
  {"limitLoad", 2, 11},
  {"limitUnLoad", 2, 12},
  {"heightLoad", 2, 11},
  {"heightUnLoad", 2, 12},
  {"forkHeightMove", 2, 101},
  {"forkLateralMove", 2, 102},
  {"forkSideMove", 2, 104},
  {"forkTiltMove", 2, 103},
  {"forkLateralMoveWithAdjust", 2, 105},
  {"palletDetect", 2, -1},
  {"palletDetectWithAdjust", 2, 908},
  {"palletBackLimit", 2, 906},
  {"unloadDetect", 1, 907},
  {"shelfPoseDetect", 2, 909},
  {"charge", 2, 13},
  {"palletNoMove", 2, 201},
  {"palletZero", 2, 202},
  {"palletRotate", 2, 204},
  {"palletCorrect", 2, 203},
  {"upStablizer", 1, 12},
  {"downStablizer", 1, 13},
  {"upStablizer", 1, -3},
  {"downStablizer", 1, 17},
  {"forkRotate", 1, 18},
  {"moveWithUpDown", 3, -4},
  {"jackRotate", 1, 10},
  {"chargeMove", 1, 11},
  {"forbidUnload", 1, 9},
  {"distAdjustWithGoal", 1, 33},
  {"trackColumnUp", 1, 6},
  {"trackColumnDown", 1, 6},
  {"palletAdjustMove", 1, 31},
  {"rotateAdjustMove", 1, 23},
  {"jackAdjsutMove", 1, 22},
  {"checkWeight", 2, 52},
  {"checkStatus", 2, -5},
  {"delay", 2, 14},
  {"checkIo", 2, -10},
  {"DIY1", 1, -11},
  {"DIY2", 1, -12},
  
  {"nav", 1, -100},
  {"action", 2, -101},
  {"nav_action", 3, -102},
};
#endif

bool DecisionConfig::LoadVoiceConfig(const std::string &json_str,
                                     LogicOption &option) {
  try {
    auto &&json = Json::parse(json_str);
    LOG_INFO("------------------Voice config---------------");
    option.volume = json["volume"].get<int>();
    LOG_INFO_STREAM("volume: " << option.volume);
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return false;
  }
  return true;
}

bool DecisionConfig::LoadLoigcConfig(const std::string &json_str,
                                     LogicOption &option) {
  try {
    if (!CFG.LoadConfig()) {
      return false;
    }
    option.agv_type = CFG.agv_type();

    auto &&json = Json::parse(json_str);
    LOG_INFO("------------------Logic config---------------");
    LOG_INFO("---------------Common_config option---------------");

    option.enable_record_tf =
        json["common_config"]["enable_record_tf"].get<bool>();
    LOG_INFO_STREAM("enable_record_tf: " << option.enable_record_tf);

    option.need_rotate_theta =
        json["common_config"]["need_rotate_theta"].get<double>();
    LOG_INFO_STREAM("need_rotate_theta: " << option.need_rotate_theta);

    LOG_INFO("---------------Forklift logic option---------------");

    auto &forklift_option = json["fork_lift_logic_option"];

    option.fork_lift_logic_option.load_distance_offset =
        forklift_option["load_distance_offset"].get<double>();
    LOG_INFO_STREAM("load_distance_offset: "
                    << option.fork_lift_logic_option.load_distance_offset);

    option.fork_lift_logic_option.unload_distance_offset =
        forklift_option["unload_distance_offset"].get<double>();
    LOG_INFO_STREAM("unload_distance_offset: "
                    << option.fork_lift_logic_option.unload_distance_offset);

    option.fork_lift_logic_option.charge_distance_offset =
        forklift_option["charge_distance_offset"].get<double>();
    LOG_INFO_STREAM("charge_distance_offset: "
                    << option.fork_lift_logic_option.charge_distance_offset);

    option.fork_lift_logic_option.enable_auto_fork_up_down =
        forklift_option["enable_auto_fork_up_down"].get<bool>();
    LOG_INFO_STREAM("enable_auto_fork_up_down: "
                    << option.fork_lift_logic_option.enable_auto_fork_up_down);

    option.fork_lift_logic_option.auto_fork_up_margin =
        forklift_option["auto_fork_up_margin"].get<double>();
    LOG_INFO_STREAM("auto_fork_up_margin: "
                    << option.fork_lift_logic_option.auto_fork_up_margin);

    option.fork_lift_logic_option.auto_fork_down_margin =
        forklift_option["auto_fork_down_margin"].get<double>();
    LOG_INFO_STREAM("auto_fork_down_margin: "
                    << option.fork_lift_logic_option.auto_fork_down_margin);

    option.fork_lift_logic_option.enable_lose_pallet_check =
        forklift_option["enable_lose_pallet_check"].get<bool>();
    LOG_INFO_STREAM("enable_lose_pallet_check: "
                    << option.fork_lift_logic_option.enable_lose_pallet_check);

    option.fork_lift_logic_option.klost_weighing_threshold =
        forklift_option["klost_weighing_threshold"].get<int>();
    LOG_INFO_STREAM("klost_weighing_threshold: "
                    << option.fork_lift_logic_option.klost_weighing_threshold);

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return false;
  }
  return true;
}

bool DecisionConfig::SaveLastPose(const std::string &data) {
  return CFG.SaveLocal(cotek_config::ConfigType::LAST_ROBOT_POSE, data);
}

// 切换分区时，修改基础配置里的分区id
bool DecisionConfig::ReQrWriteSectionId2Config(const int32_t &section_id) {
  std::string config_json_str;
  try {
    config_json_str = CFG.GetConfig(cotek_config::ConfigType::AGV_BASIC_CONFIG);
  } catch (std::out_of_range &err) {
    LOG_WARN("invalid config file");
    LOG_WARN_STREAM(err.what()
                    << " file: " << __FILE__ << " line: " << __LINE__);
    return false;
  }
  std::regex target_pattern("\"current_qr_section_id\"\\s*:\\s*(\\d+)");

  std::stringstream new_section_id_stream;
  new_section_id_stream << "\"current_qr_section_id\": " << section_id;
  std::string new_config_json_str = std::regex_replace(
      config_json_str, target_pattern, new_section_id_stream.str());
  if (new_config_json_str == config_json_str) {
    std::cout << "Cannot find or update 'current_qr_section_id' field."
              << std::endl;
    return false;
  }

  // 保存到本地
  CFG.SaveLocal(cotek_config::ConfigType::AGV_BASIC_CONFIG,
                new_config_json_str);
  return true;
}

bool DecisionConfig::ReWriteSectionId2Config(const int32_t &section_id) {
  std::string config_json_str;
  try {
    config_json_str = CFG.GetConfig(cotek_config::ConfigType::AGV_BASIC_CONFIG);
  } catch (std::out_of_range &err) {
    LOG_WARN("invalid config file");
    LOG_WARN_STREAM(err.what()
                    << " file: " << __FILE__ << " line: " << __LINE__);
    return false;
  }

  std::regex target_pattern("\"current_reflector_section_id\"\\s*:\\s*(\\d+)");

  std::stringstream new_section_id_stream;
  new_section_id_stream << "\"current_reflector_section_id\": " << section_id;
  std::string new_config_json_str = std::regex_replace(
      config_json_str, target_pattern, new_section_id_stream.str());
  if (new_config_json_str == config_json_str) {
    std::cout << "Cannot find or update 'current_reflector_section_id' field."
              << std::endl;
    return false;
  }

  // 保存到本地
  CFG.SaveLocal(cotek_config::ConfigType::AGV_BASIC_CONFIG,
                new_config_json_str);
  return true;
}

int DecisionConfig::GetGoalType(const std::string &type) {
  return kGoalType.find(type) != kGoalType.end() ? kGoalType[type] : 0;
}

int DecisionConfig::GetAtomicActionType(const std::string &type) {
  return kAtomicActionType.find(type) != kAtomicActionType.end()
             ? kAtomicActionType[type]
             : 0;
}

int DecisionConfig::GetMotionType(const std::string &type) {
  return kMotionType.find(type) != kMotionType.end() ? kMotionType[type] : 0;
}
int DecisionConfig::GetPathType(const std::string &type) {
  return kPathType.find(type) != kPathType.end() ? kPathType[type] : 0;
}

}  // namespace decision_maker