/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_CONFIG_HELPER_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_CONFIG_HELPER_H_
#include <pwd.h>
#include <ros/package.h>
#include <unistd.h>

#include <map>
#include <string>

#include "cotek_common/agv_basic_option.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/util/json11.h"
#include "cotek_common/util/local_service.h"
#include "cotek_common/util/remote_service.h"
#include "cotek_common/util/singleton.h"
#include "cotek_msgs/update_action_config.h"
#include "cotek_msgs/update_avoid_config.h"
#include "cotek_msgs/update_embedded_config.h"
#include "cotek_msgs/update_localizer_config.h"
#include "cotek_msgs/update_logic_config.h"
#include "cotek_msgs/update_navigation_config.h"

namespace cotek_config {
enum class ConfigType : uint8_t {
  NONE = 0,
  AGV_BASIC_CONFIG = 1,
  LOGIC_CONFIG = 2,
  LOCALIZER_CONFIG = 3,
  NAVIGATION_CONFIG = 4,
  ACTION_CONFIG = 5,
  AVOID_CONFIG = 6,
  EMBEDDED_CONFIG = 7,
  DEVICE_TABLE = 8,
  AVOID_AREA = 9,
  RUNNER_CONFIG = 10,
  SINGLE_TASK = 11,
  LAST_ROBOT_POSE = 12,
  VISUAL_CONFIG = 13,
  STORAGE_CONFIG = 14,
  TF_CONFIG = 15,
  SLAM_CONFIG = 16,
  VOICE_CONFIG = 17,
  SLAM_3D_CONFIG = 18,
  PATH_CONFIG = 19,

  /******地图信息类******/
  LOCALIZER_LANDMARK = 100,
  GLOBAL_MAP = 101,
  RELOCATION_LIST = 102,
  FLOOR_LIST = 103,

  /******测试类******/
  // 二维码标定
  QR_CALIBRATION_MAP = 200,
  // 用来测试代码
  TEST_CONFIG = 202,
  // 测试二维码标定
  TEST_QR_CALIBRATE = 203
};
constexpr int kConfigOk = 1;
constexpr int kConfigError = 2;

// 配置类文件
constexpr char kAgvBasicConfigPath[] = "agv_basic_config.json";
constexpr char kLogicConfigPath[] = "logic_config.json";
constexpr char kLocalizerConfigPath[] = "localizer_config.json";
constexpr char kLastRobotPoseConfigPath[] = "last_robot_pose_config.json";
constexpr char kNavigationConfigPath[] = "navigation_config.json";
constexpr char kActionConfigPath[] = "action_config.json";
constexpr char kAvoidConfigPath[] = "avoid_config.json";
constexpr char kEmbeddedConfigPath[] = "embedded_config.json";
constexpr char kDeviceTablePath[] = "device_table.json";
constexpr char kAvoidAreaPath[] = "avoid_area.json";
constexpr char kSingleTaskPath[] = "single_task.json";
constexpr char kVisualConfigPath[] = "visual_config.json";
constexpr char kStorageConfigPath[] = "storage_config.json";
constexpr char kTfConfigPath[] = "tf_config.json";
constexpr char kRunnerConfigPath[] = "runner_config.json";
constexpr char kSlamConfigPath[] = "slam_config.json";
constexpr char kVoiceConfigPath[] = "voice_config.json";
constexpr char kSlam3dConfigPath[] = "slam3d_config.json";
constexpr char kPathConfigPath[] = "path_config.json";

// 地图信息类文件
constexpr char kLocalizerLandmarkConfigPath[] = "landmark.json";
constexpr char kCalibrationConfigPath[] = "calibration_map.json";
constexpr char kGlobalMapPath[] = "global_map.json";
constexpr char kPbstreamFileName[] = "cotek.pbstream";
constexpr char kRelocationListPath[] = "relocation_list.json";
constexpr char kFloorListPath[] = "floor_list.json";
constexpr char kPcdFileName[] = "cotek.pcd";

constexpr char kTestConfigPath[] = "test.json";
constexpr char kTestQrCalibratePath[] = "landmark_revise.json";

constexpr char kWebUpdateNavigationService[] = "web_update_navigation";
constexpr char kWebUpdateActionService[] = "web_update_action";
constexpr char kWebUpdateStorageService[] = "web_update_storage";
constexpr char kWebUpdateVisualService[] = "web_update_visual";

constexpr char kUpdateCommonConfigService[] = "update_common_config";

constexpr char kUpdateLogicConfigService[] = "update_logic_config";
constexpr char kUpdateLocalizerConfigService[] = "update_localizer_config";
constexpr char kUpdateStorageConfigService[] = "update_storage_config";
constexpr char kUpdateNavigationConfigService[] = "update_navigation_config";
constexpr char kUpdateVisualConfigService[] = "update_visual_config";
constexpr char kUpdateActionConfigService[] = "update_action_config";

constexpr char kUpdateEmbeddedConfigService[] = "update_embedded_config";
constexpr char kUpdateSingleTaskService[] = "update_single_task";
constexpr char kCalibrateVisualAngleService[] = "calibrate_visual_angle";
constexpr char kCalibrateVisualBackgroundService[] =
    "calibrate_visual_background";

}  // namespace cotek_config

// 读取 json 文件 加载配置
class BasicConfigHelper {
 public:
  ~BasicConfigHelper() {}

  // 加载 json 文件获取配置
  bool LoadConfig();

  // 重新son文件获取配置，更新配置时使用
  bool ReLoadConfig();

  bool LoadSlamConfig(std::string& slam_option_string, SlamOption& slam_option);

  bool LoadLanguageConfig();

  // 动态更新后保存到本地
  bool SaveLocal(cotek_config::ConfigType type, std::string);

  // 调度读取配置
  std::string GetConfig(cotek_config::ConfigType type);

  //  TODO(@ssh)3.0配置 ------------------------------
  inline const AgvType agv_type() { return static_cast<AgvType>(agv_type_); }

  inline const CommunicateOption communicate_option() {
    return communicate_option_;
  }

  inline const ChassisModelOption chassis_model_option() {
    return chassis_model_option_;
  }

  inline const SlamOption slam_option() { return slam_option_; }

  inline const MapInfoOption map_info_option() { return map_info_option_; }

  inline const TransformMap transform_map() { return transform_map_; }

  inline const LaserMap laser_map() { return laser_map_; }

  inline const CameraMap camera_map() { return camera_map_; }

  // TODO(@ssh)3.0配置 ---------------------------

  inline const std::string server_ip() { return server_ip_; }

  inline const LandmarkMapOption landmark_map_option() {
    return landmark_map_option_;
  }

  inline const SlamMapOption slam_map_option() { return slam_map_option_; }

  inline const MechanicalOption mechanical_option() {
    return mechanical_option_;
  }

  inline const SensorTFData sensor_tf_data() { return sensor_tf_data_; }

  inline const std::string logic_config_file_name() {
    return logic_config_file_name_;
  }

  inline const std::string navigation_config_file_name() {
    return navigation_config_file_name_;
  }

  inline const std::string action_config_file_name() {
    return action_config_file_name_;
  }

  inline const std::string embedded_config_file_name() {
    return embedded_config_file_name_;
  }

  std::string GetUserName() {
    uid_t userid;
    struct passwd* pwd;
    userid = getuid();
    pwd = getpwuid(userid);
    return pwd->pw_name;
  }

  std::string GetConfigPath() { return user_path_; }
  // 获取指定类型文件路径接口
  std::string GetTargetConfigPath(const cotek_config::ConfigType& type) {
    try {
      return config_map_.at(type);
    } catch (std::out_of_range& ex) {
      LOG_ERROR_STREAM(ex.what());
      return std::string();
    }
  }

  std::string GetBasicConfigPath() { return user_path_ + "basic_config/"; }
  std::string GetSlamConfigPath() { return user_path_ + "slam_config/"; }
  std::string GetMapPath() { return user_path_ + "map/"; }

  std::map<std::string, std::map<std::string, std::string>> GetTranslations( const std::string &lang = "cn");
  std::map<std::string, std::string> GetTranslations(const std::string &section, const std::string &lang = "cn");
  std::string GetTranslation(const std::string &section, const std::string &key, const std::string &lang = "cn");

 private:
  // 单例实现
  DECLARE_SINGLETON(BasicConfigHelper);
  BasicConfigHelper()
      : has_loaded_(false),
        agv_type_(0),
        server_port_(0),
        http_landmark_map_port_(0) {
    config_map_[cotek_config::ConfigType::AGV_BASIC_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kAgvBasicConfigPath);
    config_map_[cotek_config::ConfigType::LOCALIZER_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kLocalizerConfigPath);
    config_map_[cotek_config::ConfigType::LOCALIZER_LANDMARK] =
        GetMapPath() + std::string(cotek_config::kLocalizerLandmarkConfigPath);
    config_map_[cotek_config::ConfigType::GLOBAL_MAP] =
        GetMapPath() + std::string(cotek_config::kGlobalMapPath);
    config_map_[cotek_config::ConfigType::RELOCATION_LIST] =
        GetMapPath() + std::string(cotek_config::kRelocationListPath);
    config_map_[cotek_config::ConfigType::FLOOR_LIST] =
        GetMapPath() + std::string(cotek_config::kFloorListPath);
    config_map_[cotek_config::ConfigType::LAST_ROBOT_POSE] =
        GetBasicConfigPath() +
        std::string(cotek_config::kLastRobotPoseConfigPath);
    config_map_[cotek_config::ConfigType::LOGIC_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kLogicConfigPath);
    config_map_[cotek_config::ConfigType::NAVIGATION_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kNavigationConfigPath);
    config_map_[cotek_config::ConfigType::ACTION_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kActionConfigPath);
    config_map_[cotek_config::ConfigType::AVOID_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kAvoidConfigPath);
    config_map_[cotek_config::ConfigType::EMBEDDED_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kEmbeddedConfigPath);
    config_map_[cotek_config::ConfigType::DEVICE_TABLE] =
        GetBasicConfigPath() + std::string(cotek_config::kDeviceTablePath);
    config_map_[cotek_config::ConfigType::AVOID_AREA] =
        GetBasicConfigPath() + std::string(cotek_config::kAvoidAreaPath);
    config_map_[cotek_config::ConfigType::SINGLE_TASK] =
        GetBasicConfigPath() + std::string(cotek_config::kSingleTaskPath);
    config_map_[cotek_config::ConfigType::STORAGE_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kStorageConfigPath);
    config_map_[cotek_config::ConfigType::TF_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kTfConfigPath);
    config_map_[cotek_config::ConfigType::VISUAL_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kVisualConfigPath);
    config_map_[cotek_config::ConfigType::RUNNER_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kRunnerConfigPath);
    config_map_[cotek_config::ConfigType::SLAM_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kSlamConfigPath);
    config_map_[cotek_config::ConfigType::VOICE_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kVoiceConfigPath);
    config_map_[cotek_config::ConfigType::SLAM_3D_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kSlam3dConfigPath);
    config_map_[cotek_config::ConfigType::PATH_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kPathConfigPath);

    config_map_[cotek_config::ConfigType::TEST_CONFIG] =
        GetBasicConfigPath() + std::string(cotek_config::kTestConfigPath);
    config_map_[cotek_config::ConfigType::TEST_QR_CALIBRATE] =
        GetBasicConfigPath() + std::string(cotek_config::kTestQrCalibratePath);
    config_map_[cotek_config::ConfigType::QR_CALIBRATION_MAP] =
        GetMapPath() + std::string(cotek_config::kCalibrationConfigPath);

    LoadConfig();
  }

  bool has_loaded_;

  //  TODO(@ssh)3.0配置 ------------------------------
  int agv_type_;
  CommunicateOption communicate_option_;
  ChassisModelOption chassis_model_option_;
  SlamOption slam_option_;
  TransformMap transform_map_;
  LaserMap laser_map_;
  CameraMap camera_map_;

  MapInfoOption map_info_option_;

  /*-------------------------------*/

  std::string server_ip_;
  int server_port_;
  int local_port_;
  int communicate_method_;
  std::string project_topic_;

  std::string http_get_landmark_map_url_;
  std::string http_update_landmark_map_url_;
  int http_landmark_map_port_;
  LandmarkMapOption landmark_map_option_;
  SlamMapOption slam_map_option_;
  MechanicalOption mechanical_option_;

  std::string logic_config_file_name_;
  std::string navigation_config_file_name_;
  std::string action_config_file_name_;
  std::string embedded_config_file_name_;

  SensorTFData sensor_tf_data_;
  std::map<cotek_config::ConfigType, std::string> config_map_;

  // <language, <section, <key, value>>>
  std::map<std::string, std::map<std::string, std::map<std::string, std::string>>> language_;

  // config文件 所在目录的前缀
  std::string user_path_ = "/home/" + GetUserName() + "/config/";
};

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_CONFIG_HELPER_H_
