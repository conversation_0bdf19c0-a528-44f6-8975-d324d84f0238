#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COMMON_DB_COTEK_VAR_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COMMON_DB_COTEK_VAR_H_

#include <cstdint>
#include <string>
#include <vector>
#include <map>
#include "eigen3/Eigen/SVD"

namespace common {

// 速度 vel
class vel_t {
 public:
  uint64_t time_stamp{0};
  union {
    double v{0.0};  // velocity m/s
    double speed;   // speed
  };
  union {
    double w{0.0};  // angular speed m/s
    double pos;     // motor position count
  };
  union {
    double angle{0.0};  // angle rad
    double theta;       // rad
  };
  double direction{1.0};
  double vv{0.0};
};

// 位姿 pose
struct pose_t {
  double x{0.0};      // 位置x m
  double y{0.0};      // 位置y m
  double angle{0.0};  // 角度 弧度
  double v{0.0};      // 速度 m/s

  pose_t(double _x=0.0, double _y=0.0, double _angle=0.0)
    : x(_x), y(_y), angle(_angle) { }
};

struct odom_t {
  vel_t vel;
  pose_t pose;
};

// 多边形 poly
using poly_t = std::vector<pose_t>;

// 重定位结构 relocation
struct relocation_t {
  std::string id;          // 重定位id
  std::string name;        // 重定位名
  double x{0.0};           // 位置x
  double y{0.0};           // 位置y
  double z{0.0};           // 位置z
  double angle{0.0};       // 角度
  std::string map_id;      // 地图编号
  std::string zone_id;     // 分区编号
  bool is_default{false};  // 是否为默认重定位点
};

// 地图定义 map
struct map_t {
  std::string id;          // 地图id
  std::string name;        // 地图名
  bool is_default{false};  // 是否默认地图
};

// 采集点 point
struct point_t {
  std::string id;     // 采集点id
  std::string name;   // 采集点名
  int timestamp{0};   // 采集点时间
  double x{0.0};      // 小车位置x
  double y{0.0};      // 小车位置y
  double angle{0.0};  // 小车角度 弧度
  double theta{0.0};  // 舵轮角度 弧度
  double v{0.0};      // 小车速度 m/s
};

struct node_t {
  std::string id;       // 采集点id
  std::string name;     // 采集点名
  int64_t timestamp{0}; // 采集点时间
  double x{0.0};        // 小车位置x
  double y{0.0};        // 小车位置y
  double angle{0.0};    // 小车角度 弧度
  double theta{0.0};    // 舵轮角度 弧度
  double v{0.0};        // 小车速度 m/s
  int cmd{0};           // 点位命令 0/1/2/3 无/新建/续建/覆盖
  int action{0};        // 点位动作 0/1/2/3 无/取货/卸货/休息
  std::string value{""};// 动作值
  double reloc_x{0.0};     // 地图切换重定位点x
  double reloc_y{0.0};     // 地图切换重定位点y
  double reloc_angle{0.0}; // 地图切换重定位点角度
  int type{0};          // 点位类型 0/1/2 采集点/任务点/虚拟点
  int node{0};          // 点位位置 0/1/2 中间点/起始点/终止点
  bool is_topology{0};  // 是否是拓扑图 0/1 否/是
  int32_t index{0};     // 相对于任务的索引，便于交管使用
};

// 交管 traffic
struct traffic_t {
  int id{0};                 // 交管id
  std::vector<pose_t> poly;  // 交管区域
  int traffic{0};            // 交管标志 0/1 未交管/交管中
  std::string edge_id;       // 所属边id
  std::string task_id;       // 所属任务id
  int start_index{0};        // 交管区域起点索引
  int end_index{0};          // 交管区域终点索引
};

// 边 edge
struct edge_t {
  std::string id;              // 路线id
  std::string name;            // 路线名
  node_t start_point;          // 路线起点
  node_t end_point;            // 路线终点
  std::vector<node_t> points;  // 路线点集合
  int direction{0};            // 路径方向 0/1/-1 无方向/正向/反向
  int velocity_level{0};       // 路径速度等级 1/2/3
  int avoid_level{0};          // 路径避障等级 1/2/3
  double length{0.0};          // 路线长度
  int start_index{0};
  int end_index{0};
  int column{0};
};

struct vertex_t {
  std::string id{""};
  double x{0};
  double y{0};
};

struct kwsize_t {
  double width{0};
  double length{0};
  double rowspace{0};
  double columnspace{0};
};

struct storage_t {
  std::string id{""}; 
  std::string kuqu{""};
  int column{0};
  int row{0};
  vertex_t center;
  std::vector<vertex_t> vertexs;
};

struct kuqu_t {
  std::string id{""};            // 库区编号
  std::string name{""};
  std::string direction{""};    // 进入边
  Eigen::Vector2d normal;       // 进入边的法向量
  std::vector<vertex_t> vertexs;  // 库区边界点
  kwsize_t kwsize;             // 库位尺寸
  std::vector<storage_t> storage;  // 库位集
  std::vector<vertex_t> detects;       // 路径探测点
  // std::string start_kuwei{""};
  // std::string end_kuwei{""};
};

struct db_kuqu_t {
  uint64_t id{0};           // 库区编号
  std::string name{""};
  std::string kuqu_id{""};
  std::string direction{""};    // 进入边
  double normal_x;
  double normal_y;
  double width{0};
  double length{0};
  double rowspace{0};
  double columnspace{0};
  // std::string start_kuwei{""};
  // std::string end_kuwei{""};
};

struct db_storage_t {
  uint64_t id{0};
  std::string kuwei_id{""};
  std::string kuqu{""};
  int column{0};
  int row{0};
  double center_x{0};
  double center_y{0};
  double x1{0};
  double y1{0};
  double x2{0};
  double y2{0};
  double x3{0};
  double y3{0};
  double x4{0};
  double y4{0};
};

// 任务事件 task
struct task_t {
  std::string id;             // 任务id
  std::string name;           // 任务名
  int priority{0};            // 任务编号 用于设置任务优先级
  std::vector<edge_t> edges;  // 任务路径集合 边集合
  std::vector<node_t> nodes;  // 任务点集合 点集合(路径点，分割点)
  std::vector<node_t> actions;// 任务点集合
  node_t start_point;         // 起点
  node_t end_point;           // 终点
  int velocity_level{0};      // 路径速度等级 1/2/3
  int avoid_level{0};         // 路径避障等级 1/2/3
  int loop{1};                // 任务循环次数
  std::vector<traffic_t> traffic;  // 交管区域
  std::vector<traffic_t> collision;  // 碰撞区域

  //build
  int build_state{0};         // 示教状态 0-进行中 1-开始 2-结束保存 3-结束取消
  int build_cmd{0};           // 任务命令 0/1/2/3 无/新建/续建/覆盖
  std::string orginal_id;     // 原始task id

  std::string zone_id;        // 分区id
  std::string map_id;         // 地图id
  int sn{-1};                  // 呼叫盒任务id

  int kuqu_id{-1};
  kuqu_t kuqu;        // 库区
};

struct elevator_task_t {
  bool        is_cross;
  std::string cur_map_id;
  std::string tar_map_id;
  std::string cur_floor;
  std::string tar_floor;
  pose_t      reloc_pose;
};

// 任务 task_result
struct task_result_t {
  std::string result_id;   // 结果id
  std::string task_id;     // 任务id
  std::string task_name;   // 任务名
  uint64_t begin_time{0};  // 任务开始时间
  uint64_t end_time{0};    // 任务结束时间
  int result{0};           // 任务结果
  std::string info;        // 异常信息
};

// 拓扑合并点和原始点的关联 
struct topology_node_t {
  int64_t id{0};             // 关联id
  std::string topology_id;    // 拓扑合并点id
  std::string origin_id;      // 关联原始点id
};

using db_node_t = node_t;

// 边 edge
struct db_edge_t {
  std::string id;           // 路线id
  std::string name;         // 路线名
  std::string start_point;  // 路线起点
  std::string end_point;    // 路线终点
  int direction{0};         // 路径方向 0/1/-1 无方向/正向/反向
  int velocity_level{0};    // 路径速度等级 1/2/3
  int avoid_level{0};       // 路径避障等级 1/2/3
  double length{0.0};       // 路线长度
  int column{0};
};

struct db_edge_node_t {
  uint64_t id{0};
  std::string edge_id;
  std::string node_id;
};

struct db_task_t {
  std::string id;           // 任务id
  std::string name;         // 任务名
  int priority{0};          // 任务编号 用于设置任务优先级
  std::string start_point;  // 起点
  std::string end_point;    // 终点
  int velocity_level{0};    // 路径速度等级 1/2/3
  int avoid_level{0};       // 路径避障等级 1/2/3
  int loop{1};              // 任务循环次数
  std::string zone_id;      // 分区id
  std::string map_id;       // 地图id
  int sn{-1};               // 呼叫盒任务id
  int kuqu{-1};
};

struct db_task_node_t {
  uint64_t id{0};       // 关联id
  std::string task_id;  // 任务id
  std::string node_id;  // 节点id
};

struct db_task_edge_t {
  uint64_t id{0};       // 关联id
  std::string task_id;  // 任务id
  std::string edge_id;  // 边id
};

struct db_task_traffic_t {
  uint64_t id{0};          // 关联id
  std::string task_id;     // 任务id
  uint64_t traffic_id;     // 交管id
};

struct db_traffic_t {
  int64_t id{0};          // 关联id
  std::string task_id;
  uint64_t traffic_id{0};
  double x1{0.0};
  double y1{0.0};
  double theta1{0.0};
  double x2{0.0};
  double y2{0.0};
  double theta2{0.0};
  int traffic{0};
  int start_index{0};
  int end_index{0};
};

struct db_collision_t : public db_traffic_t { };
using db_task_result_t = task_result_t;

struct task_info_t {
  std::string id;
  std::string current_odom_time;
  std::string create_odom_time;
  float current_odom{0};
  float total_odom{0.0};
  float total_duration{0.0};
  float avoid_duration{0.0};
  int current_task_count{0};
  int total_task_count{0};
  int error_task_count{0};
};
using db_task_info_t = task_info_t;

struct language_error_t {
  std::string code;
  std::string cn;
  std::string en;
  std::string fr;
  std::string de;
};
using db_language_error_t = language_error_t;

struct language_field_t {
  std::string id;
  std::string type;
  std::string cn;
  std::string en;
  std::string fr;
  std::string de;
};
using db_language_field_t = language_field_t;

}  // namespace common

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_COMMON_DB_COTEK_VAR_H_
