/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COMMON_DB_COTEK_DB_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COMMON_DB_COTEK_DB_H_

#include <iostream>

#include "cotek_common/db/cotek_var.h"
#include "cotek_common/db/sqlite_orm.h"

namespace common {

static auto init_storage(const std::string& path) {
  using namespace sqlite_orm;
  return make_storage(path,
      make_table("map", 
                make_column("id",               &map_t::id, primary_key()),
                make_column("name",             &map_t::name),
                make_column("default",          &map_t::is_default)),
      make_table("relocation",
                make_column("id",               &relocation_t::id, primary_key()),
                make_column("name",             &relocation_t::name),
                make_column("x",                &relocation_t::x),
                make_column("y",                &relocation_t::y),
                make_column("angle",            &relocation_t::angle),
                make_column("map_id",           &relocation_t::map_id),
                make_column("zone_id",          &relocation_t::zone_id),
                make_column("default",          &relocation_t::is_default)),
      make_table("node",
                make_column("id",               &node_t::id, primary_key()),
                make_column("name",             &node_t::name),
                make_column("timestamp",        &node_t::timestamp),
                make_column("x",                &node_t::x),
                make_column("y",                &node_t::y),
                make_column("angle",            &node_t::angle),
                make_column("theta",            &node_t::theta),
                make_column("v",                &node_t::v),
                make_column("cmd",              &node_t::cmd),
                make_column("action",           &node_t::action),
                make_column("type",             &node_t::type),
                make_column("node",             &node_t::node),
                make_column("is_topology",      &node_t::is_topology)),
      make_table("edge",
                make_column("id",               &db_edge_t::id, primary_key()),
                make_column("name",             &db_edge_t::name),
                make_column("start_point",      &db_edge_t::start_point),
                make_column("end_point",        &db_edge_t::end_point),
                make_column("direction",        &db_edge_t::direction),
                make_column("velocity_level",   &db_edge_t::velocity_level),
                make_column("avoid_level",      &db_edge_t::avoid_level),
                make_column("length",           &db_edge_t::length),
                make_column("column",           &db_edge_t::column)),
      make_table<db_traffic_t>("traffic",
                make_column("id",               &db_traffic_t::id, primary_key().autoincrement()),
                make_column("task_id",          &db_traffic_t::task_id),
                make_column("traffic_id",       &db_traffic_t::traffic_id),
                make_column("x1",               &db_traffic_t::x1),
                make_column("y1",               &db_traffic_t::y1),
                make_column("theta1",           &db_traffic_t::theta1),
                make_column("x2",               &db_traffic_t::x2),
                make_column("y2",               &db_traffic_t::y2),
                make_column("theta2",           &db_traffic_t::theta2),
                make_column("traffic",          &db_traffic_t::traffic),
                make_column("start_index",      &db_traffic_t::start_index),
                make_column("end_index",        &db_traffic_t::end_index)),
      make_table<db_collision_t>("collision",
                make_column("id",               &db_collision_t::id, primary_key().autoincrement()),
                make_column("task_id",          &db_collision_t::task_id),
                make_column("traffic_id",       &db_collision_t::traffic_id),
                make_column("x1",               &db_collision_t::x1),
                make_column("y1",               &db_collision_t::y1),
                make_column("theta1",           &db_collision_t::theta1),
                make_column("x2",               &db_collision_t::x2),
                make_column("y2",               &db_collision_t::y2),
                make_column("theta2",           &db_collision_t::theta2),
                make_column("traffic",          &db_collision_t::traffic),
                make_column("start_index",      &db_collision_t::start_index),
                make_column("end_index",        &db_collision_t::end_index)),
      make_table("task", 
                make_column("id",               &db_task_t::id, primary_key()),
                make_column("name",             &db_task_t::name),
                make_column("priority",         &db_task_t::priority),
                make_column("start_point",      &db_task_t::start_point),
                make_column("end_point",        &db_task_t::end_point),
                make_column("velocity_level",   &db_task_t::velocity_level),
                make_column("avoid_level",      &db_task_t::avoid_level),
                make_column("loop",             &db_task_t::loop),
                make_column("zone_id",          &db_task_t::zone_id),
                make_column("map_id",           &db_task_t::map_id),
                make_column("sn",               &db_task_t::sn),
                make_column("kuqu",             &db_task_t::kuqu)),
      make_table("task_node",
                make_column("id",               &db_task_node_t::id, primary_key().autoincrement()),
                make_column("task_id",          &db_task_node_t::task_id),
                make_column("node_id",          &db_task_node_t::node_id)),
      make_table("task_edge",
                make_column("id",               &db_task_edge_t::id, primary_key().autoincrement()),
                make_column("task_id",          &db_task_edge_t::task_id),
                make_column("edge_id",          &db_task_edge_t::edge_id)),
      make_table("task_traffic",
                make_column("id",               &db_task_traffic_t::id, primary_key().autoincrement()),
                make_column("task_id",          &db_task_traffic_t::task_id),
                make_column("traffic_id",       &db_task_traffic_t::traffic_id)),
      make_table("edge_node",
                make_column("id",               &db_edge_node_t::id, primary_key().autoincrement()),
                make_column("edge_id",          &db_edge_node_t::edge_id),
                make_column("node_id",          &db_edge_node_t::node_id)),
      make_table("topology_node",
                make_column("id",               &topology_node_t::id, primary_key().autoincrement()),
                make_column("topology_id",      &topology_node_t::topology_id),
                make_column("origin_id",        &topology_node_t::origin_id)),
      make_table("task_result",
                make_column("result_id",        &task_result_t::result_id, primary_key()),
                make_column("task_id",          &task_result_t::task_id),
                make_column("task_name",        &task_result_t::task_name),
                make_column("begin_time",       &task_result_t::begin_time),
                make_column("end_time",         &task_result_t::end_time),
                make_column("result",           &task_result_t::result),
                make_column("info",             &task_result_t::info)),
      make_table("task_info",
                make_column("id",               &task_info_t::id, primary_key()),
                make_column("current_odom_time",&task_info_t::current_odom_time),
                make_column("create_odom_time", &task_info_t::create_odom_time),
                make_column("current_odom",     &task_info_t::current_odom),
                make_column("total_odom",       &task_info_t::total_odom),
                make_column("total_duration",   &task_info_t::total_duration),
                make_column("avoid_duration",   &task_info_t::avoid_duration),
                make_column("current_task_count",&task_info_t::current_task_count),
                make_column("total_task_count", &task_info_t::total_task_count),
                make_column("error_task_count", &task_info_t::error_task_count)),
      make_table("kuqu",
                make_column("id",               &db_kuqu_t::id, primary_key().autoincrement()),
                make_column("kuqu_id",          &db_kuqu_t::kuqu_id),
                make_column("name",             &db_kuqu_t::name),
                make_column("direction",        &db_kuqu_t::direction),
                make_column("normal_x",         &db_kuqu_t::normal_x),
                make_column("normal_y",         &db_kuqu_t::normal_y),
                make_column("width",            &db_kuqu_t::width),
                make_column("length",           &db_kuqu_t::length),
                make_column("rowspace",         &db_kuqu_t::rowspace),
                make_column("columnspace",      &db_kuqu_t::columnspace)),
                // make_column("start_kuwei",      &db_kuqu_t::start_kuwei),
                // make_column("end_kuwei",        &db_kuqu_t::end_kuwei)),
      make_table("kuwei",
                make_column("id",               &db_storage_t::id, primary_key().autoincrement()),
                make_column("kuwei_id",         &db_storage_t::kuwei_id),
                make_column("kuqu",             &db_storage_t::kuqu),
                make_column("row",              &db_storage_t::row),
                make_column("column",           &db_storage_t::column),
                make_column("center_x",         &db_storage_t::center_x),
                make_column("center_y",         &db_storage_t::center_y),
                make_column("x1",               &db_storage_t::x1),
                make_column("y1",               &db_storage_t::y1),
                make_column("x2",               &db_storage_t::x2),
                make_column("y2",               &db_storage_t::y2),
                make_column("x3",               &db_storage_t::x3),
                make_column("y3",               &db_storage_t::y3),
                make_column("x4",               &db_storage_t::x4),
                make_column("y4",               &db_storage_t::y4))                
      );
}

static auto init_local_storage(const std::string& path) {
  using namespace sqlite_orm;
  return make_storage(path,
      make_table("map", 
                make_column("id",               &map_t::id, primary_key()),
                make_column("name",             &map_t::name),
                make_column("default",          &map_t::is_default)),
      make_table("relocation",
                make_column("id",               &relocation_t::id, primary_key()),
                make_column("name",             &relocation_t::name),
                make_column("x",                &relocation_t::x),
                make_column("y",                &relocation_t::y),
                make_column("angle",            &relocation_t::angle),
                make_column("map_id",           &relocation_t::map_id),
                make_column("zone_id",          &relocation_t::zone_id),
                make_column("default",          &relocation_t::is_default)),
      make_table("task_result",
                make_column("result_id",        &task_result_t::result_id, primary_key()),
                make_column("task_id",          &task_result_t::task_id),
                make_column("task_name",        &task_result_t::task_name),
                make_column("begin_time",       &task_result_t::begin_time),
                make_column("end_time",         &task_result_t::end_time),
                make_column("result",           &task_result_t::result),
                make_column("info",             &task_result_t::info)),
      make_table("task_info",
                make_column("id",               &task_info_t::id, primary_key()),
                make_column("current_odom_time",&task_info_t::current_odom_time),
                make_column("create_odom_time", &task_info_t::create_odom_time),
                make_column("current_odom",     &task_info_t::current_odom),
                make_column("total_odom",       &task_info_t::total_odom),
                make_column("total_duration",   &task_info_t::total_duration),
                make_column("avoid_duration",   &task_info_t::avoid_duration),
                make_column("current_task_count",&task_info_t::current_task_count),
                make_column("total_task_count", &task_info_t::total_task_count),
                make_column("error_task_count", &task_info_t::error_task_count)),
      make_table("language_error",
                make_column("code",             &language_error_t::code, primary_key()),
                make_column("cn",               &language_error_t::cn),
                make_column("en",               &language_error_t::en),
                make_column("fr",               &language_error_t::fr),
                make_column("de",               &language_error_t::de)),
      make_table("language_field",
                make_column("id",               &language_field_t::id, primary_key()),
                make_column("type",             &language_field_t::type),
                make_column("cn",               &language_field_t::cn),
                make_column("en",               &language_field_t::en),
                make_column("fr",               &language_field_t::fr),
                make_column("de",               &language_field_t::de))
      );
}


}  // namespace common

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_COMMON_DB_COTEK_DB_H_
