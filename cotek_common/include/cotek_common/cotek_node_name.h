/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_NODE_NAME_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_NODE_NAME_H_

// 所有节点名常量移步于此
namespace cotek_node {

constexpr int kErrorExitCode = -1;

// 节点名
constexpr char kEmbeddedNode[] = "cotek_embedded_node";
constexpr char kCommunicateNode[] = "cotek_communicate_node";
constexpr char kDecisionMakerNode[] = "decision_maker_node";
constexpr char kNavigationNode[] = "cotek_navigation_node";
constexpr char kLocalizerNode[] = "cotek_localizer_node";
constexpr char kReflectorNode[] = "cotek_reflector_localizer_node";
constexpr char kActionNode[] = "cotek_action_node";
constexpr char kDiagnosticNode[] = "cotek_diagnostic_node";
constexpr char kAvoidNode[] = "cotek_avoid_node";
constexpr char kStorageNode[] = "cotek_storage_node";
constexpr char kFakeServerNode[] = "faker_server";
constexpr char kFakeVisualNode[] = "cotek_visual_node";
}  // namespace cotek_node

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_NODE_NAME_H_
