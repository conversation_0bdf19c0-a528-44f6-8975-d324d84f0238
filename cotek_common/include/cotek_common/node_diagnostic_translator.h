/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_TRANSLATOR_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_TRANSLATOR_H_
#include <cstdint>
#include <exception>
#include <string>

#include "node_diagnostic_info.h"

namespace cotek_diagnostic {

// 故障翻译字符 中文解决办法/英文解决办法/日语等等
static const std::map<uint16_t, std::string> diagnostic_map = {
    // 通讯故障
    {static_cast<uint16_t>(CommunicateNodeStatus::MSG_TYPE_ABNORMAL), ""},
    {static_cast<uint16_t>(CommunicateNodeStatus::TIMEOUT),
     "请检查车载或服务器网络/Please check the in-car or server network"},
    {static_cast<uint16_t>(CommunicateNodeStatus::CONFIG_ERROR),
     "请检查通讯配置/ Please check communicate config"},
    {static_cast<uint16_t>(CommunicateNodeStatus::NODE_TIMEOUT),
     "通讯模块超时，请联系研发/Communication module timed out please contact "
     "the research and development team"},

    //定位故障

    {static_cast<uint16_t>(LocalizerNodeStatus::LOCALIZER_INIT_POSE_ERROR),
     "定位初始化失败，请进入重定位界面重定位/Positioning initialization "
     "failed, please enter the repositioning interface to reposition"},

    // 决策故障
    {static_cast<uint16_t>(DecisionMakerNodeStatus::TASK_ORDER_ID_ERROR),
     "任务工单号出错/Incorrect task work order number"},
    {static_cast<uint16_t>(DecisionMakerNodeStatus::TASK_SEQUENCE_NUMBER_ERROR),
     "任务序列号出错/Incorrect task sequence number"},
    {static_cast<uint16_t>(DecisionMakerNodeStatus::TASK_TYPE_ERROR),
     "任务类型出错/Incorrect task type"},
    {static_cast<uint16_t>(DecisionMakerNodeStatus::CONFIG_ERROR),
     "请检查配置/Please check config"},
    {static_cast<uint16_t>(DecisionMakerNodeStatus::DONGLE_DIED),
     "请检查是否添加密钥/Please check if the key has been added"},
    {static_cast<uint16_t>(DecisionMakerNodeStatus::LOST_GOODS), ""},

    // 避障故障
    {static_cast<uint16_t>(AvoidNodeStatus::BUMP_ERROR), ""},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_TIME_OUT), ""},

    {static_cast<uint16_t>(AvoidNodeStatus::CONFIG_ERROR), ""},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_MAP_ERROR), ""},

    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_LASER_0_TIMEOUT),
     "请检查前置左侧避障激光/Please check the front left obstacle avoidance "
     "laser"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_LASER_1_TIMEOUT),
     "请检查前置中侧避障激光/Please check the front middle obstacle avoidance "
     "laser"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_LASER_2_TIMEOUT),
     "请检查前置右侧避障激光/Please check the front right obstacle avoidance "
     "laser"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_LASER_3_TIMEOUT),
     "请检查后置中侧避障激光/Please check the back middle obstacle avoidance "
     "laser"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_LASER_4_TIMEOUT),
     "请检查后置右侧避障激光/Please check the back right obstacle avoidance "
     "laser"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_LASER_5_TIMEOUT),
     "请检查后置左侧避障激光/Please check the back left obstacle avoidance "
     "laser"},

    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_CAMERA_0_TIMEOUT),
     "请检查前置左侧避障相机/Please check the front left obstacle avoidance "
     "camera"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_CAMERA_1_TIMEOUT),
     "请检查前置中侧避障相机/Please check the front middle obstacle avoidance "
     "camera"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_CAMERA_2_TIMEOUT),
     "请检查前置右侧避障相机/Please check the front right obstacle avoidance "
     "camera"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_CAMERA_3_TIMEOUT),
     "请检查后置中侧避障相机/Please check the back middle obstacle avoidance "
     "camera"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_CAMERA_4_TIMEOUT),
     "请检查后置右侧避障相机/Please check the back right obstacle avoidance "
     "camera"},
    {static_cast<uint16_t>(AvoidNodeStatus::AVOID_CAMERA_5_TIMEOUT),
     "请检查后置左侧避障相机/Please check the back left obstacle avoidance "
     "camera"},

    {static_cast<uint16_t>(AvoidNodeStatus::LASER_0_TIMEOUT),
     "请检查0号导航激光/Please check the laser for navigation #0"},
    {static_cast<uint16_t>(AvoidNodeStatus::LASER_1_TIMEOUT),
     "请检查1号导航激光/Please check the laser for navigation #1"},
    {static_cast<uint16_t>(AvoidNodeStatus::LASER_2_TIMEOUT),
     "请检查2号导航激光/Please check the laser for navigation #2"},

    {static_cast<uint16_t>(AvoidNodeStatus::SAFETY_IO_TIMEOUT), ""},
    {static_cast<uint16_t>(AvoidNodeStatus::ULTRASONIC_TIMEOUT), ""},

    {static_cast<uint16_t>(RunnerNodeStatus::CAN1_ERROR),
     "请检查第1路can总线/Please inspect the CAN bus channel 1."},
    {static_cast<uint16_t>(RunnerNodeStatus::CAN2_ERROR),
     "请检查第1路can总线/Please inspect the CAN bus channel 2."},

    {static_cast<uint16_t>(RunnerNodeStatus::MOTOR1_ERROR),
     "请检查1号电驱/Please inspect the No. 1 motor."},
    {static_cast<uint16_t>(RunnerNodeStatus::MOTOR2_ERROR),
     "请检查2号电驱/Please inspect the No. 2 motor."},
    {static_cast<uint16_t>(RunnerNodeStatus::MOTOR3_ERROR),
     "请检查3号电驱/Please inspect the No. 3 motor."},
    {static_cast<uint16_t>(RunnerNodeStatus::MOTOR4_ERROR),
     "请检查4号电驱/Please inspect the No. 4 motor."},

    {static_cast<uint16_t>(RunnerNodeStatus::ENCODER1_ERROR),
     "请检查高度拉线编码/Please inspect the height encoder."},
    {static_cast<uint16_t>(RunnerNodeStatus::ENCODER2_ERROR),
     "请检查前移拉线编码器/Please inspect the reach encoder."},
    {static_cast<uint16_t>(RunnerNodeStatus::ENCODER3_ERROR),
     "请检查侧移拉线编码器/Please inspect the lateral shift encoder"},
    {static_cast<uint16_t>(RunnerNodeStatus::ENCODER4_ERROR),
     "请检查拉线编码器/Please check the encoder."},

    {static_cast<uint16_t>(RunnerNodeStatus::IMU1_ERROR),
     "请检查1号imu设备/Please inspect the No. 1 IMU."},
    {static_cast<uint16_t>(RunnerNodeStatus::IMU2_ERROR),
     "请检查2号imu设备/Please inspect the No. 2 IMU."},
    {static_cast<uint16_t>(RunnerNodeStatus::IMU3_ERROR),
     "请检查3号imu设备/Please inspect the No. 3 IMU."},
    {static_cast<uint16_t>(RunnerNodeStatus::IMU4_ERROR),
     "请检查4号imu设备/Please inspect the No. 4 IMU."},

    {static_cast<uint16_t>(RunnerNodeStatus::WEIGHT1_ERROR),
     "请检查1号称重传感器/Please inspect the No. 1 weighing sensor."},
    {static_cast<uint16_t>(RunnerNodeStatus::WEIGHT2_ERROR),
     "请检查2号称重传感器/Please inspect the No. 2 weighing sensor."},
    {static_cast<uint16_t>(RunnerNodeStatus::IO1_ERROR),
     "请检查1号IO设备/Please inspect the No. 1 IO sensor"},
    {static_cast<uint16_t>(RunnerNodeStatus::IO2_ERROR),
     "请检查2号IO设备/Please inspect the No. 2 IO sensor"}

};

static const std::string GetDiagnosticTranlator(const uint16_t& type) {
  std::string ret;
  try {
    ret = diagnostic_map.at(type);
  } catch (const std::exception& ex) {
  }
  return ret;
}

}  // namespace cotek_diagnostic

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_TRANSLATOR_H_
