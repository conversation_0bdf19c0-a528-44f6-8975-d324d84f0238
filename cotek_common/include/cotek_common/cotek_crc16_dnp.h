#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_CRC16_DNP_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_CRC16_DNP_H_
#include <cstdint>

namespace crc {
/** CRC16 object. */
struct crc16
{
   uint16_t value;  /**< CRC16 value. */
};

#define CRC16_DNP
#define U16_CONST uint16_t

#ifdef CRC16_DNP

/*
###############################################################################
# CRC-16-DNP          x16+   x13+ x12+ x11+  x10+  x8+   x6+ x5+      x2+  1
#          0x3D65 =   b      1    1    1     1   0 1  0  1   1  0 0   1  0 1
#
#( expression is normal, MSB-first code, high-order bit is not mentioned)
#
#
#  Name   : "CRC-16/DNP"
#  Width  : 16
#  Poly   : 0x3D65
#  Init   : 0xFFFF
#  RefIn  : FALSE
#  RefOut : FALSE
#  XorOut : 0x0000
#  Check  : 0xBBB6
*/

static const uint16_t crc16_table[256] =
{
   U16_CONST(0x0000), U16_CONST(0x3D65), U16_CONST(0x7ACA), U16_CONST(0x47AF), U16_CONST(0xF594), U16_CONST(0xC8F1), U16_CONST(0x8F5E), U16_CONST(0xB23B),
   U16_CONST(0xD64D), U16_CONST(0xEB28), U16_CONST(0xAC87), U16_CONST(0x91E2), U16_CONST(0x23D9), U16_CONST(0x1EBC), U16_CONST(0x5913), U16_CONST(0x6476),
   U16_CONST(0x91FF), U16_CONST(0xAC9A), U16_CONST(0xEB35), U16_CONST(0xD650), U16_CONST(0x646B), U16_CONST(0x590E), U16_CONST(0x1EA1), U16_CONST(0x23C4),
   U16_CONST(0x47B2), U16_CONST(0x7AD7), U16_CONST(0x3D78), U16_CONST(0x001D), U16_CONST(0xB226), U16_CONST(0x8F43), U16_CONST(0xC8EC), U16_CONST(0xF589),
   U16_CONST(0x1E9B), U16_CONST(0x23FE), U16_CONST(0x6451), U16_CONST(0x5934), U16_CONST(0xEB0F), U16_CONST(0xD66A), U16_CONST(0x91C5), U16_CONST(0xACA0),
   U16_CONST(0xC8D6), U16_CONST(0xF5B3), U16_CONST(0xB21C), U16_CONST(0x8F79), U16_CONST(0x3D42), U16_CONST(0x0027), U16_CONST(0x4788), U16_CONST(0x7AED),
   U16_CONST(0x8F64), U16_CONST(0xB201), U16_CONST(0xF5AE), U16_CONST(0xC8CB), U16_CONST(0x7AF0), U16_CONST(0x4795), U16_CONST(0x003A), U16_CONST(0x3D5F),
   U16_CONST(0x5929), U16_CONST(0x644C), U16_CONST(0x23E3), U16_CONST(0x1E86), U16_CONST(0xACBD), U16_CONST(0x91D8), U16_CONST(0xD677), U16_CONST(0xEB12),
   U16_CONST(0x3D36), U16_CONST(0x0053), U16_CONST(0x47FC), U16_CONST(0x7A99), U16_CONST(0xC8A2), U16_CONST(0xF5C7), U16_CONST(0xB268), U16_CONST(0x8F0D),
   U16_CONST(0xEB7B), U16_CONST(0xD61E), U16_CONST(0x91B1), U16_CONST(0xACD4), U16_CONST(0x1EEF), U16_CONST(0x238A), U16_CONST(0x6425), U16_CONST(0x5940),
   U16_CONST(0xACC9), U16_CONST(0x91AC), U16_CONST(0xD603), U16_CONST(0xEB66), U16_CONST(0x595D), U16_CONST(0x6438), U16_CONST(0x2397), U16_CONST(0x1EF2),
   U16_CONST(0x7A84), U16_CONST(0x47E1), U16_CONST(0x004E), U16_CONST(0x3D2B), U16_CONST(0x8F10), U16_CONST(0xB275), U16_CONST(0xF5DA), U16_CONST(0xC8BF),
   U16_CONST(0x23AD), U16_CONST(0x1EC8), U16_CONST(0x5967), U16_CONST(0x6402), U16_CONST(0xD639), U16_CONST(0xEB5C), U16_CONST(0xACF3), U16_CONST(0x9196),
   U16_CONST(0xF5E0), U16_CONST(0xC885), U16_CONST(0x8F2A), U16_CONST(0xB24F), U16_CONST(0x0074), U16_CONST(0x3D11), U16_CONST(0x7ABE), U16_CONST(0x47DB),
   U16_CONST(0xB252), U16_CONST(0x8F37), U16_CONST(0xC898), U16_CONST(0xF5FD), U16_CONST(0x47C6), U16_CONST(0x7AA3), U16_CONST(0x3D0C), U16_CONST(0x0069),
   U16_CONST(0x641F), U16_CONST(0x597A), U16_CONST(0x1ED5), U16_CONST(0x23B0), U16_CONST(0x918B), U16_CONST(0xACEE), U16_CONST(0xEB41), U16_CONST(0xD624),
   U16_CONST(0x7A6C), U16_CONST(0x4709), U16_CONST(0x00A6), U16_CONST(0x3DC3), U16_CONST(0x8FF8), U16_CONST(0xB29D), U16_CONST(0xF532), U16_CONST(0xC857),
   U16_CONST(0xAC21), U16_CONST(0x9144), U16_CONST(0xD6EB), U16_CONST(0xEB8E), U16_CONST(0x59B5), U16_CONST(0x64D0), U16_CONST(0x237F), U16_CONST(0x1E1A),
   U16_CONST(0xEB93), U16_CONST(0xD6F6), U16_CONST(0x9159), U16_CONST(0xAC3C), U16_CONST(0x1E07), U16_CONST(0x2362), U16_CONST(0x64CD), U16_CONST(0x59A8),
   U16_CONST(0x3DDE), U16_CONST(0x00BB), U16_CONST(0x4714), U16_CONST(0x7A71), U16_CONST(0xC84A), U16_CONST(0xF52F), U16_CONST(0xB280), U16_CONST(0x8FE5),
   U16_CONST(0x64F7), U16_CONST(0x5992), U16_CONST(0x1E3D), U16_CONST(0x2358), U16_CONST(0x9163), U16_CONST(0xAC06), U16_CONST(0xEBA9), U16_CONST(0xD6CC),
   U16_CONST(0xB2BA), U16_CONST(0x8FDF), U16_CONST(0xC870), U16_CONST(0xF515), U16_CONST(0x472E), U16_CONST(0x7A4B), U16_CONST(0x3DE4), U16_CONST(0x0081),
   U16_CONST(0xF508), U16_CONST(0xC86D), U16_CONST(0x8FC2), U16_CONST(0xB2A7), U16_CONST(0x009C), U16_CONST(0x3DF9), U16_CONST(0x7A56), U16_CONST(0x4733),
   U16_CONST(0x2345), U16_CONST(0x1E20), U16_CONST(0x598F), U16_CONST(0x64EA), U16_CONST(0xD6D1), U16_CONST(0xEBB4), U16_CONST(0xAC1B), U16_CONST(0x917E),
   U16_CONST(0x475A), U16_CONST(0x7A3F), U16_CONST(0x3D90), U16_CONST(0x00F5), U16_CONST(0xB2CE), U16_CONST(0x8FAB), U16_CONST(0xC804), U16_CONST(0xF561),
   U16_CONST(0x9117), U16_CONST(0xAC72), U16_CONST(0xEBDD), U16_CONST(0xD6B8), U16_CONST(0x6483), U16_CONST(0x59E6), U16_CONST(0x1E49), U16_CONST(0x232C),
   U16_CONST(0xD6A5), U16_CONST(0xEBC0), U16_CONST(0xAC6F), U16_CONST(0x910A), U16_CONST(0x2331), U16_CONST(0x1E54), U16_CONST(0x59FB), U16_CONST(0x649E),
   U16_CONST(0x00E8), U16_CONST(0x3D8D), U16_CONST(0x7A22), U16_CONST(0x4747), U16_CONST(0xF57C), U16_CONST(0xC819), U16_CONST(0x8FB6), U16_CONST(0xB2D3),
   U16_CONST(0x59C1), U16_CONST(0x64A4), U16_CONST(0x230B), U16_CONST(0x1E6E), U16_CONST(0xAC55), U16_CONST(0x9130), U16_CONST(0xD69F), U16_CONST(0xEBFA),
   U16_CONST(0x8F8C), U16_CONST(0xB2E9), U16_CONST(0xF546), U16_CONST(0xC823), U16_CONST(0x7A18), U16_CONST(0x477D), U16_CONST(0x00D2), U16_CONST(0x3DB7),
   U16_CONST(0xC83E), U16_CONST(0xF55B), U16_CONST(0xB2F4), U16_CONST(0x8F91), U16_CONST(0x3DAA), U16_CONST(0x00CF), U16_CONST(0x4760), U16_CONST(0x7A05),
   U16_CONST(0x1E73), U16_CONST(0x2316), U16_CONST(0x64B9), U16_CONST(0x59DC), U16_CONST(0xEBE7), U16_CONST(0xD682), U16_CONST(0x912D), U16_CONST(0xAC48)
};

#endif


/** crc16 table pointer */
static const uint16_t *pcrc_table;

/*===============================================[ public variables  ]================================================*/

/*===============================================[ private functions ]================================================*/

/*====================================================================================================================*/



int crc16_initialize( struct crc16 *crc16_obj, uint16_t start_value )
{
   crc16_obj->value = start_value;
   return 0;
}


void crc16_update( struct crc16 *crc16_obj, void* src, uint32_t len )
{
   uint16_t i;
   char *psrc = (char*) src;

   pcrc_table = crc16_table;
   for( i = 0U; i < len; i++ )
   {
      crc16_obj->value = ((crc16_obj->value << 8) & 0xFF00) ^ pcrc_table[(crc16_obj->value >> 8) ^ (0xFF &*psrc)];
      psrc++;
   }
}
}
#endif
