/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_DONGLE_IO_DONGLE_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_DONGLE_IO_DONGLE_H_
#include <angles/angles.h>

#include <string>
#include <vector>

#include "cotek_common/dongle/dongle_interface.h"

namespace cotek_dongle {

class IoDongle : public DongleInterface {
 public:
  explicit IoDongle(const std::string& key_name);

  DongleData GetDecryptData(const DongleData& key_data,
                            const DongleData& encrypt_data) override;

  // 加密传入数据 返回加密后的数据
  DongleData GenerateEncryptData(const DongleData& key_data,
                                 const DongleData& raw_data) override;
  bool ComputeDongleAlive(const DongleData& decrypt_data) override;

  bool ComputeDongleAlive(const DongleData& key_data,
                          const DongleData& encrypt_data) override;

 private:
  uint32_t LocalTime2TimeInt();

  std::string key_name_;
};

}  // namespace cotek_dongle

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_DONGLE_IO_DONGLE_H_
