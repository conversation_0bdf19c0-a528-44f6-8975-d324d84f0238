/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_INFO_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_INFO_H_
#include <cotek_msgs/node_diagnostic.h>

namespace cotek_diagnostic {

// 节点监控枚举
enum class CommunicateNodeStatus : uint16_t {
  /*------------INFO 0~299 ------------*/
  NORMAL = 1000,

  /*------------WARN 300~599 ------------*/
  WARN = 1300,
  MSG_TYPE_ABNORMAL = WARN + 1,
  TIMEOUT = WARN + 2,

  /*------------ERROR 600~999 ------------*/
  ERROR = 1600,
  CONFIG_ERROR = ERROR + 1,

  NODE_TIMEOUT = NORMAL + 999
};

enum class DecisionMakerNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 2000,

  /*------------WARN 300~599 ------------*/
  WARN = 2300,
  TASK_ORDER_ID_ERROR = WARN + 1,
  TASK_SEQUENCE_NUMBER_ERROR = WARN + 2,
  TASK_TYPE_ERROR = WARN + 3,
  HANDRAIL_ERROR = WARN + 4,

  /*------------ERROR 600~999 ------------*/
  ERROR = 2600,
  CONFIG_ERROR = ERROR + 1,
  DONGLE_DIED = ERROR + 2,
  LOST_GOODS = ERROR + 3,

  NODE_TIMEOUT = NORMAL + 999
};

enum class LocalizerNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 4000,

  /*------------WARN 300~599 ------------*/
  WARN = 4300,
  GET_LANDMARK_MAP_ERROR = WARN + 1,
  INIT_FAILED = WARN + 2,
  LOCALIZER_INIT_POSE_ERROR = WARN + 3,
  STATIC_LOCALIZER_ERROR = WARN + 4,
  SLAM_POSE_DELAY_WARN = WARN + 5,

  /*------------ERROR 600~999 ------------*/
  ERROR = 4600,
  CONFIG_ERROR = ERROR + 1,
  LASER_MESSAGE_EMPTY = ERROR + 2,  // 导航激光积灰
  SLAM_POSE_ERROR = ERROR + 3,
  ENVIROMENT_MATCH_WARING = ERROR + 4,
  LASER_MESSAGE_LOST = ERROR + 5,  // 导航激光数据丢失
  ENVIROMENT_MATCH_FATAL = ERROR + 6,

  ILLEGAL_QR_TAG_ERROR = ERROR + 11,
  UP_QR_CAMERA_ERROR = ERROR + 12,
  DOWN_QR_CAMERA_ERROR = ERROR + 13,

  NODE_TIMEOUT = NORMAL + 999
};

enum class CalibrationNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 5000,

  /*------------WARN 300~599 ------------*/
  WARN = 5300,
  GET_LANDMARK_MAP_ERROR = WARN + 1,
  ENVIROMENT_MATCH_WARING = WARN + 2,

  /*------------ERROR 600~999 ------------*/
  ERROR = 5600,
  CONFIG_ERROR = ERROR + 1,
  LASER_MESSAGE_EMPTY = ERROR + 2,

  NODE_TIMEOUT = NORMAL + 999

};

enum class NavigationNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 6000,

  /*------------WARN 300~599 ------------*/
  WARN = 6300,
  STABILIZE_TIMEOUT = WARN + 1,      // 托盘路径矫正超时
  POWERON_INIT_TIME_OUT = WARN + 2,  // 上电初始化 抖动超时
  PALLET_LIMIT_ERROR = WARN + 3,

  DOWN_TAG_LOSS = WARN + 11,
  UP_TAG_LOSS = WARN + 12,
  SHELF_LOSS = WARN + 13,

  /*------------ERROR 600~999 ------------*/
  ERROR = 6600,
  CONFIG_ERROR = ERROR + 1,        // config 文件错误
  OUT_OF_THE_ROUTE = ERROR + 2,    // 车辆偏离路径
  DOCKING_TIMEOUT = ERROR + 3,     // 车辆对接控制超时
  ODOM_PROTECT_ERROR = ERROR + 4,  // 里程计保护

  STOP_BACKWARD = ERROR + 6,     // 禁止倒车
  LOCATION_LOSS = ERROR + 7,     // 定位丢失
  TASK_CHECK_ERROR = ERROR + 8,  // 任务校验异常
  SENSOR_TIMEOUT = ERROR + 9,    // 插尖传感器超时异常

  // DIY_CONDITION_SET_ERROR = ERROR + 301,
  // DIY_CONDITION_RESULT_ERROR = ERROR + 302
  DIY_CONDITION_ERROR = ERROR + 301,
  NODE_TIMEOUT = NORMAL + 999
};

enum class AvoidNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 7000,

  /*------------WARN 300~599 ------------*/
  WARN = 7300,
  BUMP_ERROR = WARN + 1,
  AVOID_TIME_OUT = WARN + 2,

  /*------------ERROR 600~999 ------------*/
  ERROR = 7600,
  CONFIG_ERROR = ERROR + 1,
  AVOID_MAP_ERROR = ERROR + 2,

  AVOID_LASER_0_TIMEOUT = ERROR + 100,
  AVOID_LASER_1_TIMEOUT = ERROR + 101,
  AVOID_LASER_2_TIMEOUT = ERROR + 102,
  AVOID_LASER_3_TIMEOUT = ERROR + 103,
  AVOID_LASER_4_TIMEOUT = ERROR + 104,
  AVOID_LASER_5_TIMEOUT = ERROR + 105,

  AVOID_CAMERA_0_TIMEOUT = ERROR + 110,
  AVOID_CAMERA_1_TIMEOUT = ERROR + 111,
  AVOID_CAMERA_2_TIMEOUT = ERROR + 112,
  AVOID_CAMERA_3_TIMEOUT = ERROR + 113,
  AVOID_CAMERA_4_TIMEOUT = ERROR + 114,
  AVOID_CAMERA_5_TIMEOUT = ERROR + 115,

  LASER_0_TIMEOUT = ERROR + 120,
  LASER_1_TIMEOUT = ERROR + 121,
  LASER_2_TIMEOUT = ERROR + 122,

  SAFETY_IO_TIMEOUT = ERROR + 131,
  ULTRASONIC_TIMEOUT = ERROR + 141,

  AVOID_CLOUD_0_TIMEOUT = ERROR + 150,
  AVOID_CLOUD_1_TIMEOUT = ERROR + 151,
  AVOID_CLOUD_2_TIMEOUT = ERROR + 152,
  AVOID_CLOUD_3_TIMEOUT = ERROR + 153,
  AVOID_CLOUD_4_TIMEOUT = ERROR + 154,
  AVOID_CLOUD_5_TIMEOUT = ERROR + 155,

  NODE_TIMEOUT = NORMAL + 999
};

enum class StorageNodeStatus : uint16_t {
  /*------------INFO 0~299 ------------*/
  NORMAL = 8000,

  /*------------WARN 300~599 ------------*/
  WARN = 8300,

  /*------------ERROR 600~999 ------------*/
  ERROR = 8600,
  CONFIG_ERROR = ERROR + 1,
  STORAGE_MAP_ERROR = ERROR + 2,

  NODE_TIMEOUT = NORMAL + 999

};

enum class VisualNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 9000,

  /*------------WARN 300~599 ------------*/
  WARN = 9300,

  /*------------ERROR 600~999 ------------*/
  ERROR = 9600,
  CONFIG_ERROR = ERROR + 1,
  LACK_OF_ANGLE = ERROR + 2,
  LACK_OF_BACKGROUND = ERROR + 3,
  FAIL_INIT = ERROR + 4,
  FRONT_LEFT_LACK_OF_ANGLE = ERROR + 5,
  FRONT_LEFT_LACK_OF_BACKGROUND = ERROR + 6,
  FRONT_RIGHT_LACK_OF_ANGLE = ERROR + 7,
  FRONT_RIGHT_LACK_OF_BACKGROUND = ERROR + 8,

  APRILTAG_CAMERA_OFFLINE = ERROR + 101,
  FRONT_CAMERA_OFFLINE = ERROR + 102,
  FRONT_LEFT_CAMERA_OFFLINE = ERROR + 103,
  FRONT_RIGHT_CAMERA_OFFLINE = ERROR + 104,
  PALLET_DETECT_CAMERA_OFFLINE = ERROR + 105,
  PALLET_LEFT_AVOID_CAMERA_OFFLINE = ERROR + 106,
  PALLET_RIGHT_AVOID_CAMERA_OFFLINE = ERROR + 107,

  NODE_TIMEOUT = NORMAL + 999
};

enum class ActionNodeStatus : uint16_t {

  /*------------INFO 0~299 ------------*/
  NORMAL = 10000,

  /*------------WARN 300~599 ------------*/
  WARN = 10300,
  EXCEPTION_NO_PALLET_ERR = WARN + 1,  // 未检测到挡板
  PALLET_DETECT_ERROR = WARN + 2,      // 托盘检测失败
  PALLET_LIMIT_ERROR = WARN + 3,       // 超板检测异常
  COLUMN_DETECT_ERROR = WARN + 4,      // 柱子检测失败
  UNLOAD_DETECT_ERROR = WARN + 5,      // 库位空间检测失败

  /*------------ERROR 600~999 ------------*/
  ERROR = 10600,
  CONFIG_ERROR = ERROR + 1,     // config 文件错误
  ACTION_TYPE_ERR = ERROR + 2,  // 下发动作类型错误
  BAD_PARAMETER_ERR =
      ERROR + 3,  // 缺少相应对象内容的动作配置/动作值超出配置最大限制值
  ACTION_TIME_OUT = ERROR + 4,          // 动作超时
  PALLET_MOVE_ERR = ERROR + 5,          // 托盘车插腿长时间无动作
  UP_WEIGHT_CHECK_ERROR = ERROR + 6,    // 取货后称重检测异常
  DOWN_WEIGHT_CHECK_ERROR = ERROR + 7,  // 卸货后称重检测异常
  SHELF_LOSS = ERROR + 8,               // 货架二维码丢失
  FORK_UP_LIMIT = ERROR + 9,            // 上限位IO触发
  FORK_IO_AVOID = ERROR + 10,           // 插腿避障触发
  CHECK_LABEL_ERROR = ERROR + 11,       // 识别标签超时

  IMU_ERROR = ERROR + 101,               // IMU异常
  WEIGHT_SENSOR_ERROR = ERROR + 102,     // 重量传感器异常
  HEIGHT_ENCODER_ERROR = ERROR + 103,    // 高度拉线编码器异常
  LATERAL_ENCODER_ERROR = ERROR + 104,   // 前移拉线编码器异常
  SIDEMOVE_ENCODER_ERROR = ERROR + 105,  // 侧移拉线编码器异常
  IO_ERROR = ERROR + 106,                // IO模块异常
  ELEVATOR_ERROR = ERROR + 107,          // 电梯故障
  AUTODOOR_ERROR = ERROR + 108,          // 自动门故障

  RFID_ERROR = ERROR + 110,  // RFID模块异常
  QR_ERROR = ERROR + 111,    // 二维码模块异常
  BAR_ERROR = ERROR + 112,   // 一维码模块异常

  DIY_CONDITION_ERROR = ERROR + 301,  // 自定义条件错误/自定义错误条件触发

  NODE_TIMEOUT = NORMAL + 999
};

enum class CartoRosNodeStatus : uint16_t {
  /*------------INFO 0~299 ------------*/
  NORMAL = 11000,

  /*------------WARN 300~599 ------------*/
  WARN = 11300,
  STATIC_LOCALIZER_ERROR = WARN + 1,

  /*------------ERROR 600~999 ------------*/
  ERROR = 11600,
  CONFIG = ERROR + 1,
  ENVIROMENT_MATCH_WARING = ERROR + 2,

  NODE_TIMEOUT = NORMAL + 999

};

enum class RunnerNodeStatus : uint16_t {
  /*------------INFO 0~299 ------------*/
  NORMAL = 12000,

  /*------------WARN 300~599 ------------*/
  WARN = 12300,
  CONFIG_WARN = WARN + 1,
  CAN1_WARN = WARN + 2,
  CAN2_WARN = WARN + 3,
  MOTOR1_WARN = WARN + 11,
  MOTOR2_WARN = WARN + 12,
  MOTOR3_WARN = WARN + 13,
  MOTOR4_WARN = WARN + 14,
  ENCODER1_WARN = WARN + 21,
  ENCODER2_WARN = WARN + 22,
  ENCODER3_WARN = WARN + 23,
  ENCODER4_WARN = WARN + 24,
  IMU1_WARN = WARN + 31,
  IMU2_WARN = WARN + 32,
  IMU3_WARN = WARN + 33,
  IMU4_WARN = WARN + 34,
  WEIGHT1_WARN = WARN + 41,
  WEIGHT2_WARN = WARN + 42,
  IO1_WARN = WARN + 51,
  IO2_WARN = WARN + 52,
  AUDIO1_WARN = WARN + 61,
  AUDIO2_WARN = WARN + 62,
  BATTERY1_WARN = WARN + 71,
  BATTERY2_WARN = WARN + 72,
  /*------------ERROR 600~999 ------------*/
  ERROR = 12600,
  CONFIG_ERROR = ERROR + 1,
  CAN1_ERROR = ERROR + 2,
  CAN2_ERROR = ERROR + 3,
  MOTOR1_ERROR = ERROR + 11,
  MOTOR2_ERROR = ERROR + 12,
  MOTOR3_ERROR = ERROR + 13,
  MOTOR4_ERROR = ERROR + 14,
  ENCODER1_ERROR = ERROR + 21,
  ENCODER2_ERROR = ERROR + 22,
  ENCODER3_ERROR = ERROR + 23,
  ENCODER4_ERROR = ERROR + 24,
  IMU1_ERROR = ERROR + 31,
  IMU2_ERROR = ERROR + 32,
  IMU3_ERROR = ERROR + 33,
  IMU4_ERROR = ERROR + 34,
  WEIGHT1_ERROR = ERROR + 41,
  WEIGHT2_ERROR = ERROR + 42,
  IO1_ERROR = ERROR + 51,
  IO2_ERROR = ERROR + 52,
  AUDIO1_ERROR = ERROR + 61,
  AUDIO2_ERROR = ERROR + 62,
  BATTERY1_ERROR = ERROR + 71,
  BATTERY2_ERROR = ERROR + 72,

  NODE_TIMEOUT = NORMAL + 999,

};

enum class Slam3dNodeStatus : uint16_t {
  /*------------INFO 0~299 ------------*/
  NORMAL = 13000,
  /*------------WARN 300~599 ------------*/
  WARN = 13300,
  /*------------ERROR 600~999 ------------*/
  ERROR = 13600,
  CONFIG_ERROR = ERROR + 1,
};

struct diagnostic {
  uint32_t time_stamp;
  uint16_t status;
};

}  // namespace cotek_diagnostic

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_INFO_H_
