/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_AGV_BASIC_OPTION_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_AGV_BASIC_OPTION_H_

#include <boost/geometry.hpp>
#include <boost/geometry/geometries/point_xy.hpp>
#include <map>
#include <string>
#include <vector>

#include "Eigen/Core"

namespace bg = boost::geometry;
namespace btf = bg::strategy::transform;

using Boost_Point = bg::model::d2::point_xy<double>;
using Polygon = bg::model::polygon<Boost_Point, true, true>;
struct LoraOption {
  int lora_id = 0;
  int lora_mode = 0;
  std::string lora_chanel;
};
struct CommunicateOption {
  enum CommunicateMethod : uint8_t { NONE = 0, HTTP = 1, MQTT = 2, UDP = 3 };
  CommunicateMethod method = CommunicateMethod::NONE;
  std::string system_version;

  std::string server_ip;
  int server_port = 0;
  int server_http_port = 0;

  std::string local_ip;
  int local_port = 0;
  int local_http_port = 0;
  LoraOption lora_option;

  std::string project_topic;

  int localizer_mode{0};  // 0:unknown 1:2d 2:3d

  std::string language;
};

struct UnicycleModelOption {
  float wheel_track;              // 轮间距 m
  float wheel_diameter;           // 轮子直径 m
  float move_motor_reduce_ratio;  // 移动电机减速比 系数

  float left_motor_reduce_ratio;
  float right_motor_reduce_ratio;
  float lift_motor_reduce_ratio;    // 顶升电机减速比
  float rotate_motor_reduce_ratio;  // 旋转电机减速比
                                    //
  float lift_screw_lead;            // 顶升结构导程
  float lift_max_height;            // 顶升电机最大顶升高度

  int lift_mechanism;    // 升降机构:1-丝杆 2-剪刀叉 3-IO气缸
  bool use_lift;         // 启用顶升
  bool lift_motor_mode;  // 顶升电机工作模式：0-速度 1:绝对位置
  bool use_rotate;       // 启用旋转
  bool use_up_camera;    // 启用向上电机
  bool use_down_camera;  // 启用向下电机
  bool use_io_laser_avoid;  // 启用激光IO雷达避障

  int set_left_direction;        // 控制左轮方向
  int feedback_left_direction;   // 反馈左轮方向
  int set_right_direction;       // 控制右轮方向
  int feedback_right_direction;  // 反馈右轮方向

  int set_lift_direction;    // 控制顶升方向
  int set_rotate_direction;  // 控制旋转方向
};

struct BicycleModelOption {
  float wheel_base;  // 轴距 m
  float max_wheel_base;
  float min_wheel_base;
  float eccentric_distance;  // 舵轮距过后轮中心垂线的距离(偏心距) m
  float eccentric_angle;  // 舵轮(偏心角度) 弧度
  float reduction_ratio;  // 辅助减速比

  int set_driver_direction;       // 控制驱动轮方向
  int feedback_driver_direction;  // 反馈驱动轮方向
  int set_steer_direction;        // 控制舵轮方向
  int feedback_steer_direction;   // 反馈舵轮方向
};

struct Transform {
  double x;
  double y;
  double z;
  double roll;
  double pitch;
  double yaw;
};

typedef std::map<std::string, Transform> TransformMap;

struct LaserScanOption {
  struct Filter {
    bool positive{false};
    double angle_min{0.};
    double angle_max{0.};
    double filter_laser_point{0.};
  };
  std::string frame;
  std::string type;
  std::string topic;
  bool use_3d{false};
  int scan_sample_step{0};
  double angle_min{0.};
  double angle_max{0.};
  double angle_increment{0.};
  double range_min{0.};
  double range_max{0.};
  Filter filter;
  Transform tf;
};

typedef std::map<std::string, LaserScanOption> LaserMap;

struct CameraOption {
  std::string frame;
  std::string type;
  std::string serial_number;  // 相机序列号
  int width;
  int height;
  int mode;
  Transform tf;
};

typedef std::map<std::string, CameraOption> CameraMap;

struct MoveModelControl {
  // 传感器屏蔽相关参数
  bool use_fork_avod;
  double fork_avoid_dist;
  bool fork_io_positive;  // 叉尖光电是否高电平触发
  // 惯性补偿相关参数
  bool use_inertia_compensation;
  double velocity_coefficient;
  double omega_coefficient;
  // 手动控制相关参数
  float pallet_up_speed;
  float pallet_down_speed;
  float pallet_rotate_speed;
  float forward_speed;
  float backward_speed;
  float rotate_left_speed;
  float rotate_right_speed;
  uint8_t led_type;
  uint8_t audio_type;
  bool enter_low_power;
  bool enable_charge;
  // 电机选装方向定义
  float set_left_sign;
  float set_right_sign;
  float feedback_left_sign;
  float feedback_right_sign;
  float rotate_motor_sign;
  float lift_motor_sign;
};

struct SensorTransform {
  double translation_x;
  double translation_y;
  double rotation;
};

typedef std::map<std::string, SensorTransform> SensorTFData;

struct LaserSensorInstallOption {
  double laser_offset_x;
  double laser_offset_y;
  double laser_offset_theta;
};
struct UltrasonicSensorOption {
  int32_t type;
  int32_t num;
};

struct QrCameraInfo {
  int32_t up_camera_type;
  int32_t down_camera_type;
};

struct SensorInstallOption {
  LaserSensorInstallOption laser_sensor_install_option;
};

constexpr char kBicycleModel[] = "bicycle";
constexpr char kUnicycleModel[] = "unicycle";
struct ChassisModelOption {
  std::string name;
  std::string type;
  BicycleModelOption bicycle_model;
  UnicycleModelOption unicycle_model;
};

struct MechanicalOption {
  int model_type;
  UnicycleModelOption unicycle_model_option;
  BicycleModelOption bicycle_modle_option;
  // TODO(@someone) 后续统一所有tf
  SensorInstallOption sensor_install_option;
  QrCameraInfo qr_camera_info;
};

struct LandmarkMapOption {
  std::string http_get_landmark_map_url;
  std::string http_update_landmark_map_url;
  int http_landmark_map_port;
  int current_reflector_map_id;
  int current_reflector_section_id;
  int current_qr_map_id;
  int current_qr_section_id;
  int current_rfid_map_id;
  int current_rfid_section_id;
};

struct SlamMapOption {
  std::string http_get_slam_pgm_url;
  std::string http_get_slam_yaml_url;
  std::string http_get_slam_pbstream_url;
};

struct MapInfoOption {
  std::string current_zone_id;
  std::string current_map_id;
  std::string current_map_version;
};

struct SlamFeatureOption {
  bool use_landmarks;  // 是否使用反光板
  bool use_imu_data;   // 是否使用imu
  int min_range_data_size;  // 最少激光点数，低于此点数的激光帧过滤不要
  int front_scan_match_strong_cell;   // 前端匹配强点增强比例
  int front_ceres_strong_cell;        // 前端ceres强点增强比例
  int back_scan_match_strong_cell;    // 后端匹配强点增强比例
  double front_linear_search_window;  // 前端匹配搜索位移窗口
  double front_angular_search_window;  // 前端匹配搜索角度窗口（单位rad）
  double back_linear_search_window;  // 后端匹配搜索位移窗口
  double back_angular_search_window;  // 后端匹配搜索角度窗口（单位rad）
  std::string laser_type;             // 激光类型
  int intensity_threshould;           // 反光强度阈值
  double range_max;                   // slam激光雷达最大距离
};

struct ReflectorFeatureOption {
  bool reflector_mode_only;               // 是否单独使用反光板
  double reflector_dynamic_max_distance;  // 反光板动态匹配最大距离
};

struct Md5ConfigOption {
  std::string eth_name;
  std::string md5;
};

struct LandmarkOption {
  bool enable = false;
  double radius;
  double check_distance;
  double num_add;
  double num_dec;
};

struct SlamOption {
  SlamFeatureOption slam_feature_option;
  ReflectorFeatureOption reflector_feature_option;
  Md5ConfigOption md5_config_option;
  LandmarkOption reflector_option;
  LandmarkOption lost_check_option;
};

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_AGV_BASIC_OPTION_H_
