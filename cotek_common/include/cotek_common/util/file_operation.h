#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_FILE_OPERATION_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_FILE_OPERATION_H_

#include <cotek_common/log_porting.h>
#include <sys/stat.h>
#include <unistd.h>

#include <fstream>
#include <string>

namespace util {

void CreateDirectories(const std::string& path_in);

bool GetDirFiles(const std::string &cfg_dir, std::set<std::string> &files);

std::string GetBaseName(const std::string &path);

}  // namespace util

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_FILE_OPERATION_H_