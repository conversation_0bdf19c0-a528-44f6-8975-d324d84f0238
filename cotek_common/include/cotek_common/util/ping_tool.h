/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_PING_TOOL_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_PING_TOOL_H_

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include <iostream>
#include <string>

namespace util {

class PingTool {
 public:
  PingTool();
  ~PingTool();
  bool Ping(std::string ip);

 private:
  std::string GetResponse(const std::string &str_cmd);
};

}  // namespace util
#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_PING_TOOL_H_
