

#include "cotek_common/util/file_operation.h"
#include <dirent.h>

namespace util {

void CreateDirectories(const std::string& path_in) {
  std::string path = path_in;
  std::string dirPath;
  size_t pos = 0;
  std::string delimiter = "/";

  // Iterate through each level of the directory structure
  while ((pos = path.find(delimiter)) != std::string::npos) {
    dirPath += path.substr(0, pos) + delimiter;
    path.erase(0, pos + delimiter.length());

    // Check if the directory exists, if not, create it
    if (mkdir(dirPath.c_str(), 0755) == -1) {
      // Directory already exists or an error occurred
      continue;
    }

    // Change into the newly created directory
    if (chdir(dirPath.c_str()) == -1) {
      // Failed to change into the directory
      LOG_ERROR("dir %s create fail!!!", dirPath.c_str());
      return;
    }
  }

  LOG_INFO("dir %s create success!", dirPath.c_str());
}

bool GetDirFiles(const std::string &cfg_dir, std::set<std::string> &files)
{
  bool ret = true;
  DIR *dir_obj = nullptr;
  try {
    if ((dir_obj = opendir(cfg_dir.c_str())) == nullptr) {
      std::cout << "open dir(" << cfg_dir << ") error" << std::endl;
      return false;
    }

    struct dirent *ptr = nullptr;
    while ((ptr = readdir(dir_obj)) != NULL) {
      if (strcmp(ptr->d_name, ".") == 0 ||
          strcmp(ptr->d_name, "..") == 0) {
          //current dir or parrent dir
          continue;
      } else {
        switch (ptr->d_type)
        {
        case 8:
        {
          std::string url = cfg_dir + "/" + std::string(ptr->d_name);
#ifdef _DEBUG
          std::cout << url << std::endl;
#endif
          files.insert(url);
          break;
        }
        case 10:
        {
#ifdef _DEBUG
          std::cout << "file_name: " << cfg_dir << "/" << ptr->d_name << std::endl;
#endif
          break;
        }
        case 4:
        {
          std::string new_url = cfg_dir + "/" + std::string(ptr->d_name);
          return GetDirFiles(new_url, files);
        }
        default:
          break;
        }
      }
    }
    ret = true;
  } catch(...) {
    std::cout << "open dir(" << cfg_dir << ") exception" << std::endl;
    ret = false;
  }
  if(dir_obj) {
    closedir(dir_obj);
  }
  return true;
}

std::string GetBaseName(const std::string &url)
{
  std::string filename(url);
  auto pos = url.rfind("/");
  if (pos >= 0 && pos < url.length()) {
      filename = url.substr(pos + 1);
  }
  pos = filename.find(".");
  if (pos != std::string::npos) {
      filename = filename.substr(0, pos);
  }

  return std::move(filename);
}

}  // namespace util