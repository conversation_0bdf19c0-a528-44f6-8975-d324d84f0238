/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_LOCAL_SERVICE_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_LOCAL_SERVICE_H_
#include <fstream>
#include <sstream>
#include <string>

namespace util {
class LocalService final {
 public:
  static std::string GetStringFromFile(const std::string &file_path) {
    std::ifstream ifs(file_path);
    if (!ifs.is_open()) {
      ifs.close();
      return std::string();
    }

    std::stringstream ss;
    ss << ifs.rdbuf();
    ifs.close();
    return ss.str();
  }

  static bool SaveStringToFile(const std::string &file_path,
                               const std::string &data) {
    try {
      std::ofstream ofs(file_path, std::ios::out);
      if (!ofs) {
        return false;
      }
      ofs << data;
      ofs.flush();  // 显式刷新缓冲区，确保数据写入磁盘
      return true;
    } catch (const std::ios_base::failure &e) {
      // 处理异常（例如，记录错误）
      return false;
    }
  }
};
}  // namespace util

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_UTIL_LOCAL_SERVICE_H_
