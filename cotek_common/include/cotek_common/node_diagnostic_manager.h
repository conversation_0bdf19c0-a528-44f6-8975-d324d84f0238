/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_MANAGER_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_MANAGER_H_
#include <ros/ros.h>

#include <mutex>
#include <utility>
#include <vector>

#include "cotek_common/node_diagnostic_info.h"
#include "ros/duration.h"
#include "ros/time.h"

namespace cotek_diagnostic {
template <typename T>
class NodeStatusManager {
 public:
  struct TimeStatus {
    T status;
    ros::Time time;
  };

  NodeStatusManager() : time_(ros::Time::now()) {}

  void SetNodeStatus(const T& status) {
    if (static_cast<int>(status) == static_cast<int>(T::NORMAL)) return;

    std::unique_lock<std::mutex> lock(mutex_);
    TimeStatus temp;
    temp.status = status;
    temp.time = ros::Time::now();
    status_[status] = temp;
  }

  void ClearNodeStatus() {
    std::unique_lock<std::mutex> lock(mutex_);
    status_.clear();
  }

  void PeriodClearNodeStatus(const double& time) {
    // 周期性清除状态
    std::unique_lock<std::mutex> lock(mutex_);

    for (auto iter = status_.begin(); iter != status_.end();) {
      if (ros::Time::now() - iter->second.time > ros::Duration(time)) {
        iter = status_.erase(iter);
      } else {
        ++iter;
      }
    }
  }

  inline const std::map<T, T> GetNodeStatus() {
    std::unique_lock<std::mutex> lock(mutex_);
    std::map<T, T> temp;
    for (auto state : status_) {
      temp[state.first] = state.second.status;
    }
    return temp;
  }

 private:
  std::mutex mutex_;
  ros::Time time_;
  std::map<T, TimeStatus> status_;
};

}  // namespace cotek_diagnostic

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_NODE_DIAGNOSTIC_MANAGER_H_
