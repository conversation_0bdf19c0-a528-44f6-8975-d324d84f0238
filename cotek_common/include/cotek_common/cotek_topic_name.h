/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_TOPIC_NAME_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_TOPIC_NAME_H_
#include <cotek_msgs/node_diagnostic.h>

#include "cotek_msgs/adjust_up.h"

constexpr uint32_t kTopicSendCacheSize = 10;
constexpr uint32_t kTopicReciveCacheSize = 2;

// actionlib
constexpr char kActionClientName[] = "actionServer";
constexpr char kTrackPathClientName[] = "track_path";
constexpr char kOpenloopClientName[] = "openLoopActionServer";

// TODO(@someone) 以后将所有服务名常量移步于此
// service
namespace cotek_services {

constexpr char kStartCartoWithPureLocalizerService[] =
    "startCartoWithPureLocalizerSrv";
constexpr char kStartCartoWithMappingService[] = "startCartoWithMappingSrv";
constexpr char kSaveCartoMapService[] = "saveCartoMapSrv";
constexpr char kSaveSlam3DMapService[] = "saveSlam3DMapSrv";
constexpr char kEditSlam3DMapService[] = "editSlam3DMapSrv";
constexpr char kSaveSlam3DBackendMapService[] = "saveSlam3DBackendMapSrv";
constexpr char kStartSlam3DTrajectoryService[] = "startSlam3DTrajectorySrv";
constexpr char kUpdateCartoMapService[] = "updateCartoMapSrv";
constexpr char kFinishCartoService[] = "finishCartoSrv";
constexpr char kFinishTrajectory[] = "finishTrajectorySrv";
constexpr char kResetHeight[] = "resetHeight";
constexpr char kResetLateral[] = "resetLateral";
constexpr char kResetWeight[] = "resetWeight";

constexpr char kAgvQueryDispatchService[] = "agvQueryDispatchSrv";

constexpr char kQueryStorageInfoService[] = "storageInfo";
constexpr char kOdomInfoService[] = "odomInfo";

// 配置类
constexpr char kUpdateActionConfigService[] = "actionConfig";
constexpr char kUpdateNavigationConfigService[] = "navigationConfig";
constexpr char kUpdateBasicConfigService[] = "basicConfig";
constexpr char kUpdateLogicConfigService[] = "logicConfig";
constexpr char kUpdateAvoidAreaConfigService[] = "avoidAreaConfig";
constexpr char kUpdateAvoidConfigService[] = "avoidConfig";
constexpr char kUpdateRunnerConfigService[] = "runnerConfig";
constexpr char kUpdateLocalizerConfigService[] = "localizerConfig";
constexpr char kUpdateCalibrationConfigService[] = "calibrationConfig";
constexpr char kUpdatestorageConfigService[] = "storageConfig";
constexpr char kUpdateStorageMapService[] = "storageMap";
constexpr char kUpdatevisualConfigService[] = "visualConfig";
constexpr char kUpdateSlamConfigService[] = "slamConfig";
constexpr char kUpdateVoiceConfigService[] = "voiceConfig";

// 标定类
constexpr char kOriginMap[] = "orignMap";
constexpr char kMergeMap[] = "mergeMap";
constexpr char kSaveMap[] = "saveMap";
constexpr char kOdomCalibration[] = "odomCalibration";
constexpr char kCableCalibration[] = "cableCalibration";
constexpr char kTfCalibration[] = "tfCalibration";
constexpr char kAngleCalibration[] = "angleCalibration";
constexpr char kBackGroundCalibration[] = "backGroundCalibration";

}  // namespace cotek_services

// TODO(@someone) 以后将所有话题名常量移步于此
namespace cotek_topic {
// ros标准
constexpr char kOdomTopic[] = "odom";
constexpr char kImuTopic[] = "imu";

// 传感器
constexpr char kMotor0Topic[] = "motor0";
constexpr char kMotor1Topic[] = "motor1";
constexpr char kMotor2Topic[] = "motor2";
constexpr char kMotor3Topic[] = "motor3";
constexpr char kMotor4Topic[] = "motor4";
constexpr char kMotor5Topic[] = "motor5";

constexpr char kIo0Topic[] = "io0";
constexpr char kIo1Topic[] = "io1";
constexpr char kIo2Topic[] = "io2";
constexpr char kIo3Topic[] = "io3";
constexpr char kIo4Topic[] = "io4";
constexpr char kIo5Topic[] = "io5";

constexpr uint32_t kTopicSendCacheSize = 10;
constexpr uint32_t kTopicReciveCacheSize = 2;

// actionlib
constexpr char kActionClientName[] = "actionserver";
constexpr char kTrackPathClientName[] = "track_path";
constexpr char kOpenloopClientName[] = "openloop";

// 节点监控相关
constexpr char kCommunicateDiagnosticTopic[] = "communicateNodeDiagnostic";
constexpr char kRunnerNodeDiagnosticTopic[] = "runnerNodeDiagnostic";
constexpr char kLocalizerDiagnosticTopic[] = "localizerNodeDiagnostic";
constexpr char kCalibrationDiagnosticTopic[] = "calibrationNodeDiagnostic";
constexpr char kNavigationDiagnosticTopic[] = "navigationNodeDiagnostic";
constexpr char kDecisionDiagnosticTopic[] = "decisionNodeDiagnostic";
constexpr char kActionDiagnosticTopic[] = "actionNodeDiagnostic";
constexpr char kAvoidDiagnosticTopic[] = "avoidNodeDiagnostic";
constexpr char kStorageDiagnosticTopic[] = "storageNodeDiagnostic";
constexpr char kVisaulDiagnosticTopic[] = "visualNodeDiagnostic";
constexpr char kCartoRosDiagnosticTopic[] = "slamNodeDiagnostic";
constexpr char kErrorsTopic[] = "errors";
constexpr char kSelfCheckTopic[] = "selfCheck";
// 移动相关:
constexpr char kMoveCmdTopic[] = "moveCmd";
constexpr char kMoveFeedbackTopic[] = "moveFeedback";
constexpr char kAdjustSpeedTopic[] = "adjustSpeed";

// 动作相关:
constexpr char kActionCmdTopic[] = "actionCmd";

// 安全避障相关
constexpr char kSafetySettingTopic[] = "safetySetting";
constexpr char kSafetyStateTopic[] = "safetyState";

// 定位相关
constexpr char kAgvPositionTopic[] = "agvPosition";
constexpr char kSlamPositionTopic[] = "slamPosition";
constexpr char kStrongAreaInfoTopic[] = "strongAreaInfo";
constexpr char kHighDynamicAreaInfoTopic[] = "hignDynamicAreaInfo";
constexpr char kScanMatchedPointsTopic[] = "scanMatchedPoints";
constexpr char kReflectorMapPointsTopic[] = "reflectorMapPoints";
constexpr char kMatchedLandmarkPointsTopic[] = "matchedLandmarkPoints";
constexpr char kAllReflectorLandmarkPointsTopic[] =
    "allReflectorLandmarkPoints";
constexpr char kLostCheckMapPointsTopic[] = "lostCheckMapPoints";
constexpr char kMatchedLostCheckPointsTopic[] = "matchedLostCheckPoints";

constexpr char kLandmarkTopic[] = "landmark";

// slam实时地图
constexpr char kMapWebTopic[] = "mapWeb";
constexpr char kMap3dFrontTopic[] = "map3dFront";
constexpr char kMap3dBackTopic[] = "map3dBack";
constexpr char kMatchedPointCloudTopic[] = "matchedPointCloud";

// 配置相关
constexpr char kUpdateBasicConfigTopic[] = "updateBasicConfig";
constexpr char kUpdateAvoidAreaConfigTopic[] = "updateAvoidAreaConfig";

// 相机类
constexpr char kAvoidCamera0Topic[] = "avoidCamera0";
constexpr char kAvoidCamera1Topic[] = "avoidCamera1";
constexpr char kAvoidCamera2Topic[] = "avoidCamera2";
constexpr char kAvoidCamera3Topic[] = "avoidCamera3";
constexpr char kAvoidCamera4Topic[] = "avoidCamera4";
constexpr char kAvoidCamera5Topic[] = "avoidCamera5";

constexpr char kOrignCameraImage0Topic[] = "orignCameraImage0";
constexpr char kOrignCameraImage1Topic[] = "orignCameraImage1";
constexpr char kOrignCameraImage2Topic[] = "orignCameraImage2";
constexpr char kOrignCameraImage3Topic[] = "orignCameraImage3";
constexpr char kOrignCameraImage4Topic[] = "orignCameraImage4";
constexpr char kOrignCameraImage5Topic[] = "orignCameraImage5";

constexpr char kAvoidCameraImage0Topic[] = "avoidCameraImage0";
constexpr char kAvoidCameraImage1Topic[] = "avoidCameraImage1";
constexpr char kAvoidCameraImage2Topic[] = "avoidCameraImage2";
constexpr char kAvoidCameraImage3Topic[] = "avoidCameraImage3";
constexpr char kAvoidCameraImage4Topic[] = "avoidCameraImage4";
constexpr char kAvoidCameraImage5Topic[] = "avoidCameraImage5";

constexpr char kStablizerCameraImage3Topic[] = "stablizerCameraImage3";
constexpr char kColumnStablizerLaserTopic[] = "columnStablizerLaser";
constexpr char kPalletStablizerLaserTopic[] = "palletStablizerLaser";

// 激光类

constexpr char kAvoidLaser0Topic[] = "avoidLaser0";
constexpr char kAvoidLaser1Topic[] = "avoidLaser1";
constexpr char kAvoidLaser2Topic[] = "avoidLaser2";
constexpr char kAvoidLaser3Topic[] = "avoidLaser3";
constexpr char kAvoidLaser4Topic[] = "avoidLaser4";
constexpr char kAvoidLaser5Topic[] = "avoidLaser5";

constexpr char kNaviLaser0Topic[] = "naviLaser0";
constexpr char kNaviLaser1Topic[] = "naviLaser1";
constexpr char kNaviLaser2Topic[] = "naviLaser2";

constexpr char kAvoidCloud0Topic[] = "avoidCloud0";
constexpr char kAvoidCloud1Topic[] = "avoidCloud1";
constexpr char kAvoidCloud2Topic[] = "avoidCloud2";
constexpr char kAvoidCloud3Topic[] = "avoidCloud3";
constexpr char kAvoidCloud4Topic[] = "avoidCloud4";
constexpr char kAvoidCloud5Topic[] = "avoidCloud5";

constexpr char k3dNaviLaser0Topic[] = "navi3dLaser0";

// 抽象IO状态
constexpr char kSafetyIoStateTopic[] = "safetyIoState";  // io激光与叉尖光电
constexpr char kBumpStateTopic[] = "bumpState";
constexpr char kUpDownStateTopic[] = "upDownState";
constexpr char kLoadReachDetectTopic[] = "loadReachDetect";  // 挡板到位检测
constexpr char kChargeDoStateTopic[] = "chargeDoState";
constexpr char kBumpResetTopic[] = "bumpResetState";
constexpr char kManualTopic[] = "manual";
constexpr char kEmergencyTopic[] = "emergency";

/*---算法结果数据----*/
constexpr char kPalletCenterTopic[] = "palletCenterFeedback";
constexpr char kMeasureSizeTopic[] = "measureSizeFeedback";
constexpr char kUnloadDetectTopic[] = "unloadDetectFeedback";
// 视觉检测叉腿后方超板
constexpr char kPalletBackLimitFeedbackTopic[] = "palletBackLimitFeedback";
constexpr char kRequestPalletBackLimitTopic[] = "requestPalletBackLimit";

constexpr char kBatteryStateTopic[] = "batteryState";
constexpr char kLoadsTopic[] = "loads";

// 声光控制
constexpr char kLedControlTopic[] = "ledControl";
constexpr char kAudioControlTopic[] = "audioControl";

// 状态
constexpr char kAgvStateTopic[] = "agvState";
constexpr char kAudioStateTopic[] = "audio";

/*---传感器----*/
constexpr char kBatteryTopic[] = "battery";
constexpr char kUltarsonicTopic[] = "ultrasonic";
constexpr char kWeightTopic[] = "weight";
constexpr char kPalletActInfoTopic[] = "palletActInfo";
constexpr char kClampStateTopic[] = "clampState";
constexpr char kWeightDropCheckTopic[] = "weightingLoaded";

// 数据统计类
constexpr char kGlobalOdomInfoTopic[] = "globalOdomInfo";

// 调度控制 任务 直接动作
constexpr char kUpdateEventTopic[] = "state";
constexpr char kOrderRequestTopic[] = "order";
constexpr char kInstantActionRequestTopic[] = "instantAction";

// 定位相关
constexpr char kSlamPoseDeviationTopic[] = "slamPoseDeviation";
constexpr char kReLocationTopic[] = "reLocation";

// 3D slam相关
constexpr char kSlam3DPoseDeviationTopic[] = "slam3DPoseDeviation";

// 算法检测请求
constexpr char kRequestUnloadDetectTopic[] = "requestUnloadDetect";
constexpr char kRequestAprilTagTopic[] = "requestAprilTag";
constexpr char kRequestPalletCenterTopic[] = "requestPalletCenter";
constexpr char kRequestMeasureSizeTopic[] = "requestMeasureSize";

// 调试相关
constexpr char kPoseDeviationTopic[] = "poseDeviation";
constexpr char kQrCalibration[] = "qrCalibration";
constexpr char kActionPalletNomoveTopic[] = "palletNomoveInfo";
constexpr char kAdjustUpTopic[] = "adjustUp";

// 可能不用
constexpr char kWit61MotionTopic[] = "wit61Motion";
constexpr char kLivoxLidarTopic[] = "/livox/lidar";
constexpr char kLivoxDataTopic[] = "livox_data";
constexpr char kReflectorDisplay[] = "reflectorDisplay";
constexpr char kAllReflectorDisplay[] = "allreflectorDisplay";
constexpr char kLostCheckReflectorDisplay[] = "lostcheckReflectorDisplay";

constexpr char kFaultReportTopic[] = "faultReport";

constexpr char kStopProtectTopic[] = "stopProtect";
constexpr char kFrontCameraDistTopic[] = "frontCameraDistance";
constexpr char kFrontLeftCameraDistTopic[] = "frontLeftCameraDistance";
constexpr char kFrontRightCameraDistTopic[] = "frontRightCameraDistance";
constexpr char kPedestrianDetectTopic[] = "pedestrianPose";
constexpr char kMultiPedestrianDetectTopic[] = "multiPedestrianPose";

// 动作相关
constexpr char kForkLiftActionTopic[] = "forkLiftAction";
constexpr char kJackUpActionTopic[] = "jackUpAction";
constexpr char kRollerActionTopic[] = "rollerAction";
constexpr char kAtomicActionTopic[] = "atomicAction";

// 抽象IO状态
constexpr char kManualIoStateTopic[] = "manualIoState";
constexpr char kChargeIoStateTopic[] = "chargeIoState";
constexpr char kPalletIoStateTopic[] = "palletIoState";
constexpr char kLimitSwitchStateTopic[] = "limitSwitchState";
constexpr char kTiltLimitSwitchStateTopic[] = "tiltLimitSwitchState";

constexpr char kJackUpIoTopic[] = "jackUpIoState";
constexpr char kRollerIoTopic[] = "rollerIoState";
constexpr char kPalletForkIoTopic[] = "palletForkIoState";

// 抽象数据
constexpr char kForkPalletStablizerTopic[] = "forkPalletStablizer";
constexpr char kRightForkPalletStablizerTopic[] = "rightForkPalletStablizer";
constexpr char kAprilTagPalletStablizerTopic[] = "aprilTagPalletStablizer";
constexpr char kPalletDetectPointsTopic[] = "palletDetectPoints";
constexpr char kColumnDetectTopic[] = "columnDetectFeedback";
constexpr char kColumnDetectPointsTopic[] = "columnDetectPoints";
constexpr char kPowerSupplyTopic[] = "powerSupply";
constexpr char kLoadStateTopic[] = "loadState";

constexpr char kPalletDetectStateTopic[] = "palletDetectState";
constexpr char kHeightEncoderTopic[] = "heightEncoder";
constexpr char kLateralEncoderTopic[] = "lateralEncoder";
constexpr char kSideEncoderTopic[] = "sideEncoder";
constexpr char kIoTopic[] = "io";

// 任务相关
constexpr char kTaskResponseTopic[] = "taskResponse";
constexpr char kTaskFinishRequestTopic[] = "taskFinishRequest";
constexpr char kTaskFinishResponseTopic[] = "taskFinishResponse";
constexpr char kTaskControlRequestTopic[] = "taskControlRequest";
constexpr char kTaskCalibrationRequestTopic[] = "taskCalibrationRequest";
constexpr char kTaskGetMapRequestTopic[] = "taskGetMapRequest";
constexpr char kTaskTopic[] = "order";
constexpr char kTaskInstanActionTopic[] = "instantAction";
constexpr char kTaskUpateEventTopic[] = "updateEvent";
constexpr char kTaskInfoTopic[] = "taskInfo";
constexpr char kTaskCountTopic[] = "taskCount";

// 调度控制相关
constexpr char kTaskControlAudioRequestTopic[] = "taskControlAudioRequest";
constexpr char kTaskMotorParaRequestTopic[] = "taskMotorParaRequest";

constexpr char kBatteryInfoRequestTopic[] = "batterInfoRequest";
constexpr char kAgvInfoRequestTopic[] = "agvInfoRequest";
constexpr char kBatteryInfoResponseTopic[] = "batteryInfoResponse";
constexpr char kAgvInfoResponseTopic[] = "agvInfoResponse";

// 定位相关
constexpr char kFinishRelocationRequestTopic[] = "finishRelocationRequest";

constexpr char kRequestColumnPositionDetectTopic[] =
    "request_column_position_detect";

// 调试相关
constexpr char kTeleopModeTopic[] = "teleopMode";
constexpr char kDynamicAddReflectorTopic[] = "dynamicAddReflector";
constexpr char kMatchMarkNumTopic[] = "matchMarkNum";
constexpr char kLaserScanTopic[] = "naviLaser1";
constexpr char kSyncInitPose[] = "updateInitPose";

}  // namespace cotek_topic

// 设备相关(TODO)
namespace device_name_list {}  // namespace device_name_list

namespace install_device_name_list {
constexpr char* kRfidDownInstallDriverName = const_cast<char*>("rfidDown");

constexpr char* kAudioInstallDriverName = const_cast<char*>("audio");
constexpr char* kIOInstallDriverName = const_cast<char*>("io");
constexpr char* kIOObstacleInstallDriverName = const_cast<char*>("ioObstacle");
constexpr char* kIoRemoteControlInstallDriverName =
    const_cast<char*>("ioRemoteControl");
constexpr char* kIoSigleOutInstallDriverName = const_cast<char*>("ioSingleOut");
constexpr char* kIo2InstallDriverName = const_cast<char*>("io2");
constexpr char* kGpioInstallDriverName = const_cast<char*>("gpio");
constexpr char* kLeftSingleLineLaserInstallDriverName =
    const_cast<char*>("leftSingleLineLaser");
constexpr char* kRightSingleLineLaserInstallDriverName =
    const_cast<char*>("rightSingleLineLaser");
constexpr char* kPgvR2100InstallDriverName = const_cast<char*>("pgvR2100");
constexpr char* kGyroRionInstallDriverName = const_cast<char*>("gyroRion");
constexpr char* kGyroMemsplusInstallDriverName =
    const_cast<char*>("gyroMemsplus");
constexpr char* kSickMLSInstallDriverName = const_cast<char*>("sickMlsSensor");
constexpr char* kRfidInstallDriverName = const_cast<char*>("rfid");
constexpr char* kChargingInstallDriverName = const_cast<char*>("charging");
constexpr char* kPGV100InstallDriverName = const_cast<char*>("pgv100");
constexpr char* kRS485InstallDriverName = const_cast<char*>("rs485");
constexpr char* kRS485OLM100InstallDriverName =
    const_cast<char*>("rs485Olm100");
constexpr char* kRS485PGV100InstallDriverName =
    const_cast<char*>("rs485Pgv100");

constexpr char* kSyntronLeftMotorInstallDriverName =
    const_cast<char*>("syntronLeftMoveMotor");
constexpr char* kSyntronRightMotorInstallDriverName =
    const_cast<char*>("syntronRightMoveMotor");

constexpr char* kPGV100UpInstallDriverName = const_cast<char*>("pgv100Up");
constexpr char* kPGV100DownInstallDriverName = const_cast<char*>("pgv100Down");
constexpr char* kHikvsQrDownDriverName =
    const_cast<char*>("hikvsQrFeedbackDown");
constexpr char* kHikvsQrUpDriverName = const_cast<char*>("hikvsQrFeedbackUp");
constexpr char* kDahuaQrDownDriverName =
    const_cast<char*>("dahua_qr_feedback_down");
constexpr char* kDahuaQrUpDriverName =
    const_cast<char*>("dahua_qr_feedback_up");

}  // namespace install_device_name_list

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_TOPIC_NAME_H_
