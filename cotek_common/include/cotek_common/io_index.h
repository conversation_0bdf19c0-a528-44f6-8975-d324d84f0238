/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_IO_INDEX_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_IO_INDEX_H_
#include <iostream>
#include <map>
#include <string>
namespace io {

namespace Di {
constexpr char kBumper1[] = "bumper1";  // 防撞条
constexpr char kBumper2[] = "bumper2";

constexpr char kBumperReset1[] = "bumperReset1";
constexpr char kBumperReset2[] = "bumperReset2";

constexpr char kMotorReset1[] = "motorReset1";
constexpr char kMotorReset2[] = "motorReset2";

constexpr char kEmergencyStop1[] = "emergencyStop1";  // 急停
constexpr char kEmergencyStop2[] = "emergencyStop2";
constexpr char kEmergencyStop3[] = "emergencyStop3";

constexpr char kPositionReach1[] = "positionReach1";  // 到位检测
constexpr char kPositionReach2[] = "positionReach2";
constexpr char kPositionReach3[] = "positionReach3";

constexpr char kUpperLimitSwitch1[] = "upLimitSwitch1";  // 上限位
constexpr char kUpperLimitSwitch2[] = "upLimitSwitch2";

constexpr char kDownLimitSwitch1[] = "downLimitSwitch1";  // 下限位
constexpr char kDownLimitSwitch2[] = "downLimitSwitch2";

// 前移上限位
constexpr char kLatealUpLimitSwitch1[] = "lateralUpLimitSwitch1";
constexpr char kLatealUpLimitSwitch2[] = "lateralUpLimitSwitch2";

// 前移下限位
constexpr char kLatealDownLimitSwitch1[] = "lateralDownLimitSwitch1";
constexpr char kLatealDownLimitSwitch2[] = "lateralDownLimitSwitch2";

// 侧移上限位
constexpr char ksideUpLimitSwitch1[] = "sideUpLimitSwitch1";
constexpr char ksideUpLimitSwitch2[] = "sideUpLimitSwitch2";

// 侧移下限位
constexpr char ksideDownLimitSwitch1[] = "sideDownLimitSwitch1";
constexpr char ksideDownLimitSwitch2[] = "sideDownLimitSwitch2";

// 旋转限位
constexpr char krotateLimitSwitch1[] = "rotateLimitSwitch1";
constexpr char krotateLimitSwitch2[] = "rotateLimitSwitch2";

// 归零限位
constexpr char kzeroLimitSwitch1[] = "zeroLimitSwitch1";
constexpr char kzeroLimitSwitch2[] = "zeroLimitSwitch2";

constexpr char kManualSwtich1[] = "manualSwitch1";
constexpr char kManualSwtich2[] = "manualSwitch2";

// 左侧避障
constexpr char kLeftObstacle1[] = "leftObstacle1";
constexpr char kLeftObstacle2[] = "leftObstacle2";
// 右侧避障
constexpr char kRightObstacle1[] = "rightObstacle1";
constexpr char kRightObstacle2[] = "rightObstacle2";

// 避障激光1避障
constexpr char kLaser1LevelI[] = "laser1Level1";
constexpr char kLaser1LevelII[] = "laser1Level2";
constexpr char kLaser1LevelIII[] = "laser1Level3";

constexpr char kLaser2LevelI[] = "laser2Level1";
constexpr char kLaser2LevelII[] = "laser2Level2";
constexpr char kLaser2LevelIII[] = "laser2Level3";

constexpr char kLaser3LevelI[] = "laser2Level1";
constexpr char kLaser3LevelII[] = "laser2Level2";
constexpr char kLaser3LevelIII[] = "laser2Level3";

constexpr char kDiDiy1[] = "diDiy1";
constexpr char kDiDiy2[] = "diDiy2";
constexpr char kDiDiy3[] = "diDiy3";
constexpr char kDiDiy4[] = "diDiy4";
constexpr char kDiDiy5[] = "diDiy5";
constexpr char kDiDiy6[] = "diDiy6";
constexpr char kDiDiy7[] = "diDiy7";
constexpr char kDiDiy8[] = "diDiy8";

}  // namespace Di

namespace Do {
constexpr char kLaser1ChannelI[] = "laser1Channel1";
constexpr char kLaser1ChannelII[] = "laser1Channel2";
constexpr char kLaser1ChannelIII[] = "laser1Channel3";
constexpr char kLaser1ChannelIV[] = "laser1Channel4";
constexpr char kLaser1ChannelV[] = "laser1Channel5";

constexpr char kLaser2ChannelI[] = "laser2Channel1";
constexpr char kLaser2ChannelII[] = "laser2Channel2";
constexpr char kLaser2ChannelIII[] = "laser2Channel3";
constexpr char kLaser2ChannelIV[] = "laser2Channel4";
constexpr char kLaser2ChannelV[] = "laser2Channel5";

constexpr char kLaser3ChannelI[] = "laser3Channel1";
constexpr char kLaser3ChannelII[] = "laser3Channel2";
constexpr char kLaser3ChannelIII[] = "laser3Channel3";
constexpr char kLaser3ChannelIV[] = "laser3Channel4";
constexpr char kLaser3ChannelV[] = "laser3Channel5";

constexpr char kLedGreen[] = "ledGreen";
constexpr char kLedRed[] = "ledRed";
constexpr char kLedBlue[] = "ledBlue";
constexpr char kLedYellow[] = "ledYellow";
constexpr char kLeftLedConstant[] = "leftLedConstant";
constexpr char kRightLedonstant[] = "rightLedConstant";
constexpr char kLeftLedBlink[] = "ledLeft";
constexpr char kRightLedBlink[] = "ledRight";

constexpr char kchargeSwitch1[] = "chargeSwitch";
constexpr char kchargeSwitch2[] = "chargeSwitch2";

constexpr char kDoDiy1[] = "doDiy1";
constexpr char kDoDiy2[] = "doDiy2";
constexpr char kDoDiy3[] = "doDiy3";
constexpr char kDoDiy4[] = "doDiy4";
constexpr char kDoDiy5[] = "doDiy5";
constexpr char kDoDiy6[] = "doDiy6";
constexpr char kDoDiy7[] = "doDiy7";
constexpr char kDoDiy8[] = "doDiy8";

}  // namespace Do

namespace Ai {

constexpr char kSpeedInput[] = "speedInput";
constexpr char kSteerInput[] = "steerInput";

constexpr char kAiDiy1[] = "aiDiy1";
constexpr char kAiDiy2[] = "aiDiy2";
constexpr char kAiDiy3[] = "aiDiy3";
constexpr char kAiDiy4[] = "aiDiy4";
constexpr char kAiDiy5[] = "aiDiy5";
constexpr char kAiDiy6[] = "aiDiy6";
constexpr char kAiDiy7[] = "aiDiy7";
constexpr char kAiDiy8[] = "aiDiy8";

namespace Ao {

constexpr char kAoDiy1[] = "aoDiy1";
constexpr char kAoDiy2[] = "aoDiy2";
constexpr char kAoDiy3[] = "aoDiy3";
constexpr char kAoDiy4[] = "aoDiy4";
constexpr char kAoDiy5[] = "aoDiy5";
constexpr char kAoDiy6[] = "aoDiy6";
constexpr char kAoDiy7[] = "aoDiy7";
constexpr char kAoDiy8[] = "aoDiy8";
}  // namespace Ao

}  // namespace Ai

// io翻译字符 中文/英文/日语等等
static const std::map<std::string, std::string> io_map = {
    // Di
    {Di::kBumper1, "防撞条"},
    {Di::kBumper2, "防撞条2"},
    {Di::kBumperReset1, "急停复位"},
    {Di::kBumperReset2, "急停复位2"},
    {Di::kMotorReset1, "电机复位"},
    {Di::kMotorReset2, "电机复位2"},
    {Di::kDownLimitSwitch1, "下限位"},
    {Di::kDownLimitSwitch2, "下限位2"},
    {Di::kUpperLimitSwitch1, "上限位"},
    {Di::kUpperLimitSwitch2, "上限位2"},
    {Di::kPositionReach1, "到位开关"},
    {Di::kPositionReach2, "到位开关2"},
    {Di::kPositionReach3, "到位开关3"},
    {Di::kLeftObstacle1, "左叉腿光电"},
    {Di::kLeftObstacle2, "左叉腿光电2"},
    {Di::kRightObstacle1, "右叉腿光电"},
    {Di::kRightObstacle2, "右叉腿光电2"},
    {Di::kManualSwtich1, "手动开关"},
    {Di::kManualSwtich2, "手动开关2"},

    // Do
    {Do::kchargeSwitch1, "充电开关"},
    {Do::kLedBlue, "蓝色指示灯"},
    {Do::kLedGreen, "绿色指示灯"},
    {Do::kLedRed, "红色指示灯"},
    {Do::kLedYellow, "黄色指示灯"},
    {Do::kLeftLedBlink, "左转指示灯"},
    {Do::kRightLedBlink, "右转指示灯"}

};

static const std::string GetIoTranlator(const std::string& type) {
  std::string ret;
  try {
    ret = io_map.at(type);
  } catch (const std::exception& ex) {
  }
  return ret;
}

}  // namespace io
// IO设备集编号: 所有的IO设备及其编号

namespace io_old {

// 可添加设备与设备编号, 不可修改!!!
// 共有设备
constexpr uint8_t kNoneIoDevice = 0;
// 防撞条
constexpr uint8_t kBumper = 1;
constexpr uint8_t kEmergencyStop = 2;

// 输入 1~100
// 托盘叉车上下限位 (诺力)
constexpr uint8_t kForkUpperLimitSwitch = 11;
constexpr uint8_t kForkDownerLimitSwitch = 12;
// 托盘叉车左右叉腿防撞 (诺力)
constexpr uint8_t kLeftForkLegSwitch = 13;
constexpr uint8_t kRightForkLegSwitch = 14;
// 托盘叉车货架位置左右检测 (诺力)
constexpr uint8_t kLeftPositionSwitch = 15;
constexpr uint8_t kRightPositionSwitch = 16;
// 叉车避障等级输入(诺力 / 中力)
constexpr uint8_t kLaserObstacleLevelI = 17;
constexpr uint8_t kLaserObstacleLevelII = 18;
constexpr uint8_t kLaserObstacleLevelIII = 19;

// 叉车挡板检测开关(中力)
constexpr uint8_t kPalletPositionSwitch = 20;

// 门架高度开关 2个 (三向车)
constexpr uint8_t kHeightSwitch_1 = 21;
constexpr uint8_t kHeightSwitch_2 = 22;

// 侧移到位开关 2个 (三向车)
constexpr uint8_t kLateralLeftSwitch = 23;
constexpr uint8_t kLateralRightSwitch = 24;

// 旋转到位开关 3个 左到位,右到位,旋转减速 (三向车)
constexpr uint8_t kRotateLeftSwitch = 25;
constexpr uint8_t kRotateRightSwitch = 26;
constexpr uint8_t kRotateDecelSwtich = 27;

constexpr uint8_t kManualSwtich = 28;

// 防撞条复位开关
constexpr uint8_t kBumpReset = 29;

// 叉车io超声波避障 前 左 右 三个方向
constexpr uint8_t kForwardUltrasonic = 30;
constexpr uint8_t kLeftUltrasonic = 31;
constexpr uint8_t kRightUltrasonic = 32;

// 叉车避障激光2等级输入(诺力 / 中力)
constexpr uint8_t kLaser2ObstacleLevelI = 33;
constexpr uint8_t kLaser2ObstacleLevelII = 34;
constexpr uint8_t kLaser2ObstacleLevelIII = 35;

// 叉车输入 主电源(断电状态下，工控机继续保持带点状态)
constexpr uint8_t kPowerSupply = 60;

// 叉车输出 71~90
// 柯蒂斯复位继电器 (诺力托盘车)
constexpr uint8_t kCurtisReset = 71;
// 避障传感器选择地图4输出通道 (诺力托盘车 / 堆高车 / 三向车)
constexpr uint8_t kLaserObstacleChannelI = 72;
constexpr uint8_t kLaserObstacleChannelII = 73;
constexpr uint8_t kLaserObstacleChannelIII = 74;
constexpr uint8_t kLaserObstacleChannelIV = 75;
constexpr uint8_t kLaserObstacleChannelV = 76;
// 叉车转向灯
constexpr uint8_t kCorningLedConstantOn = 77;
constexpr uint8_t kCorningLedLeftBlink = 78;
constexpr uint8_t kCorningLedRightBlink = 79;
// v1.1.5新增叉车状态灯
constexpr uint8_t kALLGreenConstantOn = 61;
constexpr uint8_t kALLRedConstantOn = 62;
constexpr uint8_t kLeftYellowConstantOn = 63;
constexpr uint8_t kRightYellowConstantOn = 64;
constexpr uint8_t kALLBlueConstantOn = 65;
constexpr uint8_t kALLYellowConstantOn = 66;

// 叉车自动充电继电器
constexpr uint8_t kRecvChargeArrivedSignal = 78;
constexpr uint8_t kSendChargeArrivedSignal = 79;
constexpr uint8_t kChargeRelay = 80;

// 避障传感器2选择地图4输出通道 (诺力托盘车 / 堆高车 / 三向车)
constexpr uint8_t kLaser2ObstacleChannelI = 81;
constexpr uint8_t kLaser2ObstacleChannelII = 82;
constexpr uint8_t kLaser2ObstacleChannelIII = 83;
constexpr uint8_t kLaser2ObstacleChannelIV = 84;
constexpr uint8_t kLaser2ObstacleChannelV = 85;

// 顶升
// 顶升输入 91~150
// 顶升车上下限位
constexpr uint8_t kJackUpUpperLimitSwitchI = 91;
constexpr uint8_t kJackUpUpperLimitSwitchII = 92;
constexpr uint8_t kJackUpDownerLimitSwitchI = 93;
constexpr uint8_t kJackUpDownerLimitSwitchII = 94;
//滚筒车左右限位
constexpr uint8_t kRollerOutLimitSwitchI = 91;
constexpr uint8_t kRollerOutLimitSwitchII = 92;
constexpr uint8_t kRollerInLimitSwitchI = 93;
constexpr uint8_t kRollerInLimitSwitchII = 94;

constexpr uint8_t kRollerInLimitSwitch = 91;
constexpr uint8_t kRollerMiddleLimitSwitch = 92;
constexpr uint8_t kRollerOutLimitSwitch = 93;

// 顶升托盘 归零光电开关
constexpr uint8_t kJackUpPalletZeroSwitchI = 95;
constexpr uint8_t kJackUpPalletZeroSwitchII = 96;

// 顶升车设备对接信号
constexpr uint8_t kJRollerJointI = 95;
constexpr uint8_t kJRollerJointII = 96;

// 无线手柄DI信号量
constexpr uint8_t kRemoteControlModeSwitchOpen = 100;
constexpr uint8_t kRemoteControlModeSwitchClose = 150;
constexpr uint8_t kRemoteControlMoveForward = 101;
constexpr uint8_t kRemoteControlMoveBackward = 102;
constexpr uint8_t kRemoteControlMoveLeft = 103;
constexpr uint8_t kRemoteControlMoveRight = 104;
constexpr uint8_t kRemoteControlUp = 105;
constexpr uint8_t kRemoteControlDown = 106;

// 放射源车遥控信号：二进制捕获
// 1-上升 2-下降 3-前进 4-后退
// 7-进入遥控模式、8-退出遥控模式
constexpr uint8_t kRemoteControl0 = 100;
constexpr uint8_t kRemoteControl1 = 101;
constexpr uint8_t kRemoteControl2 = 102;
constexpr uint8_t kRemoteControl3 = 103;

// 前进激光避障输入 (磁条顶升/三合一顶升)
constexpr uint8_t kLaserObstacleForwardLevelI = 107;
constexpr uint8_t kLaserObstacleForwardLevelII = 108;
constexpr uint8_t kLaserObstacleForwardLevelIII = 109;
// 后退激光避障输入 (磁条顶升／三合一顶升)
constexpr uint8_t kLaserObstacleBackwardLevelI = 110;
constexpr uint8_t kLaserObstacleBackwardLevelII = 111;
constexpr uint8_t kLaserObstacleBackwardLevelIII = 112;
// 前后防撞 (磁条顶升)
constexpr uint8_t kForwardBumper = 113;
constexpr uint8_t kBackwardBumper = 114;

// 顶升输出 151~180
constexpr uint8_t kJackUpLedRed = 151;
constexpr uint8_t kJackUpLedYellow = 152;
constexpr uint8_t kJackUpLedGreen = 153;
// 顶升自动充电继电器
constexpr uint8_t kJackUpCharge = 154;
constexpr uint8_t kJackUpLiftMotorBreak = 155;
constexpr uint8_t kJackUpRotateMotorBreak = 156;
constexpr uint8_t kJackUp24vLowpower = 157;
constexpr uint8_t kJackUp48vLowpower = 158;
constexpr uint8_t kJackUpMoveMotorBreak = 159;

// 顶升前进激光避障等级输入
constexpr uint8_t kFwdObstacleLI = 72;
constexpr uint8_t kFwdObstacleLII = 73;
constexpr uint8_t kFwdObstacleLIII = 74;

// 顶升前进激光避障设置
constexpr uint8_t kSetFwdObstacleChannel1 = 121;
constexpr uint8_t kSetFwdObstacleChannel2 = 122;
constexpr uint8_t kSetFwdObstacleChannel3 = 123;
constexpr uint8_t kSetFwdObstacleChannel4 = 124;
constexpr uint8_t kSetFwdObstacleChannel5 = 125;

// 前进激光避障输出 (磁条顶升/三合一顶升)
constexpr uint8_t kLaserObstacleForwardChannelI = 159;
constexpr uint8_t kLaserObstacleForwardChannelII = 160;
constexpr uint8_t kLaserObstacleForwardChannelIII = 161;
constexpr uint8_t kLaserObstacleForwardChannelIV = 162;
constexpr uint8_t kLaserObstacleForwardChannelV = 163;
// 后退激光避障输出 (磁条顶升/三合一顶升)
constexpr uint8_t kLaserObstacleBackwardChannelI = 164;
constexpr uint8_t kLaserObstacleBackwardChannelII = 165;
constexpr uint8_t kLaserObstacleBackwardChannelIII = 166;
constexpr uint8_t kLaserObstacleBackwardChannelIV = 167;
constexpr uint8_t kLaserObstacleBackwardChannelV = 168;
// Io控制抬降(中力液压顶升)/放射源车
constexpr uint8_t kJackUpIoLift = 169;
constexpr uint8_t kJackUpIoDown = 170;

// Io控制滚筒,正反转
constexpr uint8_t kRoller1IoIn = 169;  // 1代表内侧电机
constexpr uint8_t kRoller1IoOut = 170;
constexpr uint8_t kRoller2IoIn = 190;  // 2代表外侧电机
constexpr uint8_t kRoller2IoOut = 191;

constexpr uint8_t kRollerIoFMQ = 171;

//　备用按钮
constexpr uint8_t kBackupKey1 = 171;  //故障复位--手自动复位信号
constexpr uint8_t kBackupKey2 = 172;  //电梯门触发信号
// 电梯门控制信号
constexpr uint8_t kOpenDoor = 173;
//夹抱装置输入
constexpr uint8_t kClampLeft = 181;
constexpr uint8_t kClampRight = 182;
constexpr uint8_t kClampDetect = 183;
constexpr uint8_t kClampLeftReach1 = 184;
constexpr uint8_t kClampLeftReach2 = 185;
constexpr uint8_t kClampLeftReach3 = 186;
constexpr uint8_t kClampRightReach1 = 187;
constexpr uint8_t kClampRightReach2 = 188;
constexpr uint8_t kClampRightReach3 = 189;

}  // namespace io_old

#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_IO_INDEX_H_
