/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_TF_NAME_H_
#define COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_TF_NAME_H_

// 以后将 tf 常量名移步于此
namespace cotek_tf {
constexpr char kMapFrame[] = "/map";
constexpr char kReflectorBaseFrame[] = "/ref_base_link";
constexpr char kBaseFrame[] = "/base_link";
constexpr char kShelfFrame[] = "/shelf";
constexpr char kNaviLaserFrame[] = "/naviLaser1";
constexpr char kCartoBaseFrame[] = "/carto_base_link";

}  // namespace cotek_tf
#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_COTEK_TF_NAME_H_
