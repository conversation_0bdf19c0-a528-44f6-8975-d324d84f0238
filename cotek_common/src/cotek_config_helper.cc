/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include <cotek_common/cotek_config_helper.h>
#include <yaml-cpp/yaml.h>

#include <cstdint>
#include <exception>
#include <fstream>
#include <string>

#include "angles/angles.h"
#include "cotek_common/math.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/util/file_operation.h"

using Json = nlohmann::ordered_json;

bool BasicConfigHelper::ReLoadConfig() {
  has_loaded_ = false;
  return LoadConfig();
}

bool BasicConfigHelper::LoadConfig() {
  if (has_loaded_) return true;
  try {
    std::string file_path =
        config_map_.at(cotek_config::ConfigType::AGV_BASIC_CONFIG);
    Json json = Json::parse(std::ifstream(file_path));
    communicate_option_.method =
        static_cast<CommunicateOption::CommunicateMethod>(
            json["communicate_method"].get<int>());
    communicate_option_.system_version =
        json["system_version"].get<std::string>();
    communicate_option_.server_ip = json["server_ip"].get<std::string>();
    communicate_option_.server_port = json["server_port"].get<int>();
    communicate_option_.server_http_port = json["server_http_port"].get<int>();

    communicate_option_.local_port = json["local_port"].get<int>();
    communicate_option_.local_http_port = json["local_http_port"].get<int>();

    if (json.contains("lora_id")) {
      communicate_option_.lora_option.lora_id = json["lora_id"].get<int>();
    } else {
      communicate_option_.lora_option.lora_id = 0;
    }

    if (json.contains("lora_mode")) {
      communicate_option_.lora_option.lora_mode = json["lora_mode"].get<int>();
    } else {
      communicate_option_.lora_option.lora_mode = 0;
    }

    if (json.contains("lora_channel")) {
      communicate_option_.lora_option.lora_chanel =
          json["lora_channel"].get<std::string>();
    } else {
      communicate_option_.lora_option.lora_chanel = "/dev/ttyUSB0";
    }

    if (json.contains("language")) {
      communicate_option_.language = json["language"].get<std::string>();
    } else {
      communicate_option_.language = "cn";
    }

    map_info_option_.current_zone_id =
        json.at("current_zone_id").get<std::string>();
    map_info_option_.current_map_id =
        json.at("current_map_id").get<std::string>();

    LOG_INFO_STREAM("----------- agv basic config ----------------");
    LOG_INFO_STREAM(
        "Communicate method: " << static_cast<int>(communicate_option_.method));
    LOG_INFO_STREAM("Server ip: " << communicate_option_.server_ip);
    LOG_INFO_STREAM("Server port: " << communicate_option_.server_port);
    LOG_INFO_STREAM(
        "Server http port: " << communicate_option_.server_http_port);
    LOG_INFO_STREAM("Local port: " << communicate_option_.local_port);
    LOG_INFO_STREAM("Local http port: " << communicate_option_.local_http_port);

    LOG_INFO_STREAM("Current_zone_id " << map_info_option_.current_zone_id);
    LOG_INFO_STREAM("Current_map_id " << map_info_option_.current_map_id);

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }

  try {
    std::string file_path =
        config_map_.at(cotek_config::ConfigType::RUNNER_CONFIG);
    Json json = Json::parse(std::ifstream(file_path));
    agv_type_ = json["agv_type"].get<int>();

    Json chassis_list = json["chassis"];
    for (auto &chassis : chassis_list) {
      chassis_model_option_.name = chassis["name"].get<std::string>();
      chassis_model_option_.type = chassis["type"].get<std::string>();
      if (chassis_model_option_.type == kBicycleModel) {
        BicycleModelOption option{};
        option.wheel_base = chassis["chassis_model"]["wheel_base"].get<float>();
        LOG_INFO_STREAM("wheel_base: " << option.wheel_base);

        option.min_wheel_base =
            chassis["chassis_model"]["min_wheel_base"].get<float>();
        LOG_INFO_STREAM("min_wheel_base: " << option.min_wheel_base);

        option.max_wheel_base =
            chassis["chassis_model"]["max_wheel_base"].get<float>();
        LOG_INFO_STREAM("max_wheel_base: " << option.max_wheel_base);

        option.eccentric_distance =
            chassis["chassis_model"]["eccentric_distance"].get<float>();
        LOG_INFO_STREAM("eccentric_distance: " << option.eccentric_distance);

        option.eccentric_angle =
            chassis["chassis_model"]["eccentric_angle"].get<float>();
        LOG_INFO_STREAM("eccentric_angle: " << option.eccentric_angle);

        option.reduction_ratio =
            chassis["chassis_model"]["reduction_ratio"].get<float>();
        LOG_INFO_STREAM("reduction_ratio: " << option.reduction_ratio);

        option.set_driver_direction =
            chassis["chassis_model"]["set_driver_direction"].get<int>();
        LOG_INFO_STREAM(
            "set_driver_direction: " << option.set_driver_direction);

        option.feedback_driver_direction =
            chassis["chassis_model"]["feedback_driver_direction"].get<int>();
        LOG_INFO_STREAM(
            "feedback_driver_direction: " << option.feedback_driver_direction);

        option.set_steer_direction =
            chassis["chassis_model"]["set_steer_direction"].get<int>();
        LOG_INFO_STREAM("set_steer_direction: " << option.set_steer_direction);

        option.feedback_steer_direction =
            chassis["chassis_model"]["feedback_steer_direction"].get<int>();
        LOG_INFO_STREAM(
            "feedback_steer_direction: " << option.feedback_steer_direction);

        chassis_model_option_.bicycle_model = option;

      } else if (chassis_model_option_.type == kUnicycleModel) {
        UnicycleModelOption option{};
        option.wheel_track =
            chassis["chassis_model"]["wheel_track"].get<float>();
        LOG_INFO_STREAM("wheel_track: " << option.wheel_track);

        option.wheel_diameter =
            chassis["chassis_model"]["wheel_diameter"].get<float>();
        LOG_INFO_STREAM("wheel_diameter: " << option.wheel_diameter);

        option.left_motor_reduce_ratio =
            chassis["chassis_model"]["left_motor_reduce_ratio"].get<float>();
        LOG_INFO_STREAM(
            "left_motor_reduce_ratio: " << option.left_motor_reduce_ratio);

        option.right_motor_reduce_ratio =
            chassis["chassis_model"]["right_motor_reduce_ratio"].get<float>();
        LOG_INFO_STREAM(
            "right_motor_reduce_ratio: " << option.right_motor_reduce_ratio);

        option.lift_motor_reduce_ratio =
            chassis["chassis_model"]["lift_motor_reduce_ratio"].get<float>();
        LOG_INFO_STREAM(
            "lift_motor_reduce_ratio: " << option.lift_motor_reduce_ratio);

        option.rotate_motor_reduce_ratio =
            chassis["chassis_model"]["rotate_motor_reduce_ratio"].get<float>();
        LOG_INFO_STREAM(
            "rotate_motor_reduce_ratio: " << option.rotate_motor_reduce_ratio);

        option.set_left_direction =
            chassis["chassis_model"]["set_left_direction"].get<int>();
        LOG_INFO_STREAM("set_left_direction: " << option.set_left_direction);

        option.feedback_left_direction =
            chassis["chassis_model"]["feedback_left_direction"].get<int>();
        LOG_INFO_STREAM(
            "feedback_left_direction: " << option.feedback_left_direction);

        option.set_right_direction =
            chassis["chassis_model"]["set_right_direction"].get<int>();
        LOG_INFO_STREAM("set_right_direction: " << option.set_right_direction);

        option.feedback_right_direction =
            chassis["chassis_model"]["feedback_right_direction"].get<int>();
        LOG_INFO_STREAM(
            "feedback_right_direction: " << option.feedback_right_direction);

        option.set_lift_direction =
            chassis["chassis_model"]["set_lift_direction"].get<int>();
        LOG_INFO_STREAM("set_lift_direction: " << option.set_lift_direction);

        option.set_rotate_direction =
            chassis["chassis_model"]["set_rotate_direction"].get<int>();
        LOG_INFO_STREAM(
            "set_rotate_direction: " << option.set_rotate_direction);

        chassis_model_option_.unicycle_model = option;
      }
    }

    Json ultrasonic_list = json["ultrasonic"];
    for (auto &ultrasonic : ultrasonic_list) {
      Transform transform{};
      std::string frame = ultrasonic["name"].get<std::string>();
      transform.x = ultrasonic["tf"]["x"].get<double>();
      transform.y = ultrasonic["tf"]["y"].get<double>();
      transform.z = ultrasonic["tf"]["z"].get<double>();

      transform.roll = angles::normalize_angle(
          math::Deg2Rad(ultrasonic["tf"]["roll"].get<double>()));
      transform.pitch = angles::normalize_angle(
          math::Deg2Rad(ultrasonic["tf"]["pitch"].get<double>()));
      transform.yaw = angles::normalize_angle(
          math::Deg2Rad(ultrasonic["tf"]["yaw"].get<double>()));
      transform_map_[frame] = transform;
    }

    Json laser_list = json["laser"];
    for (auto &laser : laser_list) {
      std::string frame = laser["name"].get<std::string>();

      LaserScanOption option{};
      option.frame = frame;
      option.type = laser["type"].get<std::string>();

      if (laser.contains("topic")) {
        option.topic = laser["topic"].get<std::string>();
      } else {
        option.topic = laser["name"].get<std::string>();
      }
      if (laser.contains("use_3d")) {
        option.use_3d = laser["use_3d"].get<bool>();
      } else {
        option.use_3d = false;
      }

      auto &param = laser["param"];
      option.scan_sample_step = param["scan_sample_step"].get<int>();
      option.angle_min = math::Deg2Rad(param["angle_min"].get<double>());
      option.angle_max = math::Deg2Rad(param["angle_max"].get<double>());
      option.angle_increment =
          math::Deg2Rad(param["angle_increment"].get<double>());
      option.range_min = param["range_min"].get<double>();
      option.range_max = param["range_max"].get<double>();
      auto &filter = param["filter"];
      option.filter.positive = filter["positive"].get<bool>();
      option.filter.angle_min =
          math::Deg2Rad(filter["filter_angle_min"].get<double>());
      option.filter.angle_max =
          math::Deg2Rad(filter["filter_angle_max"].get<double>());
      option.filter.filter_laser_point =
          filter["filter_laser_point"].get<double>();

      Transform transform{};
      transform.x = laser["tf"]["x"].get<double>();
      transform.y = laser["tf"]["y"].get<double>();
      transform.z = laser["tf"]["z"].get<double>();

      transform.roll = angles::normalize_angle(
          math::Deg2Rad(laser["tf"]["roll"].get<double>()));
      transform.pitch = angles::normalize_angle(
          math::Deg2Rad(laser["tf"]["pitch"].get<double>()));
      transform.yaw = angles::normalize_angle(
          math::Deg2Rad(laser["tf"]["yaw"].get<double>()));
      transform_map_[frame] = transform;

      option.tf = transform;
      laser_map_[frame] = option;
    }

    Json camera_list = json["camera"];
    for (auto &camera : camera_list) {
      std::string frame = camera["name"].get<std::string>();
      CameraOption option{};
      option.frame = frame;
      option.type = camera["type"].get<std::string>();
      option.serial_number = camera["serial_num"].get<std::string>();
      option.width = camera["image_width"].get<int>();
      option.height = camera["image_height"].get<int>();
      option.mode = camera["mode"].get<int>();

      Transform transform{};
      transform.x = camera["tf"]["x"].get<double>();
      transform.y = camera["tf"]["y"].get<double>();
      transform.z = camera["tf"]["z"].get<double>();
      transform.roll = angles::normalize_angle(
          math::Deg2Rad(camera["tf"]["roll"].get<double>()));
      transform.pitch = angles::normalize_angle(
          math::Deg2Rad(camera["tf"]["pitch"].get<double>()));
      transform.yaw = angles::normalize_angle(
          math::Deg2Rad(camera["tf"]["yaw"].get<double>()));
      transform_map_[frame] = transform;

      option.tf = transform;
      camera_map_[frame] = option;
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }

  has_loaded_ = true;
  return true;
}

bool BasicConfigHelper::LoadSlamConfig(std::string &slam_option_string,
                                       SlamOption &slam_option) {
  try {
    Json json = Json::parse(slam_option_string);

    LOG_INFO_STREAM("----------- agv slam config ----------------");

    LOG_INFO_STREAM("----------- agv slam feature config ----------------");
    SlamFeatureOption slam_feature_option;
    slam_feature_option.use_landmarks =
        json["slam_feature_option"]["use_landmarks"].get<bool>();
    LOG_INFO_STREAM("use_landmarks: " << slam_feature_option.use_landmarks);
    slam_feature_option.use_imu_data =
        json["slam_feature_option"]["use_imu_data"].get<bool>();
    LOG_INFO_STREAM("use_imu_data: " << slam_feature_option.use_imu_data);
    slam_feature_option.min_range_data_size =
        json["slam_feature_option"]["min_range_data_size"].get<int>();
    LOG_INFO_STREAM(
        "min_range_data_size: " << slam_feature_option.min_range_data_size);
    slam_feature_option.front_scan_match_strong_cell =
        json["slam_feature_option"]["front_scan_match_strong_cell"].get<int>();
    LOG_INFO_STREAM("front_scan_match_strong_cell: "
                    << slam_feature_option.front_scan_match_strong_cell);
    slam_feature_option.front_ceres_strong_cell =
        json["slam_feature_option"]["front_ceres_strong_cell"].get<int>();
    LOG_INFO_STREAM("front_ceres_strong_cell: "
                    << slam_feature_option.front_ceres_strong_cell);
    slam_feature_option.back_scan_match_strong_cell =
        json["slam_feature_option"]["back_scan_match_strong_cell"].get<int>();
    LOG_INFO_STREAM("back_scan_match_strong_cell: "
                    << slam_feature_option.back_scan_match_strong_cell);
    slam_feature_option.front_linear_search_window =
        json["slam_feature_option"]["front_linear_search_window"].get<double>();
    LOG_INFO_STREAM("front_linear_search_window: "
                    << slam_feature_option.front_linear_search_window);
    slam_feature_option.front_angular_search_window =
        json["slam_feature_option"]["front_angular_search_window"]
            .get<double>();
    LOG_INFO_STREAM("front_angular_search_window: "
                    << slam_feature_option.front_angular_search_window);
    slam_feature_option.back_linear_search_window =
        json["slam_feature_option"]["back_linear_search_window"].get<double>();
    LOG_INFO_STREAM("back_linear_search_window: "
                    << slam_feature_option.back_linear_search_window);
    slam_feature_option.back_angular_search_window =
        json["slam_feature_option"]["back_angular_search_window"].get<double>();
    LOG_INFO_STREAM("back_angular_search_window: "
                    << slam_feature_option.back_angular_search_window);
    slam_feature_option.laser_type =
        json["slam_feature_option"]["laser_type"].get<std::string>();
    LOG_INFO_STREAM("laser_type: " << slam_feature_option.laser_type);
    slam_feature_option.intensity_threshould =
        json["slam_feature_option"]["intensity_threshould"].get<int>();
    LOG_INFO_STREAM(
        "intensity_threshould: " << slam_feature_option.intensity_threshould);
    if (json["slam_feature_option"].contains("range_max")) {
      slam_feature_option.range_max =
          json["slam_feature_option"]["range_max"].get<double>();
    } else {
      slam_feature_option.range_max = 30.;
    }

    LOG_INFO_STREAM("range_max: " << slam_feature_option.range_max);
    slam_option.slam_feature_option = slam_feature_option;

    LOG_INFO_STREAM(
        "----------- agv reflector feature config ----------------");
    ReflectorFeatureOption reflector_feature_option;
    reflector_feature_option.reflector_mode_only =
        json["reflector_feature_option"]["reflector_mode_only"].get<bool>();
    LOG_INFO_STREAM("reflector_mode_only: "
                    << reflector_feature_option.reflector_mode_only);
    reflector_feature_option.reflector_dynamic_max_distance =
        json["reflector_feature_option"]["reflector_dynamic_max_distance"]
            .get<double>();
    LOG_INFO_STREAM("reflector_dynamic_max_distance: "
                    << reflector_feature_option.reflector_dynamic_max_distance);
    slam_option.reflector_feature_option = reflector_feature_option;

    LOG_INFO_STREAM("----------- agv slam md5 config ----------------");
    Md5ConfigOption md5_config_option;
    md5_config_option.eth_name =
        json["md5_config_option"]["eth_name"].get<std::string>();
    LOG_INFO_STREAM("eth_name: " << md5_config_option.eth_name);
    md5_config_option.md5 =
        json["md5_config_option"]["md5_string"].get<std::string>();
    LOG_INFO_STREAM("md5_string: " << md5_config_option.md5);
    slam_option.md5_config_option = md5_config_option;

    LOG_INFO_STREAM("----------- agv slam lost check config ----------------");
    LandmarkOption lost_check_option;
    lost_check_option.enable = json["lost_check_option"]["enable"].get<bool>();
    LOG_INFO_STREAM("enable: " << lost_check_option.enable);
    if (lost_check_option.enable) {
      lost_check_option.radius =
          json["lost_check_option"]["radius"].get<double>();
      LOG_INFO_STREAM("radius: " << lost_check_option.radius);
      lost_check_option.check_distance =
          json["lost_check_option"]["check_distance"].get<double>();
      LOG_INFO_STREAM("check_distance: " << lost_check_option.check_distance);
      lost_check_option.num_add =
          json["lost_check_option"]["num_add"].get<double>();
      LOG_INFO_STREAM("num_add: " << lost_check_option.num_add);
      lost_check_option.num_dec =
          json["lost_check_option"]["num_dec"].get<double>();
      LOG_INFO_STREAM("num_dec: " << lost_check_option.num_dec);
    }
    slam_option.lost_check_option = lost_check_option;

    LOG_INFO_STREAM("----------- agv slam reflector config ----------------");
    LandmarkOption reflector_option;
    reflector_option.enable = json["reflector_option"]["enable"].get<bool>();
    LOG_INFO_STREAM("enable: " << reflector_option.enable);
    if (reflector_option.enable) {
      reflector_option.radius =
          json["reflector_option"]["radius"].get<double>();
      LOG_INFO_STREAM("radius: " << reflector_option.radius);
      reflector_option.check_distance =
          json["reflector_option"]["check_distance"].get<double>();
      LOG_INFO_STREAM("check_distance: " << reflector_option.check_distance);
      reflector_option.num_add =
          json["reflector_option"]["num_add"].get<double>();
      LOG_INFO_STREAM("num_add: " << reflector_option.num_add);
      reflector_option.num_dec =
          json["reflector_option"]["num_dec"].get<double>();
      LOG_INFO_STREAM("num_dec: " << reflector_option.num_dec);
    }
    slam_option.reflector_option = reflector_option;

    return true;

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return false;
  }
}

bool BasicConfigHelper::LoadLanguageConfig() {
  bool ret = false;
  try {
    language_.clear();
    std::string language_path = user_path_ + "/language";
    std::set<std::string> files;
    if (!util::GetDirFiles(language_path, files)) {
      LOG_ERROR_STREAM("read language dir(" << language_path << ") error");
      return false;
    }
    for (auto &file : files) {
      try {
        std::string lang = util::GetBaseName(file);
        if (lang.empty()) {
          continue;
        }
        YAML::Node cfg_node = YAML::LoadFile(file);
        // 遍历 YAML 文件中的每个 section
        for (YAML::const_iterator it = cfg_node.begin(); it != cfg_node.end();
             ++it) {
          std::string section = it->first.as<std::string>();
          YAML::Node section_node = it->second;

          // 处理 key:value 对
          if (section_node.IsMap()) {
            for (YAML::const_iterator key_it = section_node.begin();
                 key_it != section_node.end(); ++key_it) {
              std::string key = key_it->first.as<std::string>();
              std::string value = key_it->second.as<std::string>();
              language_[lang][section][key] = value;
            }
          }
        }
      } catch (const std::exception &e) {
        LOG_ERROR_STREAM("read yaml file exception: " << e.what());
        return false;
      }
    }
  } catch (const std::exception &e) {
    LOG_ERROR_STREAM("load language config exception: " << e.what());
    return false;
  }
  return true;
}

bool BasicConfigHelper::SaveLocal(cotek_config::ConfigType type,
                                  std::string data) {
  return util::LocalService::SaveStringToFile(config_map_.at(type), data);
}

std::string BasicConfigHelper::GetConfig(cotek_config::ConfigType type) {
  std::string config_json_str;
  if (static_cast<uint8_t>(type) ==
      static_cast<uint8_t>(cotek_config::ConfigType::NONE)) {
    return std::string();
  }

  config_json_str = util::LocalService::GetStringFromFile(config_map_.at(type));
  std::string file_name = config_map_.at(type);
  if (config_json_str.empty()) {
    LOG_ERROR_STREAM(file_name << " is empty.");
    return "";
  }
  return config_json_str;
}

std::map<std::string, std::map<std::string, std::string>>
BasicConfigHelper::GetTranslations(const std::string &lang) {
  std::map<std::string, std::map<std::string, std::string>> datas;
  if (language_.find(lang) != language_.end()) {
    datas = language_[lang];
  }
  return datas;
}

std::map<std::string, std::string> BasicConfigHelper::GetTranslations(
    const std::string &section, const std::string &lang) {
  std::map<std::string, std::string> sections;
  if (language_.find(lang) != language_.end()) {
    auto &datas = language_[lang];
    if (datas.find(section) != datas.end()) {
      sections = datas[section];
    }
  }
  return sections;
}

std::string BasicConfigHelper::GetTranslation(const std::string &section,
                                              const std::string &key,
                                              const std::string &lang) {
  std::string value;
  if (language_.find(lang) != language_.end()) {
    auto &datas = language_[lang];
    if (datas.find(section) != datas.end()) {
      auto &sections = datas[section];
      if (sections.find(key) != sections.end()) {
        value = sections[key];
      }
    }
  }
  return value;
}
