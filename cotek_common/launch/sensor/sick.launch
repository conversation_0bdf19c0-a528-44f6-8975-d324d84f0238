<launch>
  
  <!-- SICK-LMS151-10100 Driver -->
  <arg name="hostname" default="************" />
    <!-- robot_description and robot_state_publisher hier evtl. einbauen  -->
  <node pkg="sick_scan" name="sick_lms_1xx" type="sick_generic_caller" respawn="false" output="screen">
    <!-- default values: -->
    <!--
      <param name="intensity" type="bool" value="True" />
      <param name="skip" type="int" value="0" />
      <param name="frame_id" type="str" value="laser" />
      <param name="time_offset" type="double" value="-0.001" />
      <param name="publish_datagram" type="bool" value="False" />
      <param name="subscribe_datagram" type="bool" value="false" />
      <param name="device_number" type="int" value="0" />
      <param name="range_min" type="double" value="0.05" />
	<param name="min_ang" type="double" value="-1.658" />
	<param name="max_ang" type="double" value="1.658" />
    -->
      <param name="frame_id" type="str" value="laser" />
    	<param name="use_binary_protocol" type="bool" value="False" />
	<param name="scanner_type" type="string" value="sick_lms_1xx"/>
	<param name="range_max" type="double" value="25.0" />
        <param name="hostname" type="string" value="$(arg hostname)" />
	<param name="port" type="string" value="2112" />
	<param name="timelimit" type="int" value="5" />
    
  </node>

</launch>