<launch>

    <!-- P+F R2000 Driver -->
    <arg name="frame_id" default="laser"/>
    <arg name="scanner_ip" default="*************"/>
    <arg name="scan_frequency" default="40"/>
    <arg name="samples_per_scan" default="2100"/>

    <node pkg="pepperl_fuchs_r2000" type="r2000_node" name="r2000_node" respawn="true" output="screen">
        <param name="frame_id" value="$(arg frame_id)"/>
        <param name="scanner_ip" value="$(arg scanner_ip)"/>
        <param name="scan_frequency" value="$(arg scan_frequency)"/>
        <param name="samples_per_scan" value="$(arg samples_per_scan)"/>
    <!-- <remap from="scan" to="scan_1" /> -->
    </node>

</launch>