# Define the goal
string order_id #任务工单号
string task_id  #任务序列
string edge_id  #当前边 edge id
uint32 start_index #当前边上的起始点索引
uint32 end_index   #当前边上的结束点索引
int32  kuqu_id
uint32 dir #当前边的方向
string next_edge_id #下条边 edge id
uint32 next_start_index #下条边上的起始点索引
uint32 next_end_index   #下条边上的结束点索引

uint32 navi_type # 0:none, 1:激光,二维码, 2:磁条
uint32 motion_type # 0:none, 1:start_point, 2:直线, 3:b样条曲线, 4:圆弧, 5:叉车、堆高车插货, 11:行走充电, 12:向上整定, 13:向下整定, , 20:分区切换，路经调整
uint32 path_type  # 0:none 1: point  2: 直线 3: 曲线 11: 拟合路线

string[] action_value #路径action参数 

uint32 end_id # 磁导航 rfid 需要

#边属性
string start_node_id
string end_node_id

float32 max_height # 路径举升最大高度
float32 min_height # 路径举升最低高度

#边的起点，终止
float32 start_x
float32 start_y
float32 start_yaw

float32 end_x
float32 end_y
float32 end_yaw

float64 max_speed # 不需要
float64 target_speed  # 限速值
bool rotation_allowed # 是否允许路径旋转
float32 max_rotation_speed #最大旋转速度
float32 length  #路径长度，该字段数值非必须

# b样条控制点
float32 c1_x
float32 c1_y
float32 c2_x
float32 c2_y

#特殊属性 起始点
float32 start_pose_x
float32 start_pose_y
float32 start_pose_yaw

#特殊点 速度调整
float32 adjust_speed 
float32 adjust_speed_pose_x
float32 adjust_speed_pose_y
float32 adjust_speed_pose_yaw

#高精度点
float32 high_precision_pose_x
float32 high_precision_pose_y
float32 high_precision_pose_yaw

# 下一条边 直线 曲线 末端切换
uint32 next_path_type # 0:none, 1:点, 2:线, 3:b样条曲线, 4:圆弧   用来变曲率--> 目前不需要
uint32  next_move_type 
uint32  next_end_id # 磁导航 rfid 需要

float32 next_end_x 
float32 next_end_y
float32 next_end_yaw

float32 next_c1_x
float32 next_c1_y
float32 next_c2_x
float32 next_c2_y

#自定义条件
string mix_condition  # 自定义条件
string finish_condition # 自定义完成条件  达到角度/ 到点距离/ 停止线速度/ 停止角速度/ 
                        #               在码上/ 充电时电机电流/ 充电时电池电流 /有货架上二维码 /顶升状态
                        #               挡板状态/ 动作时长/ 避障状态/
string error_condition  # 自定义错误条件

---
# Define the result
uint32 is_finish   # 0:未完成 1：正常完成，无跳转  2:跳至任务动作点 3:跳至任务终点  用作特殊移动任务完成状态使用
---
# Define a feedback message
# error == 1 : have problem
bool error
uint32 motion_type # --> 沒有利用, 不需要
# 判断动作是否准备就绪
uint32 percent   # 完成百分比
