/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_DATA_CENTER_H_
#define SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_DATA_CENTER_H_
#include <memory.h>
#include <ros/ros.h>
#include <sensor_msgs/Imu.h>
#include <std_msgs/Int32.h>

#include <ctime>
#include <mutex>
#include <string>

#include "cotek_action/cotek_action_enum.h"
#include "cotek_common/common.h"
#include "cotek_common/util/json11.h"
#include "cotek_msgs/battery_feedback.h"
#include "cotek_msgs/column_detect_feedback.h"
#include "cotek_msgs/ds_clear_data.h"
#include "cotek_msgs/hinson_mls_feedback.h"
#include "cotek_msgs/io_feedback.h"
#include "cotek_msgs/jack_up_io_state.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/move_feedback.h"
#include "cotek_msgs/pallet_act_info.h"
#include "cotek_msgs/pallet_back_limit_feedback.h"
#include "cotek_msgs/pallet_center_feedback.h"
#include "cotek_msgs/pallet_detect_state.h"
#include "cotek_msgs/pgv100_feedback.h"
#include "cotek_msgs/request_unload_detect.h"
#include "cotek_msgs/safety_io_state.h"
#include "cotek_msgs/self_check.h"
#include "cotek_msgs/unload_detect_feedback.h"
#include "cotek_msgs/weighing_feedback.h"
#include "cotek_msgs/wire_encoder_feedback.h"
#include "cotek_msgs/elevator_feedback.h"
#include "hikvs_mv_im5005_ros/hikvs_mv_im5005.h"

namespace cotek_action {

class IoData {
 public:
  IoData() { memset(&io_feedback_, 0, sizeof(IoFeedback)); }
  ~IoData() {}

  void SetData(const cotek_msgs::io_feedback::ConstPtr& data) {
    std::unique_lock<std::mutex> lock(mutex_);
    io_feedback_.seq = data->header.seq;
    io_feedback_.time_stamp = ros::Time::now();
    // io_feedback.frame_id = data->header.frame_id;
    io_feedback_.di_state.clear();
    io_feedback_.do_state.clear();
    io_feedback_.io_key_map.clear();
    io_feedback_.io_name_map.clear();
    for (size_t i = 0; i < data->io.size(); i++) {
      Pin pin_msg;
      pin_msg.key = data->io[i].key;
      pin_msg.name = data->io[i].name;
      pin_msg.switch_type = data->io[i].switch_type;
      pin_msg.value = data->io[i].value;
      if (pin_msg.key.find("di") != std::string::npos) {
        io_feedback_.di_state.push_back(pin_msg);
        io_feedback_.io_key_map[pin_msg.key] = pin_msg.value;
        io_feedback_.io_name_map[pin_msg.name] = pin_msg.value;
      } else if (pin_msg.key.find("do") != std::string::npos) {
        io_feedback_.do_state.push_back(pin_msg);
        io_feedback_.io_key_map[pin_msg.key] = pin_msg.value;
        io_feedback_.io_name_map[pin_msg.name] = pin_msg.value;
      } else {
        LOG_ERROR("Unknown io key !!!");
      }
    }
    lock.unlock();
  }

  cotek_action::IoFeedback GetIoData() {
    std::unique_lock<std::mutex> lock(mutex_);
    return io_feedback_;
  }

 private:
  cotek_action::IoFeedback io_feedback_;

  std::mutex mutex_;
};

class AgvData {
 public:
  static AgvData* get() {
    static AgvData instance;
    return &instance;
  }

  void Reset() {
    // memset(&get()->agvdata_, 0, sizeof(SensorData));
    get()->goal_.action_type = 0;
    get()->goal_.action_value = 0;
    get()->state_.percent = 0;
  }
  void OpenLoopReset() {
    // memset(&get()->agvdata_, 0, sizeof(SensorData));
    get()->open_loop_goal_.action_type = 0;
    get()->open_loop_goal_.action_value = 0;
    get()->open_loop_state_.percent = 0;
  }

  void SetState(const uint16_t& percent, const bool& finsh_flag,
                const ActionStatus& status) {
    state_.percent = percent;
    state_.is_finish = finsh_flag;
    state_.action_status = static_cast<uint16_t>(status);
  }
  void SetOpenLoopState(const uint16_t& percent, const bool& finsh_flag,
                        const ActionStatus& status) {
    open_loop_state_.percent = percent;
    open_loop_state_.is_finish = finsh_flag;
    open_loop_state_.action_status = static_cast<uint16_t>(status);
  }

  void SetStateSucceed() {
    state_.percent = 100;
    state_.is_finish = true;
    state_.action_status = static_cast<uint16_t>(ActionStatus::FINISH);
  }
  void SetOpenLoopStateSucceed() {
    open_loop_state_.percent = 100;
    open_loop_state_.is_finish = true;
    open_loop_state_.action_status =
        static_cast<uint16_t>(ActionStatus::FINISH);
  }

  void UpdataGoal(ActionGoal* goal) { get()->goal_ = *goal; }
  void UpdataOpenLoopGoal(ActionGoal* open_loop_goal) {
    get()->open_loop_goal_ = *open_loop_goal;
  }

  void SetOption(const AgvActionOption* option) { get()->option_ = *option; }
  void SetBasicOption(const AgvBasicOption* option) {
    get()->basic_option_ = *option;
  }

  // 车体控制与反馈
  void AddShelfCheckState(const cotek_msgs::self_check::ConstPtr& msg) {
    get()->agvdata_.shelf_check_result = static_cast<bool>(msg->result);
  }
  void AddMoveCmd(const cotek_msgs::move_cmd::ConstPtr& msg) {
    get()->state_.move_omega = msg->omega;
  }
  void AddMoveFeedback(const cotek_msgs::move_feedback::ConstPtr& msg) {
    get()->agvdata_.move_feedback.velocity = msg->velocity;
    get()->agvdata_.move_feedback.omega = msg->omega;
  }

  // 抽象数据
  void AddAgvManualIoState(const std_msgs::Int32::ConstPtr& state) {
    get()->agvdata_.agv_io_state.manual_state = static_cast<bool>(state->data);
  }
  void AddSafetyIoState(const std_msgs::Int32::ConstPtr& state) {
    get()->agvdata_.agv_io_state.safety_io_state = state->data;
  }
  void AddChargeIoState(const std_msgs::Int32::ConstPtr& state) {
    get()->agvdata_.agv_io_state.charge_do_state =
        static_cast<bool>(state->data);
  }
  void AddPalletIoState(const std_msgs::Int32::ConstPtr& state) {
    get()->agvdata_.forklift_pallet_state.fork_pallet_state = state->data;
  }
  void AddLimitSwitchState(const std_msgs::Int32::ConstPtr& state) {
    get()->agvdata_.forklift_pallet_state.fork_up_down_state = state->data;
  }
  void AddJackUpIo(const cotek_msgs::jack_up_io_state::ConstPtr& state) {
    get()->agvdata_.jackup_pallet_state.up_down_state = state->up_down_state;
    get()->agvdata_.jackup_pallet_state.roate_state = state->roate_state;
  }
  void AddPalletActInfo(
      const cotek_msgs::pallet_act_info::ConstPtr& pallet_act) {
    get()->agvdata_.jackup_pallet_state.lift_height = pallet_act->lift_height;
    get()->agvdata_.jackup_pallet_state.lift_velocity =
        pallet_act->lift_velocity;
    get()->agvdata_.jackup_pallet_state.height_init = pallet_act->height_init;

    get()->agvdata_.jackup_pallet_state.rotate_degree =
        pallet_act->rotate_degree;
    get()->agvdata_.jackup_pallet_state.rotate_omega = pallet_act->rotate_omega;
    get()->agvdata_.jackup_pallet_state.degree_init = pallet_act->degree_init;
  }

  // 传感器外部驱动
  void AddPgv100UpData(const cotek_msgs::pgv100_feedback::ConstPtr& data) {
    get()->agvdata_.up_pgv.tag_number = data->tag_number;
    get()->agvdata_.up_pgv.angle = data->angle;
    get()->agvdata_.jackup_pallet_state.palletag = data->tag_number;
    get()->agvdata_.up_pgv.tag_number = data->tag_number;
  }
  void AddPalletHikQrData(
      const hikvs_mv_im5005_ros::hikvs_mv_im5005::ConstPtr& data) {
    get()->agvdata_.jackup_pallet_state.palletag = data->tag_number;
    get()->agvdata_.up_pgv.tag_number = data->tag_number;
  }
  void AddBaseHikQrData(
      const hikvs_mv_im5005_ros::hikvs_mv_im5005::ConstPtr& data) {
    get()->agvdata_.jackup_pallet_state.basetag = data->tag_number;
  }
  void AddForwardMlsData(
      const cotek_msgs::hinson_mls_feedback::ConstPtr& data) {
    get()->agvdata_.hinson_forward_mls_data.time_stamp = data->time_stamp;
    get()->agvdata_.hinson_forward_mls_data.lines_info =
        static_cast<LinesInfo>(data->lines_info);
    get()->agvdata_.hinson_forward_mls_data.line_offset = data->line_offset;
    get()->agvdata_.hinson_forward_mls_data.strength = data->strength;
    get()->agvdata_.hinson_forward_mls_data.interaction_count =
        data->interaction_count;
  }
  void AddBackwardMlsData(
      const cotek_msgs::hinson_mls_feedback::ConstPtr& data) {
    get()->agvdata_.hinson_backward_mls_data.time_stamp = data->time_stamp;
    get()->agvdata_.hinson_backward_mls_data.lines_info =
        static_cast<LinesInfo>(data->lines_info);
    get()->agvdata_.hinson_backward_mls_data.line_offset = data->line_offset;
    get()->agvdata_.hinson_backward_mls_data.strength = data->strength;
    get()->agvdata_.hinson_backward_mls_data.interaction_count =
        data->interaction_count;
  }

  // 底盘传感器数据反馈
  void AddBatteryData(const cotek_msgs::battery_feedback::ConstPtr& data) {
    get()->agvdata_.battery_monitor.capacity = data->percentage;
    get()->agvdata_.battery_monitor.current = data->current;
    get()->agvdata_.battery_monitor.voltage = data->voltage;
  }
  void AddWeigtData(const cotek_msgs::weighing_feedback::ConstPtr& data) {
    get()->agvdata_.weigh_monitor.weight = data->data;
    get()->agvdata_.weigh_monitor.weigh_errcode = data->error_code;
  }
  void AddImuData(const sensor_msgs::Imu::ConstPtr& data) {
    // todo: IMU 错误信息上报
    get()->agvdata_.imu_monitor.angle_x = data->orientation.x;
    get()->agvdata_.imu_monitor.angle_y = data->orientation.y;
    get()->agvdata_.imu_monitor.angle_z = data->orientation.z;

    get()->agvdata_.imu_monitor.omega_x = data->angular_velocity.x;
    get()->agvdata_.imu_monitor.omega_y = data->angular_velocity.y;
    get()->agvdata_.imu_monitor.omega_z = data->angular_velocity.z;

    get()->agvdata_.imu_monitor.acc_x = data->linear_acceleration.x;
    get()->agvdata_.imu_monitor.acc_y = data->linear_acceleration.y;
    get()->agvdata_.imu_monitor.acc_z = data->linear_acceleration.z;

    get()->agvdata_.imu_monitor.time_stamp = ros::Time::now();
    // get()->agvdata_.imu_monitor.error_code = data->error_code;
  }
  void AddHightEncoderData(
      const cotek_msgs::wire_encoder_feedback::ConstPtr& data) {
    // 拉线传感器测量值转换为真实值
    get()->agvdata_.forklift_pallet_state.height = data->data;
    get()->agvdata_.forklift_pallet_state.lift_velocity = data->velocity;
    get()->agvdata_.forklift_pallet_state.height_stamp = ros::Time::now();
    get()->agvdata_.forklift_pallet_state.hight_error_code = data->error_code;
  }
  void AddLateralEncoderData(
      const cotek_msgs::wire_encoder_feedback::ConstPtr& data) {
    get()->agvdata_.forklift_pallet_state.lateral = data->data;
    get()->agvdata_.forklift_pallet_state.lateral_velocity = data->velocity;
    get()->agvdata_.forklift_pallet_state.lateral_stamp = ros::Time::now();
    get()->agvdata_.forklift_pallet_state.lateral_error_code = data->error_code;
  }
  void AddSideEncoderData(
      const cotek_msgs::wire_encoder_feedback::ConstPtr& data) {
    get()->agvdata_.forklift_pallet_state.sideshift = data->data;
    get()->agvdata_.forklift_pallet_state.sideshift_velocity = data->velocity;
    get()->agvdata_.forklift_pallet_state.sideshift_stamp = ros::Time::now();
    get()->agvdata_.forklift_pallet_state.sideshift_error_code =
        data->error_code;
  }
  void AddIoData(const cotek_msgs::io_feedback::ConstPtr& data) {
    get()->io_data_ptr_->SetData(data);
    get()->io_feedback = get()->io_data_ptr_->GetIoData();
  }

  // 其余请求反馈消息
  void AddPalletDetectFeedback(
      const cotek_msgs::pallet_detect_state::ConstPtr& feedback) {
    get()->agvdata_.others_feedback.pallet_detect_state =
        feedback->pallet_state;
  }
  void AddPalletCenterFeedback(
      const cotek_msgs::pallet_center_feedback::ConstPtr& feedback) {
    get()->agvdata_.others_feedback.pallet_center_data.time = ros::Time::now();
    get()->agvdata_.others_feedback.pallet_center_data.delta_y =
        feedback->pose.y;
  }
  void AddPalletBackLimitFeedback(
      const cotek_msgs::pallet_back_limit_feedback::ConstPtr& feedback) {
    get()->agvdata_.others_feedback.pallet_back_limit_type =
        static_cast<PalletBackLimitType>(feedback->limit_type);
  }
  void AddUnloadDetectFeedback(
      const cotek_msgs::unload_detect_feedback::ConstPtr& feedback) {
    get()->agvdata_.others_feedback.unload_detect_data.time = ros::Time::now();
    get()->agvdata_.others_feedback.unload_detect_data.permit_unload =
        feedback->permit_unload;
  }
  void AddColumnDetectFeedback(
      const cotek_msgs::column_detect_feedback::ConstPtr& feedback) {
    get()->agvdata_.others_feedback.column_pos_data.time = ros::Time::now();
    get()->agvdata_.others_feedback.column_pos_data.delta_y = feedback->delta_y;
  }

  void AddClearDataFeedback(
      const cotek_msgs::ds_clear_data::ConstPtr& feedback) {
    if (feedback->type == "loadState") {
      get()->agvdata_.load_state = std::stoi(feedback->value);
    }
  }

  void AddElevatorFeedback(
      const cotek_msgs::elevator_feedback::ConstPtr& feedback) {
    get()->elevator_data_.time_stamp = ros::Time::now();
    get()->elevator_data_.close_state = feedback->close_state;
    get()->elevator_data_.open_state = feedback->open_state;
    get()->elevator_data_.error = feedback->error;
    get()->elevator_data_.floor = feedback->floor;
    get()->elevator_data_.occupy_flag = feedback->occupy_flag;
    get()->elevator_data_.warn_signal = feedback->warn_signal;
  }

  void AddAutoDoorFeedback(
      const cotek_msgs::elevator_feedback::ConstPtr& feedback) {
    get()->autodoor_data_.time_stamp = ros::Time::now();
    get()->autodoor_data_.close_state = feedback->close_state;
    get()->autodoor_data_.open_state = feedback->open_state;
    get()->autodoor_data_.warn_signal = feedback->warn_signal;
  }

  void AddManualConfirm(const int8_t& data) {
    get()->agvdata_.manual_confirm = data;
  }

  void SetConfirmState(const int8_t& data) {
    get()->agvdata_.manual_confirm = data;
  }

  void SetLoadState(const int& state) { get()->agvdata_.load_state = state; }

  SensorData agvdata_;
  ActionGoal goal_;
  ActionState state_;
  ActionGoal open_loop_goal_;
  ActionState open_loop_state_;
  AgvActionOption option_;
  AgvBasicOption basic_option_;
  IoFeedback io_feedback;
  ElevatorData elevator_data_;
  ElevatorData autodoor_data_;

  std::shared_ptr<cotek_action::IoData> io_data_ptr_;

 private:
  // config initial value
  AgvData() {
    memset(&agvdata_, 0, sizeof(SensorData));
    io_data_ptr_ = std::make_shared<cotek_action::IoData>();
    goal_.Reset();
  }
};

}  // namespace cotek_action
#endif  // SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_DATA_CENTER_H_
