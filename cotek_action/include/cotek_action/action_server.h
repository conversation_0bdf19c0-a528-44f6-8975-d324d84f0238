/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_SERVER_H_
#define SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_SERVER_H_
#include <actionlib/server/simple_action_server.h>
#include <cotek_common/cotek_topic_name.h>

#include <cstdio>
#include <iostream>
#include <memory>
#include <string>
#include <thread>

#include "action_config.h"
#include "cotek_action/action_model/action_interface.h"
#include "cotek_action/action_model/atomic_action.h"
#include "cotek_action/cotek_action_enum.h"
#include "cotek_action/data_center.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/node_diagnostic_manager.h"
#include "cotek_msgs/agv_actionAction.h"
#include "cotek_msgs/node_diagnostic.h"

namespace cotek_action {

class ActionServer {
 public:
  ActionServer() = delete;
  ActionServer(std::string name, 
               std::shared_ptr<NodeStatusManager> state_diag_ptr)
      : server_(name, false), server_name_(name) {
    ros::NodeHandle nh;
    node_status_manager_ptr_ = state_diag_ptr;
    server_.registerGoalCallback(boost::bind(&ActionServer::GoalCB, this));
    server_.registerPreemptCallback(
        boost::bind(&ActionServer::PreemptCB, this));
    server_.start();
  }
  ~ActionServer() {}
  // start action server
  void ServerStart();
  bool Init();

  void GoalCB();
  void PreemptCB();
  void UpdateActionOption(const AgvActionOption& option);
  void UpdateBasicOption(const AgvBasicOption& option);
  
 private:
  void Runner();

  actionlib::SimpleActionServer<cotek_msgs::agv_actionAction> server_;
  std::string server_name_;
  cotek_msgs::node_diagnostic fault_;
  cotek_msgs::agv_actionFeedback feedback_;
  cotek_msgs::agv_actionResult result_;

  std::shared_ptr<std::thread> run_executor_;
  std::shared_ptr<cotek_action::ActionInterface> action_interface_;
  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;
};

}  // namespace cotek_action
#endif  // SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_SERVER_H_
