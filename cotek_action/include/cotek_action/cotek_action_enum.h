/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_TYPE_ENUM_H_
#define SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_TYPE_ENUM_H_
#include <ros/ros.h>

#include <string>
#include <vector>

#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/cotek_enum_type.h"

namespace cotek_action {
enum class ActionStatus {
  UNSTART = 0,
  DOING = 1,
  FINISH = 2,
  PAUSE = 3,
  DOING_FAULT = 4,
  CANCEL = 5,
  TIMEOUT = 6,
  EXCEPTION = 7
};

enum class FinishCondition {
  CHECK_DI1 = 0,  // 检测IO状态
  CHECK_DI2 = 1,
  CHECK_DI3 = 2,
  CHECK_DI4 = 3,
  CHECK_DI5 = 4,
  CHECK_DI6 = 5,
  CHECK_DI7 = 6,
  CHECK_DI8 = 7,
  CHECK_DI9 = 8,
  CHECK_DI10 = 9,
  CHECK_DI11 = 10,
  CHECK_DI12 = 11,
  CHECK_DI13 = 12,
  CHECK_DI14 = 13,
  CHECK_DI15 = 14,
  CHECK_DI16 = 15,
  CHECK_DO1 = 16,
  CHECK_DO2 = 17,
  CHECK_DO3 = 18,
  CHECK_DO4 = 19,
  CHECK_DO5 = 20,
  CHECK_DO6 = 21,
  CHECK_DO7 = 22,
  CHECK_DO8 = 23,
  CHECK_DO9 = 24,
  CHECK_DO10 = 25,
  CHECK_DO11 = 26,
  CHECK_DO12 = 27,
  CHECK_DO13 = 28,
  CHECK_DO14 = 29,
  CHECK_DO15 = 30,
  CHECK_DO16 = 31,
  CHECK_UP_LIMIT = 32,     // 上限位信号
  CHECK_DOWN_LIMIT = 33,   // 下限位信号
  FORK_HEIGHT = 34,        // 插齿高度距离
  FORK_TILT = 35,          // 插齿倾斜角度
  FORK_FORWARD = 36,       // 插齿前移距离
  FORK_BACKWARD = 37,      // 插齿回缩距离
  FORK_SIDEMOVE = 38,      // 插齿横移距离
  CHARGE_STATE = 39,       // 检测充电状态
  CHECK_UP_WEIGHT = 40,    // 取货后称重检测
  CHECK_DOWN_WEIGHT = 41,  // 卸货后称重检测
};

// public topic name and size
enum class ChargeDetectionType : int32_t {
  NONE = 0,     // 电流电压均不检测
  CURRENT = 1,  // 电流检测
  VOLTAGE = 2,  // 电压检测
  BOTH = 3      // 电流电压均检测
};

enum class Pallet_Zero_State { UNZEROED = 0, ZEROED = 1 };

enum class ForkStateType : uint32_t { DOWN = 0, MIDDLE = 1, UP = 2 };

enum class PalletDetectType : uint32_t {
  NONE = 0,
  DETECTION = 1,    // 检测到有货(障碍物)
  NO_DETECTION = 2  // 检测到无货(障碍物)
};

struct Pgv100 {
  double x_deviation;
  double y_deviation;
  double angle;
  uint32_t tag_number;
  uint8_t color;
  uint8_t direction;
  uint16_t warningcode;
};

struct MoveFeedback {
  double velocity;
  double omega;
};

enum class PalletBackLimitType : uint32_t {
  NONE = 0,         // 默认值
  LEFT_LIMIT = 1,   // 左侧有超板
  RIGHT_LIMIT = 2,  // 右侧有超板
  ALL_LIMIT = 3,    // 两侧均有超板
  FREE = 10         // 无超板
};

enum class LinesInfo : uint16_t {
  NO_LINE = 0,
  ONE_LINE_LEFT = 1,
  ONE_LINE_CENTER = 2,
  ONE_LINE_RIGHT = 4,
  TWO_LINE_CENTER_LEFT = 3,
  TWO_LINE_CENTER_RIGHT = 6,
  THREE_LINE = 7
};

struct HinsonMlsData {
  uint32_t time_stamp;
  LinesInfo lines_info;
  float line_offset;  // -155 ~ +155mm, left > 0, right < 0, center = 0;
  int16_t strength;
  int16_t interaction_count;
};

struct ActionGoal {
  std::string order_id;
  // std::string task_id;
  uint32_t action_type;
  // todo::change vector
  double action_value;
  std::string io_pin;
  std::string mix_condition;
  std::string finish_condition;
  std::string error_condition;
  double storage_pose_x;
  double storage_pose_y;
  double storage_pose_yaw;

  void Reset() {
    order_id.clear();
    // task_id.clear();
    action_type = 0;
    action_value = 0.;
    mix_condition = "";
    finish_condition = "";
    error_condition = "";
  }
};

struct ForkliftPallet {
  int32_t fork_up_down_state;
  int32_t fork_pallet_state;

  int32_t height;
  double lift_velocity;       // 单位: m/s
  ros::Time height_stamp;     // 拉线编码器时间戳
  uint16_t hight_error_code;  // 高度传感器错误码

  int32_t lateral;              // 水平前后移动
  double lateral_velocity;      // 单位: m/s
  uint16_t lateral_error_code;  // 水平前后移动传感器错误码
  ros::Time lateral_stamp;      // 水平前后移动拉线编码器时间戳

  int32_t sideshift;              // 水平侧移
  double sideshift_velocity;      // 单位: m/s
  uint16_t sideshift_error_code;  // 水平侧移动传感器错误码
  ros::Time sideshift_stamp;      // 水平侧向移动拉线编码器时间戳
};

// Pallet state
struct JackupPallet {
  uint8_t up_down_state;
  uint8_t roate_state;
  double lift_height;
  double lift_velocity;
  bool height_init;
  double rotate_degree;
  double rotate_omega;
  bool degree_init;
  uint64_t palletag;
  uint64_t basetag;
};

struct Pallet {
  double w;
  double v;
  bool pallet_nomove_flag;
  double pallet_nomove_tar_angle;
  double up_relative_down;
};

// Roller state
// struct RollerPallet {
//   uint8_t in_out_state;
//   uint8_t joint_state;
//   double roller_velocity;
//   bool roller_init;
// };

// clamp
struct ClampData {
  uint8_t clamp_left_state;         // 夹抱机构左侧信号
  uint8_t clamp_right_state;        // 夹抱机构右侧信号
  uint8_t clamp_limit_left_state;   // 夹抱机构左侧位限
  uint8_t clamp_limit_right_state;  // 夹抱机构右侧位限
  uint8_t clamp_detect_state;       // 货架检测
};

struct Battery {
  uint8_t battery_type;
  uint8_t capacity;
  int16_t current;
  uint16_t voltage;
};

struct Weigh {
  double weight;
  uint32_t weigh_status;
  uint32_t weigh_errcode;
};

struct Imu {
  double angle_x;
  double angle_y;
  double angle_z;

  double omega_x;
  double omega_y;
  double omega_z;

  double acc_x;
  double acc_y;
  double acc_z;

  ros::Time time_stamp;
  uint16_t error_code;
};

struct Pin {
  std::string key;
  uint8_t value;
  std::string name;
  uint8_t switch_type;
};

struct IoFeedback {
  uint32_t seq;
  ros::Time time_stamp;
  std::string frame_id;
  uint32_t error_code;
  std::vector<Pin> di_state;
  std::vector<Pin> do_state;
  std::map<std::string, int> io_key_map;
  std::map<std::string, int> io_name_map;
};

struct UnloadDetect {
  ros::Time time;
  bool permit_unload = false;
};

struct PalletCenterDetect {
  ros::Time time;
  double delta_y;
};

struct ColumnPosDetect {
  ros::Time time;
  double delta_y;
};

struct AgvIoState {
  int32_t safety_io_state;
  bool charge_do_state;
  bool manual_state;
};

struct ElevatorData {
  ros::Time time_stamp;
  int floor;
  bool open_state;
  bool close_state;
  int error;
  bool warn_signal;
  bool occupy_flag;
};

struct OthersFeedback {
  PalletCenterDetect pallet_center_data;  // 托盘识别y方向的距离
  ColumnPosDetect column_pos_data;
  uint32_t pallet_detect_state;
  PalletBackLimitType pallet_back_limit_type;
  UnloadDetect unload_detect_data;
};

// sensor dataset
struct SensorData {
  MoveFeedback move_feedback;
  int load_state;
  AgvIoState agv_io_state;
  Battery battery_monitor;
  Imu imu_monitor;
  Weigh weigh_monitor;
  Pgv100 up_pgv;
  HinsonMlsData hinson_forward_mls_data;
  HinsonMlsData hinson_backward_mls_data;
  // IoFeedback io_feedback;

  ForkliftPallet forklift_pallet_state;
  JackupPallet jackup_pallet_state;
  Pallet pallet;
  // RollerPallet roller_pallet_state;
  ClampData clamp_data;

  OthersFeedback others_feedback;

  bool shelf_check_result;
  int manual_confirm{0};
};

struct ActionState {
  uint16_t percent;
  bool is_finish;
  uint16_t action_status;
  double move_omega;
  bool lift_break_state;
};

struct Charge {
  int charge_detection;
  double start_charge_current;
  double stop_charge_current;
  double start_charge_voltage;
  double stop_charge_voltage;
};

struct ManualConfirm {
  bool overtime_confirm; // 是否允许超时确认
  double wait_time;  // 超时等待时间
};

struct OpenDoor {
  bool enable_timewait;
  double open_door_wait_time;  // 开门等待时间
};

struct Jackup_lift {
  bool check_pallet;
  bool motor_safe_break;
  bool speed_optimize_control;

  double max_speed;
  double safe_speed;
  double max_height;
  double fixed_speed;
  double safe_height;
};

struct Jackup_rotate {
  bool speed_optimize_control;

  double max_speed;
  double safe_speed;
  double fixed_speed;
};

struct Jackup_pallet_pid {
  double kp;
  double ki;
  double kd;
  double output_limit;
  double ramp_rate;
};

// TODO(@cxh) 预留顶升车
struct JackUpOption {
  Jackup_lift pallet_up_down_option;
  Jackup_rotate pallet_rotation_zero_option;
  Jackup_pallet_pid nomove_pid;
  Jackup_pallet_pid rotate_pid;
};

struct ForkCommonOption {
  bool check_pallet;
  bool auto_height_calibration;
  double kload_weighing_threshold;
  double tilt_standard_angle;
};

struct Fork_height_move_option {
  float kdist_output_limit;
  float kdist_ramp_rate;
  float kspeed_output_limit;
  float kspeed_ramp_rate;
  float kdist_p;
  float kdist_i;
  float kdist_d;
  float kup_speed_p;
  float kup_speed_i;
  float kup_speed_d;
  float kup_min_speed;
  float kup_max_speed_no_pallet;
  float kup_max_speed_with_pallet;
  float kdown_speed_p;
  float kdown_speed_i;
  float kdown_speed_d;
  float kdown_min_speed;
  float kdown_max_speed_no_pallet;
  float kdown_max_speed_with_pallet;
  float offset;
  float kup_value;
  float kdown_value;
  float kup_limit;
  float kdown_limit;
};

struct Fork_side_move_option {
  float kdist_output_limit;
  float kdist_ramp_rate;
  float kspeed_output_limit;
  float kspeed_ramp_rate;
  float kdist_p;
  float kdist_i;
  float kdist_d;
  float kspeed_p;
  float kspeed_i;
  float kspeed_d;
  float kmin_speed;
  float kmax_speed_no_pallet;
  float kmax_speed_with_pallet;
  float offset;
  float limit;
};

struct Fork_tilt_move_option {
  float kdist_output_limit;
  float kdist_ramp_rate;
  float kspeed_output_limit;
  float kspeed_ramp_rate;
  float kdist_p;
  float kdist_i;
  float kdist_d;
  float kspeed_p;
  float kspeed_i;
  float kspeed_d;
  float kmin_speed;
  float kmax_speed_no_pallet;
  float kmax_speed_with_pallet;
  float offset;
};

struct ForkliftOption {
  ForkCommonOption fork_common_option;
  Fork_height_move_option height_fork;
  Fork_height_move_option lateral_fork;
  Fork_side_move_option sideshift_fork;
  Fork_tilt_move_option tilt_fork;
};

struct AgvBasicOption {
  AgvType agv_type;  // agv类型
};

struct AgvActionOption {
  // AgvType agv_type;             // agv类型
  bool enable_local_debug;      // 使能日志
  bool enable_timeout;          // 使能动作超时报警故障
  double action_timeout_value;  // 动作超时时间
  double control_period;        // 控制循环时间

  OpenDoor open_door_option;
  Charge charge_option;
  JackUpOption jack_up_option;
  ForkliftOption forklift_option;
  ManualConfirm manual_confirm;

  AgvActionOption() {
    std::memset(&open_door_option, 0, sizeof(OpenDoor));
    std::memset(&charge_option, 0, sizeof(Charge));
    std::memset(&jack_up_option, 0, sizeof(JackUpOption));
    std::memset(&forklift_option, 0, sizeof(ForkliftOption));
  }
};

}  // namespace cotek_action
#endif  // SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_TYPE_ENUM_H_
