/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_PALLET_FORKLIFT_ACTION_H_
#define COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_PALLET_FORKLIFT_ACTION_H_
#include <ros/ros.h>

#include <map>

#include "cotek_action/action_model/action_interface.h"
#include "cotek_msgs/agv_info_response.h"
#include "cotek_msgs/forklift_action.h"
#include "cotek_msgs/pallet_back_limit_feedback.h"
#include "cotek_msgs/query_storage_info.h"
#include "cotek_msgs/request_pallet_back_limit.h"
#include "cotek_msgs/weighting_loaded.h"

namespace cotek_action {

class PalletForkliftAction : public ActionInterface {
 public:
  PalletForkliftAction() : ActionInterface(), start_up_flag_(false) {
    ros::NodeHandle nh;
    forklift_action_pub_ = nh.advertise<cotek_msgs::forklift_action>(
        cotek_topic::kForkLiftActionTopic, kTopicSendCacheSize);
    agv_info_pub_ = nh.advertise<cotek_msgs::agv_info_response>(
        cotek_topic::kAgvInfoResponseTopic, 10);
    load_weight_base_pub_ = nh.advertise<cotek_msgs::weighting_loaded>(
        cotek_topic::kWeightDropCheckTopic, 10);
    request_pallet_back_limit_pub_ =
        nh.advertise<cotek_msgs::request_pallet_back_limit>(
            cotek_topic::kRequestPalletBackLimitTopic, 10);
    ActionInit();
  }

  ~PalletForkliftAction() {}
  // to do .. update constrct data strct in this scope
  void UpdateData(AgvData *Get_info) override;
  // return action progress =()%
  uint32_t FeedBack() override;
  // return whethe action done
  bool IsFinished() override;
  // release goal..
  void DataReset();

  void Waiting(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void Rest(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void Init(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void ControlAudioLevel(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void CheckUPWeight(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void CheckDownWeight(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void DelayAction(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void LowPowerMode(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // pallet up
  void PalletForkUp(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // pallet down
  void PalletForkDown(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void PalletDetectBeforeUp(cotek_msgs::forklift_action *msg,
                            AgvData *Get_info);
  void PalletDetectBeforeDown(cotek_msgs::forklift_action *msg,
                              AgvData *Get_info);

  void PalletBackLimit(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  bool QueryStorageInfo(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void OpenloopForkUp(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void OpenloopForkDown(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  // motor break
  void Break(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // beeper lauding..
  void Beep(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // curtis controller reset
  void ForkliftBodyCotrollerReset(cotek_msgs::forklift_action *msg,
                                  AgvData *Get_info);
  // charge
  void Charge(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void ActionInit();

  void ExecuteAction(AgvData *Get_info) override;

  void PublishActionMsg() override;

  void PoweronInit(cotek_msgs::forklift_action *msg, AgvData *Get_info);

 private:
  ros::Publisher forklift_action_pub_;
  ros::Publisher load_weight_base_pub_;
  ros::Publisher agv_info_pub_;
  ros::Publisher request_pallet_back_limit_pub_;

  bool start_up_flag_;

  // uint32_t load_weight_base_{0};
  // bool close_check_weight_{false};
  // LoadState load_state_{LoadState::NO_LOAD};

  cotek_msgs::forklift_action msg_;

  OpenloopAction open_loop_action_;

  std::map<uint32_t,
           std::function<void(cotek_msgs::forklift_action *, AgvData *)> >
      action_map_;
};

}  // namespace cotek_action
#endif  // COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_PALLET_FORKLIFT_ACTION_H_
