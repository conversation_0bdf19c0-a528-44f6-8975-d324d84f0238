/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ACTION_INTERFACE_H_
#define COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ACTION_INTERFACE_H_
#include <cotek_common/cotek_topic_name.h>
#include <ros/ros.h>

#include <memory>

#include "cotek_action/cotek_action_enum.h"
#include "cotek_action/data_center.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/math.h"
#include "cotek_common/node_diagnostic_manager.h"

namespace cotek_action {
using AgvTaskOperationType = dispacth::AgvTaskOperationType;
using AtomicActionType = action::AtomicActionType;
using ActionNodeStatus = cotek_diagnostic::ActionNodeStatus;
using NodeStatusManager = cotek_diagnostic::NodeStatusManager<ActionNodeStatus>;

class ActionInterface {
 public:
  ActionInterface() : doing_action_(false), up_action_flag_(false) {}
  virtual ~ActionInterface() {}

  virtual void UpdateData(AgvData* Get_info) = 0;

  virtual uint16_t FeedBack() = 0;
  virtual uint16_t OpenLoopFeedBack() = 0;

  virtual bool IsFinished() = 0;
  virtual bool IsOpenLoopFinished() = 0;

  virtual void ExecuteAction(AgvData* Get_info) = 0;
  virtual void ExecuteOpenLoopAction(AgvData* Get_info) = 0;

  virtual void PublishActionMsg() = 0;
  virtual void PublishOpenLoopActionMsg() = 0;

  inline bool DoingAction() { return doing_action_; }
  inline void ResetDoingAction() {
    doing_action_ = false;
    up_action_flag_ = false;
  }
  ros::Time GetActionStartTime() { return action_time_; }
  inline void SetDoingAction(const bool& set) {
    doing_action_ = set;
  }
  inline void SetActionTime() { action_time_ = ros::Time::now(); }
  
  bool CheckTimeout(bool use, double limit_time, AgvData* Get_info) {
    if (!use) return false;

    if (doing_action_ == false) {
      action_time_ = ros::Time::now();
      doing_action_ = true;
    }
    ros::Duration action_seconds(limit_time, 0);
    if ((ros::Time::now() - action_time_) > action_seconds) {
      // LOG_ERROR_STREAM_COND(Get_info->option_.enable_local_debug,
      //                       " ERROR:...Action-Time out...");
      LOG_ERROR_THROTTLE(1, "ERROR:...Action-Time out...");
      return true;
    }
    return false;
  }

  bool CheckUpTimeout(bool use, double limit_time, AgvData* Get_info) {
    if (!use) return false;

    if (up_action_flag_ == false) {
      up_action_time_ = ros::Time::now();
      up_action_flag_ = true;
    }
    ros::Duration action_seconds(limit_time, 0);
    if ((ros::Time::now() - up_action_time_) > action_seconds) {
      return true;
    }
    return false;
  }

  inline void SetStatusManagerPtr(
      std::shared_ptr<NodeStatusManager> status_manager_ptr) {
    node_status_manager_ptr_ = status_manager_ptr;
  }

  virtual void UpdateInitParm() = 0;

 protected:
  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;

 private:
  ros::Time action_time_;
  ros::Time up_action_time_;
  bool doing_action_;
  bool up_action_flag_;
};

}  // namespace cotek_action

#endif  // COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ACTION_INTERFACE_H_
