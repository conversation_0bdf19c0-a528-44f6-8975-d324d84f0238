/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_HEAP_FORKLIFT_ACTION_H_
#define COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_HEAP_FORKLIFT_ACTION_H_
#include <ros/ros.h>

#include <map>

#include "cotek_action/action_model/action_interface.h"
#include "cotek_common/pid/mini_pid.h"
#include "cotek_msgs/agv_info_response.h"
#include "cotek_msgs/forklift_action.h"
#include "cotek_msgs/weighting_loaded.h"

namespace cotek_action {
class HeapForkliftAction : public ActionInterface {
 public:
  HeapForkliftAction()
      : cmd_height_base_(0),
        load_state_(LoadState::UNKOWN),
        up_down_flag_(0),
        delay_flag_(0),
        setting_speed_(0.) {
    ros::NodeHandle nh;

    unload_detect_pub_ = nh.advertise<cotek_msgs::request_unload_detect>(
        cotek_topic::kRequestUnloadDetectTopic, kTopicSendCacheSize);
    forklift_action_pub_ = nh.advertise<cotek_msgs::forklift_action>(
        cotek_topic::kForkLiftActionTopic, kTopicSendCacheSize);
    agv_info_pub_ = nh.advertise<cotek_msgs::agv_info_response>(
        cotek_topic::kAgvInfoResponseTopic, 10);

    load_weight_base_pub_ = nh.advertise<cotek_msgs::weighting_loaded>(
        cotek_topic::kWeightDropCheckTopic, 10);

    auto heap_option = AgvData::get()->option_.forklift_option.height_fork;

    dist_pid_ptr_ = std::make_shared<MiniPID>(
        heap_option.kdist_p, heap_option.kdist_i, heap_option.kdist_d);
    dist_pid_ptr_->setOutputLimits(-0.2, 0.2);
    dist_pid_ptr_->setOutputRampRate(0.2);

    lift_speed_pid_ptr_ = std::make_shared<MiniPID>(heap_option.kup_speed_p,
                                                    heap_option.kup_speed_i,
                                                    heap_option.kup_speed_d);
    lift_speed_pid_ptr_->setOutputLimits(-255, 255);
    lift_speed_pid_ptr_->setOutputRampRate(200);

    descent_speed_pid_ptr_ = std::make_shared<MiniPID>(
        heap_option.kdown_speed_p, heap_option.kdown_speed_i,
        heap_option.kdown_speed_d);
    descent_speed_pid_ptr_->setOutputLimits(-220, 220);
    descent_speed_pid_ptr_->setOutputRampRate(200);

    unload_speed_pid_ptr_ = std::make_shared<MiniPID>(5, 0.001, 0);
    unload_speed_pid_ptr_->setOutputLimits(-220, 220);
    unload_speed_pid_ptr_->setOutputRampRate(200);

    ActionInit();
  }

  virtual ~HeapForkliftAction() {}
  // to do .. update constrct data strct in this scope
  void UpdateData(AgvData *Get_info) override;
  // return action progress =()%
  uint32_t FeedBack() override;
  // return whethe action done
  bool IsFinished() override;
  // release goal..
  void DataReset();

  void Waiting(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void Rest(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void ControlAudioLevel(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void LowPowerMode(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // pallet up
  void PalletForkUp(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // pallet down
  void PalletForkDown(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // pallet heap move down and up..
  void HeapForkMove(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // 堆高车抬货
  void HeapLiftLoad(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // 堆高车卸货
  void HeapUnLoad(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // 堆高车放货检测
  void UnloadDetect(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // 堆高车货物重量检测
  void CheckUPWeight(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // 堆高车货物重量检测
  void CheckDownWeight(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // motor break
  void Break(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // beeper lauding..
  void Beep(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // curtis controller reset
  void ForkliftBodyCotrollerReset(cotek_msgs::forklift_action *msg,
                                  AgvData *Get_info);
  // charge
  void Charge(cotek_msgs::forklift_action *msg, AgvData *Get_info);

  void ActionInit();

  void ExecuteAction(AgvData *Get_info) override;

  void PublishActionMsg() override;
  // 延时动作
  void DelayAction(cotek_msgs::forklift_action *msg, AgvData *Get_info);
  // 发布负载重量topic
  void PublishWeightLoadingBase();

  bool DelayTime(double second);

  void ResetDelayFlag() { delay_flag_ = 0; }

  inline void ResetLastSpeed() { setting_speed_ = 0.; };

  const float GetForkMoveSpeed(const double &target_height,
                               const double &current_height,
                               const double &current_speed);

  const float GetForkMoveSpeed(const double &target_height,
                               const double &current_height,
                               const double &current_speed, bool);

 private:
  void PublishRequestUnloadDetect();

  ros::Publisher unload_detect_pub_;

  ros::Publisher forklift_action_pub_;

  ros::Publisher agv_info_pub_;

  ros::Publisher load_weight_base_pub_;

  cotek_msgs::forklift_action msg_;

  std::map<uint32_t,
           std::function<void(cotek_msgs::forklift_action *, AgvData *)> >
      action_map_;
  double cmd_height_base_;
  static uint32_t load_weight_base_;
  static bool close_check_weight_;
  static bool check_fork_state_;
  LoadState load_state_;
  bool up_down_flag_;
  bool delay_flag_;
  ros::Time delay_time_start_;
  double setting_speed_;
  std::shared_ptr<MiniPID> dist_pid_ptr_;
  std::shared_ptr<MiniPID> lift_speed_pid_ptr_;
  std::shared_ptr<MiniPID> descent_speed_pid_ptr_;
  std::shared_ptr<MiniPID> unload_speed_pid_ptr_;
};

}  // namespace cotek_action
#endif  // COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_HEAP_FORKLIFT_ACTION_H_
