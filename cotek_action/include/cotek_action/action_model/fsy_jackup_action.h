/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_FSY_JACKUP_ACTION_H_
#define COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_FSY_JACKUP_ACTION_H_
#include <ros/ros.h>
#include <tf/transform_listener.h>

#include <map>

#include "cotek_action/action_model/action_interface.h"
#include "cotek_msgs/jack_up_action.h"
#include "tf/transform_broadcaster.h"

namespace cotek_action {
class FSYJackupAction : public ActionInterface {
 public:
  FSYJackupAction()
      : timeout_(false),
        pallet_nomove_flag_(0),
        acting_timeout_(false),
        shelf_pose_vaild_(false) {
    ros::NodeHandle nh;
    jackup_action_pub_ = nh.advertise<cotek_msgs::jack_up_action>(
        cotek_topic::kJackUpActionTopic, kTopicSendCacheSize);
    shelf_tf_ = std::make_shared<tf::TransformListener>(ros::Duration(5.0));
    ActionInit();
  }
  // virtual ~FSYJackupAction(){}

  void UpdateData(AgvData *Get_info) override;

  uint32_t FeedBack() override;

  bool IsFinished() override;

  void DataReset(cotek_msgs::jack_up_action *msg);

  void PalletStateProcess(uint8_t flag, AgvData *Get_info);

  void ActionNone(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void Rest(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void ControlAudioLevel(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void PalletNoMove(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void PalletUp(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void PalletDown(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void Delay(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void PalletRotation(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void PalletZero(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void MotorDisable(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void MotorEnable(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void ActionCharge(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void ActionOpenDoor(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void MotorClearAlarm(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  // pause action
  void ActionWaiting(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  // Low_Power_Mode
  void LowPowerMode(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  void MagneticSwitchDir(cotek_msgs::jack_up_action *msg, AgvData *Get_info);

  HinsonMlsData SelectMlsData(AgvData *Get_info);

  void LiftSafeBreakControl(ros::Time time, cotek_msgs::jack_up_action *msg,
                            AgvData *Get_info);

  // Init action list
  void ActionInit();

  void ExecuteAction(AgvData *Get_info) override;

  void PublishActionMsg() override;

 private:
  ros::Publisher jackup_action_pub_;
  ros::Time action_time_;
  bool doing_action_;
  cotek_msgs::jack_up_action msg_;
  std::shared_ptr<tf::TransformListener> shelf_tf_;
  bool shelf_pose_vaild_;
  double shelf_cur_theta_;
  std::map<int32_t,
           std::function<void(cotek_msgs::jack_up_action *, AgvData *)>>
      action_map_;
  bool timeout_;
  uint32_t pallet_nomove_flag_;
  double pallet_tar_theta_;
  bool CheckPallet(bool use, AgvData *Get_info);
  bool CheckTimeout(bool use, double limit_time, AgvData *Get_info);
  bool IsResetNoMoveFlag(const AgvData *Get_info);
  ActionGoal newgoal_;
  ActionGoal lastgoal_;
  bool acting_timeout_;
};
}  // namespace cotek_action

#endif  // COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_FSY_JACKUP_ACTION_H_
