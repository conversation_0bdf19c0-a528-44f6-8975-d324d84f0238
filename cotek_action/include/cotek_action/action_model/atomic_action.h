/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ATOMIC_ACTION_H_
#define COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ATOMIC_ACTION_H_
#include <ros/ros.h>
#include <tf/transform_listener.h>

#include "cotek_action/action_model/action_interface.h"
#include "cotek_common/cotek_tf_name.h"
#include "cotek_common/pid/mini_pid.h"
#include "cotek_msgs/action_cmd.h"
#include "cotek_msgs/agv_info_response.h"
#include "cotek_msgs/load.h"
#include "cotek_msgs/load_state.h"
#include "cotek_msgs/loads.h"
#include "cotek_msgs/pallet_back_limit_feedback.h"
#include "cotek_msgs/pallet_nomove_info.h"
#include "cotek_msgs/query_storage_info.h"
#include "cotek_msgs/request_column_position_detect.h"
#include "cotek_msgs/request_pallet_back_limit.h"
#include "cotek_msgs/request_pallet_center.h"

namespace cotek_action {

struct OriginalCondition {
  int logic_layer;
  std::string combination;
  std::string id;
  std::string op;
  std::string last_level_op;
  std::string same_level_op;
  double value1;
  double value2;
};

struct Condition {
  int logic_layer;
  int combination;
  int id;
  int op;
  std::string last_level_op;
  std::string same_level_op;
  bool io_state;
  double value;
  double deviation;
};

enum class CombinationType {
  AND = 0,  // &&
  OR = 1,   // ||
  UNKNOWN = 99
};

enum class OperatorType {
  GREATER_THAN = 0,  // > OR >=
  LESS_THAN = 1,     // < OR <=
  EQUAL = 2,         // ==
  RANGE = 3,         // [value-diff, value+diff]
  UNKNOWN = 99
};

enum class CheckConditionType {
  CHECK_DI1 = 1,  // 检测IO状态
  CHECK_DI2 = 2,
  CHECK_DI3 = 3,
  CHECK_DI4 = 4,
  CHECK_DI5 = 5,
  CHECK_DI6 = 6,
  CHECK_DI7 = 7,
  CHECK_DI8 = 8,
  CHECK_DI9 = 9,
  CHECK_DI10 = 10,
  CHECK_DI11 = 11,
  CHECK_DI12 = 12,
  CHECK_DI13 = 13,
  CHECK_DI14 = 14,
  CHECK_DI15 = 15,
  CHECK_DI16 = 16,
  CHECK_DO1 = 17,
  CHECK_DO2 = 18,
  CHECK_DO3 = 19,
  CHECK_DO4 = 20,
  CHECK_DO5 = 21,
  CHECK_DO6 = 22,
  CHECK_DO7 = 23,
  CHECK_DO8 = 24,
  CHECK_DO9 = 25,
  CHECK_DO10 = 26,
  CHECK_DO11 = 27,
  CHECK_DO12 = 28,
  CHECK_DO13 = 29,
  CHECK_DO14 = 30,
  CHECK_DO15 = 31,
  CHECK_DO16 = 32,

  CHECK_UP_LIMIT = 33,      // 上限位信号
  CHECK_DOWN_LIMIT = 34,    // 下限位信号
  CHECK_PALLET_STATE = 35,  // 检测挡板信号
  CHECK_CHARGE_STATE = 36,  // 检测充电状态

  FORK_HEIGHT = 40,    // 插齿高度距离
  FORK_TILT = 41,      // 插齿倾斜角度
  FORK_LATERAL = 42,   // 插齿横移距离
  FORK_SIDEMOVE = 43,  // 插齿横移距离

  CHECK_UP_WEIGHT = 50,    // 取货后称重检测
  CHECK_DOWN_WEIGHT = 51,  // 卸货后称重检测

  RUN_TIME = 60,  // 运行时间

  UNKNOWN = 99
};

struct Action {
  int cmd;
  float data;
  std::string io_key;
  std::string io_name;
};

class AtomicAction : public ActionInterface {
 public:
  AtomicAction()
      : ActionInterface(),
        use_diy_finish_condition_(false),
        use_diy_error_condition_(false),
        op_use_diy_finish_condition_(false),
        op_use_diy_error_condition_(false),
        load_state_(0) {
    ros::NodeHandle nh;

    atomic_action_pub_ = nh.advertise<cotek_msgs::action_cmd>(
        cotek_topic::kActionCmdTopic, kTopicSendCacheSize);
    request_pallet_back_limit_pub_ =
        nh.advertise<cotek_msgs::request_pallet_back_limit>(
            cotek_topic::kRequestPalletBackLimitTopic, 10);
    request_pallet_pub_ = nh.advertise<cotek_msgs::request_pallet_center>(
        cotek_topic::kRequestPalletCenterTopic, 2);
    unload_detect_pub_ = nh.advertise<cotek_msgs::request_unload_detect>(
        cotek_topic::kRequestUnloadDetectTopic, kTopicSendCacheSize);
    column_position_detect_pub_ =
        nh.advertise<cotek_msgs::request_column_position_detect>(
            cotek_topic::kRequestColumnPositionDetectTopic,
            kTopicSendCacheSize);
    pallet_nomove_pub_ = nh.advertise<cotek_msgs::pallet_nomove_info>(
        cotek_topic::kActionPalletNomoveTopic, 10);
    manual_confirm_pub_ = nh.advertise<std_msgs::Int32>("manualConfirm", 10);
    elevator_task_pub_ = nh.advertise<std_msgs::Int32>("elevatorTask", 10);
    autodoor_task_pub_ = nh.advertise<std_msgs::Int32>("autodoorTask", 10);

    load_state_pub_ =
        nh.advertise<cotek_msgs::load_state>(cotek_topic::kLoadStateTopic, 10);
    loads_pub_ = nh.advertise<cotek_msgs::loads>(cotek_topic::kLoadsTopic, 10);

    shelf_tf_ = std::make_shared<tf::TransformListener>(ros::Duration(5.0));

    // 插齿升降PID
    auto heap_option = AgvData::get()->option_.forklift_option.height_fork;
    heap_dist_pid_ptr_ = std::make_shared<MiniPID>(
        heap_option.kdist_p, heap_option.kdist_i, heap_option.kdist_d);
    heap_dist_pid_ptr_->setOutputLimits(-heap_option.kdist_output_limit,
                                        heap_option.kdist_output_limit);
    heap_dist_pid_ptr_->setOutputRampRate(heap_option.kdist_ramp_rate);

    heap_lift_speed_pid_ptr_ = std::make_shared<MiniPID>(
        heap_option.kup_speed_p, heap_option.kup_speed_i,
        heap_option.kup_speed_d);
    heap_lift_speed_pid_ptr_->setOutputLimits(-heap_option.kspeed_output_limit,
                                              heap_option.kspeed_output_limit);
    heap_lift_speed_pid_ptr_->setOutputRampRate(heap_option.kspeed_ramp_rate);

    heap_descent_speed_pid_ptr_ = std::make_shared<MiniPID>(
        heap_option.kdown_speed_p, heap_option.kdown_speed_i,
        heap_option.kdown_speed_d);
    heap_descent_speed_pid_ptr_->setOutputLimits(
        -heap_option.kspeed_output_limit, heap_option.kspeed_output_limit);
    heap_descent_speed_pid_ptr_->setOutputRampRate(
        heap_option.kspeed_ramp_rate);

    // 前后移PID
    auto lateral_option = AgvData::get()->option_.forklift_option.lateral_fork;
    lateral_dist_pid_ptr_ = std::make_shared<MiniPID>(
        lateral_option.kdist_p, lateral_option.kdist_i, lateral_option.kdist_d);
    lateral_dist_pid_ptr_->setOutputLimits(-lateral_option.kdist_output_limit,
                                           lateral_option.kdist_output_limit);
    lateral_dist_pid_ptr_->setOutputRampRate(lateral_option.kdist_ramp_rate);

    lateral_lift_speed_pid_ptr_ = std::make_shared<MiniPID>(
        lateral_option.kup_speed_p, lateral_option.kup_speed_i,
        lateral_option.kup_speed_d);
    lateral_lift_speed_pid_ptr_->setOutputLimits(
        -lateral_option.kspeed_output_limit,
        lateral_option.kspeed_output_limit);
    lateral_lift_speed_pid_ptr_->setOutputRampRate(
        lateral_option.kspeed_ramp_rate);

    lateral_descent_speed_pid_ptr_ = std::make_shared<MiniPID>(
        lateral_option.kdown_speed_p, lateral_option.kdown_speed_i,
        lateral_option.kdown_speed_d);
    lateral_descent_speed_pid_ptr_->setOutputLimits(
        -lateral_option.kspeed_output_limit,
        lateral_option.kspeed_output_limit);
    lateral_descent_speed_pid_ptr_->setOutputRampRate(
        lateral_option.kspeed_ramp_rate);

    // 横移PID
    auto side_option = AgvData::get()->option_.forklift_option.sideshift_fork;
    side_dist_pid_ptr_ = std::make_shared<MiniPID>(
        side_option.kdist_p, side_option.kdist_i, side_option.kdist_d);
    side_dist_pid_ptr_->setOutputLimits(-side_option.kdist_output_limit,
                                        side_option.kdist_output_limit);
    side_dist_pid_ptr_->setOutputRampRate(side_option.kdist_ramp_rate);

    side_lift_speed_pid_ptr_ = std::make_shared<MiniPID>(
        side_option.kspeed_p, side_option.kspeed_i, side_option.kspeed_d);
    side_lift_speed_pid_ptr_->setOutputLimits(-side_option.kspeed_output_limit,
                                              side_option.kspeed_output_limit);
    side_lift_speed_pid_ptr_->setOutputRampRate(side_option.kspeed_ramp_rate);

    side_descent_speed_pid_ptr_ = std::make_shared<MiniPID>(
        side_option.kspeed_p, side_option.kspeed_i, side_option.kspeed_d);
    side_descent_speed_pid_ptr_->setOutputLimits(
        -side_option.kspeed_output_limit, side_option.kspeed_output_limit);
    side_descent_speed_pid_ptr_->setOutputRampRate(
        side_option.kspeed_ramp_rate);

    // 倾斜PID
    auto tilt_option = AgvData::get()->option_.forklift_option.tilt_fork;
    tilt_dist_pid_ptr_ = std::make_shared<MiniPID>(
        tilt_option.kdist_p, tilt_option.kdist_i, tilt_option.kdist_d);
    tilt_dist_pid_ptr_->setOutputLimits(-tilt_option.kdist_output_limit,
                                        tilt_option.kdist_output_limit);
    tilt_dist_pid_ptr_->setOutputRampRate(tilt_option.kdist_ramp_rate);

    tilt_speed_pid_ptr_ = std::make_shared<MiniPID>(
        tilt_option.kspeed_p, tilt_option.kspeed_i, tilt_option.kspeed_d);
    tilt_speed_pid_ptr_->setOutputLimits(-tilt_option.kspeed_output_limit,
                                         tilt_option.kspeed_output_limit);
    tilt_speed_pid_ptr_->setOutputRampRate(tilt_option.kspeed_ramp_rate);

    // 顶升PID
    auto nomove_pid = AgvData::get()->option_.jack_up_option.nomove_pid;
    // jackup_pallet_move_->setPID(nomove_pid.kp, nomove_pid.ki, nomove_pid.kd);
    jackup_pallet_move_ =
        std::make_shared<MiniPID>(nomove_pid.kp, nomove_pid.ki, nomove_pid.kd);
    jackup_pallet_move_->setOutputLimits(nomove_pid.output_limit);
    jackup_pallet_move_->setOutputRampRate(nomove_pid.ramp_rate);

    auto rotate_pid = AgvData::get()->option_.jack_up_option.rotate_pid;
    // jackup_pallet_rotate_->setPID(rotate_pid.kp, rotate_pid.ki,
    // rotate_pid.kd);
    jackup_pallet_rotate_ =
        std::make_shared<MiniPID>(nomove_pid.kp, nomove_pid.ki, nomove_pid.kd);
    jackup_pallet_rotate_->setOutputLimits(rotate_pid.output_limit);
    jackup_pallet_rotate_->setOutputRampRate(rotate_pid.ramp_rate);

    ActionInit();
  }

  ~AtomicAction() {}

  bool UpdateDiyConditionState(
      std::map<int, std::vector<Condition>> condition_map, AgvData *Get_info,
      cotek_msgs::action_cmd *msg, bool is_open_loop, bool is_error_map);

  bool CheckState(std::vector<Condition> condition_vec, AgvData *Get_info,
                  cotek_msgs::action_cmd *msg, bool is_open_loop,
                  bool is_error_map);

  void UpdateData(AgvData *Get_info) override;
  // return action progress =()%
  uint16_t FeedBack() override;
  uint16_t OpenLoopFeedBack() override;
  // return whether action done
  bool IsFinished() override;
  bool IsOpenLoopFinished() override;
  // release goal..
  void DataReset();
  void OpenLoopDataReset();

  void PalletStateProcess(uint8_t flag, AgvData *Get_info);

  const float GetForkMoveSpeed(const double &target_height,
                               const double &current_height,
                               const double &current_speed);

  const float GetLateralSpeed(const double &target_position,
                              const double &current_position,
                              const double &current_speed);

  const float GetSideMoveSpeed(const double &target_position,
                               const double &current_position,
                               const double &current_speed);

  const float GetForkTiltSpeed(const double &target_angle,
                               const double &current_angle,
                               const double &current_speed);

  void PublishRequestPalletCheck(double x, double y, double yaw);
  void PublishRequestUnloadDetect();
  void PublishRequestPalletBackLimit();
  void PublishRequestColumnPositionDetect(double x, double y, double yaw);

  bool DelayTime(double seconds);
  bool JackUpCheckPallet(bool use, AgvData *Get_info);
  void LiftSafeBreakControl(ros::Time time, cotek_msgs::action_cmd *msg,
                            AgvData *Get_info);

  void ActionInit();

  void Waiting(cotek_msgs::action_cmd *msg, AgvData *Get_info,
               bool is_open_loop, bool use_diy_finish, bool use_diy_error,
               std::map<int, std::vector<Condition>> finish_map,
               std::map<int, std::vector<Condition>> error_map);
  void Rest(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
            bool use_diy_finish, bool use_diy_error,
            std::map<int, std::vector<Condition>> finish_map,
            std::map<int, std::vector<Condition>> error_map);
  void Init(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
            bool use_diy_finish, bool use_diy_error,
            std::map<int, std::vector<Condition>> finish_map,
            std::map<int, std::vector<Condition>> error_map);
  void DelayAction(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                   bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                   std::map<int, std::vector<Condition>> finish_map,
                   std::map<int, std::vector<Condition>> error_map);

  void IoOperator(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                  bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                  std::map<int, std::vector<Condition>> finish_map,
                  std::map<int, std::vector<Condition>> error_map);

  void LiftLoad(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                std::map<int, std::vector<Condition>> finish_map,
                std::map<int, std::vector<Condition>> error_map);
  void UnLoad(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
              bool use_diy_finish, bool use_diy_error,
              std::map<int, std::vector<Condition>> finish_map,
              std::map<int, std::vector<Condition>> error_map);

  void OpenLiftLoad(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                    bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                    std::map<int, std::vector<Condition>> finish_map,
                    std::map<int, std::vector<Condition>> error_map);

  void ForkHeightMove(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                      bool is_open_loop, bool use_diy_finish,
                      bool use_diy_error,
                      std::map<int, std::vector<Condition>> finish_map,
                      std::map<int, std::vector<Condition>> error_map);
  void ForkTiltMove(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                    bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                    std::map<int, std::vector<Condition>> finish_map,
                    std::map<int, std::vector<Condition>> error_map);
  void ForkLateralMove(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                       bool is_open_loop, bool use_diy_finish,
                       bool use_diy_error,
                       std::map<int, std::vector<Condition>> finish_map,
                       std::map<int, std::vector<Condition>> error_map);
  void ForkSideMove(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                    bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                    std::map<int, std::vector<Condition>> finish_map,
                    std::map<int, std::vector<Condition>> error_map);
  void ForkCompensateForward(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                             bool is_open_loop, bool use_diy_finish,
                             bool use_diy_error,
                             std::map<int, std::vector<Condition>> finish_map,
                             std::map<int, std::vector<Condition>> error_map);
  void WaitingForkRest(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                       bool is_open_loop, bool use_diy_finish,
                       bool use_diy_error,
                       std::map<int, std::vector<Condition>> finish_map,
                       std::map<int, std::vector<Condition>> error_map);

  void Charge(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
              bool use_diy_finish, bool use_diy_error,
              std::map<int, std::vector<Condition>> finish_map,
              std::map<int, std::vector<Condition>> error_map);

  void CheckUPWeight(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                     bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                     std::map<int, std::vector<Condition>> finish_map,
                     std::map<int, std::vector<Condition>> error_map);
  void CheckDownWeight(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                       bool is_open_loop, bool use_diy_finish,
                       bool use_diy_error,
                       std::map<int, std::vector<Condition>> finish_map,
                       std::map<int, std::vector<Condition>> error_map);

  void PalletRotation(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                      bool is_open_loop, bool use_diy_finish,
                      bool use_diy_error,
                      std::map<int, std::vector<Condition>> finish_map,
                      std::map<int, std::vector<Condition>> error_map);
  void PalletNoMove(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                    bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                    std::map<int, std::vector<Condition>> finish_map,
                    std::map<int, std::vector<Condition>> error_map);
  void PalletZero(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                  bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                  std::map<int, std::vector<Condition>> finish_map,
                  std::map<int, std::vector<Condition>> error_map);
  void PalletCorrect(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                     bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                     std::map<int, std::vector<Condition>> finish_map,
                     std::map<int, std::vector<Condition>> error_map);

  void ColumnPositionDetect(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                            bool is_open_loop, bool use_diy_finish,
                            bool use_diy_error,
                            std::map<int, std::vector<Condition>> finish_map,
                            std::map<int, std::vector<Condition>> error_map);
  void PalletCenterDetect(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                          bool is_open_loop, bool use_diy_finish,
                          bool use_diy_error,
                          std::map<int, std::vector<Condition>> finish_map,
                          std::map<int, std::vector<Condition>> error_map);
  void UnloadDetect(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                    bool is_open_loop, bool use_diy_finish, bool use_diy_error,
                    std::map<int, std::vector<Condition>> finish_map,
                    std::map<int, std::vector<Condition>> error_map);
  void PalletBackLimit(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                       bool is_open_loop, bool use_diy_finish,
                       bool use_diy_error,
                       std::map<int, std::vector<Condition>> finish_map,
                       std::map<int, std::vector<Condition>> error_map);
  void QueryStorageInfo(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                        bool is_open_loop, bool use_diy_finish,
                        bool use_diy_error,
                        std::map<int, std::vector<Condition>> finish_map,
                        std::map<int, std::vector<Condition>> error_map);
  void ManualConfirm(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
                     bool use_diy_finish, bool use_diy_error,
                     std::map<int, std::vector<Condition>> finish_map,
                     std::map<int, std::vector<Condition>> error_map);
  void CallElevator(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
                    bool use_diy_finish, bool use_diy_error,
                    std::map<int, std::vector<Condition>> finish_map,
                    std::map<int, std::vector<Condition>> error_map);
  void ControlElevator(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
                       bool use_diy_finish, bool use_diy_error,
                       std::map<int, std::vector<Condition>> finish_map,
                       std::map<int, std::vector<Condition>> error_map);
  void ControlAutoDoor(cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
                       bool use_diy_finish, bool use_diy_error,
                       std::map<int, std::vector<Condition>> finish_map,
                       std::map<int, std::vector<Condition>> error_map);
  // void ControlAudioLevel(cotek_msgs::action_cmd *msg, AgvData *Get_info,
  // const bool is_open_loop); void LowPowerMode(cotek_msgs::action_cmd *msg,
  // AgvData *Get_info, const bool is_open_loop); void
  // PalletDetectBeforeUp(cotek_msgs::action_cmd *msg, AgvData *Get_info, const
  // bool is_open_loop); void PalletDetectBeforeDown(cotek_msgs::action_cmd
  // *msg, AgvData *Get_info, const bool is_open_loop);

  void ExecuteAction(AgvData *Get_info) override;
  void ExecuteOpenLoopAction(AgvData *Get_info) override;

  void PublishActionMsg() override;
  void PublishOpenLoopActionMsg() override;

  void UpdateInitParm() override;

  inline void ResetConditionMap() {
    finish_map_.clear();
    error_map_.clear();
  }

  inline void ResetOpConditionMap() {
    op_finish_map_.clear();
    op_error_map_.clear();
  }

 private:
  ros::Publisher atomic_action_pub_;
  ros::Publisher request_pallet_back_limit_pub_;
  ros::Publisher unload_detect_pub_;
  ros::Publisher request_pallet_pub_;
  ros::Publisher column_position_detect_pub_;
  ros::Publisher pallet_nomove_pub_;
  ros::Publisher load_state_pub_;
  ros::Publisher loads_pub_;
  ros::Publisher manual_confirm_pub_;
  ros::Publisher elevator_task_pub_;
  ros::Publisher autodoor_task_pub_;

  cotek_msgs::action_cmd atomic_action_msg_;

  std::map<uint32_t,
           std::function<void(cotek_msgs::action_cmd *, AgvData *, bool, bool,
                              bool, std::map<int, std::vector<Condition>>,
                              std::map<int, std::vector<Condition>>)>>
      action_map_;

  std::shared_ptr<MiniPID> heap_dist_pid_ptr_;
  std::shared_ptr<MiniPID> heap_lift_speed_pid_ptr_;
  std::shared_ptr<MiniPID> heap_descent_speed_pid_ptr_;

  std::shared_ptr<MiniPID> lateral_dist_pid_ptr_;
  std::shared_ptr<MiniPID> lateral_lift_speed_pid_ptr_;
  std::shared_ptr<MiniPID> lateral_descent_speed_pid_ptr_;

  std::shared_ptr<MiniPID> side_dist_pid_ptr_;
  std::shared_ptr<MiniPID> side_lift_speed_pid_ptr_;
  std::shared_ptr<MiniPID> side_descent_speed_pid_ptr_;

  std::shared_ptr<MiniPID> tilt_dist_pid_ptr_;
  std::shared_ptr<MiniPID> tilt_speed_pid_ptr_;

  std::vector<float> record_angle_;
  double static_angle_;
  // TODO:后续是否采用相对高度方式，or给出绝对高度
  double cmd_height_base_;
  double setting_speed_;

  double pallet_delta_y_;
  double column_delta_y_;

  std::shared_ptr<tf::TransformListener> shelf_tf_;

  std::shared_ptr<MiniPID> jackup_pallet_move_;
  std::shared_ptr<MiniPID> jackup_pallet_rotate_;

  bool up_tag_loss_;
  bool shelf_pose_vaild_;
  uint32_t pallet_nomove_flag_;
  double pallet_tar_theta_;
  double shelf_cur_theta_;

  std::map<int, std::vector<Condition>> finish_map_;
  std::map<int, std::vector<Condition>> error_map_;
  bool use_diy_finish_condition_;
  bool use_diy_error_condition_;

  std::map<int, std::vector<Condition>> op_finish_map_;
  std::map<int, std::vector<Condition>> op_error_map_;
  bool op_use_diy_finish_condition_;
  bool op_use_diy_error_condition_;

  bool needs_check_pallet_;

  std::map<std::string, Action> exec_action_;
  std::map<std::string, int> clear_action_;

  bool rt_delay_flag_;

  int load_state_;
};

}  // namespace cotek_action
#endif  // COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_PALLET_FORKLIFT_ACTION_H_
