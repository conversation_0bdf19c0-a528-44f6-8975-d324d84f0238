/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ROLLER_ACTION_H_
#define COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ROLLER_ACTION_H_
#include <ros/ros.h>
#include <tf/transform_listener.h>

#include <map>

#include "cotek_action/action_model/action_interface.h"
#include "cotek_msgs/roller_action.h"

namespace cotek_action {
class RollerAction : public ActionInterface {
 public:
  RollerAction()
      : timeout_(false), roller_nomove_flag_(0), acting_timeout_(false) {
    ros::NodeHandle nh;
    roller_action_pub_ = nh.advertise<cotek_msgs::roller_action>(
        cotek_topic::kRollerActionTopic, kTopicSendCacheSize);
    ActionInit();
  }

  void UpdateData(AgvData *Get_info) override;

  uint32_t FeedBack() override;

  bool IsFinished() override;

  void DataReset(cotek_msgs::roller_action *msg);

  void RollerStateProcess(uint8_t flag, AgvData *Get_info);

  void RollerCorrect(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void ActionNone(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void Rest(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void ControlAudioLevel(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void RollerNoMove(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void RollerIn(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void RollerOut(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void RollerRotate(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void Delay(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void RollerZero(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void MotorDisable(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void MotorEnable(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void ActionCharge(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void ActionOpenDoor(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void MotorClearAlarm(cotek_msgs::roller_action *msg, AgvData *Get_info);

  // roller action
  void ActionWaiting(cotek_msgs::roller_action *msg, AgvData *Get_info);

  // Low_Power_Mode
  void LowPowerMode(cotek_msgs::roller_action *msg, AgvData *Get_info);

  void MagneticSwitchDir(cotek_msgs::roller_action *msg, AgvData *Get_info);

  HinsonMlsData SelectMlsData(AgvData *Get_info);

  // Init action list
  void ActionInit();

  void ExecuteAction(AgvData *Get_info) override;

  void PublishActionMsg() override;

 private:
  ros::Publisher roller_action_pub_;
  ros::Time action_time_;
  bool doing_action_;
  cotek_msgs::roller_action msg_;
  std::map<int32_t, std::function<void(cotek_msgs::roller_action *, AgvData *)>>
      action_map_;
  bool timeout_;
  uint32_t roller_nomove_flag_;
  bool CheckRoller(bool use, AgvData *Get_info);
  bool CheckTimeout(bool use, double limit_time, AgvData *Get_info);
  bool IsResetNoMoveFlag(const AgvData *Get_info);
  ActionGoal newgoal_;
  ActionGoal lastgoal_;
  bool acting_timeout_;
};
}  // namespace cotek_action

#endif  // COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_MODEL_ROLLER_ACTION_H_
