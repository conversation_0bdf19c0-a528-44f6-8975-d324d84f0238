/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_CONFIG_H_
#define SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_CONFIG_H_
#include <ros/ros.h>

#include <fstream>
#include <map>
#include <sstream>
#include <string>

#include "cotek_action/cotek_action_enum.h"
#include "cotek_common/common.h"

namespace cotek_action {

class ActionConfig {
 public:
  ~ActionConfig() {}
  static ActionConfig& Instance() {
    static ActionConfig instance;
    return instance;
  }
  ActionConfig(const ActionConfig&) = delete;
  ActionConfig& operator=(const ActionConfig&) = delete;

  std::string GetUserName() {
    uid_t userid;
    struct passwd* pwd;
    userid = getuid();
    pwd = getpwuid(userid);
    return pwd->pw_name;
  }

  std::string GetConfigPath() { return user_path_; }
  std::string GetBasicConfigPath() { return user_path_ + "basic_config/"; }
  std::string GetMapPath() { return user_path_ + "map/"; }
  
  std::string GetConfig(cotek_config::ConfigType type) {
    std::string config_json_str;
    if (static_cast<uint8_t>(type) ==
        static_cast<uint8_t>(cotek_config::ConfigType::NONE)) {
      return std::string();
    }

    std::string path;
    if (type == cotek_config::ConfigType::ACTION_CONFIG) {
      path = GetBasicConfigPath() + std::string(cotek_config::kActionConfigPath);
    } else if (type == cotek_config::ConfigType::AGV_BASIC_CONFIG) {
      path = GetBasicConfigPath() + std::string(cotek_config::kAgvBasicConfigPath);
    } else if (type == cotek_config::ConfigType::RUNNER_CONFIG) {
      path = GetBasicConfigPath() + std::string(cotek_config::kRunnerConfigPath);
    } else {
      return "";
    }

    config_json_str = util::LocalService::GetStringFromFile(path);
    if (config_json_str.empty()) {
      LOG_ERROR_STREAM(path << " is empty.");
      return "";
    }
    return config_json_str;
  }

  bool SaveLocal(cotek_config::ConfigType type,
                 std::string data) {
    std::string path;
    if (type == cotek_config::ConfigType::ACTION_CONFIG) {
      path = GetBasicConfigPath() + std::string(cotek_config::kStorageConfigPath);
    } else {
      return false;
    }

    return util::LocalService::SaveStringToFile(path, data);
  }

 private:
  ActionConfig() {}

  // config文件 所在目录的前缀
  std::string user_path_ = "/home/" + GetUserName() + "/config/";
  // std::string user_path_ = "/home/<USER>/config/";
};
}  // namespace cotek_action

#endif  // SRC_COTEK_ACTION_INCLUDE_COTEK_ACTION_ACTION_CONFIG_H_
