/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef SRC_COTEK_ACTION_INCLUDE_NODE_COTEK_ACTION_NODE_H_
#define SRC_COTEK_ACTION_INCLUDE_NODE_COTEK_ACTION_NODE_H_
#include <ros/package.h>
#include <ros/ros.h>

#include <memory>
#include <string>
#include <vector>

#include "cotek_action/action_config.h"
#include "cotek_action/action_server.h"
#include "cotek_action/data_center.h"
#include "cotek_action/open_loop_action_server.h"
#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/cotek_node_name.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/node_diagnostic_manager.h"
#include "cotek_msgs/node_diagnostic.h"
#include "cotek_msgs/update_basic_config.h"
#include "cotek_msgs/update_config.h"
#include "cotek_msgs/manual_confirm.h"
#include "std_msgs/Int32.h"

namespace {
constexpr uint32_t kMessageCacheSize = 2;
constexpr char kActionServer[] = "actionServer";
constexpr char kOpenLoopActionServer[] = "openLoopActionServer";

using ActionNodeStatus = cotek_diagnostic::ActionNodeStatus;
using NodeStatusManager = cotek_diagnostic::NodeStatusManager<ActionNodeStatus>;
using n_excetion = nlohmann::json_abi_v3_11_2::detail::exception;
}  // namespace

class CotekActionNode final {
 public:
  explicit CotekActionNode(ros::NodeHandle* nh)
      : action_server_ptr_(nullptr), open_loop_action_server_ptr_(nullptr) {
    node_status_manager_ptr_ = std::make_shared<NodeStatusManager>();
    timer_ = nh->createTimer(ros::Duration(1 / 20.),
                             &CotekActionNode::NodeDiagnostic, this);
    node_diagnostic_pub_ = nh->advertise<cotek_msgs::node_diagnostic>(
        cotek_topic::kActionDiagnosticTopic, 10);
    update_action_config_service_server_ =
        nh->advertiseService(cotek_config::kUpdateActionConfigService,
                             &CotekActionNode::UpdateActionConfig, this);
    web_update_action_service_server_ =
        nh->advertiseService(cotek_services::kUpdateActionConfigService,
                             &CotekActionNode::WebUpdateActionConfig, this);
    // update_common_config_service_server_ =
    //     nh->advertiseService(cotek_services::kUpdateBasicConfigService,
    //                          &CotekActionNode::UpdateCommonConfig, this);

    subscribers_.emplace_back(nh->subscribe<cotek_msgs::update_basic_config>(
        cotek_topic::kUpdateBasicConfigTopic, kTopicReciveCacheSize,
        boost::bind(&CotekActionNode::UpdateBasicConfig, this, _1)));

    init_ = false;
  }
  ~CotekActionNode() {}

  void NodeDiagnostic(const ros::TimerEvent& e) {
    cotek_msgs::node_diagnostic msg;
    msg.header.stamp = ros::Time::now();
    for (auto status : node_status_manager_ptr_->GetNodeStatus()) {
      msg.status.push_back(static_cast<int>(status.second));
    }
    node_diagnostic_pub_.publish(msg);
    node_status_manager_ptr_->PeriodClearNodeStatus(2.0);
  }

  void NodeRun() {
    action_server_ptr_->ServerStart();
    open_loop_action_server_ptr_->ServerStart();
  }

  bool Init(ros::NodeHandle* nh) {
    if (init_) return true;
    LOG_INFO_ONCE("---------------cotek_action option---------------");
    std::string basic_config_file =
        cotek_action::ActionConfig::Instance().GetConfig(
            cotek_config::ConfigType::RUNNER_CONFIG);
    cotek_action::AgvBasicOption basic_option;
    if (!LoadBasicOption(basic_config_file, &basic_option)) {
      LOG_INFO_ONCE("action_node load basic_config failed.");
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::CONFIG_ERROR);
      return false;
    }

    std::string action_config_file =
        cotek_action::ActionConfig::Instance().GetConfig(
            cotek_config::ConfigType::ACTION_CONFIG);
    cotek_action::AgvActionOption action_option;
    std::string json_str = "";
    if (!LoadActionOption(action_config_file, &action_option, json_str)) {
      LOG_INFO_ONCE("action_node load action_config failed.");
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::CONFIG_ERROR);
      return false;
    }

    action_server_ptr_ = std::make_shared<cotek_action::ActionServer>(
        kActionServer, node_status_manager_ptr_);
    open_loop_action_server_ptr_ =
        std::make_shared<cotek_action::OpenLoopActionServer>(
            kOpenLoopActionServer, node_status_manager_ptr_);

    action_server_ptr_->UpdateActionOption(action_option);
    action_server_ptr_->UpdateBasicOption(basic_option);

    {  // 话题订阅
      // 自检状态
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::self_check>(
          cotek_topic::kSelfCheckTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleShelfCheckMessage, this, _1)));
      // cmd
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::move_cmd>(
          cotek_topic::kMoveCmdTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleMoveCmdMessage, this, _1)));
      // feedback
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::move_feedback>(
          cotek_topic::kMoveFeedbackTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleMoveFeedbackMessage, this, _1)));

      // 手自动IO
      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kManualTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleManualIoStateMessage, this, _1)));
      // 避障IO
      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kSafetyIoStateTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleSafetyIoStateMessage, this, _1)));
      // 充电继电器IO
      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kChargeIoStateTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleChargeIoStateMessage, this, _1)));
      // 挡板信号
      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kPalletIoStateTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandlePalletIoStateMessage, this, _1)));
      // 上下限位信号
      subscribers_.emplace_back(nh->subscribe<std_msgs::Int32>(
          cotek_topic::kLimitSwitchStateTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleLimitSwitchStateMessage, this,
                      _1)));
      // jackup_io_state
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::jack_up_io_state>(
          cotek_topic::kJackUpIoTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleJackupIoMessage, this, _1)));
      // pallet info
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::pallet_act_info>(
          cotek_topic::kPalletActInfoTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandlePalletActInfoMessage, this, _1)));

      // pgv100 up
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::pgv100_feedback>(
          install_device_name_list::kPGV100UpInstallDriverName,
          kMessageCacheSize,
          boost::bind(&CotekActionNode::HandlePgv100UpMessage, this, _1)));
      subscribers_.emplace_back(
          nh->subscribe<hikvs_mv_im5005_ros::hikvs_mv_im5005>(
              install_device_name_list::kHikvsQrUpDriverName,
              kTopicReciveCacheSize,
              boost::bind(&CotekActionNode::HandleHikvsQrCodeMessage, this,
                          _1)));
      subscribers_.emplace_back(
          nh->subscribe<hikvs_mv_im5005_ros::hikvs_mv_im5005>(
              install_device_name_list::kHikvsQrDownDriverName,
              kTopicReciveCacheSize,
              boost::bind(&CotekActionNode::HandleHikQrDownCodeMessage, this,
                          _1)));

      // battery data
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::battery_feedback>(
          cotek_topic::kBatteryTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleBatteryMessage, this, _1)));
      // weight
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::weighing_feedback>(
          cotek_topic::kWeightTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleWeightMessage, this, _1)));
      // wit61_motion_feedback
      subscribers_.emplace_back(nh->subscribe<sensor_msgs::Imu>(
          cotek_topic::kImuTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleImuMessage, this, _1)));
      // hight_encoder
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::wire_encoder_feedback>(
              cotek_topic::kHeightEncoderTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandleHightEncoderMessage, this,
                          _1)));
      // lateral_encoder
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::wire_encoder_feedback>(
              cotek_topic::kLateralEncoderTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandleLateralEncoderMessage, this,
                          _1)));
      // vertical_encoder
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::wire_encoder_feedback>(
              cotek_topic::kSideEncoderTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandleSideEncoderMessage, this,
                          _1)));
      // io
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::io_feedback>(
          cotek_topic::kIoTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleIoMessage, this, _1)));

      // pallet_detect_state
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::pallet_detect_state>(
          cotek_topic::kPalletDetectStateTopic, kMessageCacheSize,
          boost::bind(&CotekActionNode::HandlePalletDetectFeedback, this, _1)));
      // pallet_center_feedback
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::pallet_center_feedback>(
              cotek_topic::kPalletCenterTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandlePalletCenterFeedback, this,
                          _1)));
      // pallet_back_limit_feedback
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::pallet_back_limit_feedback>(
              cotek_topic::kPalletBackLimitFeedbackTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandlePalletBackLimitFeedback, this,
                          _1)));
      // 放货检测
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::unload_detect_feedback>(
              cotek_topic::kUnloadDetectTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandleUnloadDetectFeedback, this,
                          _1)));
      // 货架检测
      subscribers_.emplace_back(
          nh->subscribe<cotek_msgs::column_detect_feedback>(
              cotek_topic::kColumnDetectTopic, kMessageCacheSize,
              boost::bind(&CotekActionNode::HandleColumnDetectFeedback, this,
                          _1)));

      // 调度数据清除
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::ds_clear_data>(
          "clearData", kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleClearDataFeedback, this, _1)));

      // 电梯
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::elevator_feedback>(
          "elevator", kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleElevatorFeedback, this, _1)));

      // 自动门
      subscribers_.emplace_back(nh->subscribe<cotek_msgs::elevator_feedback>(
          "autodoor", kMessageCacheSize,
          boost::bind(&CotekActionNode::HandleAutoDoorFeedback, this, _1)));

      manual_confirm_server_ =
          nh->advertiseService("manualConfirm",
                              &CotekActionNode::HandleManualConfirm, this);
    }

    LOG_INFO("Init action server.");
    if (!action_server_ptr_->Init()) return false;
    if (!open_loop_action_server_ptr_->Init()) return false;

    NodeRun();
    init_ = true;
    return true;
  }

 private:
  bool HandleManualConfirm(cotek_msgs::manual_confirm::Request& req,
                           cotek_msgs::manual_confirm::Response& res) {
    static ros::Time last_confirm_time = ros::Time::now() - ros::Duration(10);
    if (ros::Time::now() - last_confirm_time > ros::Duration(2.0)) {
      agv_data_ptr_->AddManualConfirm(req.data);
      last_confirm_time = ros::Time::now();
    }
    res.msg = "";
    return true;
  }

  bool UpdateActionConfig(cotek_msgs::update_action_config::Request& req,
                          cotek_msgs::update_action_config::Response& res) {
    LOG_DEBUG("update action config ...");

    std::string action_config_file =
        cotek_action::ActionConfig::Instance().GetConfig(
            cotek_config::ConfigType::ACTION_CONFIG);
    if (action_server_ptr_) {
      cotek_action::AgvActionOption option;
      std::string json_str = "";
      if (!LoadActionOption(action_config_file, &option, json_str)) {
        LOG_INFO_ONCE("update action config failed.");
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::CONFIG_ERROR);
        return false;
      }
      action_server_ptr_->UpdateActionOption(option);
    } else {
      LOG_INFO_ONCE("no action server.");
      return false;
    }

    LOG_INFO_ONCE("update action config succeed.");
    return true;
  }

  bool WebUpdateActionConfig(cotek_msgs::update_config::Request& req,
                             cotek_msgs::update_config::Response& res) {
    LOG_INFO("update action option from web-setting.");

    nlohmann::ordered_json json_data;
    try {
      json_data = nlohmann::ordered_json::parse(req.data);
    } catch (const n_excetion& ex) {
      std::cout << (ex.what()) << std::endl;
      LOG_INFO("json format error.");
      res.status = cotek_config::kConfigError;
      return false;
    }

    std::string action_config_file =
        cotek_action::ActionConfig::Instance().GetConfig(
            cotek_config::ConfigType::ACTION_CONFIG);
    if (action_server_ptr_) {
      cotek_action::AgvActionOption option;
      // prase &check
      if (!LoadActionOption(action_config_file, &option, req.data)) {
        LOG_INFO_ONCE("web update action config failed");
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::CONFIG_ERROR);
        res.status = cotek_config::kConfigError;
        return false;
      }
      // write
      if (!cotek_action::ActionConfig::Instance().SaveLocal(
              cotek_config::ConfigType::ACTION_CONFIG, json_data.dump(4))) {
        LOG_WARN("Save action config failed.");
      }

      // update using option
      action_server_ptr_->UpdateActionOption(option);
      res.status = cotek_config::kConfigOk;
    } else {
      LOG_INFO_ONCE("no action server.");
      return false;
    }

    LOG_INFO_ONCE("web update action config succeed.");
    return true;
  }

  // todo: update common config
  bool UpdateBasicConfig(
      const cotek_msgs::update_basic_config::ConstPtr& feedback) {
    LOG_INFO("action node update common option.");

    std::string basic_config_file =
        cotek_action::ActionConfig::Instance().GetConfig(
            cotek_config::ConfigType::RUNNER_CONFIG);
    if (action_server_ptr_) {
      cotek_action::AgvBasicOption option;
      LoadBasicOption(basic_config_file, &option);
      action_server_ptr_->UpdateBasicOption(option);
    } else {
      LOG_INFO_ONCE("no action server.");
      return false;
    }

    LOG_INFO_ONCE("action node update common config succeed.");
    return true;
  }

  bool LoadBasicOption(std::string file, cotek_action::AgvBasicOption* option);
  bool LoadActionOption(std::string path, cotek_action::AgvActionOption* option,
                        std::string json_str);

  void HandleShelfCheckMessage(const cotek_msgs::self_check::ConstPtr& msg) {
    agv_data_ptr_->AddShelfCheckState(msg);
  }
  void HandleMoveCmdMessage(const cotek_msgs::move_cmd::ConstPtr& msg) {
    agv_data_ptr_->AddMoveCmd(msg);
  }
  void HandleMoveFeedbackMessage(
      const cotek_msgs::move_feedback::ConstPtr& msg) {
    agv_data_ptr_->AddMoveFeedback(msg);
  }

  void HandleManualIoStateMessage(const std_msgs::Int32::ConstPtr& state) {
    agv_data_ptr_->AddAgvManualIoState(state);
  }
  void HandleSafetyIoStateMessage(const std_msgs::Int32::ConstPtr& state) {
    agv_data_ptr_->AddSafetyIoState(state);
  }
  void HandleChargeIoStateMessage(const std_msgs::Int32::ConstPtr& state) {
    agv_data_ptr_->AddChargeIoState(state);
  }
  void HandlePalletIoStateMessage(const std_msgs::Int32::ConstPtr& state) {
    agv_data_ptr_->AddPalletIoState(state);
  }
  void HandleLimitSwitchStateMessage(const std_msgs::Int32::ConstPtr& state) {
    agv_data_ptr_->AddLimitSwitchState(state);
  }
  void HandleJackupIoMessage(
      const cotek_msgs::jack_up_io_state::ConstPtr& state) {
    agv_data_ptr_->AddJackUpIo(state);
  }
  void HandlePalletActInfoMessage(
      const cotek_msgs::pallet_act_info::ConstPtr& state) {
    agv_data_ptr_->AddPalletActInfo(state);
  }

  void HandlePgv100UpMessage(
      const cotek_msgs::pgv100_feedback::ConstPtr& data) {
    agv_data_ptr_->AddPgv100UpData(data);
  }
  void HandleHikvsQrCodeMessage(
      const hikvs_mv_im5005_ros::hikvs_mv_im5005::ConstPtr& data) {
    agv_data_ptr_->AddPalletHikQrData(data);
  }
  void HandleHikQrDownCodeMessage(
      const hikvs_mv_im5005_ros::hikvs_mv_im5005::ConstPtr& data) {
    agv_data_ptr_->AddBaseHikQrData(data);
  }

  void HandleBatteryMessage(
      const cotek_msgs::battery_feedback::ConstPtr& data) {
    agv_data_ptr_->AddBatteryData(data);
  }
  void HandleWeightMessage(
      const cotek_msgs::weighing_feedback::ConstPtr& data) {
    agv_data_ptr_->AddWeigtData(data);
  }
  void HandleImuMessage(const sensor_msgs::Imu::ConstPtr& data) {
    agv_data_ptr_->AddImuData(data);
  }
  void HandleHightEncoderMessage(
      const cotek_msgs::wire_encoder_feedback::ConstPtr& data) {
    agv_data_ptr_->AddHightEncoderData(data);
  }
  void HandleLateralEncoderMessage(
      const cotek_msgs::wire_encoder_feedback::ConstPtr& data) {
    agv_data_ptr_->AddLateralEncoderData(data);
  }
  void HandleSideEncoderMessage(
      const cotek_msgs::wire_encoder_feedback::ConstPtr& data) {
    agv_data_ptr_->AddSideEncoderData(data);
  }
  void HandleIoMessage(const cotek_msgs::io_feedback::ConstPtr& data) {
    agv_data_ptr_->AddIoData(data);
  }

  void HandlePalletDetectFeedback(
      const cotek_msgs::pallet_detect_state::ConstPtr& feedback) {
    agv_data_ptr_->AddPalletDetectFeedback(feedback);
  }
  void HandlePalletCenterFeedback(
      const cotek_msgs::pallet_center_feedback::ConstPtr& feedback) {
    agv_data_ptr_->AddPalletCenterFeedback(feedback);
  }
  void HandlePalletBackLimitFeedback(
      const cotek_msgs::pallet_back_limit_feedback::ConstPtr& feedback) {
    agv_data_ptr_->AddPalletBackLimitFeedback(feedback);
  }
  void HandleUnloadDetectFeedback(
      const cotek_msgs::unload_detect_feedback::ConstPtr& feedback) {
    agv_data_ptr_->AddUnloadDetectFeedback(feedback);
  }
  void HandleColumnDetectFeedback(
      const cotek_msgs::column_detect_feedback::ConstPtr& feedback) {
    agv_data_ptr_->AddColumnDetectFeedback(feedback);
  }

  void HandleClearDataFeedback(const cotek_msgs::ds_clear_data::ConstPtr& feedback) {
    agv_data_ptr_->AddClearDataFeedback(feedback);
  }

  void HandleElevatorFeedback(const cotek_msgs::elevator_feedback::ConstPtr& feedback) {
    agv_data_ptr_->AddElevatorFeedback(feedback);
  }
  
  void HandleAutoDoorFeedback(const cotek_msgs::elevator_feedback::ConstPtr& feedback) {
    agv_data_ptr_->AddAutoDoorFeedback(feedback);
  }

  ros::Timer timer_;
  std::vector<ros::Subscriber> subscribers_;
  ros::Publisher node_diagnostic_pub_;
  ros::ServiceServer update_action_config_service_server_;
  ros::ServiceServer web_update_action_service_server_;
  ros::ServiceServer update_common_config_service_server_;
  ros::ServiceServer manual_confirm_server_;

  std::shared_ptr<cotek_action::AgvData> agv_data_ptr_;
  std::shared_ptr<cotek_action::ActionServer> action_server_ptr_;
  std::shared_ptr<cotek_action::OpenLoopActionServer>
      open_loop_action_server_ptr_;
  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;

  bool init_;
};

#endif  // SRC_COTEK_ACTION_INCLUDE_NODE_COTEK_ACTION_NODE_H_
