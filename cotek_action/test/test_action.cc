/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include <actionlib/client/simple_action_client.h>
#include <actionlib/client/terminal_state.h>
#include <ros/ros.h>
#include "cotek_msgs/agv_actionAction.h"
#include "cotek_msgs/pallet_fork_io_state.h"
#include "data_center.h"
#include "cotek_action/cotek_action_enum.h"


cotek_action::Forklift_Pallet fork_pallet = {0};
static uint8_t flag = 1;

void ActionTestCB(
    const cotek_msgs::pallet_fork_io_state::ConstPtr& pallet_io_state) {
  fork_pallet.fork_up_down_state = pallet_io_state->fork_up_down_state;
  LOG_INFO("Subscriber fork_pallet up down state: %d ",
           fork_pallet.fork_up_down_state);
}

void doneCb(const actionlib::SimpleClientGoalState& state,
            const cotek_msgs::agv_actionResultConstPtr& result) {
  LOG_INFO("Finished in state [%s]", state.toString().c_str());
  LOG_INFO("Is Finished: %i", result->is_finish);

  ros::Duration(3, 0).sleep();
  flag = (flag == 1) ? 2 : 1;
}

void activeCb() { LOG_INFO("Goal just went active"); }
void feedbackCb(const cotek_msgs::agv_actionFeedbackConstPtr& feedback) {
  LOG_INFO("Got Feedback percent %d", feedback->percent);
}

int main(int argc, char** argv) {
  ros::init(argc, argv, "test_action");
  ros::NodeHandle nh;
  ros::Rate rate(10);
  actionlib::SimpleActionClient<cotek_msgs::agv_actionAction> action_client(
      "actionserver", true);

  LOG_INFO("Waiting for action server to start.");
  action_client.waitForServer();  // will wait for infinite time

  ros::Subscriber subscriber_ =
      nh.subscribe("pallet_fork_io_state", 100, &ActionTestCB);

  LOG_INFO("Action server started, sending goal.");
  cotek_msgs::agv_actionGoal goal;

  while (ros::ok()) {
    switch (flag) {
      case 1:
        // LOG_DEBUG("Step 1............................................");
        // goal.audio_control_type = 1;
        // goal.audio_control_level = 15;  // 0-30
        goal.action_type = static_cast<uint32_t>(
            cotek_action::Forklift_Action_Type::PALLET_FORK_UP);
        break;
      case 2:
        // LOG_DEBUG("Step 2............................................");
        // goal.audio_control_type = 2;
        // goal.audio_control_level = 15;  // 0-30
        goal.action_type = static_cast<uint32_t>(
            cotek_action::Forklift_Action_Type::PALLET_FORK_DOWN);
        break;
      case 3:
        break;
    }
    static uint8_t i = 0;
    if (flag != i) {
      LOG_INFO("..........Send goal............");
      action_client.sendGoal(goal, &doneCb, &activeCb, &feedbackCb);
      i = flag;
    }

    // bool finished_before_timeout =
    // action_client.waitForResult(ros::Duration(30.0));

    // if (finished_before_timeout)
    // {
    //     actionlib::SimpleClientGoalState state = action_client.getState();
    //     LOG_INFO("Action finished: %s",state.toString().c_str());
    //     action_client.cancelGoal();
    //     flag = 2;
    // }
    // else
    //     LOG_INFO("Action did not finish before the time out.");
    static uint8_t cnt = 0;
    cnt++;
    LOG_INFO("Test action Running ... %d", cnt);
    // ros::spin();
    rate.sleep();
  }

  return 0;
}
