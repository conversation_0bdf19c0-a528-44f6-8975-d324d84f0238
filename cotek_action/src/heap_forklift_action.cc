/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_model/heap_forklift_action.h"

#include <fstream>
#include <iostream>

#include "cotek_action/action_config.h"
#include "cotek_common/common.h"
#include "cotek_common/cotek_protocal.h"
namespace cotek_action {

uint32_t HeapForkliftAction::load_weight_base_ = 0;
bool HeapForkliftAction::close_check_weight_ = false;
bool HeapForkliftAction::check_fork_state_ = true;
bool load_weight_flag = false;

void HeapForkliftAction::DataReset() {
  AgvData::get()->Reset();
  ResetDelayFlag();
  ResetDoingAction();
  ResetLastSpeed();
}

uint32_t HeapForkliftAction::FeedBack() {
  return AgvData::get()->state_.percent;
}

void HeapForkliftAction::UpdateData(AgvData *Get_info) {
  // to do ..
}

bool HeapForkliftAction::IsFinished() {
  return AgvData::get()->state_.is_finish;
}

bool HeapForkliftAction::DelayTime(double second) {
  if (!delay_flag_) {
    delay_flag_ = true;
    delay_time_start_ = ros::Time::now();
  } else if (ros::Time::now() - delay_time_start_ < ros::Duration(second)) {
    LOG_INFO_THROTTLE(0.5, "delay time...");
    return false;
  } else {
    return true;
  }
}

// 双闭环PID调节，外环调速，内环调加速度，输出增量
const float HeapForkliftAction::GetForkMoveSpeed(const double &target_height,
                                                 const double &current_height,
                                                 const double &current_speed) {
  auto target_speed =
      dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed =
      target_height - current_height > 0
          ? lift_speed_pid_ptr_->getOutputFromDiff(target_speed - current_speed)
          : descent_speed_pid_ptr_->getOutputFromDiff(target_speed -
                                                      current_speed);
  LOG_INFO_STREAM("target_speed: " << target_speed
                                   << ", current speed: " << current_speed
                                   << ", delta_speed: " << delta_speed);
  return delta_speed;
}

// 卸载调速，分段PID
const float HeapForkliftAction::GetForkMoveSpeed(const double &target_height,
                                                 const double &current_height,
                                                 const double &current_speed,
                                                 bool bl) {
  auto target_speed =
      dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed =
      unload_speed_pid_ptr_->getOutputFromDiff(target_speed - current_speed);
  LOG_INFO_STREAM("target_speed: " << target_speed
                                   << ", current speed: " << current_speed
                                   << ", delta_speed: " << delta_speed);
  return delta_speed;
}

// No.0
void HeapForkliftAction::Waiting(cotek_msgs::forklift_action *msg,
                                 AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;
  Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
}

void HeapForkliftAction::ControlAudioLevel(cotek_msgs::forklift_action *msg,
                                           AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "======== HeapFork:Doing Action----Control_Audio_Level %d=========",
      Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  audio_level_ = Get_info->goal_.action_value;
  audio_level_dispatch_ = true;
  Get_info->SetStateSucceed();
  DataReset();
}

void HeapForkliftAction::Rest(cotek_msgs::forklift_action *msg,
                              AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

// No.1
void HeapForkliftAction::LowPowerMode(cotek_msgs::forklift_action *msg,
                                      AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift:Doing Action----Low_Power_Mode %d=========",
                Get_info->goal_.action_type);
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static uint8_t cnt = 0;
  cnt++;
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
  msg->atomic_action_value = 0;
  Get_info->state_.percent = cnt;
  if (cnt == 100) {
    cnt = 0;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Low_Power_Mode Set Done!!!");
    LOG_INFO_STREAM(" Done action: Low_Power_Mode");
    DataReset();
  }
}

void HeapForkliftAction::CheckUPWeight(cotek_msgs::forklift_action *msg,
                                       AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          Get_info->option_.forklift_option.fork_common_option.check_weigh &&
          DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Check_UP_Weight =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto heap_option = Get_info->option_.forklift_option.fork_common_option;

  // 若使用重量检测
  if (heap_option.check_weigh && pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }
  // 不使用重量检测
  if (!heap_option.check_weigh) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

  LOG_DEBUG_STREAM("weight: " << pallet_status.weigh);

  // 抬货重量检测， 并记录抬货重量
  if (heap_option.check_weigh) {
    // 抬货完成等待1s，静等称重读数稳定，记录货物重量，以供掉货检测、卸货使用
    if (load_weight_flag && DelayTime(1.0)) {
      load_weight_base_ = pallet_status.weigh;
      load_state_ = LoadState::HAVE_LOAD;
      close_check_weight_ = true;
      LOG_INFO_STREAM(
          "forklift up weight checked succeed!!!  load_weight_base: "
          << load_weight_base_);

      // 发送调度重量信息
      {
        cotek_msgs::agv_info_response response;
        response.type = cotek_protocal::msg_type::kLoadInfoEvent;

        json11::Json load_info = json11::Json::object{
            {"time", common::GetCurrentTime<std::string>()},
            {"orderId", Get_info->goal_.order_id},
            {"taskId", Get_info->goal_.task_id},
            {"loadId", std::string("null")},
            {"weight", static_cast<double>(pallet_status.weigh)}};

        std::string info = load_info.dump();

        LOG_INFO_STREAM(info);

        response.data = info;
        // QOS2表示用http传输
        response.qos = static_cast<uint8_t>(cotek_protocal::Qos::QOS2);
        agv_info_pub_.publish(response);
      }

      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      load_weight_flag = false;
      Get_info->SetStateSucceed();
      DataReset();
      PublishWeightLoadingBase();

    } else if (!delay_flag_) {
      LOG_WARN_STREAM_THROTTLE(1, "checking up weight error.\nweight: "
                                      << pallet_status.weigh
                                      << ", kload_weighing_threshold: "
                                      << heap_option.kload_weighing_threshold);

      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
      load_state_ = LoadState::NO_LOAD;
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      return;
    }
  }
}

void HeapForkliftAction::CheckDownWeight(cotek_msgs::forklift_action *msg,
                                         AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          Get_info->option_.forklift_option.fork_common_option.check_weigh &&
          DoingAction() == false,
      "========= Forklift:Doing "
      "Action----Heap_Fork_Check_Down_Weight =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto heap_option = Get_info->option_.forklift_option.fork_common_option;

  // 使用重量检测
  if (heap_option.check_weigh && pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }
  // 不使用重量检测
  if (!heap_option.check_weigh) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

  LOG_DEBUG_STREAM("weight: " << pallet_status.weigh);

  // 放货重量检测
  if (heap_option.check_weigh) {
    // 卸货重量检测
    if (pallet_status.weigh < heap_option.kload_weighing_threshold) {
      load_weight_base_ = 0;
      close_check_weight_ = false;
      load_state_ = LoadState::NO_LOAD;
      LOG_INFO("forklift down weight checked succeed!!!");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
      PublishWeightLoadingBase();
    } else {
      LOG_WARN_STREAM_THROTTLE(1, "checking up weight error.\nweight: "
                                      << pallet_status.weigh
                                      << ", kload_weighing_threshold: "
                                      << heap_option.kload_weighing_threshold);

      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
      load_state_ = LoadState::HAVE_LOAD;
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      return;
    }
  }
}

// 堆高车放货检测
void HeapForkliftAction::UnloadDetect(cotek_msgs::forklift_action *msg,
                                      AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_UnloadDetect =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;

  PublishRequestUnloadDetect();

  auto &unload_detect = Get_info->agvdata_.unload_detect_data;

  if ((ros::Time::now() - unload_detect.time < ros::Duration(2.0) &&
       unload_detect.permit_unload)) {
    LOG_INFO("Permit unload!!!");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    if (ros::Time::now() - GetActionStartTime() > ros::Duration(3.0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::PALLET_DETECT_ERROR);
    }
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->state_.is_finish = false;
  }
}

// 堆高车抬货
void HeapForkliftAction::HeapLiftLoad(cotek_msgs::forklift_action *msg,
                                      AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Lift_Load =========");

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;
  double fork_speed = pallet_status.lift_velocity;
  auto error = pallet_status.hight_error_code;
  ros::Time height_time = pallet_status.height_stamp;
  auto heap_option = Get_info->option_.forklift_option.height_fork;
  auto height_upper_limit = heap_option.kup_limit;
  auto cmd_height = cmd_height_base_ + heap_option.kup_value;
  cmd_height =
      cmd_height > height_upper_limit ? height_upper_limit : cmd_height;
  auto delta_dist = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual_height: " << height);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 抬货带托盘检测
  if (0 == pallet_status.fork_pallet_state &&
      Get_info->option_.forklift_option.fork_common_option.check_pallet) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    LOG_WARN_STREAM_COND(
        Get_info->option_.enable_local_debug &&
            (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
             100) % 100 ==
                0,
        " EXCEPTION: Have NO PALLET !!!");
    return;
  }

  // 编码器错误 无法执行抬降
  if (error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 拉线编码器到指定高度
  if (delta_dist < heap_option.kup_offset) {
    // 完成
    LOG_INFO("heap fork arrived");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    load_weight_flag = true;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    //  抬
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
    setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
    setting_speed_ = math::Clamp(
        setting_speed_, static_cast<double>(heap_option.kup_min_speed),
        static_cast<double>(heap_option.kup_max_speed));
    msg->atomic_action_value = std::fabs(setting_speed_);
    LOG_INFO("heap fork up, input ratio: %d",
             static_cast<int>(msg->atomic_action_value));
    Get_info->state_.is_finish = false;
  }
}

// 堆高车卸货
void HeapForkliftAction::HeapUnLoad(cotek_msgs::forklift_action *msg,
                                    AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Un_Load =========");
  close_check_weight_ = false;
  PublishWeightLoadingBase();

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;
  double fork_speed = pallet_status.lift_velocity;
  auto error = pallet_status.hight_error_code;
  ros::Time height_time = pallet_status.height_stamp;
  auto heap_option = Get_info->option_.forklift_option.height_fork;
  auto height_upper_limit = heap_option.kup_limit;
  auto cmd_height = cmd_height_base_ - heap_option.kdown_value;
  cmd_height =
      cmd_height > height_upper_limit ? height_upper_limit : cmd_height;
  auto delta_dist = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual height: " << height);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 拉线编码器下降到最低点或降到指定高度
  if ((delta_dist > -1. * heap_option.kdown_offset) ||
      (height < heap_option.kdown_limit)) {
    // 完成
    LOG_INFO("heap fork arrived");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    //  降
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
    setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
    setting_speed_ = math::Clamp(
        setting_speed_, -static_cast<double>(heap_option.kdown_max_speed),
        -static_cast<double>(heap_option.kdown_min_speed));
    msg->atomic_action_value = std::fabs(setting_speed_);
    LOG_INFO("heap fork down, input ratio: %d",
             static_cast<int>(msg->atomic_action_value));
    Get_info->state_.is_finish = false;
  }
}

// No.4
void HeapForkliftAction::HeapForkMove(cotek_msgs::forklift_action *msg,
                                      AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Heap_Fork_Move =========");

  auto fork_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = fork_status.height;
  float fork_speed = fork_status.lift_velocity;
  auto error = fork_status.hight_error_code;
  ros::Time height_time = fork_status.height_stamp;
  auto heap_option = Get_info->option_.forklift_option.height_fork;
  auto height_lower_limit = heap_option.kdown_limit;
  auto height_upper_limit = heap_option.kup_limit;
  auto cmd_height = math::Meter2CMillimeter(Get_info->goal_.action_value);
  cmd_height =
      cmd_height > height_upper_limit ? height_upper_limit : cmd_height;
  auto delta_dist = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual_height: " << height);

  // 动作开始只进行一次上升、下降标志判定
  // if (DoingAction() == false) {
  up_down_flag_ = cmd_height >= height ? true : false;
  // }

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 拉线编码器错误或掉帧，无法执行抬降
  if (error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 堆高车上限位触发，无法执行抬降
  if (AgvData::get()->agvdata_.forklift_pallet_state.fork_up_down_state == 2) {
    LOG_WARN_THROTTLE(1, "height fork reach uplimit!!!");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  if (up_down_flag_) {
    // 完成
    if (std::fabs(delta_dist) <=
        Get_info->option_.forklift_option.height_fork.kup_offset) {
      LOG_INFO("heap fork arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
      cmd_height_base_ = cmd_height;
      check_fork_state_ = true;
      load_weight_base_ = fork_status.weigh;
      PublishWeightLoadingBase();
      Get_info->SetStateSucceed();
      DataReset();
      PublishWeightLoadingBase();
      return;
    } else {
      //  抬
      check_fork_state_ = false;
      PublishWeightLoadingBase();
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed),
          static_cast<double>(heap_option.kup_max_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("heap fork up, input ratio: %d",
               static_cast<int>(msg->atomic_action_value));
      Get_info->state_.is_finish = false;
    }
  } else {
    // 完成
    if (std::fabs(delta_dist) <=
            Get_info->option_.forklift_option.height_fork.kdown_offset ||
        height < height_lower_limit) {
      LOG_INFO("heap fork arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
      cmd_height_base_ =
          cmd_height < height_lower_limit ? height_lower_limit : cmd_height;
      check_fork_state_ = true;
      load_weight_base_ = fork_status.weigh;
      PublishWeightLoadingBase();
      Get_info->SetStateSucceed();
      DataReset();
      PublishWeightLoadingBase();
      return;
    } else {
      // 降
      check_fork_state_ = false;
      PublishWeightLoadingBase();
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, -static_cast<double>(heap_option.kdown_max_speed),
          -static_cast<double>(heap_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("heap fork down, input ratio: %d",
               static_cast<int>(msg->atomic_action_value));
      Get_info->state_.is_finish = false;
    }
  }
}
// No.5
void HeapForkliftAction::Break(cotek_msgs::forklift_action *msg,
                               AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Break %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_BRAKE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  static int actcnt = 0;
  ++actcnt;
  Get_info->state_.percent = actcnt;
  if (actcnt == 100) {
    actcnt = 0;
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    Get_info->state_.percent = 100;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    DataReset();
    LOG_INFO("Break Done!!!");
  }
}
// No.6
void HeapForkliftAction::Beep(cotek_msgs::forklift_action *msg,
                              AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "========= Forklift:Doing Action----Beep %d =========",
                Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::BEEP);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.is_finish = true;
  DataReset();
  LOG_INFO("Beep Done!!!");
}
// No.7
void HeapForkliftAction::ForkliftBodyCotrollerReset(
    cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Curtis_Reset %d =========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static ros::Time action_time;
  static bool first_flag = false;
  if (!first_flag) {
    first_flag = true;
    action_time = ros::Time::now();
  }
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_RESET);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.percent = 50;
  // 执行超过10s认为完成
  if (ros::Time::now() - action_time > ros::Duration(10)) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    LOG_INFO_STREAM(" Steering Reset!!!");
    DataReset();
  }
}

// No.8
void HeapForkliftAction::Charge(cotek_msgs::forklift_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Charge %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->SetState(50, false, ActionStatus::DOING);
  switch (static_cast<cotek_action::ChargeDetectionType>(
      Get_info->option_.charge_option.charge_detection)) {
    case ChargeDetectionType::CURRENT: {
      LOG_INFO("==========charge detection: current ==========");
      if ((Get_info->goal_.action_value == 1) &&
          (Get_info->agvdata_.battery_moniter.current >
           Get_info->option_.charge_option.charge_current_upper)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Open Charge Done!!!");
      } else if ((Get_info->goal_.action_value == 0) &&
                 (Get_info->agvdata_.battery_moniter.current <
                  Get_info->option_.charge_option.charge_current_lower)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Close Charge Done!!!");
      }
      break;
    }
    case ChargeDetectionType::VOLTAGE: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: voltage ==========");
      break;
    }
    case ChargeDetectionType::BOTH: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: both ==========");
      break;
    }
    default: {
      LOG_INFO("==========charge detection: none ==========");
      // 检查继电器是否打开
      if ((Get_info->agvdata_.charge_do_state) &&
          (Get_info->goal_.action_value == 1)) {
        // TODO(@ssh) 临时解决　调度需打开继电器后至少发一帧doing 此处延时
        ros::Duration(1.0).sleep();
        Get_info->SetStateSucceed();
        LOG_INFO("Open Charge Done!!!");
        DataReset();
      } else if ((!Get_info->agvdata_.charge_do_state) &&
                 (Get_info->goal_.action_value == 0)) {
        ros::Time timeNow = ros::Time::now();
        while (ros::Time::now().toSec() - timeNow.toSec() < 5) {
          ros::Duration(0.2).sleep();
        }
        Get_info->SetStateSucceed();
        LOG_INFO("Close Charge Done!!!");
        DataReset();
      }
      break;
    }
  }
}

// delay action
void HeapForkliftAction::DelayAction(cotek_msgs::forklift_action *msg,
                                     AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Delay ==========");
  if (CheckTimeout(1, 40, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  Get_info->state_.is_finish = false;
  if (!delay_flag_) {
    delay_time_start_ = ros::Time::now();
    delay_flag_ = true;
  }

  if (ros::Time::now() - delay_time_start_ < ros::Duration(1)) {
    LOG_INFO_THROTTLE(1, "delay time!!!");
    return;
  } else {
    delay_flag_ = false;
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

}  // namespace cotek_action

void HeapForkliftAction::ActionInit() {
  // 注册动作
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&HeapForkliftAction::Waiting, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&HeapForkliftAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&HeapForkliftAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&HeapForkliftAction::HeapLiftLoad, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MEASURE_SIZE)] =
      boost::bind(&HeapForkliftAction::UnloadDetect, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_UP_WEIGHT)] =
      boost::bind(&HeapForkliftAction::CheckUPWeight, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_DOWN_WEIGHT)] =
      boost::bind(&HeapForkliftAction::CheckDownWeight, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&HeapForkliftAction::HeapUnLoad, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::HEIGHT_FORK_MOVE)] =
      boost::bind(&HeapForkliftAction::HeapForkMove, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&HeapForkliftAction::Charge, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
      boost::bind(&HeapForkliftAction::LowPowerMode, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
      boost::bind(&HeapForkliftAction::ControlAudioLevel, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&HeapForkliftAction::DelayAction, this, _1, _2);
}

void HeapForkliftAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;
  std::map<uint32_t, std::function<void(cotek_msgs::forklift_action *,
                                        AgvData *)>>::iterator tep_map;
  uint32_t i = goal.action_type;
  if (0 == i) {
    LOG_DEBUG_COND(Get_info->option_.enable_local_debug,
                   "ForkLift AGV:No Action Executing..");
    ResetDoingAction();
  } else {
    static uint32_t new_goal = 0;
    if (new_goal != i) {
      ResetDoingAction();
      new_goal = i;
      LOG_WARN_COND(Get_info->option_.enable_local_debug, "New goal...");
    }
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
           100) % 500 ==
              0 &&
          (goal.action_type != 0),
      "ForkLift AGV:Doing action No.%d", goal.action_type);
  tep_map = action_map_.find(i);
  if (tep_map != action_map_.end()) {
    action_map_[i](&msg_, Get_info);
  } else {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug,
                   "Wrong action type: %d", i);
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    return;
  }
}

void HeapForkliftAction::PublishRequestUnloadDetect() {
  cotek_msgs::request_unload_detect msg;
  msg.stamp = ros::Time::now();
  unload_detect_pub_.publish(msg);
}
void HeapForkliftAction::PublishWeightLoadingBase() {
  cotek_msgs::weighting_loaded msg;
  msg.stamp = ros::Time::now();
  msg.load_weight_base = load_weight_base_;
  msg.load_state = static_cast<uint32_t>(load_state_);
  msg.close_check_weight = close_check_weight_;
  msg.check_fork_state = check_fork_state_;
  load_weight_base_pub_.publish(msg);
}

void HeapForkliftAction::PublishActionMsg() {
  msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
  msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
  // 选择本地配置音量 | 调度下发音量
  msg_.audio_control_level =
      audio_level_dispatch_
          ? audio_level_
          : VolProcess(AgvData::get()->goal_.audio_control_type);
  msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
  // 音频模块硬件问题
  static int cnt = 0;
  if (msg_.audio_control_type == 0) {
    cnt++;
    if (cnt > 10) {
      msg_.audio_control_type = 1;
      msg_.audio_control_level = 0;
    }
  } else {
    cnt = 0;
  }

  forklift_action_pub_.publish(msg_);
}

}  // namespace cotek_action