// /**
//  * Copyright (c) 2023 COTEK Inc. All rights reserved.
//  */
// #include "cotek_action/action_model/zhongli_2t_jackup_action.h"

// #include <bitset>

// #include "cotek_action/action_config.h"
// #include "cotek_common/pid/mini_pid.h"

// namespace cotek_action {

// void Zhongli2TJackupAction::UpdateData(cotek_action::AgvData *Get_info) {}

// uint32_t Zhongli2TJackupAction::FeedBack() {
//   return AgvData::get()->state_.percent;
// }
// bool Zhongli2TJackupAction::IsFinished() {
//   return AgvData::get()->state_.is_finish;
// }

// void Zhongli2TJackupAction::DataReset(cotek_msgs::jack_up_action *msg) {
//   AgvData::get()->Reset();
//   memset(msg, 0, sizeof(cotek_msgs::jack_up_action));
// }

// // None..
// void Zhongli2TJackupAction::ActionNone(cotek_msgs::jack_up_action *msg,
//                                        AgvData *Get_info) {
//   msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
//   msg->atomic_action_value = 0;
//   Get_info->state_.is_finish = false;
//   Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
// }

// // Rest..
// void Zhongli2TJackupAction::Rest(cotek_msgs::jack_up_action *msg,
//                                  AgvData *Get_info) {
//   msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
//   msg->atomic_action_value = 0;
//   Get_info->SetStateSucceed();
//   DataReset(msg);
// }

// void Zhongli2TJackupAction::ControlAudioLevel(cotek_msgs::jack_up_action *msg,
//                                               AgvData *Get_info) {
//   LOG_INFO_COND(
//       Get_info->option_.enable_local_debug && DoingAction() == false,
//       "======== JackUp:Doing Action----Control_Audio_Level %d=========",
//       Get_info->goal_.action_type);
//   msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
//   msg->atomic_action_value = 0;
//   audio_level_ = Get_info->goal_.action_value;
//   audio_level_dispatch_ = true;
//   Get_info->SetStateSucceed();
//   DataReset(msg);
// }

// //
// void Zhongli2TJackupAction::PalletNoMove(cotek_msgs::jack_up_action *msg,
//                                          AgvData *Get_info) {
//   LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                 "============ Doing Action---Pallet_NoMove ============");
//   pallet_nomove_flag_ = static_cast<uint32_t>(Get_info->goal_.action_value);
//   LOG_INFO(
//       "[action node]Pallet_Nomove Setting Scusess,pallet_nomove_flag:%d!!!",
//       pallet_nomove_flag_);
//   Get_info->SetStateSucceed();
//   DataReset(msg);
// }
// // pallet up
// void Zhongli2TJackupAction::PalletUp(cotek_msgs::jack_up_action *msg,
//                                      AgvData *Get_info) {
//   LOG_INFO_STREAM_COND(
//       Get_info->option_.enable_local_debug && false == doing_action_,
//       "============ Doing Action---Pallet_Up ============");

//   if (CheckTimeout(Get_info->option_.enable_timeout,
//                    Get_info->option_.jack_up_option.pallet_up_down_option
//                        .action_timeout_value,
//                    Get_info)) {
//     node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
//   }

//   static bool have_pallet = false;
//   if (!CheckPallet(Get_info->option_.check_pallet, Get_info) &&
//       ((!Get_info->option_.jack_up_option.pallet_up_down_option
//              .check_pallet_once) ||
//        (Get_info->option_.jack_up_option.pallet_up_down_option
//             .check_pallet_once &&
//         false == have_pallet))) {
//     LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
//                          "============ No Pallet ============");
//     return;
//   }
//   have_pallet = true;

//   auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
//   auto pallet = Get_info->agvdata_.jackup_pallet_state;

//   if (pallet.up_down_state == 0 || pallet.up_down_state >= 4) {
//     msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
//     msg->atomic_action_value = Get_info->goal_.action_value;
//     Get_info->state_.is_finish = false;
//   } else {
//     msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
//     msg->atomic_action_value = 0;
//     Get_info->SetStateSucceed();
//     Get_info->state_.is_finish = true;
//     doing_action_ = false;
//     DataReset(msg);
//     ROS_INFO_STREAM(" Pallet_Up Done!!!");
//     have_pallet = false;
//   }
// }
// // pallet down..
// void Zhongli2TJackupAction::PalletDown(cotek_msgs::jack_up_action *msg,
//                                        AgvData *Get_info) {
//   LOG_INFO_STREAM_COND(
//       Get_info->option_.enable_local_debug && doing_action_ == false,
//       "============ Doing Action---Pallet_Down ============");

//   if (CheckTimeout(Get_info->option_.enable_timeout,
//                    Get_info->option_.jack_up_option.pallet_up_down_option
//                        .action_timeout_value,
//                    Get_info)) {
//     node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
//   }

//   auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
//   auto pallet = Get_info->agvdata_.jackup_pallet_state;

//   if (pallet.up_down_state >= 0 && pallet.up_down_state <= 4) {
//     msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
//     msg->atomic_action_value = Get_info->goal_.action_value;
//     Get_info->state_.is_finish = false;
//   } else {
//     msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
//     msg->atomic_action_value = 0;
//     Get_info->SetStateSucceed();
//     Get_info->state_.is_finish = true;
//     doing_action_ = false;
//     DataReset(msg);
//     ROS_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
//                          " Pallet_Down Done!!!");
//   }
// }

// void Zhongli2TJackupAction::PalletRotation(cotek_msgs::jack_up_action *msg,
//                                            AgvData *Get_info) {
//   if (!CheckPallet(Get_info->option_.check_pallet, Get_info)) return;

//   LOG_INFO_STREAM_COND(
//       Get_info->option_.enable_local_debug && doing_action_ == false,
//       "============ Doing Action---Pallet_Rotation ============");
//   // test
//   // shelf_cur_theta_ = Get_info->agvdata_.up_pgv.angle;
//   LOG_INFO("shelf_cur_theta_%f", shelf_cur_theta_);
//   LOG_INFO("pallet_tar_theta_ %f", pallet_tar_theta_);
//   if (doing_action_ == false) {
//     pallet_tar_theta_ = common::AngleAddition(
//         shelf_cur_theta_, common::Degree2rad(Get_info->goal_.action_value));
//     pallet_nomove_flag_ = 0;
//   }
//   if (CheckTimeout(Get_info->option_.enable_timeout,
//                    Get_info->option_.jack_up_option.pallet_rotation_zero_option
//                        .action_timeout_value,
//                    Get_info)) {
//     node_status_manager_ptr_->SetNodeStatus(
//         ActionNodeStatus::PALLET_ROTATE_ERR);
//   }

//   msg->atomic_action_type =
//       static_cast<uint32_t>(AtomicActionType::PALLET_ROTATION);
//   msg->atomic_action_value = 0;
//   if (fabs(common::AngleSubtract(pallet_tar_theta_, shelf_cur_theta_)) >
//       common::Degree2rad(1.0)) {
//     static PID_Handle pidhandle = {0};
//     pidhandle.kp = 1.0;  // Get_info->option_.jack_up_option.pallet_nomove_kp;
//     pidhandle.ki = 0.4;  // Get_info->option_.jack_up_option.pallet_nomove_ki;
//     pidhandle.kd = 0.6;  // Get_info->option_.jack_up_option.pallet_nomove_kd;
//     float diff = common::AngleSubtract(pallet_tar_theta_, shelf_cur_theta_);
//     msg_.rotate_w = -PID_Process(&pidhandle, diff);
//     msg_.rotate_w = fabs(msg_.rotate_w) > 2
//                         ? msg_.rotate_w * (2 / fabs(msg_.rotate_w))
//                         : msg_.rotate_w;
//   } else {
//     pallet_nomove_flag_ = 2;
//     msg_.rotate_w = 0.;
//     msg_.lift_v = 0.;
//     Get_info->state_.is_finish = true;
//     doing_action_ = false;
//     DataReset(msg);
//     LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
//                          " Pallet_Rotate Done!!!");
//   }
//   LOG_INFO("Pallet [rotate_w]: %f ;[up-degree]: %f", msg_.rotate_w,
//            shelf_cur_theta_);
// }
// // pallet zero ..
// void Zhongli2TJackupAction::PalletZero(cotek_msgs::jack_up_action *msg,
//                                        AgvData *Get_info) {
//   ros::Duration action_seconds(
//       Get_info->option_.jack_up_option.pallet_rotation_zero_option
//           .action_timeout_value,
//       0);
//   LOG_INFO_STREAM_COND(
//       Get_info->option_.enable_local_debug && doing_action_ == false,
//       "============ Doing Action---Pallet_Zero ============");

//   uint32_t percent = static_cast<int>(
//       100 *
//       static_cast<double>((ros::Time::now().toSec() - action_time_.toSec())) /
//       static_cast<double>(action_seconds.toSec()));

//   pallet_nomove_flag_ = 0;

//   if (CheckTimeout(Get_info->option_.enable_timeout,
//                    Get_info->option_.jack_up_option.pallet_rotation_zero_option
//                        .action_timeout_value,
//                    Get_info)) {
//     node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::PALLET_ZERO_ERR);
//   }

//   if (Get_info->agvdata_.jackup_pallet_state.roate_state == 0) {
//     msg->atomic_action_type =
//         static_cast<uint32_t>(AtomicActionType::PALLET_ZERO);
//     msg->atomic_action_value = Get_info->goal_.action_value;
//     Get_info->state_.percent =
//         percent < 100 && timeout_ == false ? percent : 99;
//     msg->lift_v = 0;
//     msg->rotate_w = Get_info->option_.jack_up_option.pallet_w;
//     Get_info->state_.is_finish = false;
//   } else {
//     msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
//     msg->atomic_action_value = 0;
//     msg->lift_v = 0;
//     Get_info->SetStateSucceed();
//     doing_action_ = false;
//     DataReset(msg);
//     LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
//                          " Pallet_Zero Done!!!");
//   }
// }
// // Disable motor
// void Zhongli2TJackupAction::MotorDisable(cotek_msgs::jack_up_action *msg,
//                                          AgvData *Get_info) {
//   LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                 "============ Doing Action---Motor_Disable ============");
//   msg->atomic_action_type =
//       static_cast<uint32_t>(AtomicActionType::MOTOR_DISABLE);
//   msg->atomic_action_value = Get_info->goal_.action_value;

//   std::bitset<32> dismoto(Get_info->goal_.action_value);
//   if (dismoto.test(0)) {
//     msg->left_motor_enable = 1;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug, "1:left_motor_disable");
//   }
//   if (dismoto.test(1)) {
//     msg->right_motor_enable = 1;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                   "2:right_motor_disable");
//   }
//   if (dismoto.test(2)) {
//     msg->lift_motor_enable = 1;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug, "3:lift_motor_disable");
//   }
//   if (dismoto.test(3)) {
//     msg->rotate_motor_enable = 1;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                   "4:rotate_motor_disable");
//   }

//   Get_info->state_.is_finish = true;
//   DataReset(msg);
//   LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
//                        " Motor_Disable Done!!!");
// }
// // Enable Motor
// void Zhongli2TJackupAction::MotorEnable(cotek_msgs::jack_up_action *msg,
//                                         AgvData *Get_info) {
//   LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                 "============ Doing Action---Motor_Enable ============");
//   msg->atomic_action_type =
//       static_cast<uint32_t>(AtomicActionType::MOTOR_ENABLE);
//   msg->atomic_action_value = Get_info->goal_.action_value;

//   std::bitset<32> dismoto(Get_info->goal_.action_value);
//   if (dismoto.test(0)) {
//     msg->left_motor_enable = 0;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug, "1:left_motor_Enable");
//   }
//   if (dismoto.test(1)) {
//     msg->right_motor_enable = 0;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug, "2:right_motor_Enable");
//   }
//   if (dismoto.test(2)) {
//     msg->lift_motor_enable = 0;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug, "3:lift_motor_Enable");
//   }
//   if (dismoto.test(3)) {
//     msg->rotate_motor_enable = 0;
//     LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                   "4:rotate_motor_Enable");
//   }

//   Get_info->state_.is_finish = true;
//   DataReset(msg);
//   LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
//                        " Motor_Enable Done!!!");
// }
// // charge ...
// void Zhongli2TJackupAction::ActionCharge(cotek_msgs::jack_up_action *msg,
//                                          AgvData *Get_info) {
//   LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
//                 "========== Forklift:Doing Action----Charge %d===========",
//                 Get_info->goal_.action_type);

//   if (CheckTimeout(Get_info->option_.enable_timeout,
//                    Get_info->option_.action_timeout_value, Get_info)) {
//     node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
//   }

//   msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
//   msg->atomic_action_value = Get_info->goal_.action_value;
//   Get_info->SetState(50, false, ActionStatus::DOING);
//   switch (static_cast<cotek_action::ChargeDetectionType>(
//       Get_info->option_.charge_option.charge_detection)) {
//     case ChargeDetectionType::CURRENT: {
//       LOG_INFO("==========charge detection: current ==========");
//       if ((Get_info->goal_.action_value == 1) &&
//           (Get_info->agvdata_.battery_moniter.current >
//            Get_info->option_.charge_option.charge_current_upper *
//                Get_info->option_.forklift_option.pallet_fork_up_down
//                    .kampere_factor)) {
//         Get_info->SetStateSucceed();
//         DataReset(msg);
//         LOG_INFO("Open Charge Done!!!");
//       } else if ((Get_info->goal_.action_value == 0) &&
//                  (Get_info->agvdata_.battery_moniter.current <
//                   Get_info->option_.charge_option.charge_current_lower *
//                       Get_info->option_.forklift_option.pallet_fork_up_down
//                           .kampere_factor)) {
//         Get_info->SetStateSucceed();
//         DataReset(msg);
//         LOG_INFO("Close Charge Done!!!");
//       }
//       break;
//     }
//     case ChargeDetectionType::VOLTAGE: {
//       // TODO(@ssh)
//       LOG_INFO("==========charge detection: voltage ==========");
//       break;
//     }
//     case ChargeDetectionType::BOTH: {
//       // TODO(@ssh)
//       LOG_INFO("==========charge detection: both ==========");
//       break;
//     }
//     default: {
//       LOG_INFO("==========charge detection: none ==========");
//       // 检查继电器是否打开
//       if ((Get_info->agvdata_.charge_do_state) &&
//           (Get_info->goal_.action_value == 1)) {
//         // TODO(@ssh) 临时解决　调度需打开继电器后至少发一帧doing 此处延时
//         ros::Duration(1.0).sleep();
//         Get_info->SetStateSucceed();
//         LOG_INFO("Open Charge Done!!!");
//         DataReset(msg);
//       } else if ((!Get_info->agvdata_.charge_do_state) &&
//                  (Get_info->goal_.action_value == 0)) {
//         Get_info->SetStateSucceed();
//         LOG_INFO("Close Charge Done!!!");
//         DataReset(msg);
//       }
//       break;
//     }
//   }
// }
// // clear motor alarm
// void Zhongli2TJackupAction::MotorClearAlarm(cotek_msgs::jack_up_action *msg,
//                                             AgvData *Get_info) {
//   // LOG_INFO_COND(Get_info->option_.enable_local_debug,
//   //               "Doing Action---Motor_ClearAlarm");
//   msg->atomic_action_type =
//       static_cast<uint32_t>(AtomicActionType::MOTOR_CLEARALARM);
//   msg->atomic_action_value = Get_info->goal_.action_value;

//   static bool flag = false, delay_over = false;
//   static ros::Time start_time = ros::Time::now();
//   ros::Time Now_time = ros::Time::now();

//   ros::Duration delay_seconds(
//       Get_info->option_.jack_up_option.clear_alarm_delaytime, 0);
//   ros::NodeHandle n;
//   static ros::Timer time = n.createTimer(
//       delay_seconds, [&](const ros::TimerEvent &) { delay_over = true; });
//   if (flag == false) {
//     time.start();
//     flag = true;
//     start_time = ros::Time::now();
//   }
//   Get_info->state_.percent = static_cast<int>(
//       100 * static_cast<double>((Now_time.toSec() - start_time.toSec())) /
//       static_cast<double>(delay_seconds.toSec()));

//   if (delay_over == true) {
//     time.stop();
//     flag = false;
//     delay_over = false;
//     msg->atomic_action_type = 0;
//     msg->atomic_action_value = 0;
//     Get_info->state_.percent = 100;
//     Get_info->state_.is_finish = true;
//     DataReset(msg);
//   }
// }
// // pause action
// void Zhongli2TJackupAction::ActionWaiting(cotek_msgs::jack_up_action *msg,
//                                           AgvData *Get_info) {
//   LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
//                 "Doing Action---Action_Waiting");
//   if (1 == Get_info->goal_.action_value) {
//     Get_info->state_.is_finish = false;
//     return;
//   } else if (0 == Get_info->goal_.action_value) {
//     DataReset(msg);
//     Get_info->goal_ = lastgoal_;
//     Get_info->state_.is_finish = true;
//   } else {
//     if (CheckTimeout(true, Get_info->goal_.action_value, Get_info)) {
//       DataReset(msg);
//       Get_info->goal_ = lastgoal_;
//       Get_info->state_.is_finish = true;
//     }
//   }
// }

// // Low_Power_Mode
// void Zhongli2TJackupAction::LowPowerMode(cotek_msgs::jack_up_action *msg,
//                                          AgvData *Get_info) {
//   LOG_INFO_COND(Get_info->option_.enable_local_debug,
//                 "Doing Action----Low_Power_Mode");
//   static uint8_t cnt = 0;
//   msg->atomic_action_type =
//       static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
//   msg->atomic_action_value = Get_info->goal_.action_value;
//   ++cnt;
//   msg->atomic_action_value = 0;
//   Get_info->state_.percent = cnt;
//   if (cnt == 100) {
//     Get_info->state_.is_finish = true;
//     Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
//     LOG_INFO("Low_Power_Mode Set Done!!!");
//     LOG_INFO_STREAM(" Done action: Low_Power_Mode");
//     DataReset(msg);
//   }
// }

// bool Zhongli2TJackupAction::CheckPallet(bool use, AgvData *Get_info) {
//   if (!use) return true;
//   if (0 == Get_info->agvdata_.up_pgv.tag_number) {
//     node_status_manager_ptr_->SetNodeStatus(
//         ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
//     LOG_WARN_STREAM_COND(
//         Get_info->option_.enable_local_debug &&
//             (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
//              100) % 100 ==
//                 0,
//         "EXCEPTION:Doing Action-Pallet_Up: Have NO PALLET !!!");
//     return false;
//   }
//   return true;
// }

// bool Zhongli2TJackupAction::CheckTimeout(bool use, double limit_time,
//                                          AgvData *Get_info) {
//   if (doing_action_ == false) {
//     action_time_ = ros::Time::now();
//     doing_action_ = true;
//   }
//   if (!use) return false;
//   ros::Duration action_seconds(limit_time, 0);
//   if ((ros::Time::now() - action_time_) > action_seconds) {
//     LOG_ERROR_STREAM_COND(Get_info->option_.enable_local_debug,
//                           " ERROR:...Action-Time out...");
//     timeout_ = true;
//     return true;
//   }
//   return false;
// }

// // Init action list
// void Zhongli2TJackupAction::ActionInit() {
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
//       boost::bind(&Zhongli2TJackupAction::ActionNone, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
//       boost::bind(&Zhongli2TJackupAction::Rest, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
//       boost::bind(&Zhongli2TJackupAction::PalletUp, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
//       boost::bind(&Zhongli2TJackupAction::PalletDown, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
//       boost::bind(&Zhongli2TJackupAction::ActionCharge, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
//       boost::bind(&Zhongli2TJackupAction::LowPowerMode, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_NOMOVE)] =
//       boost::bind(&Zhongli2TJackupAction::PalletNoMove, this, _1, _2);
//   action_map_[static_cast<uint32_t>(
//       AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
//       boost::bind(&Zhongli2TJackupAction::ControlAudioLevel, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_DISABLE)] =
//       boost::bind(&Zhongli2TJackupAction::MotorDisable, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_ENABLE)] =
//       boost::bind(&Zhongli2TJackupAction::MotorEnable, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_CLEARALARM)] =
//       boost::bind(&Zhongli2TJackupAction::MotorClearAlarm, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::WAIT)] =
//       boost::bind(&Zhongli2TJackupAction::ActionWaiting, this, _1, _2);
//   action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
//       boost::bind(&Zhongli2TJackupAction::Rest, this, _1, _2);

//   LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
//                 "Action_Init..... size:%d", action_map_.size());
// }
// // do action..
// void Zhongli2TJackupAction::ExecuteAction(AgvData *Get_info) {
//   auto goal = Get_info->goal_;
//   std::map<int32_t, std::function<void(cotek_msgs::jack_up_action *,
//                                        AgvData *)>>::iterator tep_map;
//   uint32_t i = goal.action_type;
//   if (0 == i) {
//     LOG_WARN_COND(Get_info->option_.enable_local_debug && 0 != goal.action_type,
//                   "JackUP AGV: No Action Executing..");
//     timeout_ = false;
//     doing_action_ = false;
//   } else {
//     if (newgoal_.action_type != i) {
//       timeout_ = false;
//       doing_action_ = false;
//       lastgoal_ = newgoal_;
//       newgoal_ = goal;
//       LOG_ERROR_COND(Get_info->option_.enable_local_debug, "New goal...");
//     }
//   }
//   tep_map = action_map_.find(i);

//   if (tep_map != action_map_.end()) {
//     action_map_[i](&msg_, Get_info);
//   } else {
//     LOG_ERROR_COND(Get_info->option_.enable_local_debug,
//                    "Wrong action type: %d", i);
//     DataReset(&msg_);
//     return;
//   }
// }
// // publish action msg to driver
// void Zhongli2TJackupAction::PublishActionMsg() {
//   LOG_DEBUG_COND(AgvData::get()->option_.enable_local_debug,
//                  "[action node]JackUpAGV--Publish");
//   msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
//   msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
//   msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
//   // 选择本地配置音量 | 调度下发音量
//   msg_.audio_control_level = audio_level_dispatch_
//                                  ? audio_level_
//                                  : AgvData::get()->option_.audio_level;
//   // 音频模块硬件问题
//   static int cnt = 0;
//   if (msg_.audio_control_type == 0) {
//     cnt++;
//     if (cnt > 10) {
//       msg_.audio_control_type = 1;
//       msg_.audio_control_level = 0;
//     }
//   } else {
//     cnt = 0;
//   }

//   jackup_action_pub_.publish(msg_);
// }

// // 顶升电机抱闸保护
// void Zhongli2TJackupAction::LiftSafeBreakControl(
//     ros::Time time, cotek_msgs::jack_up_action *msg, AgvData *Get_info) {
//   if (!Get_info->option_.jack_up_option.pallet_up_down_option.motor_safe_break)
//     return;
//   pallet_nomove_flag_ = 0;
//   if (((ros::Time::now() - time) > ros::Duration(1.0)) &&
//       ((ros::Time::now() - time) < ros::Duration(1.5)) &&
//       Get_info->state_.lift_break_state == false) {
//     msg->atomic_action_type =
//         static_cast<uint32_t>(AtomicActionType::LIFT_MOTOR_BREAK_CONTROL);
//     msg->atomic_action_value = 1.0;
//     ROS_INFO("LIFT_MOTOR_BREAK_CONTROL=1");
//   }
//   if ((ros::Time::now() - time) > ros::Duration(1.5)) {
//     msg->atomic_action_type =
//         static_cast<uint32_t>(AtomicActionType::LIFT_MOTOR_BREAK_CONTROL);
//     msg->atomic_action_value = 0.0;
//     ROS_INFO("LIFT_MOTOR_BREAK_CONTROL=0");
//   }
// }

// }  // namespace cotek_action
