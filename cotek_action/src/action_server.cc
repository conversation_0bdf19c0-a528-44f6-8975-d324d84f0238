/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_server.h"

namespace cotek_action {
void ActionServer::ServerStart() {
  run_executor_ =
      std::make_shared<std::thread>(std::bind(&ActionServer::Runner, this));
  run_executor_->detach();
}

void ActionServer::UpdateActionOption(const AgvActionOption& option) {
  AgvData::get()->SetOption(&option);
  if (action_interface_) {
    action_interface_->UpdateInitParm();
  }
}

void ActionServer::UpdateBasicOption(const AgvBasicOption& option) {
  AgvData::get()->SetBasicOption(&option);
}

bool ActionServer::Init() {
  switch (AgvData::get()->basic_option_.agv_type) {
    case AgvType::FORKLIFT:
    case AgvType::HEAP_FORKLIFT:
    case AgvType::JACK_UP:
      action_interface_ = std::shared_ptr<ActionInterface>(new AtomicAction);
      LOG_ERROR("[Action server] AGV Type: %d!!!",
                AgvData::get()->basic_option_.agv_type);
      break;
    default:
      LOG_ERROR("[Action server] Error: Input Wrong AGV Type!!!");
      return false;
      break;
  }
  // 添加故障诊断
  action_interface_->SetStatusManagerPtr(node_status_manager_ptr_);

  LOG_INFO("[Action server] Init finished");
  return true;
}

void ActionServer::GoalCB() {
  cotek_action::ActionGoal goal;

  auto acceptgoal = server_.acceptNewGoal();
  goal.order_id = acceptgoal->order_id;
  // goal.task_id = acceptgoal->task_id;
  goal.action_type = acceptgoal->action_type;
  if (acceptgoal->action_value.size() == 0) {
    goal.action_value = 0;
  } else {
    if (goal.action_type != static_cast<uint32_t>(AgvTaskOperationType::IO_OPERATOR)) {
      goal.action_value = std::stof(acceptgoal->action_value[0]);
    } else {
      goal.io_pin = acceptgoal->action_value[0];
      goal.action_value = std::stof(acceptgoal->action_value[1]);
    }
  }
  goal.mix_condition = acceptgoal->mix_condition;
  // goal.finish_condition = acceptgoal->finish_condition;
  // goal.error_condition = acceptgoal->error_condition;
  AgvData::get()->UpdataGoal(&goal);
}

void ActionServer::PreemptCB() {
  LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
                "[Action server] Preempt goal");
  AgvData::get()->goal_.Reset();
  result_.is_finish = action_interface_->IsFinished();
  server_.setPreempted(result_);
}

void ActionServer::Runner() {
  ros::Rate rate(AgvData::get()->option_.control_period);
  auto Get_Info = AgvData::get();

  LOG_DEBUG("[Action server] Start cycle run");
  while (ros::ok()) {
    action_interface_->UpdateData(Get_Info);
    action_interface_->ExecuteAction(Get_Info);
    action_interface_->PublishActionMsg();

    if (Get_Info->goal_.action_type != 0) {
      feedback_.percent = Get_Info->state_.percent;
      LOG_DEBUG_COND(Get_Info->option_.enable_local_debug,
                     "[Action server] Action feedback percent: %d",
                     feedback_.percent);
      server_.publishFeedback(feedback_);
    }

    if (Get_Info->state_.is_finish == true &&
        Get_Info->state_.action_status ==
            static_cast<uint8_t>(ActionStatus::FINISH) &&
        server_.isActive()) {
      result_.is_finish = action_interface_->IsFinished();
      server_.setSucceeded(result_);
      Get_Info->state_.is_finish = false;
    }

    if (rate.cycleTime() >
        ros::Duration(1.0 / Get_Info->option_.control_period)) {
      LOG_WARN_COND(Get_Info->option_.enable_local_debug,
                    "[Action server] Action server missed its desired rate "
                    "time out: %.2f",
                    rate.cycleTime().toSec());
    }

    rate.sleep();
  }
}

}  // namespace cotek_action
