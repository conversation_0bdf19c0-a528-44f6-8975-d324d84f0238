#include "cotek_action/action_model/atomic_action.h"

#include <fstream>
#include <iostream>

#include <std_msgs/Int32.h>

#include "cotek_action/action_config.h"
#include "cotek_common/common.h"
#include "cotek_common/cotek_protocal.h"
#include "cotek_common/io_index.h"
#include "cotek_common/nlohmann/json.hpp"

namespace cotek_action {

using string = std::string;
using J<PERSON> = nlohmann::ordered_json;
using n_excetion = nlohmann::json_abi_v3_11_2::detail::exception;

static std::vector<OriginalCondition> PraseJson(
    string condtion_str, std::shared_ptr<NodeStatusManager> state_diag_ptr) {
  std::vector<OriginalCondition> condition_vec;
  // string condition_type;
  // if (condtion_str.find("finishConditions") != string::npos) {
  //   condition_type = "finishConditions";
  // } else if (condtion_str.find("errorConditions") != string::npos) {
  //   condition_type = "errorConditions";
  // } else {
  //   std::cout << "unkonwn condition type: " << std::endl;
  //   //state_diag_ptr->SetNodeStatus(ActionNodeStatus::DIY_CONDITION_ERROR);
  //   return condition_vec;
  // }

  std::vector<string::size_type> index;
  string::size_type pos = 0;
  string searchStr = "conditions";
  while ((pos = condtion_str.find(searchStr, pos)) != string::npos) {
    // std::cout << "Found at position: " << pos << std::endl;
    index.push_back(pos - 1);
    pos += searchStr.length();
  }
  int deep = index.size();
  // std::cout << "deep: " << deep << std::endl;
  if (deep < 1) return condition_vec;

  Json json_data;
  condtion_str.erase(0, 1);
  condtion_str.erase(condtion_str.length() - 1);
  try {
    json_data = Json::parse(condtion_str);
    std::string condition = json_data.dump();
    LOG_INFO_STREAM("PRASE DATA: " << condition);
  } catch (const n_excetion &ex) {
    std::cout << (ex.what()) << std::endl;
    LOG_WARN("json format error.");
    // state_diag_ptr->SetNodeStatus(ActionNodeStatus::DIY_CONDITION_ERROR);
    return condition_vec;
  }

  auto outer_array = json_data;
  auto condition_array = outer_array["conditions"];
  std::string array = condition_array.dump();
  LOG_INFO_STREAM("CONDITION: " << array);
  // auto condition_array = json_data["conditions"];

  int layer = 0;
  for (int i = 1; i <= deep; i++) {
    int size = condition_array.size();
    // std::cout << "current deep: " << i << ", same layer nums: " << size <<
    // std::endl;
    for (int j = 0; j < size; j++) {
      // std::cout << "  current array pos: " << j << std::endl;
      auto condition = condition_array[j];

      if (condition.count("conditions") < 1) {
        OriginalCondition ori_condition;

        ori_condition.logic_layer = i;
        ori_condition.combination =
            outer_array["combination"].get<std::string>();
        ori_condition.id = condition["id"].get<std::string>();
        ori_condition.op = condition["operator"].get<std::string>();
        ori_condition.value1 = std::stod(condition["value"][0].get<string>());
        auto value_array = condition["value"];
        if (value_array.size() > 1) {
          ori_condition.value2 = std::stod(condition["value"][1].get<string>());
        } else {
          ori_condition.value2 = 0.;
        }

        // std::cout << "    -- logic_layer: " << ori_condition.logic_layer <<
        // std::endl; std::cout << "    -- Combination: " <<
        // ori_condition.combination << std::endl; std::cout << "    -- ID: " <<
        // ori_condition.id << std::endl; std::cout << "    -- Operator: " <<
        // ori_condition.op << std::endl; std::cout << "    -- Value 1: " <<
        // ori_condition.value1 << std::endl; std::cout << "    -- Value 2: " <<
        // ori_condition.value2 << std::endl;

        condition_vec.push_back(ori_condition);
      } else {
        layer = j;
        // std::cout << "    -- Find the next condition level" << std::endl;
      }
    }
    outer_array = outer_array["conditions"][layer];
    condition_array = outer_array["conditions"];
    // std::cout << "-- deep: " << i << " finished" << std::endl;
    // std::cout << std::endl;
  }

  return condition_vec;
};

static Condition PraseCondition(
    OriginalCondition ori_condition,
    std::shared_ptr<NodeStatusManager> state_diag_ptr) {
  Condition condition;
  string type = ori_condition.id;
  string combination = ori_condition.combination;
  string op = ori_condition.op;
  string::size_type pos = 0;

  condition.logic_layer = ori_condition.logic_layer;

  if (combination.find("&&", pos) != string::npos) {
    condition.combination = static_cast<int>(CombinationType::AND);
  } else if (combination.find("||", pos) != string::npos) {
    condition.combination = static_cast<int>(CombinationType::OR);
  } else {
    condition.combination = static_cast<int>(CombinationType::UNKNOWN);
  }
  pos = 0;

  if ((op.find(">=", pos) != string::npos) ||
      (op.find(">", pos) != string::npos)) {
    condition.op = static_cast<int>(OperatorType::GREATER_THAN);
  } else if ((op.find("<=", pos) != string::npos) ||
             (op.find("<", pos) != string::npos)) {
    condition.op = static_cast<int>(OperatorType::LESS_THAN);
  } else if (op.find("==", pos) != string::npos) {
    condition.op = static_cast<int>(OperatorType::EQUAL);
  } else if (op.find("~=", pos) != string::npos) {
    condition.op = static_cast<int>(OperatorType::RANGE);
  } else {
    condition.op = static_cast<int>(OperatorType::UNKNOWN);
    LOG_WARN("unknown diy conition operate type.");
    // state_diag_ptr->SetNodeStatus(ActionNodeStatus::DIY_CONDITION_ERROR);
  }
  pos = 0;

  if (type.find("checkDi", pos) != string::npos) {
    string substr = type.substr(7, 2);
    int num = stoi(substr);
    condition.id = num + 1;
    condition.value = 0;
    condition.deviation = 0;
    condition.io_state = static_cast<bool>(ori_condition.value1);
  } else if (type.find("checkDo", pos) != string::npos) {
    string substr = type.substr(7, 2);
    int num = stoi(substr);
    condition.id = num + 17;
    condition.value = 0;
    condition.deviation = 0;
    condition.io_state = static_cast<bool>(ori_condition.value1);
  } else if (type.find("checkUpLimit", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::CHECK_UP_LIMIT);
    condition.value = 0;
    condition.deviation = 0;
    condition.io_state = static_cast<bool>(ori_condition.value1);
  } else if (type.find("checkDownLimit", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::CHECK_DOWN_LIMIT);
    condition.value = 0;
    condition.deviation = 0;
    condition.io_state = static_cast<bool>(ori_condition.value1);
  } else if (type.find("checkPalletState", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::CHECK_PALLET_STATE);
    condition.value = 0;
    condition.deviation = 0;
    condition.io_state = static_cast<bool>(ori_condition.value1);
  } else if (type.find("checkChargeState", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::CHECK_CHARGE_STATE);
    condition.value = 0;
    condition.deviation = 0;
    condition.io_state = static_cast<bool>(ori_condition.value1);
  } else if (type.find("checkUpWeight", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::CHECK_UP_WEIGHT);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else if (type.find("checkDownWeight", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::CHECK_DOWN_WEIGHT);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else if (type.find("forkHeight", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::FORK_HEIGHT);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else if (type.find("forkLateral", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::FORK_LATERAL);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else if (type.find("forkSideMove", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::FORK_SIDEMOVE);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else if (type.find("forkTilt", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::FORK_TILT);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else if (type.find("runTime", pos) != string::npos) {
    condition.id = static_cast<int>(CheckConditionType::RUN_TIME);
    condition.value = ori_condition.value1;
    condition.deviation = ori_condition.value2;
    condition.io_state = false;
  } else {
    condition.id = static_cast<int>(CheckConditionType::UNKNOWN);
    LOG_WARN("unknown diy conition operate type.");
    // state_diag_ptr->SetNodeStatus(ActionNodeStatus::DIY_CONDITION_ERROR);
  }

  return condition;
}

static std::map<int, std::vector<Condition>> ConditionVec2Map(
    std::vector<Condition> condition_vec) {
  int last_layer = 1;
  std::map<int, std::vector<Condition>> same_layer_map;
  std::vector<Condition> smae_layer_vec;

  for (int i = 0; i < condition_vec.size(); i++) {
    int current_layer = condition_vec[i].logic_layer;
    if (last_layer == current_layer) {
      smae_layer_vec.push_back(condition_vec[i]);
    } else {
      same_layer_map.insert(std::make_pair(last_layer, smae_layer_vec));
      smae_layer_vec.clear();
      last_layer = current_layer;
      smae_layer_vec.push_back(condition_vec[i]);
    }

    if (i == condition_vec.size() - 1) {
      same_layer_map.insert(std::make_pair(last_layer, smae_layer_vec));
    }
  }

  // for (auto& pair : same_layer_map) {
  //   std::cout << "Key: " << pair.first << ", Value: " << std::endl;
  //   for (auto& condition : pair.second) {
  //     std::cout << "---" << std::endl;
  //     std::cout << "  logic_layer: " << condition.logic_layer << std::endl;
  //     std::cout << "  Combination: " << condition.combination << std::endl;
  //     std::cout << "  ID: " << condition.id << std::endl;
  //     std::cout << "  Operator: " << condition.op << std::endl;
  //     std::cout << "  IO_State: " << condition.io_state << std::endl;
  //     std::cout << "  Value: " << condition.value << std::endl;
  //     std::cout << "  Deviation: " << condition.deviation << std::endl;
  //   }
  //   std::cout << std::endl;
  // }

  return same_layer_map;
}

static std::map<int, std::vector<Condition>> GetCondition(
    string condtion_str, std::shared_ptr<NodeStatusManager> state_diag_ptr) {
  std::map<int, std::vector<Condition>> condition_map;

  // ------ 解析原始string
  std::vector<OriginalCondition> ori_condition_vec =
      PraseJson(condtion_str, state_diag_ptr);
  if (ori_condition_vec.empty()) return condition_map;

  // ------ 数据映射
  std::vector<Condition> condition_vec;
  for (int i = 0; i < ori_condition_vec.size(); i++) {
    Condition condition = PraseCondition(ori_condition_vec[i], state_diag_ptr);
    condition_vec.push_back(condition);
  }
  // ------ to map
  condition_map = ConditionVec2Map(condition_vec);

  return condition_map;
}

bool AtomicAction::CheckState(std::vector<Condition> condition_vec,
                              AgvData *Get_info, cotek_msgs::action_cmd *msg,
                              bool is_open_loop, bool is_error_map) {
  needs_check_pallet_ = false;

  static bool delay_flag = false;
  static ros::Time run_time_start = ros::Time::now();

  std::vector<bool> result;
  for (int i = 0; i < condition_vec.size(); i++) {
    auto condition = condition_vec[i];
    if (condition.id >= static_cast<int>(CheckConditionType::CHECK_DI1) &&
        condition.id <= static_cast<int>(CheckConditionType::CHECK_DI16)) {
      auto io_feedback = Get_info->io_feedback;
      uint32_t io_error_code = io_feedback.error_code;
      ros::Time io_time = io_feedback.time_stamp;

      if (io_error_code) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
        LOG_ERROR_THROTTLE(
            1, "[Atomic-action Error] Diy_condition: IO Error, code: %d !!!",
            io_error_code);
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (ros::Time::now() - io_time > ros::Duration(0.5)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
        LOG_ERROR_THROTTLE(1,
                           "[Atomic-action Error] Diy_condition: IO Feedback "
                           "Has Delay In Timestamp !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (condition.op != static_cast<int>(OperatorType::EQUAL)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(
            1,
            "[Atomic-action WARN] Diy_condition: Only support '==' type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      int type = condition.id - 1;
      if (type > io_feedback.di_state.size() || type < 0) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_ERROR_THROTTLE(
            1,
            "[Atomic-action Error] Diy_condition: Check IO Out Of Range !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (io_feedback.di_state[type].value == condition.io_state) {
        result.push_back(true);
      } else {
        result.push_back(false);
      }
      LOG_INFO(
          "[Atomic-action] Diy_condition: Di%d check, condition:%d, actual:%d "
          "!!!",
          type, condition.io_state, io_feedback.di_state[type].value);

    } else if (condition.id >=
                   static_cast<int>(CheckConditionType::CHECK_DO1) &&
               condition.id <=
                   static_cast<int>(CheckConditionType::CHECK_DO16)) {
      auto io_feedback = Get_info->io_feedback;
      uint32_t io_error_code = io_feedback.error_code;
      ros::Time io_time = io_feedback.time_stamp;

      if (io_error_code) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
        LOG_ERROR_THROTTLE(
            1, "[Atomic-action Error] Diy_condition: IO Error, code: %d !!!",
            io_error_code);
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (ros::Time::now() - io_time > ros::Duration(0.5)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
        LOG_ERROR_THROTTLE(1,
                           "[Atomic-action Error] Diy_condition: IO Feedback "
                           "Has Delay In Timestamp !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (condition.op != static_cast<int>(OperatorType::EQUAL)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(
            1,
            "[Atomic-action WARN] Diy_condition: Only support '==' type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      int type = condition.id - 17;
      if (type > io_feedback.do_state.size() || type < 0) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_ERROR_THROTTLE(
            1,
            "[Atomic-action Error] Diy_condition: Check IO Out Of Range !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (io_feedback.do_state[type].value == condition.io_state) {
        result.push_back(true);
      } else {
        result.push_back(false);
      }
      LOG_INFO(
          "[Atomic-action] Diy_condition: Do%d check, condition:%d, actual:%d "
          "!!!",
          type, condition.io_state, io_feedback.di_state[type].value);

    } else if (condition.id >=
                   static_cast<int>(CheckConditionType::CHECK_UP_LIMIT) &&
               condition.id <=
                   static_cast<int>(CheckConditionType::CHECK_CHARGE_STATE)) {
      bool state, io_state;
      auto io_name_map = Get_info->io_feedback.io_name_map;

      auto io_feedback = Get_info->io_feedback;
      uint32_t io_error_code = io_feedback.error_code;
      ros::Time io_time = io_feedback.time_stamp;

      if (io_error_code) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
        LOG_ERROR_THROTTLE(
            1, "[Atomic-action Error] Diy_condition: IO Error, code: %d !!!",
            io_error_code);
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (ros::Time::now() - io_time > ros::Duration(0.5)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
        LOG_ERROR_THROTTLE(1,
                           "[Atomic-action Error] Diy_condition: IO Feedback "
                           "Has Delay In Timestamp !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (condition.op != static_cast<int>(OperatorType::EQUAL)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(
            1,
            "[Atomic-action WARN] Diy_condition: Only support '==' type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      if (condition.id ==
          static_cast<int>(CheckConditionType::CHECK_UP_LIMIT)) {
        auto it = io_name_map.find(io::Di::kUpperLimitSwitch1);
        if (it != io_name_map.end()) {
          io_state = static_cast<bool>(it->second);
          // state = io_state == condition.io_state ? true : false;
        } else {
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
          }
          /*   node_status_manager_ptr_->SetNodeStatus(
       ActionNodeStatus::DIY_CONDITION_ERROR);*/
          LOG_WARN_THROTTLE(1,
                            "[Atomic-action WARN] Diy_condition: not have up "
                            "limit io config !!!");
          if (is_error_map)
            return true;
          else
            return false;
        }

        if (condition.io_state == true) {
          state = Get_info->agvdata_.forklift_pallet_state.fork_up_down_state ==
                          static_cast<uint8_t>(ForkStateType::UP)
                      ? true
                      : false;
        } else {
          state = Get_info->agvdata_.forklift_pallet_state.fork_up_down_state !=
                          static_cast<uint8_t>(ForkStateType::UP)
                      ? true
                      : false;
        }

      } else if (condition.id ==
                 static_cast<int>(CheckConditionType::CHECK_DOWN_LIMIT)) {
        auto it = io_name_map.find(io::Di::kDownLimitSwitch1);
        if (it != io_name_map.end()) {
          io_state = static_cast<bool>(it->second);
          // state = io_state == condition.io_state ? true : false;
        } else {
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
          }
          /*   node_status_manager_ptr_->SetNodeStatus(
       ActionNodeStatus::DIY_CONDITION_ERROR);*/
          LOG_WARN_THROTTLE(1,
                            "[Atomic-action WARN] Diy_condition: not have down "
                            "limit io config !!!");
          if (is_error_map)
            return true;
          else
            return false;
        }

        if (condition.io_state == true) {
          state = Get_info->agvdata_.forklift_pallet_state.fork_up_down_state ==
                          static_cast<uint8_t>(ForkStateType::DOWN)
                      ? true
                      : false;
        } else {
          state = Get_info->agvdata_.forklift_pallet_state.fork_up_down_state !=
                          static_cast<uint8_t>(ForkStateType::DOWN)
                      ? true
                      : false;
        }

      } else if (condition.id ==
                 static_cast<int>(CheckConditionType::CHECK_PALLET_STATE)) {
        needs_check_pallet_ = true;
        auto it = io_name_map.find(io::Di::kPositionReach1);
        if (it != io_name_map.end()) {
          io_state = static_cast<bool>(it->second);
          // state = io_state == condition.io_state ? true : false;
        } else {
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
          }
          /*   node_status_manager_ptr_->SetNodeStatus(
       ActionNodeStatus::DIY_CONDITION_ERROR);*/
          LOG_WARN_THROTTLE(1,
                            "[Atomic-action WARN] Diy_condition: not have "
                            "pallet arrive io config !!!");
          if (is_error_map)
            return true;
          else
            return false;
        }

        if (condition.io_state == true) {
          state = Get_info->agvdata_.forklift_pallet_state.fork_pallet_state ==
                          static_cast<uint8_t>(ForkPalletState::NONE)
                      ? true
                      : false;
        } else {
          state = Get_info->agvdata_.forklift_pallet_state.fork_pallet_state !=
                          static_cast<uint8_t>(ForkPalletState::NONE)
                      ? true
                      : false;
        }

      } else if (condition.id ==
                 static_cast<int>(CheckConditionType::CHECK_CHARGE_STATE)) {
        auto it = io_name_map.find(io::Do::kchargeSwitch1);
        if (it != io_name_map.end()) {
          io_state = static_cast<bool>(it->second);
          // state = io_state == condition.io_state ? true : false;
        } else {
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
          }
          /*   node_status_manager_ptr_->SetNodeStatus(
       ActionNodeStatus::DIY_CONDITION_ERROR);*/
          LOG_WARN_THROTTLE(1,
                            "[Atomic-action WARN] Diy_condition: not have "
                            "charge io config !!!");
          if (is_error_map)
            return true;
          else
            return false;
        }

        if (condition.io_state == true) {
          state = Get_info->agvdata_.agv_io_state.charge_do_state == true
                      ? true
                      : false;
        } else {
          state = Get_info->agvdata_.agv_io_state.charge_do_state != true
                      ? true
                      : false;
        }

      } else {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(
            1, "[Atomic-action WARN] Diy_condition: Unkown condition type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }
      result.push_back(state);
      LOG_INFO(
          "[Atomic-action] Diy_condition: check_type: %d, io_state:%d(%d) "
          "state: %d !!!",
          condition.id, io_state, condition.io_state, state);

    } else if (condition.id >=
                   static_cast<int>(CheckConditionType::FORK_HEIGHT) &&
               condition.id <=
                   static_cast<int>(CheckConditionType::FORK_SIDEMOVE)) {
      double value;
      if (condition.id == static_cast<int>(CheckConditionType::FORK_HEIGHT)) {
        value = Get_info->agvdata_.forklift_pallet_state.height;
      } else if (condition.id ==
                 static_cast<int>(CheckConditionType::FORK_TILT)) {
        value = Get_info->agvdata_.imu_monitor.angle_x;
      } else if (condition.id ==
                 static_cast<int>(CheckConditionType::FORK_LATERAL)) {
        value = Get_info->agvdata_.forklift_pallet_state.lateral;
      } else if (condition.id ==
                 static_cast<int>(CheckConditionType::FORK_SIDEMOVE)) {
        value = Get_info->agvdata_.forklift_pallet_state.sideshift;
      } else {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(
            1, "[Atomic-action WARN] Diy_condition: Unkown condition type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      bool state;
      if (condition.op == static_cast<int>(OperatorType::GREATER_THAN)) {
        state =
            value >= math::Meter2CMillimeter(condition.value) ? true : false;
        result.push_back(state);
      } else if (condition.op == static_cast<int>(OperatorType::LESS_THAN)) {
        state =
            value <= math::Meter2CMillimeter(condition.value) ? true : false;
        result.push_back(state);
      } else if (condition.op == static_cast<int>(OperatorType::RANGE)) {
        if ((value >=
             math::Meter2CMillimeter(condition.value - condition.deviation)) &&
            (value <=
             math::Meter2CMillimeter(condition.value + condition.deviation))) {
          state = true;
        } else {
          state = false;
        }
        result.push_back(state);
      } else if (condition.op == static_cast<int>(OperatorType::EQUAL)) {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(1,
                          "[Atomic-action WARN] Diy_condition: Fork move not "
                          "support '==' condition type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      } else {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(
            1, "[Atomic-action WARN] Diy_condition: Unkown condition type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }
      LOG_INFO(
          "[Atomic-action] Diy_condition: check_type: %d, value: %f, goal: "
          "%f(%f), "
          "state: %d !!!",
          condition.id, value, condition.value, condition.deviation, state);

    } else if (condition.id >=
                   static_cast<int>(CheckConditionType::CHECK_UP_WEIGHT) &&
               condition.id <=
                   static_cast<int>(CheckConditionType::CHECK_DOWN_WEIGHT)) {
      bool state;
      double value = Get_info->agvdata_.weigh_monitor.weight;
      if (condition.id ==
          static_cast<int>(CheckConditionType::CHECK_UP_WEIGHT)) {
        if (condition.op == static_cast<int>(OperatorType::GREATER_THAN)) {
          state = value >= condition.value ? true : false;
          result.push_back(state);
        } else if (condition.op == static_cast<int>(OperatorType::LESS_THAN)) {
          state = value <= condition.value ? true : false;
          result.push_back(state);
        } else if (condition.op == static_cast<int>(OperatorType::RANGE)) {
          state = value >= condition.value ? true : false;
          result.push_back(state);
        } else {
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
          }
          /*   node_status_manager_ptr_->SetNodeStatus(
       ActionNodeStatus::DIY_CONDITION_ERROR);*/
          LOG_WARN_THROTTLE(1,
                            "[Atomic-action WARN] Diy_condition: Check up "
                            "weight not support '==' condition type !!!");
          if (is_error_map)
            return true;
          else
            return false;
        }
      } else {
        if (condition.op == static_cast<int>(OperatorType::GREATER_THAN)) {
          state = value >= condition.value ? true : false;
          result.push_back(state);
        } else if (condition.op == static_cast<int>(OperatorType::LESS_THAN)) {
          state = value <= condition.value ? true : false;
          result.push_back(state);
        } else if (condition.op == static_cast<int>(OperatorType::RANGE)) {
          state = value >= condition.value ? true : false;
          result.push_back(state);
        } else {
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
          }
          /*   node_status_manager_ptr_->SetNodeStatus(
       ActionNodeStatus::DIY_CONDITION_ERROR);*/
          LOG_WARN_THROTTLE(1,
                            "[Atomic-action WARN] Diy_condition: Check down "
                            "weight not support '==' condition type !!!");
          if (is_error_map)
            return true;
          else
            return false;
        }
      }
      LOG_INFO(
          "[Atomic-action] Diy_condition: check_type: %d, value: %f, goal: %f, "
          "state: %d !!!",
          condition.id, value, condition.value, state);

    } else if (condition.id >= static_cast<int>(CheckConditionType::RUN_TIME) &&
               condition.id <= static_cast<int>(CheckConditionType::UNKNOWN)) {
      if (!rt_delay_flag_) {
        run_time_start = ros::Time::now();
        rt_delay_flag_ = true;
      }

      if (condition.op == static_cast<int>(OperatorType::GREATER_THAN)) {
        if ((ros::Time::now() - run_time_start) >=
            ros::Duration(condition.value)) {
          result.push_back(true);
          // rt_delay_flag_ = false;
          node_status_manager_ptr_->SetNodeStatus(
              ActionNodeStatus::ACTION_TIME_OUT);
        } else {
          result.push_back(false);
        }
      } else if (condition.op == static_cast<int>(OperatorType::LESS_THAN)) {
        if ((ros::Time::now() - run_time_start) <=
            ros::Duration(condition.value)) {
          result.push_back(true);
          // rt_delay_flag_ = false;
          node_status_manager_ptr_->SetNodeStatus(
              ActionNodeStatus::ACTION_TIME_OUT);
        } else {
          result.push_back(false);
        }
      } else {
        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_WARN_THROTTLE(1,
                          "[Atomic-action WARN] Diy_condition: Check down "
                          "weight not support '[]' '==' condition type !!!");
        if (is_error_map)
          return true;
        else
          return false;
      }

      LOG_INFO("[Atomic-action] Diy_condition: check_type: %d, goal: %f !!!",
               condition.id, condition.value);

    } else {
      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_WARN_THROTTLE(
          1, "[Atomic-action WARN] Diy_condition: Unkown condition type !!!");
      if (is_error_map)
        return true;
      else
        return false;
    }
  }

  if (result.size() < 2) return result[0];

  bool final_result = result[0];
  int logic = condition_vec[0].combination;
  for (int i = 1; i < result.size(); i++) {
    if (logic == 0) {
      final_result = final_result && result[i];
    } else {
      final_result = final_result || result[i];
    }
  }

  return final_result;
}

bool AtomicAction::UpdateDiyConditionState(
    std::map<int, std::vector<Condition>> condition_map, AgvData *Get_info,
    cotek_msgs::action_cmd *msg, bool is_open_loop, bool is_error_map) {
  bool last_state;
  bool final_state;
  int keyCount = condition_map.size();

  for (auto it = condition_map.rbegin(); it != condition_map.rend(); ++it) {
    bool state =
        CheckState(it->second, Get_info, msg, is_open_loop, is_error_map);
    if (keyCount == it->first) {
      last_state = state;
      final_state = last_state;
    } else {
      if (it->second[0].combination == 0) {
        final_state = final_state && state;
      } else {
        final_state = final_state || state;
      }
    }
  }

  return final_state;
}

void AtomicAction::DataReset() { AgvData::get()->Reset(); }

void AtomicAction::OpenLoopDataReset() { AgvData::get()->OpenLoopReset(); }

uint16_t AtomicAction::FeedBack() { return AgvData::get()->state_.percent; }

uint16_t AtomicAction::OpenLoopFeedBack() {
  return AgvData::get()->open_loop_state_.percent;
}

bool AtomicAction::IsFinished() { return AgvData::get()->state_.is_finish; }

bool AtomicAction::IsOpenLoopFinished() {
  return AgvData::get()->open_loop_state_.is_finish;
}

void AtomicAction::UpdateData(AgvData *Get_info) {
  if (AgvType::JACK_UP == AgvData::get()->basic_option_.agv_type) {
    tf::StampedTransform shelf_transform;
    LOG_DEBUG("shelf_tag_num: %d",
              Get_info->agvdata_.jackup_pallet_state.palletag);

    if (0 != Get_info->agvdata_.jackup_pallet_state.palletag) {
      try {
        shelf_tf_->lookupTransform(cotek_tf::kMapFrame, cotek_tf::kShelfFrame,
                                   ros::Time(0), shelf_transform);
      } catch (tf::TransformException &ex) {
        LOG_WARN_STREAM_THROTTLE(0.2, ex.what());
        LOG_WARN_STREAM_THROTTLE(0.2, "Current shelf pose Exception.");
        shelf_pose_vaild_ = false;
        pallet_tar_theta_ = 0.0;
        shelf_cur_theta_ = 0.0;
        return;
      }
      shelf_cur_theta_ =
          angles::normalize_angle(tf::getYaw(shelf_transform.getRotation()));
      auto x = shelf_transform.getOrigin().x();
      auto y = shelf_transform.getOrigin().y();
      //归一化托盘保持目标角度
      if (0 != pallet_nomove_flag_) {
        pallet_tar_theta_ = angles::normalize_angle(
            common::Direction2Angle(common::Angle2Direction(shelf_cur_theta_)));
      }
    } else {
      pallet_tar_theta_ = 0.0;
      shelf_cur_theta_ = 0.0;
    }

    if (!up_tag_loss_) {
      shelf_pose_vaild_ = true;
    }
    LOG_DEBUG_THROTTLE(0.2, "shelf_delta: %lf, pallet_tar_theta_: %f",
                       shelf_cur_theta_, pallet_tar_theta_);

    PalletStateProcess(pallet_nomove_flag_, AgvData::get());
  }
}

void AtomicAction::UpdateInitParm() {
  // 插齿升降PID
  auto heap_option = AgvData::get()->option_.forklift_option.height_fork;
  heap_dist_pid_ptr_->init();
  heap_dist_pid_ptr_->setPID(heap_option.kdist_p, heap_option.kdist_i,
                             heap_option.kdist_d);
  heap_dist_pid_ptr_->setOutputLimits(-heap_option.kdist_output_limit,
                                      heap_option.kdist_output_limit);
  heap_dist_pid_ptr_->setOutputRampRate(heap_option.kdist_ramp_rate);

  heap_lift_speed_pid_ptr_->init();
  heap_lift_speed_pid_ptr_->setPID(heap_option.kup_speed_p,
                                   heap_option.kup_speed_i,
                                   heap_option.kup_speed_d);
  heap_lift_speed_pid_ptr_->setOutputLimits(-heap_option.kspeed_output_limit,
                                            heap_option.kspeed_output_limit);
  heap_lift_speed_pid_ptr_->setOutputRampRate(heap_option.kspeed_ramp_rate);

  heap_descent_speed_pid_ptr_->init();
  heap_descent_speed_pid_ptr_->setPID(heap_option.kdown_speed_p,
                                      heap_option.kdown_speed_i,
                                      heap_option.kdown_speed_d);
  heap_descent_speed_pid_ptr_->setOutputLimits(-heap_option.kspeed_output_limit,
                                               heap_option.kspeed_output_limit);
  heap_descent_speed_pid_ptr_->setOutputRampRate(heap_option.kspeed_ramp_rate);

  // 前后移PID
  auto lateral_option = AgvData::get()->option_.forklift_option.lateral_fork;
  lateral_dist_pid_ptr_->init();
  lateral_dist_pid_ptr_->setPID(lateral_option.kdist_p, lateral_option.kdist_i,
                                lateral_option.kdist_d);
  lateral_dist_pid_ptr_->setOutputLimits(-lateral_option.kdist_output_limit,
                                         lateral_option.kdist_output_limit);
  lateral_dist_pid_ptr_->setOutputRampRate(lateral_option.kdist_ramp_rate);

  lateral_lift_speed_pid_ptr_->init();
  lateral_lift_speed_pid_ptr_->setPID(lateral_option.kup_speed_p,
                                      lateral_option.kup_speed_i,
                                      lateral_option.kup_speed_d);
  lateral_lift_speed_pid_ptr_->setOutputLimits(
      -lateral_option.kspeed_output_limit, lateral_option.kspeed_output_limit);
  lateral_lift_speed_pid_ptr_->setOutputRampRate(
      lateral_option.kspeed_ramp_rate);

  lateral_descent_speed_pid_ptr_->init();
  lateral_descent_speed_pid_ptr_->setPID(lateral_option.kdown_speed_p,
                                         lateral_option.kdown_speed_i,
                                         lateral_option.kdown_speed_d);
  lateral_descent_speed_pid_ptr_->setOutputLimits(
      -lateral_option.kspeed_output_limit, lateral_option.kspeed_output_limit);
  lateral_descent_speed_pid_ptr_->setOutputRampRate(
      lateral_option.kspeed_ramp_rate);

  // 横移PID
  auto side_option = AgvData::get()->option_.forklift_option.sideshift_fork;
  side_dist_pid_ptr_->init();
  side_dist_pid_ptr_->setPID(side_option.kdist_p, side_option.kdist_i,
                             side_option.kdist_d);
  side_dist_pid_ptr_->setOutputLimits(-side_option.kdist_output_limit,
                                      side_option.kdist_output_limit);
  side_dist_pid_ptr_->setOutputRampRate(side_option.kdist_ramp_rate);

  side_lift_speed_pid_ptr_->init();
  side_lift_speed_pid_ptr_->setPID(side_option.kspeed_p, side_option.kspeed_i,
                                   side_option.kspeed_d);
  side_lift_speed_pid_ptr_->setOutputLimits(-side_option.kspeed_output_limit,
                                            side_option.kspeed_output_limit);
  side_lift_speed_pid_ptr_->setOutputRampRate(side_option.kspeed_ramp_rate);

  side_descent_speed_pid_ptr_->init();
  side_descent_speed_pid_ptr_->setPID(
      side_option.kspeed_p, side_option.kspeed_i, side_option.kspeed_d);
  side_descent_speed_pid_ptr_->setOutputLimits(-side_option.kspeed_output_limit,
                                               side_option.kspeed_output_limit);
  side_descent_speed_pid_ptr_->setOutputRampRate(side_option.kspeed_ramp_rate);

  // 倾斜PID
  auto tilt_option = AgvData::get()->option_.forklift_option.tilt_fork;
  tilt_dist_pid_ptr_->init();
  tilt_dist_pid_ptr_->setPID(tilt_option.kdist_p, tilt_option.kdist_i,
                             tilt_option.kdist_d);
  tilt_dist_pid_ptr_->setOutputLimits(-tilt_option.kdist_output_limit,
                                      tilt_option.kdist_output_limit);
  tilt_dist_pid_ptr_->setOutputRampRate(tilt_option.kdist_ramp_rate);

  tilt_speed_pid_ptr_->init();
  tilt_speed_pid_ptr_->setPID(tilt_option.kspeed_p, tilt_option.kspeed_i,
                              tilt_option.kspeed_d);
  tilt_speed_pid_ptr_->setOutputLimits(-tilt_option.kspeed_output_limit,
                                       tilt_option.kspeed_output_limit);
  tilt_speed_pid_ptr_->setOutputRampRate(tilt_option.kspeed_ramp_rate);

  // 顶升PID
  auto nomove_pid = AgvData::get()->option_.jack_up_option.nomove_pid;
  jackup_pallet_move_->init();
  jackup_pallet_move_->setPID(nomove_pid.kp, nomove_pid.ki, nomove_pid.kd);
  jackup_pallet_move_->setOutputLimits(nomove_pid.output_limit);
  jackup_pallet_move_->setOutputRampRate(nomove_pid.ramp_rate);

  auto rotate_pid = AgvData::get()->option_.jack_up_option.rotate_pid;
  jackup_pallet_rotate_->init();
  jackup_pallet_rotate_->setPID(rotate_pid.kp, rotate_pid.ki, rotate_pid.kd);
  jackup_pallet_rotate_->setOutputLimits(rotate_pid.output_limit);
  jackup_pallet_rotate_->setOutputRampRate(rotate_pid.ramp_rate);
}

void AtomicAction::PalletStateProcess(uint8_t flag, AgvData *Get_info) {
  if (0 == pallet_nomove_flag_) {
    return;
  }

  // 托盘在底部时，直接返回
  if (Get_info->agvdata_.jackup_pallet_state.up_down_state ==
          static_cast<uint8_t>(JackUpDownState::UP_NONE) ||
      Get_info->agvdata_.jackup_pallet_state.up_down_state >=
          static_cast<uint8_t>(JackUpDownState::DOWN_NONE) ||
      !shelf_pose_vaild_ ||
      Get_info->agvdata_.jackup_pallet_state.palletag == 0) {
    //托盘在顶部，且扫不到二维码，返回与车体反方向角速度
    if ((Get_info->agvdata_.jackup_pallet_state.up_down_state >
         static_cast<uint8_t>(JackUpDownState::UP_NONE)) &&
        (Get_info->agvdata_.jackup_pallet_state.up_down_state <
         static_cast<uint8_t>(JackUpDownState::DOWN_NONE)) &&
        (!shelf_pose_vaild_ ||
         Get_info->agvdata_.jackup_pallet_state.palletag == 0)) {
      // atomic_action_msg_.rotate_w = Get_info->state_.move_omega;
      return;
    }
    // atomic_action_msg_.rotate_w = 0.0;
    return;
  }

  double diff =
      angles::normalize_angle(angles::normalize_angle(pallet_tar_theta_) -
                              angles::normalize_angle(shelf_cur_theta_));
  double make_up_pallet_w;

  if (up_tag_loss_) {
    LOG_ERROR_THROTTLE(1, "[PalletStateProcess] up_tag_loss_");
    // atomic_action_msg_.rotate_w = 0.0;
    return;
  } else {
    if (1 == pallet_nomove_flag_) {
      //行走时的托盘保持
      jackup_pallet_rotate_->reset();
      // atomic_action_msg_.rotate_w =
      // -jackup_pallet_move_->getOutputFromDiff(diff); LOG_DEBUG_THROTTLE(0.1,
      //                   "[MiniPID-Move] rotate_w: %f, shelf_cur_theta_: %f, "
      //                   "pallet_tar_theta_: %f",
      //                   atomic_action_msg_.rotate_w, shelf_cur_theta_,
      //                   pallet_tar_theta_);
    } else if (2 == pallet_nomove_flag_) {
      //车体旋转时候的托盘保持
      jackup_pallet_move_->reset();
      make_up_pallet_w = jackup_pallet_rotate_->getOutputFromDiff(diff);
      // atomic_action_msg_.rotate_w = Get_info->state_.move_omega -
      // make_up_pallet_w; LOG_DEBUG_THROTTLE(0.1,
      //                   "[MiniPID-Rotate] rotate_w: %f, "
      //                   "make_up_pallet_w: %f, shelf_cur_theta_: %f, "
      //                   "pallet_tar_theta_: %f",
      //                   atomic_action_msg_.rotate_w, make_up_pallet_w,
      //                   shelf_cur_theta_, pallet_tar_theta_);
    } else {
    }
  }

#if 1
  cotek_msgs::pallet_nomove_info pallet_info;
  pallet_info.pallet_angle_dif = math::Rad2Deg(diff);
  // pallet_info.pallet_cmd_w = atomic_action_msg_.rotate_w;
  pallet_info.pallet_feedback_w = Get_info->state_.move_omega;
  pallet_info.PID_make_up_w = make_up_pallet_w;
  pallet_info.pallet_flag = pallet_nomove_flag_;
  pallet_nomove_pub_.publish(pallet_info);
#endif

  // LOG_DEBUG_THROTTLE(0.2,
  //                    "[PalletStateProcess] rotate_w: %.4f, "
  //                    "make_up_pallet_w: %.4f, diff: %.4f, Type: %d, "
  //                    "pallet_tar_theta_: %.4f, shelf_cur_theta_: %f ",
  //                    atomic_action_msg_.rotate_w, make_up_pallet_w,
  //                    diff, pallet_nomove_flag_,
  //                    pallet_tar_theta_, shelf_cur_theta_);
}

// 双闭环PID调节，外环调速，内环调加速度，输出增量
const float AtomicAction::GetForkMoveSpeed(const double &target_height,
                                           const double &current_height,
                                           const double &current_speed) {
  auto target_speed =
      heap_dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed = target_height - current_height > 0
                           ? heap_lift_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed)
                           : heap_descent_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed);
  LOG_INFO_STREAM("target_speed: " << target_speed
                                   << ", current speed: " << current_speed
                                   << ", delta_speed: " << delta_speed);
  return delta_speed;
}

const float AtomicAction::GetLateralSpeed(const double &target_position,
                                          const double &current_position,
                                          const double &current_speed) {
  auto target_speed = lateral_dist_pid_ptr_->getOutputFromDiff(
      target_position - current_position);
  double delta_speed = target_position - current_position > 0
                           ? lateral_lift_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed)
                           : lateral_descent_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed);
  LOG_INFO_STREAM("target_speed: " << target_speed
                                   << ", current speed: " << current_speed
                                   << ", delta_speed: " << delta_speed);
  return delta_speed;
}

const float AtomicAction::GetSideMoveSpeed(const double &target_position,
                                           const double &current_position,
                                           const double &current_speed) {
  auto target_speed =
      side_dist_pid_ptr_->getOutputFromDiff(target_position - current_position);
  double delta_speed = target_position - current_position > 0
                           ? side_lift_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed)
                           : side_descent_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed);
  LOG_INFO_STREAM("target_speed: " << target_speed
                                   << ", current speed: " << current_speed
                                   << ", delta_speed: " << delta_speed);
  return delta_speed;
}

const float AtomicAction::GetForkTiltSpeed(const double &target_angle,
                                           const double &current_angle,
                                           const double &current_speed) {
  auto target_speed =
      tilt_dist_pid_ptr_->getOutputFromDiff(target_angle - current_angle);
  double delta_speed =
      tilt_speed_pid_ptr_->getOutputFromDiff(target_speed - current_speed);
  LOG_INFO_STREAM("target_tilt_speed: "
                  << target_speed << ", current tilt speed: " << current_speed
                  << ", delta_speed: " << delta_speed);
  return delta_speed;
}

void AtomicAction::PublishRequestColumnPositionDetect(double x, double y,
                                                      double yaw) {
  cotek_msgs::request_column_position_detect msg;
  msg.stamp = ros::Time::now();
  msg.storage_pose_x = x;
  msg.storage_pose_y = y;
  msg.storage_pose_yaw = yaw;
  column_position_detect_pub_.publish(msg);
}

void AtomicAction::PublishRequestPalletCheck(double x, double y, double yaw) {
  cotek_msgs::request_pallet_center request;
  request.stamp = ros::Time::now();
  request.expect_pose.x = x;
  request.expect_pose.y = y;
  request.expect_pose.theta = math::Deg2Rad(yaw);
  request_pallet_pub_.publish(request);
}

void AtomicAction::PublishRequestUnloadDetect() {
  cotek_msgs::request_unload_detect msg;
  msg.stamp = ros::Time::now();
  unload_detect_pub_.publish(msg);
}

void AtomicAction::PublishRequestPalletBackLimit() {
  cotek_msgs::request_pallet_back_limit request;
  request.stamp = ros::Time::now();
  request_pallet_back_limit_pub_.publish(request);
}

bool AtomicAction::DelayTime(double seconds) {
  static bool delay_flag = false;
  static ros::Time delay_time_start = ros::Time::now();
  if (!delay_flag) {
    delay_flag = true;
    delay_time_start = ros::Time::now();
    return false;
  }
  if (ros::Time::now() - delay_time_start < ros::Duration(seconds)) {
    LOG_INFO_THROTTLE(0.5, "[Atomic-action] Delay time... ");
    return false;
  } else {
    delay_flag = false;
    return true;
  }
}

bool AtomicAction::JackUpCheckPallet(bool use, AgvData *Get_info) {
  if (!use) return true;
  if (0 == Get_info->agvdata_.jackup_pallet_state.palletag) {
    return false;
  }
  return true;
}

// 顶升电机抱闸保护
void AtomicAction::LiftSafeBreakControl(ros::Time time,
                                        cotek_msgs::action_cmd *msg,
                                        AgvData *Get_info) {
  if (!Get_info->option_.jack_up_option.pallet_up_down_option
           .motor_safe_break) {
    return;
  }

  if (((ros::Time::now() - time) > ros::Duration(1.0)) &&
      ((ros::Time::now() - time) < ros::Duration(1.5)) &&
      Get_info->state_.lift_break_state == false) {
    // msg->atomic_action_type =
    //     static_cast<uint32_t>(AtomicActionType::LIFT_MOTOR_BREAK_CONTROL);
    // msg->atomic_action_value = 1.0;
    LOG_INFO_THROTTLE(0.5, "[Atomic-action] LIFT_MOTOR_BREAK_CONTROL = 1 !!!");
  }
  if ((ros::Time::now() - time) > ros::Duration(1.5)) {
    // msg->atomic_action_type =
    //     static_cast<uint32_t>(AtomicActionType::LIFT_MOTOR_BREAK_CONTROL);
    // msg->atomic_action_value = 0.0;
    LOG_INFO_THROTTLE(0.5, "[Atomic-action] LIFT_MOTOR_BREAK_CONTROL = 0 !!!");
  }
}

void AtomicAction::Waiting(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                           bool is_open_loop, bool use_diy_finish,
                           bool use_diy_error,
                           std::map<int, std::vector<Condition>> finish_map,
                           std::map<int, std::vector<Condition>> error_map) {
  if (is_open_loop) {
    Get_info->open_loop_state_.is_finish = false;
    Get_info->open_loop_state_.action_status =
        static_cast<uint8_t>(ActionStatus::UNSTART);
  } else {
    Get_info->state_.is_finish = false;
    Get_info->state_.action_status =
        static_cast<uint8_t>(ActionStatus::UNSTART);
  }
}

void AtomicAction::Rest(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                        bool is_open_loop, bool use_diy_finish,
                        bool use_diy_error,
                        std::map<int, std::vector<Condition>> finish_map,
                        std::map<int, std::vector<Condition>> error_map) {
  if (is_open_loop) {
    Get_info->SetOpenLoopStateSucceed();
    OpenLoopDataReset();
  } else {
    Get_info->SetStateSucceed();
    DataReset();
  }
}

void AtomicAction::DelayAction(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========== Doing Atomic Action ---- Delay ==========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  static bool delay_flag = false;
  static ros::Time delay_time_start = ros::Time::now();

  if (!delay_flag) {
    delay_time_start = ros::Time::now();
    delay_flag = true;
  }
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Delay diy error-condition error.");
      return;
    }
  }
  bool diy_finish_state = false;
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  // 使用默认完成条件
  ros::Duration elapsed_time =
      ros::Duration(ros::Time::now() - delay_time_start);
  if ((elapsed_time < ros::Duration(action_value) && !use_diy_finish) ||
      (use_diy_finish && !diy_finish_state)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    LOG_INFO_THROTTLE(1, "delay time: %lf !!!", action_value);
    return;
  } else {
    delay_flag = false;
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(" ======== Atomic Action Done ---- Delay_time =========");
  }
}

void AtomicAction::Init(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                        bool is_open_loop, bool use_diy_finish,
                        bool use_diy_error,
                        std::map<int, std::vector<Condition>> finish_map,
                        std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========== Doing Atomic Action ---- Init ==========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    // node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  if (!Get_info->agvdata_.shelf_check_result) {
    LOG_INFO_THROTTLE(1, "[Atomic-action] Init: Wait shelf check. ");
    return;
  } else {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(" ======== Atomic Action Done ---- Init =========");
  }
}

// IO操作
void AtomicAction::IoOperator(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                              bool is_open_loop, bool use_diy_finish,
                              bool use_diy_error,
                              std::map<int, std::vector<Condition>> finish_map,
                              std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Io_Operator =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto io_feedback = Get_info->io_feedback;
  uint32_t io_error_code = io_feedback.error_code;
  ros::Time io_time = io_feedback.time_stamp;

  if (io_error_code) {
    auto it = exec_action_.find("IO_OPERATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("IO_OPERATOR");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Diy_condition: IO Error, code: %d !!!",
        io_error_code);
    return;
  }

  if (ros::Time::now() - io_time > ros::Duration(0.5)) {
    auto it = exec_action_.find("IO_OPERATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("IO_OPERATOR");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IO_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Diy_condition: IO Feedback Has "
                       "Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      auto it = exec_action_.find("IO_OPERATOR");
      if (it != exec_action_.end()) {
        exec_action_.erase("IO_OPERATOR");
      }
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Io_Operator diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  double action_value;
  string pin;
  bool is_key;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
    pin = Get_info->open_loop_goal_.io_pin;
  } else {
    action_value = Get_info->goal_.action_value;
    pin = Get_info->goal_.io_pin;
  }

  if (pin.find("di") != std::string::npos ||
      pin.find("do") != std::string::npos) {
    auto it = io_feedback.io_key_map.find(pin);
    if (it == io_feedback.io_key_map.end()) {
      LOG_ERROR_STREAM("No such " << pin << " io.");
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::ACTION_TYPE_ERR);
      return;
    }
    is_key = true;
    LOG_INFO_STREAM("[Atomic-action] Io_Operator: "
                    << pin
                    << ", actual_state: " << io_feedback.io_key_map.at(pin)
                    << ", target_state: " << action_value);
  } else {
    auto it = io_feedback.io_name_map.find(pin);
    if (it == io_feedback.io_name_map.end()) {
      LOG_ERROR_STREAM("No such " << pin << " io.");
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::ACTION_TYPE_ERR);
      return;
    }
    is_key = false;
    LOG_INFO_STREAM("[Atomic-action] Io_Operator: "
                    << pin
                    << ", actual_state: " << io_feedback.io_name_map.at(pin)
                    << ", target_state: " << action_value);
  }

  if (((((io_feedback.io_key_map.at(pin) ==
          static_cast<uint8_t>(action_value)) &&
         is_key) ||
        ((io_feedback.io_name_map.at(pin) ==
          static_cast<uint8_t>(action_value)) &&
         !is_key)) &&
       !use_diy_finish) ||
      (use_diy_finish && !diy_finish_state)) {
    auto it = exec_action_.find("IO_OPERATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("IO_OPERATOR");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(" ======== Atomic Action Done ---- Io_Operator =========");
    return;
  } else {
    Action action;
    action.cmd = static_cast<uint32_t>(AtomicActionType::IO_OPERATOR);
    action.data = action_value;
    if (is_key) {
      action.io_key = pin;
      action.io_name = "";
    } else {
      action.io_key = "";
      action.io_name = pin;
    }
    exec_action_["IO_OPERATOR"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
}

// 呼叫电梯
void AtomicAction::CallElevator(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ======== Doing Atomic Action ---- Call_Elevator ========= ");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto elevator = Get_info->elevator_data_;
  uint32_t elevator_err = elevator.error;
  ros::Time time = elevator.time_stamp;

  if ((elevator_err && elevator_err != -1) || elevator.warn_signal) {
    auto it = exec_action_.find("CALL_ELEVATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("CALL_ELEVATOR");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ELEVATOR_ERROR);
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Elevator Error, signal:%d, code: %d !!!",
        elevator.warn_signal, elevator_err);
    return;
  }

  if (elevator.occupy_flag) {
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Elevator is occupied, have to wait !!!");
    return;
  }

  if (ros::Time::now() - time > ros::Duration(10) && elevator_err != -1) {
    auto it = exec_action_.find("CALL_ELEVATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("CALL_ELEVATOR");
    }

    LOG_WARN_THROTTLE(
        1, "[Atomic-action Error] Elevator Feedback Has Delay In Timestamp !!!");
    return;
  }

  static ros::Time arrive_time = ros::Time::now();
  static bool first = true;
  Action action;
  int target_floor;
  if (is_open_loop) {
    target_floor = Get_info->open_loop_goal_.action_value;
  } else {
    target_floor = Get_info->goal_.action_value;
  }

  LOG_INFO_THROTTLE(1, "[Atomic-action] Call_elevator: target(%d), current(%d)", 
                    target_floor, elevator.floor);
  if (target_floor != elevator.floor) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::CALL_ELEVATOR);
    action.data = target_floor;
    exec_action_["CALL_ELEVATOR"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    arrive_time = ros::Time::now();
    first = false;
    LOG_INFO_THROTTLE(1, "[Atomic-action] Call_elevator: Doing !!!");
  } else {
    if (first) {
      arrive_time = ros::Time::now();
      first = false;
    }
    // 延时持续确认
    if (ros::Time::now() - arrive_time > ros::Duration(5.)) {
      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      auto it = exec_action_.find("CALL_ELEVATOR");
      if (it != exec_action_.end()) {
        exec_action_.erase("CALL_ELEVATOR");
      }
      first = true;
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Call_elevator =========");
    }

    LOG_INFO_THROTTLE(0.5, "[Atomic-action] Call_elevator: Arrived, wait ensure.");
    return;
  }

  std_msgs::Int32 info;
  info.data = 3;
  elevator_task_pub_.publish(info);
}

// 控制电梯
void AtomicAction::ControlElevator(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ======== Doing Atomic Action ---- Control_Elevator =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
           
  auto elevator = Get_info->elevator_data_;
  uint32_t elevator_err = elevator.error;
  ros::Time time = elevator.time_stamp;

  if ((elevator_err && elevator_err != -1) || elevator.warn_signal) {
    auto it = exec_action_.find("CONTROL_ELEVATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("CONTROL_ELEVATOR");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ELEVATOR_ERROR);
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Elevator Error, signal:%d, code: %d !!!",
        elevator.warn_signal, elevator_err);
    return;
  }

  if (elevator.occupy_flag) {
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Elevator is occupied, have to wait !!!");
    return;
  }

  if (ros::Time::now() - time > ros::Duration(10) && elevator_err != -1) {
    auto it = exec_action_.find("CONTROL_ELEVATOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("CONTROL_ELEVATOR");
    }

    LOG_WARN_THROTTLE(
        1, "[Atomic-action Error] Elevator Feedback Has Delay In Timestamp !!!");
    return;
  }

  static ros::Time arrive_time = ros::Time::now();
  static bool first = true;
  Action action;
  int target_state;
  if (is_open_loop) {
    target_state = Get_info->open_loop_goal_.action_value;
  } else {
    target_state = Get_info->goal_.action_value;
  }

  LOG_INFO_THROTTLE(1, "[Atomic-action] Control_elevator: target(%d), current(%d, %d)", 
                    target_state, elevator.open_state, elevator.close_state);
  if ((target_state == 1 && elevator.open_state != true) ||
      (target_state == -1 && elevator.close_state != true)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::CONTROL_ELEVATOR);
    action.data = target_state;
    exec_action_["CONTROL_ELEVATOR"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    arrive_time = ros::Time::now();
    first = false;
    LOG_INFO_THROTTLE(1, "[Atomic-action] Control_elevator: Doing !!!");
  } else {
    if (first) {
      arrive_time = ros::Time::now();
      first = false;
    }

    if (ros::Time::now() - arrive_time > ros::Duration(3.)) {
      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      auto it = exec_action_.find("CONTROL_ELEVATOR");
      if (it != exec_action_.end()) {
        exec_action_.erase("CONTROL_ELEVATOR");
      }
      first = true;
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Control_elevator =========");
    }

    LOG_INFO_THROTTLE(0.5, "[Atomic-action] Control_elevator: wait ensure.");
    return;
  }

  std_msgs::Int32 info;
  if (target_state == 1) {
    info.data = 1;
  } else {
    info.data = 2;
  }
  elevator_task_pub_.publish(info);
}

// 控制自动门
void AtomicAction::ControlAutoDoor(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ======== Doing Atomic Action ---- Control_autodoor =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
           
  auto autodoor = Get_info->autodoor_data_;
  uint32_t autodoor_err = autodoor.error;
  ros::Time time = autodoor.time_stamp;

  if (autodoor.warn_signal) {
    auto it = exec_action_.find("CONTROL_AUTODOOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("CONTROL_AUTODOOR");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::AUTODOOR_ERROR);
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] AutoDoor Error, signal:%d",
        autodoor.warn_signal);
    return;
  }

  if (ros::Time::now() - time > ros::Duration(10) && autodoor_err != -1) {
    auto it = exec_action_.find("CONTROL_AUTODOOR");
    if (it != exec_action_.end()) {
      exec_action_.erase("CONTROL_AUTODOOR");
    }

    LOG_WARN_THROTTLE(
        1, "[Atomic-action Error] AutoDoor Feedback Has Delay In Timestamp !!!");
    return;
  }

  static ros::Time arrive_time = ros::Time::now();
  static bool first = true;
  Action action;
  int target_state;
  if (is_open_loop) {
    target_state = Get_info->open_loop_goal_.action_value;
  } else {
    target_state = Get_info->goal_.action_value;
  }

  LOG_INFO_THROTTLE(1, "[Atomic-action] Control_autodoor: target(%d), current(%d, %d)", 
                    target_state, autodoor.open_state, autodoor.close_state);
  if ((target_state == 1 && autodoor.open_state != true) ||
      (target_state == -1 && autodoor.close_state != true)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::CONTROL_AUTODOOR);
    action.data = target_state;
    exec_action_["CONTROL_AUTODOOR"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    arrive_time = ros::Time::now();
    first = true;
    LOG_INFO_THROTTLE(1, "[Atomic-action] Control_autodoor: Doing !!!");
  } else {
    if (first) {
      arrive_time = ros::Time::now();
      first = false;
    }

    if (ros::Time::now() - arrive_time > ros::Duration(3.)) {
      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      auto it = exec_action_.find("CONTROL_AUTODOOR");
      if (it != exec_action_.end()) {
        exec_action_.erase("CONTROL_AUTODOOR");
      }
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Control_autodoor =========");
    }

    return;
  }

  std_msgs::Int32 info;
  if (target_state == 1) {
    info.data = 1;
  } else {
    info.data = 2;
  }
  autodoor_task_pub_.publish(info);
}

// 取货
void AtomicAction::OpenLiftLoad(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ======== Doing Atomic Action ---- Open_Lift_load =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  Action action;
  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      auto it = exec_action_.find("UP_DOWN");
      if (it != exec_action_.end()) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;
      }
      it = exec_action_.find("FORK_HEIGHT_MOVE");
      if (it != exec_action_.end()) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
        action.data = 0.;
        exec_action_["FORK_HEIGHT_MOVE"] = action;
      }
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Lift_load diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  if (AgvType::FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;

    if ((!use_diy_finish && forklift_pallet_status.fork_up_down_state <
                                static_cast<uint8_t>(ForkStateType::UP)) ||
        (use_diy_finish && !diy_finish_state)) {
      // 使用默认条件未完成 || 自定义完成条件未完成
      if (DoingAction() &&
          ros::Time::now() - GetActionStartTime() > ros::Duration(12.0)) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(
            ActionNodeStatus::PALLET_MOVE_ERR);
        LOG_WARN_THROTTLE(
            1,
            "[Atomic-action Error] Lift_load: No Action For A Long Time !!!");
        return;
      }

      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 1.0;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      LOG_INFO_THROTTLE(1, "[Atomic-action] Lift_load: Doing !!!");
    } else {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;
      // exec_action_.erase("UP_DOWN");

      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Lift_load =========");
      return;
    }

  } else if (AgvType::HEAP_FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;
    auto height = forklift_pallet_status.height;
    double fork_speed = forklift_pallet_status.lift_velocity;
    auto height_encoder_error = forklift_pallet_status.hight_error_code;
    ros::Time height_encoder_time = forklift_pallet_status.height_stamp;
    auto heap_option = Get_info->option_.forklift_option.height_fork;
    // auto cmd_height = cmd_height_base_ + heap_option.kup_value;
    double action_value;
    if (is_open_loop) {
      action_value = Get_info->open_loop_goal_.action_value;
    } else {
      action_value = Get_info->goal_.action_value;
    }
    auto cmd_height = math::Meter2CMillimeter(action_value);

    if ((cmd_height > heap_option.kup_limit) || (cmd_height < 0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::BAD_PARAMETER_ERR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Lift_load: cmd_height: %f, "
          "setting_max_height: %f, setting_min_height: %f, out of range !!!",
          cmd_height, heap_option.kup_limit, heap_option.kdown_limit);
      return;
    }

    // 编码器错误 无法执行抬降
    if (height_encoder_error) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Lift_load: Height Encoder Error, code: %d !!!",
          height_encoder_error);
      return;
    }

    // 编码器时间戳超时 无法执行抬降
    if (ros::Time::now() - height_encoder_time > ros::Duration(0.5)) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Lift_load: Height Encoder Has "
                         "Delay In Timestamp !!!");
      return;
    }

    if (static_cast<uint8_t>(ForkPalletState::NONE) ==
            forklift_pallet_status.fork_pallet_state &&
        ((Get_info->option_.forklift_option.fork_common_option.check_pallet &&
          !use_diy_finish) ||
         (use_diy_finish && needs_check_pallet_))) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
      LOG_WARN_THROTTLE(1,
                        "[Atomic-action Error] Lift_load: Check No Pallet !!!");
      return;
    }

    auto delta_dist = cmd_height - height;
    // 拉线编码器到指定高度
    if (((delta_dist < heap_option.offset || cmd_height < height) &&
         !use_diy_finish) ||
        (use_diy_finish && diy_finish_state)) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Lift_load =========");
      return;
    } else {
      // 抬
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed),
          static_cast<double>(heap_option.kup_max_speed_with_pallet));
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = setting_speed_;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = setting_speed_;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
    }
    LOG_INFO_STREAM("[Atomic-action] Lift_Load: cmd_height: "
                    << cmd_height << ", actual_height: " << height
                    << ", speed: " << setting_speed_);

  } else if (AgvType::JACK_UP == AgvData::get()->basic_option_.agv_type) {
    auto jackup_pallet_option =
        Get_info->option_.jack_up_option.pallet_up_down_option;
    auto jackup_pallet_state = Get_info->agvdata_.jackup_pallet_state;

    // TODO:取货是否检测托盘，由前端任务界面配置，若无则默认使用config中配置
    if (!JackUpCheckPallet(jackup_pallet_option.check_pallet, Get_info)) {
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::REST); msg->atomic_action_value
      // = 0;
      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Lift_load: No Pallet Tag !!!");
      return;
    }

    // 动作
    if ((jackup_pallet_state.up_down_state ==
             static_cast<uint8_t>(JackUpDownState::UP_NONE) ||
         jackup_pallet_state.up_down_state >=
             static_cast<uint8_t>(JackUpDownState::DOWN_NONE)) &&
        (!jackup_pallet_state.height_init ||
         (jackup_pallet_state.height_init &&
          (jackup_pallet_state.lift_height <
           jackup_pallet_option.max_height)))) {
      if (jackup_pallet_state.height_init &&
          jackup_pallet_option.speed_optimize_control) {
        if (jackup_pallet_state.lift_height >=
            (jackup_pallet_option.max_height -
             jackup_pallet_option.safe_height)) {
          double ratio = fabs(jackup_pallet_state.lift_height -
                              (jackup_pallet_option.max_height -
                               jackup_pallet_option.safe_height)) /
                         jackup_pallet_option.safe_height;
          double speed = jackup_pallet_option.max_speed -
                         ((jackup_pallet_option.max_speed -
                           jackup_pallet_option.safe_speed) *
                          ratio);
          double speed_mold = speed < jackup_pallet_option.safe_speed
                                  ? jackup_pallet_option.safe_speed
                                  : speed;
          // msg->lift_v = -speed_mold;
        } else {
          // msg->lift_v = -jackup_pallet_option.max_speed;
        }
      } else {
        // msg->lift_v = -jackup_pallet_option.fixed_speed;
      }
      // msg->rotate_w = 0;
      // msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      // msg->atomic_action_value = Get_info->goal_.action_value;
      auto percent = jackup_pallet_state.height_init
                         ? 100 * (jackup_pallet_state.lift_height /
                                  jackup_pallet_option.max_height)
                         : 50;
      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      // LOG_INFO_THROTTLE(0.2, "[Atomic-action] Lift_load: lift_v: %f,
      // up_down_state: %d !!!",
      //                   msg->lift_v, jackup_pallet_state.up_down_state);
    } else {
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::REST); msg->atomic_action_value
      // = 0; msg->lift_v = 0; msg->rotate_w = 0;
      if (!DelayTime(2.0)) {
        // 动作完成 延迟2s的保护动作
        LOG_INFO_THROTTLE(0.5,
                          "[Atomic-action] Lift_load: Delay Protecting !!!");
        return;
      }
      pallet_nomove_flag_ = 2;
      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Lift_load =========");
      return;
    }

  } else {
    LOG_WARN_COND(
        Get_info->option_.enable_local_debug,
        "[Atomic-action Error] Lift_load: Unsupported AGV Type To Do !!!");
  }
}

// 取货
void AtomicAction::LiftLoad(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                            bool is_open_loop, bool use_diy_finish,
                            bool use_diy_error,
                            std::map<int, std::vector<Condition>> finish_map,
                            std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ======== Doing Atomic Action ---- Lift_load =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  Action action;
  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      auto it = exec_action_.find("UP_DOWN");
      if (it != exec_action_.end()) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;
      }
      it = exec_action_.find("FORK_HEIGHT_MOVE");
      if (it != exec_action_.end()) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
        action.data = 0.;
        exec_action_["FORK_HEIGHT_MOVE"] = action;
      }
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Lift_load diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  if (AgvType::FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;

    // 挡板检测
    if ((static_cast<uint8_t>(ForkPalletState::NONE) ==
         forklift_pallet_status.fork_pallet_state) &&
        ((Get_info->option_.forklift_option.fork_common_option.check_pallet &&
          !use_diy_finish) ||
         (use_diy_finish && needs_check_pallet_))) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
      LOG_WARN_THROTTLE(1,
                        "[Atomic-action Error] Lift_load: Check No Pallet !!!");
      return;
    }

    if ((!use_diy_finish && forklift_pallet_status.fork_up_down_state <
                                static_cast<uint8_t>(ForkStateType::UP)) ||
        (use_diy_finish && !diy_finish_state)) {
      // 使用默认条件未完成 || 自定义完成条件未完成

      if (CheckUpTimeout(true, 6.0, Get_info)) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
        }
        node_status_manager_ptr_->SetNodeStatus(
            ActionNodeStatus::PALLET_MOVE_ERR);
        LOG_WARN_THROTTLE(
            1,
            "[Atomic-action Error] Lift_load: No Action For A Long Time !!!");
        return; // 你看要不要注掉retrun,不注掉超时后不会抬保护电机什么的防止有烧掉的风险，注掉超时后继续试着抬
      }

      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 1.0;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      LOG_INFO_THROTTLE(1, "[Atomic-action] Lift_load: Doing !!!");
    } else {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;
      // exec_action_.erase("UP_DOWN");

      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      Get_info->SetLoadState(static_cast<int>(LoadStateType::ON_LOAD));
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Lift_load =========");
      return;
    }

  } else if (AgvType::HEAP_FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;
    auto height = forklift_pallet_status.height;
    double fork_speed = forklift_pallet_status.lift_velocity;
    auto height_encoder_error = forklift_pallet_status.hight_error_code;
    ros::Time height_encoder_time = forklift_pallet_status.height_stamp;
    auto heap_option = Get_info->option_.forklift_option.height_fork;
    // auto cmd_height = cmd_height_base_ + heap_option.kup_value;
    double action_value;
    if (is_open_loop) {
      action_value = Get_info->open_loop_goal_.action_value;
    } else {
      action_value = Get_info->goal_.action_value;
    }
    auto cmd_height = math::Meter2CMillimeter(action_value);

    if ((cmd_height > heap_option.kup_limit) || (cmd_height < 0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::BAD_PARAMETER_ERR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Lift_load: cmd_height: %f, "
          "setting_max_height: %f, setting_min_height: %f, out of range !!!",
          cmd_height, heap_option.kup_limit, heap_option.kdown_limit);
      return;
    }

    // 编码器错误 无法执行抬降
    if (height_encoder_error) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Lift_load: Height Encoder Error, code: %d !!!",
          height_encoder_error);
      return;
    }

    // 编码器时间戳超时 无法执行抬降
    if (ros::Time::now() - height_encoder_time > ros::Duration(0.5)) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Lift_load: Height Encoder Has "
                         "Delay In Timestamp !!!");
      return;
    }

    if (static_cast<uint8_t>(ForkPalletState::NONE) ==
            forklift_pallet_status.fork_pallet_state &&
        ((Get_info->option_.forklift_option.fork_common_option.check_pallet &&
          !use_diy_finish) ||
         (use_diy_finish && needs_check_pallet_))) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
      LOG_WARN_THROTTLE(1,
                        "[Atomic-action Error] Lift_load: Check No Pallet !!!");
      return;
    }

    auto delta_dist = cmd_height - height;
    // 拉线编码器到指定高度
    if (((delta_dist < heap_option.offset || cmd_height < height) &&
         !use_diy_finish) ||
        (use_diy_finish && diy_finish_state)) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      Get_info->SetLoadState(static_cast<int>(LoadStateType::ON_LOAD));

      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Lift_load =========");
      return;
    } else {
      // 抬
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed),
          static_cast<double>(heap_option.kup_max_speed_with_pallet));
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = setting_speed_;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = setting_speed_;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
    }
    LOG_INFO_STREAM("[Atomic-action] Lift_Load: cmd_height: "
                    << cmd_height << ", actual_height: " << height
                    << ", speed: " << setting_speed_);

  } else if (AgvType::JACK_UP == AgvData::get()->basic_option_.agv_type) {
    auto jackup_pallet_option =
        Get_info->option_.jack_up_option.pallet_up_down_option;
    auto jackup_pallet_state = Get_info->agvdata_.jackup_pallet_state;

    // TODO:取货是否检测托盘，由前端任务界面配置，若无则默认使用config中配置
    if (!JackUpCheckPallet(jackup_pallet_option.check_pallet, Get_info)) {
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::REST); msg->atomic_action_value
      // = 0;
      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Lift_load: No Pallet Tag !!!");
      return;
    }

    // 动作
    if ((jackup_pallet_state.up_down_state ==
             static_cast<uint8_t>(JackUpDownState::UP_NONE) ||
         jackup_pallet_state.up_down_state >=
             static_cast<uint8_t>(JackUpDownState::DOWN_NONE)) &&
        (!jackup_pallet_state.height_init ||
         (jackup_pallet_state.height_init &&
          (jackup_pallet_state.lift_height <
           jackup_pallet_option.max_height)))) {
      if (jackup_pallet_state.height_init &&
          jackup_pallet_option.speed_optimize_control) {
        if (jackup_pallet_state.lift_height >=
            (jackup_pallet_option.max_height -
             jackup_pallet_option.safe_height)) {
          double ratio = fabs(jackup_pallet_state.lift_height -
                              (jackup_pallet_option.max_height -
                               jackup_pallet_option.safe_height)) /
                         jackup_pallet_option.safe_height;
          double speed = jackup_pallet_option.max_speed -
                         ((jackup_pallet_option.max_speed -
                           jackup_pallet_option.safe_speed) *
                          ratio);
          double speed_mold = speed < jackup_pallet_option.safe_speed
                                  ? jackup_pallet_option.safe_speed
                                  : speed;
          // msg->lift_v = -speed_mold;
        } else {
          // msg->lift_v = -jackup_pallet_option.max_speed;
        }
      } else {
        // msg->lift_v = -jackup_pallet_option.fixed_speed;
      }
      // msg->rotate_w = 0;
      // msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      // msg->atomic_action_value = Get_info->goal_.action_value;
      auto percent = jackup_pallet_state.height_init
                         ? 100 * (jackup_pallet_state.lift_height /
                                  jackup_pallet_option.max_height)
                         : 50;
      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      // LOG_INFO_THROTTLE(0.2, "[Atomic-action] Lift_load: lift_v: %f,
      // up_down_state: %d !!!",
      //                   msg->lift_v, jackup_pallet_state.up_down_state);
    } else {
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::REST); msg->atomic_action_value
      // = 0; msg->lift_v = 0; msg->rotate_w = 0;
      if (!DelayTime(2.0)) {
        // 动作完成 延迟2s的保护动作
        LOG_INFO_THROTTLE(0.5,
                          "[Atomic-action] Lift_load: Delay Protecting !!!");
        return;
      }
      pallet_nomove_flag_ = 2;
      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      Get_info->SetLoadState(static_cast<int>(LoadStateType::ON_LOAD));
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Lift_load =========");
      return;
    }

  } else {
    LOG_WARN_COND(
        Get_info->option_.enable_local_debug,
        "[Atomic-action Error] Lift_load: Unsupported AGV Type To Do !!!");
  }
}

// 卸货
void AtomicAction::UnLoad(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                          bool is_open_loop, bool use_diy_finish,
                          bool use_diy_error,
                          std::map<int, std::vector<Condition>> finish_map,
                          std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ======== Doing Atomic Action ---- Un_load =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  Action action;
  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      auto it = exec_action_.find("UP_DOWN");
      if (it != exec_action_.end()) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;
      }
      it = exec_action_.find("FORK_HEIGHT_MOVE");
      if (it != exec_action_.end()) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
        action.data = 0.;
        exec_action_["FORK_HEIGHT_MOVE"] = action;
      }
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Un_load diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  if (AgvType::FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;

    if ((forklift_pallet_status.fork_up_down_state >
             static_cast<uint8_t>(ForkStateType::DOWN) &&
         !use_diy_finish) ||
        (use_diy_finish && !diy_finish_state)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = -1.0;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      LOG_INFO_THROTTLE(1, "[Atomic-action] Un_load: Doing !!!");
    } else {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;
      // exec_action_.erase("UP_DOWN");

      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      Get_info->SetLoadState(static_cast<int>(LoadStateType::NO_LOAD));
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Un_load =========");
      return;
    }

  } else if (AgvType::HEAP_FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;
    auto height = forklift_pallet_status.height;
    double fork_speed = forklift_pallet_status.lift_velocity;
    auto height_encoder_error = forklift_pallet_status.hight_error_code;
    ros::Time height_encoder_time = forklift_pallet_status.height_stamp;
    auto heap_option = Get_info->option_.forklift_option.height_fork;
    // auto cmd_height = cmd_height_base_ - heap_option.kdown_value;
    double action_value;
    if (is_open_loop) {
      action_value = Get_info->open_loop_goal_.action_value;
    } else {
      action_value = Get_info->goal_.action_value;
    }
    auto cmd_height = math::Meter2CMillimeter(action_value);

    if ((cmd_height > heap_option.kup_limit) || (cmd_height < 0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::BAD_PARAMETER_ERR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Lift_load: cmd_height: %f,"
          "setting_max_height: %f, setting_min_height: %f, out of range !!!",
          cmd_height, heap_option.kup_limit, heap_option.kdown_limit);
      return;
    }

    // 编码器错误 无法执行抬降
    if (height_encoder_error) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Un_load: Height Encoder Error, code: %d !!!",
          height_encoder_error);
      return;
    }

    // 编码器时间戳超时 无法执行抬降
    if (ros::Time::now() - height_encoder_time > ros::Duration(0.5)) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Un_load: Height Encoder Has "
                         "Delay In Timestamp !!!");
      return;
    }

    auto delta_dist = cmd_height - height;
    // 拉线编码器下降到最低点或降到指定高度
    if ((((delta_dist > -1. * heap_option.offset) || (cmd_height > height) ||
          height < heap_option.kdown_limit) &&
         !use_diy_finish) ||
        (use_diy_finish && diy_finish_state)) {
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = 0.;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      Get_info->SetLoadState(static_cast<int>(LoadStateType::NO_LOAD));
      LOG_INFO_STREAM(" ======== Atomic Action Done ---- Un_load =========");
      return;
    } else {
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_,
          -static_cast<double>(heap_option.kdown_max_speed_with_pallet),
          -static_cast<double>(heap_option.kdown_min_speed));
      // action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_HEIGHT_MOVE);
      // action.data = setting_speed_;
      // exec_action_["FORK_HEIGHT_MOVE"] = action;
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = setting_speed_;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
    }
    LOG_INFO_STREAM("[Atomic-action] Un_load: cmd_height: "
                    << cmd_height << ", actual_height: " << height
                    << ", speed: " << setting_speed_);

  } else if (AgvType::JACK_UP == AgvData::get()->basic_option_.agv_type) {
    auto jackup_pallet_option =
        Get_info->option_.jack_up_option.pallet_up_down_option;
    auto jackup_pallet_state = Get_info->agvdata_.jackup_pallet_state;

    if (jackup_pallet_state.up_down_state >=
            static_cast<uint8_t>(JackUpDownState::UP_NONE) &&
        jackup_pallet_state.up_down_state <=
            static_cast<uint8_t>(JackUpDownState::DOWN_NONE) &&
        (!jackup_pallet_state.height_init ||
         (jackup_pallet_state.height_init &&
          (jackup_pallet_state.lift_height >
           -jackup_pallet_option.max_height)))) {
      if (jackup_pallet_state.height_init &&
          jackup_pallet_option.speed_optimize_control) {
        if (jackup_pallet_state.lift_height <=
            jackup_pallet_option.safe_height) {
          double ratio = (jackup_pallet_option.safe_height -
                          jackup_pallet_state.lift_height) /
                         jackup_pallet_option.safe_height;
          double speed = jackup_pallet_option.max_speed -
                         ((jackup_pallet_option.max_speed -
                           jackup_pallet_option.safe_speed) *
                          ratio);
          double speed_mold = speed < jackup_pallet_option.safe_speed
                                  ? jackup_pallet_option.safe_speed
                                  : speed;
          // msg->lift_v = speed_mold;
        } else {
          // msg->lift_v = jackup_pallet_option.max_speed;
        }
      } else {
        // msg->lift_v = jackup_pallet_option.fixed_speed;
      }
      // msg->rotate_w = 0;
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::DOWN); msg->atomic_action_value
      // = Get_info->goal_.action_value;
      auto percent = jackup_pallet_state.height_init
                         ? 100 - (jackup_pallet_state.lift_height /
                                  jackup_pallet_option.max_height) *
                                     100
                         : 50;
      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      // LOG_INFO_THROTTLE(0.2, "[Atomic-action] Un_load: lift_v: %f,
      // up_down_state: %d !!!",
      //                   msg->lift_v, jackup_pallet_state.up_down_state);
    } else {
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::NONE); msg->atomic_action_value
      // = 0; msg->lift_v = 0; msg->rotate_w = 0;
      static bool DelayStart = false;
      static ros::Time DelayStartTime;
      if (!DelayStart) {
        DelayStartTime = ros::Time::now();
        DelayStart = true;
      }
      if (ros::Time::now() - DelayStartTime < ros::Duration(2)) {
        LiftSafeBreakControl(DelayStartTime, msg, Get_info);
        LOG_INFO_THROTTLE(0.5, "[Atomic-action] Un_load: Delay Protecting !!!");
        return;
      } else {
        DelayStart = false;
        pallet_nomove_flag_ = 0;
        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        Get_info->SetLoadState(static_cast<int>(LoadStateType::NO_LOAD));
        LOG_INFO_STREAM(" ======== Atomic Action Done ---- Un_load =========");
        return;
      }
    }

  } else {
    LOG_WARN_COND(
        Get_info->option_.enable_local_debug,
        "[Atomic-action Error] Un_load: Unsupported AGV Type To Do !!!");
  }
}

// 插齿高度移动
void AtomicAction::ForkHeightMove(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Heap_Fork_Move =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  Action action;
  if (AgvType::FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto forklift_pallet_status = Get_info->agvdata_.forklift_pallet_state;
    if (action_value == 1) {
      if (forklift_pallet_status.fork_up_down_state <
          static_cast<uint8_t>(ForkStateType::UP)) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 1.0;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING);
        }
        LOG_INFO_THROTTLE(1, "[Atomic-action] Lift_load: Doing !!!");
      } else {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;
        // exec_action_.erase("UP_DOWN");

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Lift_load =========");
        return;
      }
    } else {
      if (forklift_pallet_status.fork_up_down_state >
          static_cast<uint8_t>(ForkStateType::DOWN)) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = -1.0;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING);
        }
        LOG_INFO_THROTTLE(1, "[Atomic-action] Un_load: Doing !!!");
      } else {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;
        // exec_action_.erase("UP_DOWN");

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(" ======== Atomic Action Done ---- Un_load =========");
        return;
      }
    }
  } else if (AgvType::HEAP_FORKLIFT == AgvData::get()->basic_option_.agv_type) {
    auto fork_status = Get_info->agvdata_.forklift_pallet_state;
    auto height = fork_status.height;
    float fork_speed = fork_status.lift_velocity;
    auto height_encoder_error = fork_status.hight_error_code;
    ros::Time height_time = fork_status.height_stamp;
    auto heap_option = Get_info->option_.forklift_option.height_fork;
    auto cmd_height = math::Meter2CMillimeter(action_value);

    if ((cmd_height > heap_option.kup_limit) || (cmd_height < 0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::BAD_PARAMETER_ERR);
      LOG_ERROR_THROTTLE(
          1,
          "[Atomic-action Error] Heap_Fork_Move: cmd_height: %f,"
          "setting_max_height: %f, setting_min_height: %f, out of range !!!",
          cmd_height, heap_option.kup_limit, heap_option.kdown_limit);
      return;
    }

    // 拉线编码器错误或掉帧，无法执行抬降
    if (height_encoder_error) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Heap_Fork_Move: Height Encoder "
                         "Error, code: %d !!!",
                         height_encoder_error);
      return;
    }

    // 编码器时间戳超时 无法执行抬降
    if (ros::Time::now() - height_time > ros::Duration(0.5)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::HEIGHT_ENCODER_ERROR);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Heap_Fork_Move: Height Encoder "
                         "Has Delay In Timestamp !!!");
      return;
    }

    // 堆高车上限位触发，无法执行抬降
    if (fork_status.fork_up_down_state ==
        static_cast<uint8_t>(ForkStateType::UP)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
      action.data = 0.;
      exec_action_["UP_DOWN"] = action;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::FORK_UP_LIMIT);
      LOG_ERROR_THROTTLE(1,
                         "[Atomic-action Error] Heap_Fork_Move: Upper Limit "
                         "Switch is triggered !!!");
      return;
    }

    bool diy_finish_state = false;
    // 使用自定义错误条件
    if (use_diy_error) {
      if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                  use_diy_error)) {
        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;

        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_ERROR("Heap_Fork_Move diy error-condition error.");
        return;
      }
    }
    if (use_diy_finish) {
      diy_finish_state = UpdateDiyConditionState(finish_map, Get_info, msg,
                                                 is_open_loop, false);
    }

    auto delta_dist = cmd_height - height;
    bool up_down_flag = cmd_height >= height ? true : false;
    if (up_down_flag) {
      // 完成
      if (((std::fabs(delta_dist) <=
            Get_info->option_.forklift_option.height_fork.offset) &&
           !use_diy_finish) ||
          (use_diy_finish && diy_finish_state)) {
        // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
        // cmd_height_base_ = cmd_height;

        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Heap_Fork_Move =========");
        return;
      } else {
        // 抬FORK_HEIGHT_MOVE
        setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
        if (static_cast<uint8_t>(ForkPalletState::NONE) ==
            fork_status.fork_pallet_state) {
          setting_speed_ = math::Clamp(
              setting_speed_, static_cast<double>(heap_option.kup_min_speed),
              static_cast<double>(heap_option.kup_max_speed_no_pallet));
        } else {
          setting_speed_ = math::Clamp(
              setting_speed_, static_cast<double>(heap_option.kup_min_speed),
              static_cast<double>(heap_option.kup_max_speed_with_pallet));
        }

        if (std::fabs(delta_dist) <=
            Get_info->option_.forklift_option.height_fork.offset) {
          setting_speed_ = 0;
        }

        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = setting_speed_;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING);
        }
      }
    } else {
      // 完成
      if (((std::fabs(delta_dist) <=
                Get_info->option_.forklift_option.height_fork.offset ||
            height < heap_option.kdown_limit) &&
           !use_diy_finish) ||
          (use_diy_finish && diy_finish_state)) {
        // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
        // cmd_height_base_ = cmd_height;

        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = 0.;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Heap_Fork_Move =========");
        return;
      } else {
        setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
        if (static_cast<uint8_t>(ForkPalletState::NONE) ==
            fork_status.fork_pallet_state) {
          setting_speed_ = math::Clamp(
              setting_speed_,
              -static_cast<double>(heap_option.kdown_max_speed_no_pallet),
              -static_cast<double>(heap_option.kdown_min_speed));
        } else {
          setting_speed_ = math::Clamp(
              setting_speed_,
              -static_cast<double>(heap_option.kdown_max_speed_with_pallet),
              -static_cast<double>(heap_option.kdown_min_speed));
        }

        if (std::fabs(delta_dist) <=
            Get_info->option_.forklift_option.height_fork.offset) {
          setting_speed_ = 0;
        }

        action.cmd = static_cast<uint32_t>(AtomicActionType::UP_DOWN);
        action.data = setting_speed_;
        exec_action_["UP_DOWN"] = action;

        if (is_open_loop) {
          Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
        } else {
          Get_info->SetState(50, false, ActionStatus::DOING);
        }
      }
    }
    LOG_INFO_STREAM("[Atomic-action] Heap_Fork_Move: cmd_height: "
                    << cmd_height << ", actual_height: " << height
                    << ", speed: " << setting_speed_);
  } else {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    LOG_WARN_STREAM_THROTTLE(1, "Not support car type: " << static_cast<int>(
                                    AgvData::get()->basic_option_.agv_type));
  }
}

// 插齿倾斜
void AtomicAction::ForkTiltMove(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Fork_Tilt =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto imu_data = Get_info->agvdata_.imu_monitor;
  auto angle = imu_data.angle_x;  //当前叉腿的实时俯仰角度
  auto imu_error = imu_data.error_code;
  ros::Time imu_time = imu_data.time_stamp;
  auto tilt_speed = imu_data.omega_x;

  auto tilt_option = Get_info->option_.forklift_option.tilt_fork;
  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;

  Action action;

  if (imu_error) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_TILT_MOVE);
    action.data = 0.;
    exec_action_["FORK_TILT_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IMU_ERROR);
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Fork_Tilt: IMU Error, code: %d !!!",
        imu_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - imu_time > ros::Duration(0.5)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_TILT_MOVE);
    action.data = 0.;
    exec_action_["FORK_TILT_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IMU_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Fork_Tilt: IMU "
                       "Has Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_TILT_MOVE);
      action.data = 0.;
      exec_action_["FORK_TILT_MOVE"] = action;
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Fork_Tilt diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  double cmd_angle = action_value;
  auto delta_angle = cmd_angle - angle;
  if ((std::fabs(delta_angle) < tilt_option.offset && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_TILT_MOVE);
    action.data = 0.;
    exec_action_["FORK_TILT_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(" ======== Atomic Action Done ---- Fork_Tilt =========");
    return;
  } else {
    setting_speed_ += GetForkTiltSpeed(cmd_angle, angle, std::fabs(tilt_speed));
    if (delta_angle > 0) {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(tilt_option.kmin_speed),
            static_cast<double>(tilt_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(tilt_option.kmin_speed),
            static_cast<double>(tilt_option.kmax_speed_with_pallet));
      }
    } else {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(tilt_option.kmin_speed),
            -static_cast<double>(tilt_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(tilt_option.kmin_speed),
            -static_cast<double>(tilt_option.kmax_speed_with_pallet));
      }
    }
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_TILT_MOVE);
    action.data = setting_speed_;
    exec_action_["FORK_TILT_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
  LOG_INFO_STREAM("[Atomic-action] Fork_Tilt: cmd_angle: "
                  << cmd_angle << ", actual_angle: " << angle
                  << ", speed: " << setting_speed_);
}

// 等待插齿静止不晃动
void AtomicAction::WaitingForkRest(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Waiting_Fork_Rest =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto imu_data = Get_info->agvdata_.imu_monitor;
  auto imu_error = imu_data.error_code;
  ros::Time imu_time = imu_data.time_stamp;

  if (imu_error) {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IMU_ERROR);
    LOG_ERROR_THROTTLE(
        1, "[Atomic-action Error] Waiting_Fork_Rest: IMU Error, code: %d !!!",
        imu_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - imu_time > ros::Duration(0.5)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::IMU_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Waiting_Fork_Rest: IMU "
                       "Has Delay In Timestamp !!!");
    return;
  }

  static bool nosway_flag = false;
  if (record_angle_.size() < 25 && !nosway_flag) {
    record_angle_.push_back(imu_data.angle_x);
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    return;
  } else {
    if (!nosway_flag) {
      for (int i = 1; i < record_angle_.size(); i++) {
        LOG_INFO("record_angle[%d]: %.2f, ", i, record_angle_[i - 1]);
        if (std::fabs(record_angle_[0] - record_angle_[i]) > 0.05) {
          record_angle_.erase(record_angle_.begin());
          if (is_open_loop) {
            Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
          } else {
            Get_info->SetState(50, false, ActionStatus::DOING);
          }
          LOG_WARN_THROTTLE(
              0.2,
              "[Atomic-action] Waiting_Fork_Rest: Fork is still swaying !!!");
          return;
        }
      }
    }
    nosway_flag = true;
  }

  if (nosway_flag) {
    nosway_flag = false;
    record_angle_.clear();
    static_angle_ = imu_data.angle_x;
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Waiting_Fork_Rest =========");
    return;
  }
}

// 插齿前后移
void AtomicAction::ForkLateralMove(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Fork_Lateral_Move =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto lateral = pallet_status.lateral;
  auto lateral_error = pallet_status.lateral_error_code;
  double lateral_speed = pallet_status.lateral_velocity;
  ros::Time lateral_time = pallet_status.lateral_stamp;
  auto lateral_option = Get_info->option_.forklift_option.lateral_fork;

  Action action;

  // 编码器错误 无法执行抬降
  if (lateral_error) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::LATERAL_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Fork_Lateral_Move: Lateral "
                       "Encoder Error, code: %d !!!",
                       lateral_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - lateral_time > ros::Duration(0.5)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::LATERAL_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(
        1,
        "[Atomic-action Error] Fork_Lateral_Move: Lateral Encoder "
        "Has Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
      action.data = 0.;
      exec_action_["FORK_LATERAL_MOVE"] = action;
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Fork_Lateral_Move diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  auto cmd_lateral = math::Meter2CMillimeter(action_value);
  float delta_lateral = cmd_lateral - lateral;
  if ((Get_info->agvdata_.agv_io_state.safety_io_state >=
       static_cast<uint8_t>(AvoidLevel::FORK_LEFT_LEG)) &&
      (delta_lateral > 0)) {
    // 叉尖io避障, 停止前移
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::FORK_IO_AVOID);
    LOG_ERROR_THROTTLE(0.5,
                       "[Atomic-action] Fork_Lateral_Move: Fork IO Avoid !!!");
    return;
  }

  if ((std::fabs(delta_lateral) < lateral_option.offset && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Fork_Lateral_Move =========");
    return;
  } else {
    setting_speed_ += GetLateralSpeed(cmd_lateral, lateral, lateral_speed);
    if (cmd_lateral > lateral) {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(lateral_option.kup_min_speed),
            static_cast<double>(lateral_option.kup_max_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(lateral_option.kup_min_speed),
            static_cast<double>(lateral_option.kup_max_speed_with_pallet));
      }
    } else {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_,
            -static_cast<double>(lateral_option.kdown_min_speed),
            -static_cast<double>(lateral_option.kdown_max_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_,
            -static_cast<double>(lateral_option.kdown_min_speed),
            -static_cast<double>(lateral_option.kdown_max_speed_with_pallet));
      }
    }
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = setting_speed_;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
  LOG_INFO_STREAM("[Atomic-action] Fork_Lateral_Move: cmd_lateral: "
                  << cmd_lateral << ", actual_lateral: " << lateral
                  << ", speed: " << setting_speed_);
}

//前移
void AtomicAction::ForkCompensateForward(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Fork_Compensate_Forward =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;
  auto lateral = pallet_status.lateral;
  auto lateral_error = pallet_status.lateral_error_code;
  double lateral_speed = pallet_status.lateral_velocity;
  ros::Time lateral_time = pallet_status.lateral_stamp;
  auto lateral_option = Get_info->option_.forklift_option.lateral_fork;

  Action action;

  // 编码器错误 无法执行抬降
  if (lateral_error) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::LATERAL_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Fork_Compensate_Forward: Lateral "
                       "Encoder Error, code: %d !!!",
                       lateral_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - lateral_time > ros::Duration(0.5)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::LATERAL_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(
        1,
        "[Atomic-action Error] Fork_Compensate_Forward: Lateral Encoder "
        "Has Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
      action.data = 0.;
      exec_action_["FORK_LATERAL_MOVE"] = action;
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Fork_Compensate_Forward diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  if (static_angle_ == 0) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;
    LOG_INFO_THROTTLE(1,
                      "[Atomic-action Error] Fork_Compensate_Forward: No "
                      "waiting fork rest action !!!");
    return;
  }

  float delta_l = 0.0;
  auto tilt_standard_angle =
      Get_info->option_.forklift_option.fork_common_option.tilt_standard_angle;
  if (static_angle_ < tilt_standard_angle) {
    delta_l =
        height * std::tan(math::Deg2Rad(tilt_standard_angle - static_angle_));
    LOG_INFO_THROTTLE(
        0.2,
        "[Atomic-action Error] Fork_Compensate_Forward: "
        "delta_l: %.2f, tilt_standard_angle: %.2f, static_angle: %.2f",
        delta_l, tilt_standard_angle, static_angle_);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  float goal_lateral = action_value;
  float cmd_lateral = goal_lateral - delta_l;  //根据现场需修改配置
  float delta_lateral = lateral - cmd_lateral;

  if ((std::fabs(delta_lateral) < lateral_option.offset && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    static_angle_ = 0;
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = 0.;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Fork_Compensate_Forward =========");
    return;
  } else {
    setting_speed_ += GetLateralSpeed(cmd_lateral, lateral, lateral_speed);
    if (static_cast<uint8_t>(ForkPalletState::NONE) ==
        pallet_status.fork_pallet_state) {
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(lateral_option.kup_min_speed),
          static_cast<double>(lateral_option.kup_max_speed_no_pallet));
    } else {
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(lateral_option.kup_min_speed),
          static_cast<double>(lateral_option.kup_max_speed_with_pallet));
    }
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_LATERAL_MOVE);
    action.data = setting_speed_;
    exec_action_["FORK_LATERAL_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
  LOG_INFO_STREAM("[Atomic-action] Fork_Compensate_Forward: cmd_lateral: "
                  << cmd_lateral << ", actual_lateral: " << lateral
                  << ", speed: " << setting_speed_);
}

// 插齿侧移
void AtomicAction::ForkSideMove(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Side_Shift_Move =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto sideshift = pallet_status.sideshift;
  auto sideshift_error = pallet_status.sideshift_error_code;
  double sidemove_speed = pallet_status.sideshift_velocity;
  ros::Time side_encoder_time = pallet_status.sideshift_stamp;

  Action action;

  // 编码器错误 无法执行抬降
  if (sideshift_error) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::SIDEMOVE_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Side_Shift_Move: Sideshift "
                       "Encoder Error, code: %d !!!",
                       sideshift_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - side_encoder_time > ros::Duration(0.5)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::SIDEMOVE_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(
        1,
        "[Atomic-action Error] Side_Shift_Move: SideShift Encoder "
        "Has Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
      action.data = 0.;
      exec_action_["FORK_SIDE_MOVE"] = action;
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Side_Shift_Move diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  auto side_option = Get_info->option_.forklift_option.sideshift_fork;

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  double cmd_sideshift = action_value;
  if (cmd_sideshift > side_option.limit) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::BAD_PARAMETER_ERR);
    LOG_WARN_THROTTLE(1,
                      "[Atomic-action Error] Side_Shift_Move: Max Dist Limit "
                      "Is Exceeded !!!");
    return;
  }

  float delta_sideshift = cmd_sideshift - sideshift;
  if ((std::fabs(delta_sideshift) < side_option.offset && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Side_Shift_Move =========");
    return;
  } else {
    setting_speed_ +=
        GetSideMoveSpeed(cmd_sideshift, sideshift, sidemove_speed);
    if (delta_sideshift > 0) {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(side_option.kmin_speed),
            static_cast<double>(side_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(side_option.kmin_speed),
            static_cast<double>(side_option.kmax_speed_with_pallet));
      }
    } else {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(side_option.kmin_speed),
            -static_cast<double>(side_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(side_option.kmin_speed),
            -static_cast<double>(side_option.kmax_speed_with_pallet));
      }
    }
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = setting_speed_;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
  LOG_INFO_STREAM("[Atomic-action] Side_Shift_Move: cmd_lateral: "
                  << cmd_sideshift << ", actual_sideshift: " << sideshift
                  << ", speed: " << setting_speed_);
}

// 充电
void AtomicAction::Charge(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                          bool is_open_loop, bool use_diy_finish,
                          bool use_diy_error,
                          std::map<int, std::vector<Condition>> finish_map,
                          std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Charge =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  Action action;

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      auto it = exec_action_.find("CHARGE");
      if (it != exec_action_.end()) {
        exec_action_.erase("CHARGE");
      }
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Charge diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  action.cmd = static_cast<uint32_t>(AtomicActionType::CHARGE);
  action.data = action_value;
  exec_action_["CHARGE"] = action;

  if (is_open_loop) {
    Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
  } else {
    Get_info->SetState(50, false, ActionStatus::DOING);
  }

  // TODO:充电使用哪种检测，由前端任务界面配置，若无则默认使用config中配置
  switch (static_cast<cotek_action::ChargeDetectionType>(
      Get_info->option_.charge_option.charge_detection)) {
    // TODO:修改电流、电压为起充电压，起充电流，充电过压，停止电流
    case ChargeDetectionType::CURRENT: {
      LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: Current Detection !!!");
      if ((action_value == 1) &&
          (Get_info->agvdata_.battery_monitor.current >
           Get_info->option_.charge_option.start_charge_current)) {
        // 电池电流 > 起充电流
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Start Charge ========= ");
        return;
      } else if ((action_value == 0) &&
                 (Get_info->agvdata_.battery_monitor.current <
                  Get_info->option_.charge_option.stop_charge_current)) {
        // 电池电流 < 停止电流
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Stop Charge =========");
        return;
      } else {
        LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: Doing !!!");
      }
      break;
    }
    case ChargeDetectionType::VOLTAGE: {
      LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: Voltage Detection !!!");
      if ((action_value == 1) &&
          (Get_info->agvdata_.battery_monitor.voltage >
           Get_info->option_.charge_option.start_charge_voltage)) {
        // 电池电压 > 起充电压
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Start Charge =========");
        return;
      } else if ((action_value == 0) &&
                 (Get_info->agvdata_.battery_monitor.voltage >
                  Get_info->option_.charge_option.stop_charge_voltage)) {
        // 电池电压 > 停止电压
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Stop Charge =========");
        return;
      } else {
        LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: Doing !!!");
      }
      break;
    }
    case ChargeDetectionType::BOTH: {
      LOG_INFO_THROTTLE(
          1, "[Atomic-action] Charge: Current & Voltage Detection !!!");
      if ((action_value == 1) &&
          (Get_info->agvdata_.battery_monitor.current >
           Get_info->option_.charge_option.start_charge_current) &&
          (Get_info->agvdata_.battery_monitor.voltage >
           Get_info->option_.charge_option.start_charge_voltage)) {
        // 电池电流 > 起充电流 & 电池电压 > 起充电压
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Start Charge =========");
        return;
      } else if ((action_value == 0) &&
                 (Get_info->agvdata_.battery_monitor.current <
                  Get_info->option_.charge_option.stop_charge_current) &&
                 (Get_info->agvdata_.battery_monitor.voltage >
                  Get_info->option_.charge_option.stop_charge_voltage)) {
        // 电池电流 < 停止电流 & 电池电压 > 停止电压
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Stop Charge =========");
        return;
      } else {
        LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: Doing !!!");
      }
      break;
    }
    default: {
      LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: None Detection !!!");
      // 检查继电器是否打开
      if (((Get_info->agvdata_.agv_io_state.charge_do_state) &&
           (action_value == 1) && !use_diy_finish) ||
          (use_diy_finish && diy_finish_state)) {
        // TODO(@ssh) 临时解决　调度需打开继电器后至少发一帧doing 此处延时
        ros::Duration(1.0).sleep();
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Start Charge =========");
        return;
      } else if (((!Get_info->agvdata_.agv_io_state.charge_do_state) &&
                  (action_value == 0) && !use_diy_finish) ||
                 (use_diy_finish && diy_finish_state)) {
        auto it = exec_action_.find("CHARGE");
        if (it != exec_action_.end()) {
          exec_action_.erase("CHARGE");
        }

        if (is_open_loop) {
          Get_info->SetOpenLoopStateSucceed();
          OpenLoopDataReset();
        } else {
          Get_info->SetStateSucceed();
          DataReset();
        }
        LOG_INFO_STREAM(
            " ======== Atomic Action Done ---- Stop Charge =========");
        return;
      } else {
        LOG_INFO_THROTTLE(1, "[Atomic-action] Charge: Doing !!!");
      }
      break;
    }
  }
}

// 取货后称重
void AtomicAction::CheckUPWeight(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Check_up_weight =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto weigh_data = Get_info->agvdata_.weigh_monitor;
  auto weight_error = weigh_data.weigh_errcode;
  double weight = weigh_data.weight;
  auto weight_option = Get_info->option_.forklift_option.fork_common_option;

  if (weight_error) {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::WEIGHT_SENSOR_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Check_up_weight: Weight Sensor "
                       "Error, code: %d !!!",
                       weight_error);
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error))
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Check_up_weight diy error-condition error.");
    return;
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  LOG_INFO_STREAM_THROTTLE(
      0.5, "[Atomic-action] Check_up_weight: expected_min_weight: "
               << weight_option.kload_weighing_threshold
               << ", current_weight: " << weight);

  if ((weight > weight_option.kload_weighing_threshold && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Check_up_weight =========");
    return;
  } else {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
    LOG_ERROR_THROTTLE(0.5,
                       "[Atomic-action Error] Check_up_weight: Weight Lower "
                       "Than Expected !!!");
  }
}

void AtomicAction::CheckDownWeight(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Check_down_weight =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto weigh_data = Get_info->agvdata_.weigh_monitor;
  auto weight_error = weigh_data.weigh_errcode;
  double weight = weigh_data.weight;
  auto weight_option = Get_info->option_.forklift_option.fork_common_option;

  if (weight_error) {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::WEIGHT_SENSOR_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Check_down_weight: Weight Sensor "
                       "Error, code: %d !!!",
                       weight_error);
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error))
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Check_down_weight diy error-condition error.");
    return;
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  LOG_INFO_STREAM_THROTTLE(
      0.5, "[Atomic-action] Check_down_weight: expected_max_weight: "
               << weight_option.kload_weighing_threshold
               << ", current_weight: " << weight);

  if ((weight < weight_option.kload_weighing_threshold && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Check_down_weight =========");
    return;
  } else {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
    LOG_ERROR_THROTTLE(0.5,
                       "[Atomic-action Error] Check_down_weight: Weight More "
                       "Than Expected !!!");
    return;
  }
}

// 顶升车托盘旋转
void AtomicAction::PalletRotation(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Pallet_rotation =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  pallet_nomove_flag_ = 0;
  static int cnt = 0;
  static int rotate_cnt = 0;

  bool use_rotate = BasicConfigHelper::Instance()
                        .mechanical_option()
                        .unicycle_model_option.use_rotate;

  if (Get_info->agvdata_.jackup_pallet_state.up_down_state ==
          static_cast<uint8_t>(JackUpDownState::UP_NONE) ||
      Get_info->agvdata_.jackup_pallet_state.up_down_state >=
          static_cast<uint8_t>(JackUpDownState::DOWN_NONE) ||
      !use_rotate) {
    // 顶升机构在底部，或者不启用旋转电机，则直接完成
    rotate_cnt = 0;
    pallet_nomove_flag_ = 2;
    // msg->rotate_w = 0.;
    // msg->lift_v = 0.;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM("[Atomic-action] Pallet_rotation: Pallet is on bottom !!!");
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Pallet_rotation =========");
    return;
  }

  if (Get_info->agvdata_.jackup_pallet_state.palletag == 0) {
    // 托盘旋转需要有码做参照
    cnt++;
    if (cnt > 50) {
      rotate_cnt = 0;
      up_tag_loss_ = true;
      // msg->rotate_w = 0.;
      // msg->lift_v = 0.;
      // msg->atomic_action_type =
      // static_cast<uint32_t>(AtomicActionType::REST); msg->atomic_action_value
      // = 0;

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
      }
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::SHELF_LOSS);
      LOG_ERROR_THROTTLE(
          1, "[Atomic-action Error] Pallet_rotation: Shelf No Tag !!!");
      return;
    }
    return;
  } else {
    cnt = 0;
    up_tag_loss_ = false;
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }
  double pallet_rotate_val = common::Degree2rad(action_value);

  float diff = common::AngleSubtract(pallet_rotate_val, shelf_cur_theta_);
  if (fabs(diff) > common::Degree2rad(0.5)) {
    rotate_cnt++;
    // msg->atomic_action_type =
    //     static_cast<uint32_t>(AtomicActionType::PALLET_ROTATION);
    // msg->atomic_action_value = 0;
    // msg->rotate_w = -math::Sign(diff) *
    //                 std::min(std::fabs(diff), static_cast<float>(rotate_cnt)
    //                 * math::Deg2Rad(0.1f));
    // msg->rotate_w = math::Clamp(msg->rotate_w, -0.5, 0.5);

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    // LOG_INFO_THROTTLE(0.1, "[Atomic-action] Pallet_rotation:"
    //                   "shelf_cur_theta: %f, goal_theta: %f, rotate_w: %f,
    //                   diff: %f", common::rad2degree(shelf_cur_theta_),
    //                   Get_info->goal_.action_value, msg->rotate_w,
    //                   common::rad2degree(diff));
  } else {
    rotate_cnt = 0;
    pallet_nomove_flag_ = 2;
    // msg->rotate_w = 0.;
    // msg->lift_v = 0.;
    // msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    // msg->atomic_action_value = 0;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "[Atomic-action] Pallet_rotation: Arrived!!! diff:%f",
                  common::rad2degree(diff));
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Pallet_rotation =========");
    return;
  }
}

void AtomicAction::PalletNoMove(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Pallet_no_move =========");

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }
  pallet_nomove_flag_ = static_cast<uint32_t>(action_value);
  // msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  // msg->atomic_action_value = 0;
  if (is_open_loop) {
    Get_info->SetOpenLoopStateSucceed();
    OpenLoopDataReset();
  } else {
    Get_info->SetStateSucceed();
    DataReset();
  }
  LOG_INFO("[Atomic-action] Pallet_no_move: pallet_nomove_flag: %d",
           pallet_nomove_flag_);
}

// 顶升车托盘归零
void AtomicAction::PalletZero(cotek_msgs::action_cmd *msg, AgvData *Get_info,
                              bool is_open_loop, bool use_diy_finish,
                              bool use_diy_error,
                              std::map<int, std::vector<Condition>> finish_map,
                              std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Pallet_zero =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  pallet_nomove_flag_ = 0;

  if (Get_info->agvdata_.jackup_pallet_state.roate_state == 0) {
    // msg->atomic_action_type =
    //     static_cast<uint32_t>(AtomicActionType::PALLET_ZERO);
    // msg->atomic_action_value = Get_info->goal_.action_value;
    // msg->lift_v = 0;
    // msg->rotate_w =
    //     Get_info->option_.jack_up_option.pallet_rotation_zero_option.safe_speed;
    // msg->rotate_w =
    //     fabs(msg->rotate_w) > fabs(Get_info->option_.jack_up_option
    //                                   .pallet_rotation_zero_option.max_speed)
    //         ?
    //         Get_info->option_.jack_up_option.pallet_rotation_zero_option.max_speed
    //         : msg->rotate_w;
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    // LOG_INFO_THROTTLE(0.2, "[Atomic-action] Pallet_zero: rotate_w: %f",
    // msg->rotate_w);
  } else {
    // msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    // msg->atomic_action_value = 0;
    // msg->lift_v = 0;
    // msg->rotate_w = 0;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(" ======== Atomic Action Done ---- Pallet_zero =========");
    return;
  }
}

void AtomicAction::PalletCorrect(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- Pallet_correct =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  if (Get_info->agvdata_.jackup_pallet_state.up_down_state ==
          static_cast<uint8_t>(JackUpDownState::UP_NONE) ||
      Get_info->agvdata_.jackup_pallet_state.up_down_state >=
          static_cast<uint8_t>(JackUpDownState::DOWN_NONE)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO("[Atomic-action] Pallet_correct: Pallet on bottom !!!");
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Pallet_correct =========");
    return;
  }

  float diff = angles::normalize_angle(pallet_tar_theta_ - shelf_cur_theta_);
  LOG_WARN_THROTTLE(0.1, "[JackupAction::PalletCorrect] diff:%f",
                    math::Rad2Deg(diff));

  pallet_nomove_flag_ = 1;
  static bool DelayStart = false;
  static ros::Time DelayStartTime;

  // msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  // msg->atomic_action_value = 0;
  // msg->lift_v = 0;
  // msg->rotate_w = 0;

  if (!DelayStart) {
    DelayStartTime = ros::Time::now();
    DelayStart = true;
  }
  if (ros::Time::now() - DelayStartTime > ros::Duration(4) ||
      fabs(diff) < common::Degree2rad(0.1)) {
    DelayStart = false;
    pallet_nomove_flag_ = 2;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Pallet_correct =========");
    return;
  } else {
    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    LOG_INFO_THROTTLE(1,
                      "[Atomic-action] Pallet_correct: shelf_cur_theta_: %f,"
                      "pallet_tar_theta_: %f, diff: %f",
                      shelf_cur_theta_, pallet_tar_theta_, math::Rad2Deg(diff));
  }
}

// 放货检测
void AtomicAction::UnloadDetect(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                " ========= Doing Atomic Action ---- UnLoad_detect =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  PublishRequestUnloadDetect();

  auto &unload_detect = Get_info->agvdata_.others_feedback.unload_detect_data;

  if (ros::Time::now() - unload_detect.time < ros::Duration(2.0)) {
    if (unload_detect.permit_unload) {
      if (is_open_loop) {
        Get_info->SetOpenLoopStateSucceed();
        OpenLoopDataReset();
      } else {
        Get_info->SetStateSucceed();
        DataReset();
      }
      LOG_INFO("[Atomic-action] UnLoad_detect: Permit Unload !!!");
    } else {
      LOG_INFO("[Atomic-action] UnLoad_detect: Unallowed unload !!!");
      return;
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- UnLoad_detect =========");
    return;
  } else {
    if (ros::Time::now() - GetActionStartTime() > ros::Duration(5.0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::UNLOAD_DETECT_ERROR);
      LOG_INFO_THROTTLE(1, "[Atomic-action] UnLoad_detect: Detect Error !!!");
    }

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
    LOG_INFO_THROTTLE(1, "[Atomic-action] UnLoad_detect: Doing !!!");
  }
}

void AtomicAction::PalletBackLimit(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Pallet_back_limit =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  PublishRequestPalletBackLimit();

  auto &pallet_back_limit =
      Get_info->agvdata_.others_feedback.pallet_back_limit_type;

  if (pallet_back_limit != PalletBackLimitType::FREE &&
      ros::Time::now() - GetActionStartTime() > ros::Duration(2.0)) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::PALLET_LIMIT_ERROR);
    LOG_INFO_THROTTLE(1, "[Atomic-action] Pallet_back_limit: Detect Error !!!");
  }
  if (pallet_back_limit == PalletBackLimitType::FREE &&
      ros::Time::now() - GetActionStartTime() < ros::Duration(2.0)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Pallet_back_limit =========");
    return;
  }
}

void AtomicAction::QueryStorageInfo(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Query_storage_info =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  double action_value;
  if (is_open_loop) {
    action_value = Get_info->open_loop_goal_.action_value;
  } else {
    action_value = Get_info->goal_.action_value;
  }

  cotek_msgs::query_storage_info query_info;
  query_info.request.order_id = Get_info->goal_.order_id;
  query_info.request.storage_area_id = action_value;
  ros::NodeHandle nh;
  ros::ServiceClient query_storage_srv =
      nh.serviceClient<cotek_msgs::query_storage_info>("query_storage");
  if (!query_storage_srv.call(query_info)) {
    LOG_ERROR_THROTTLE(1, "[Atomic-action] Query_storage_info: Failed !!!");
  } else {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Query_storage_info =========");
    return;
  }
}

// 托盘位置检测带横移
void AtomicAction::PalletCenterDetect(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ---- Pallet_center_detect =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto &pallet_center_detect =
      Get_info->agvdata_.others_feedback.pallet_center_data;

  if (ros::Time::now() - pallet_center_detect.time < ros::Duration(2.0) &&
      pallet_delta_y_ == 0) {
    pallet_delta_y_ = pallet_center_detect.delta_y;
    LOG_INFO("[Atomic-action] Pallet_center_detect: delta_y: %f",
             pallet_delta_y_);
  } else {
    if (pallet_delta_y_ == 0) {
      PublishRequestPalletCheck(Get_info->goal_.storage_pose_x,
                                Get_info->goal_.storage_pose_y,
                                Get_info->goal_.storage_pose_yaw);

      if (ros::Time::now() - GetActionStartTime() > ros::Duration(5.0)) {
        node_status_manager_ptr_->SetNodeStatus(
            ActionNodeStatus::PALLET_DETECT_ERROR);
        LOG_INFO_THROTTLE(
            1, "[Atomic-action] Pallet_center_detect: Detect Error !!!");
      }

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      return;
    }
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto sideshift = pallet_status.sideshift;
  auto sideshift_error = pallet_status.sideshift_error_code;
  double sidemove_speed = pallet_status.sideshift_velocity;
  ros::Time side_encoder_time = pallet_status.sideshift_stamp;

  Action action;

  // 编码器错误 无法执行抬降
  if (sideshift_error) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::SIDEMOVE_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Pallet_center_detect: Sideshift "
                       "Encoder Error, code: %d !!!",
                       sideshift_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - side_encoder_time > ros::Duration(0.5)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::SIDEMOVE_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(
        1,
        "[Atomic-action Error] Pallet_center_detect: SideShift Encoder "
        "Has Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
      action.data = 0.;
      exec_action_["FORK_SIDE_MOVE"] = action;
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Pallet_center_detect diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  auto side_option = Get_info->option_.forklift_option.sideshift_fork;

  double cmd_sideshift = pallet_delta_y_;
  if (cmd_sideshift > side_option.limit) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::BAD_PARAMETER_ERR);
    LOG_WARN_THROTTLE(1,
                      "[Atomic-action Error] Pallet_center_detect: Max Dist "
                      "Limit Is Exceeded !!!");
    return;
  }

  float delta_sideshift = cmd_sideshift - sideshift;
  if ((std::fabs(delta_sideshift) < side_option.offset && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    pallet_delta_y_ = 0;
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Pallet_center_detect =========");
    return;
  } else {
    setting_speed_ +=
        GetSideMoveSpeed(cmd_sideshift, sideshift, sidemove_speed);
    if (delta_sideshift > 0) {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(side_option.kmin_speed),
            static_cast<double>(side_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(side_option.kmin_speed),
            static_cast<double>(side_option.kmax_speed_with_pallet));
      }
    } else {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(side_option.kmin_speed),
            -static_cast<double>(side_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(side_option.kmin_speed),
            -static_cast<double>(side_option.kmax_speed_with_pallet));
      }
    }

    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = setting_speed_;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
  LOG_INFO_STREAM("[Atomic-action] Pallet_center_detect: cmd_side: "
                  << cmd_sideshift << ", actual_side: " << sideshift
                  << ", speed: " << setting_speed_);
}

// 货架位置检测
void AtomicAction::ColumnPositionDetect(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ----Column_pos_detect =========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      ((Get_info->state_.is_finish != true && !is_open_loop) ||
       (Get_info->open_loop_state_.is_finish != true && is_open_loop))) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto &column_detect = Get_info->agvdata_.others_feedback.column_pos_data;

  if (ros::Time::now() - column_detect.time < ros::Duration(2.0) &&
      column_delta_y_ == 0) {
    column_delta_y_ = column_detect.delta_y;
    LOG_INFO("[Atomic-action] Column_pos_detect: delta_y: %f", column_delta_y_);
  } else {
    if (column_delta_y_ == 0) {
      PublishRequestColumnPositionDetect(Get_info->goal_.storage_pose_x,
                                         Get_info->goal_.storage_pose_y,
                                         Get_info->goal_.storage_pose_yaw);

      if (ros::Time::now() - GetActionStartTime() > ros::Duration(5.0)) {
        node_status_manager_ptr_->SetNodeStatus(
            ActionNodeStatus::COLUMN_DETECT_ERROR);
        LOG_INFO_THROTTLE(
            1, "[Atomic-action] Column_pos_detect: Detect Error !!!");
      }

      if (is_open_loop) {
        Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
      } else {
        Get_info->SetState(50, false, ActionStatus::DOING);
      }
      return;
    }
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto sideshift = pallet_status.sideshift;
  auto sideshift_error = pallet_status.sideshift_error_code;
  double sidemove_speed = pallet_status.sideshift_velocity;
  ros::Time side_encoder_time = pallet_status.sideshift_stamp;

  Action action;

  if (sideshift_error) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::SIDEMOVE_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(1,
                       "[Atomic-action Error] Column_pos_detect: Sideshift "
                       "Encoder Error, code: %d !!!",
                       sideshift_error);
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - side_encoder_time > ros::Duration(0.5)) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::SIDEMOVE_ENCODER_ERROR);
    LOG_ERROR_THROTTLE(
        1,
        "[Atomic-action Error] Column_pos_detect: SideShift Encoder "
        "Has Delay In Timestamp !!!");
    return;
  }

  bool diy_finish_state = false;
  // 使用自定义错误条件
  if (use_diy_error) {
    if (UpdateDiyConditionState(error_map, Get_info, msg, is_open_loop,
                                use_diy_error)) {
      action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
      action.data = 0.;
      exec_action_["FORK_SIDE_MOVE"] = action;
      /*   node_status_manager_ptr_->SetNodeStatus(
             ActionNodeStatus::DIY_CONDITION_ERROR);*/
      LOG_ERROR("Column_pos_detect diy error-condition error.");
      return;
    }
  }
  if (use_diy_finish) {
    diy_finish_state =
        UpdateDiyConditionState(finish_map, Get_info, msg, is_open_loop, false);
  }

  auto side_option = Get_info->option_.forklift_option.sideshift_fork;

  double cmd_sideshift = column_delta_y_;
  if (cmd_sideshift > side_option.limit) {
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING_FAULT);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING_FAULT);
    }
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::BAD_PARAMETER_ERR);
    LOG_WARN_THROTTLE(1,
                      "[Atomic-action Error] Column_pos_detect: Max Dist Limit "
                      "Is Exceeded !!!");
    return;
  }

  float delta_sideshift = cmd_sideshift - sideshift;
  if ((std::fabs(delta_sideshift) < side_option.offset && !use_diy_finish) ||
      (use_diy_finish && diy_finish_state)) {
    column_delta_y_ = 0;
    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = 0.;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Column_pos_detect =========");
    return;
  } else {
    setting_speed_ +=
        GetSideMoveSpeed(cmd_sideshift, sideshift, sidemove_speed);
    if (delta_sideshift > 0) {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(side_option.kmin_speed),
            static_cast<double>(side_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, static_cast<double>(side_option.kmin_speed),
            static_cast<double>(side_option.kmax_speed_with_pallet));
      }
    } else {
      if (static_cast<uint8_t>(ForkPalletState::NONE) ==
          pallet_status.fork_pallet_state) {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(side_option.kmin_speed),
            -static_cast<double>(side_option.kmax_speed_no_pallet));
      } else {
        setting_speed_ = math::Clamp(
            setting_speed_, -static_cast<double>(side_option.kmin_speed),
            -static_cast<double>(side_option.kmax_speed_with_pallet));
      }
    }

    action.cmd = static_cast<uint32_t>(AtomicActionType::FORK_SIDE_MOVE);
    action.data = setting_speed_;
    exec_action_["FORK_SIDE_MOVE"] = action;

    if (is_open_loop) {
      Get_info->SetOpenLoopState(50, false, ActionStatus::DOING);
    } else {
      Get_info->SetState(50, false, ActionStatus::DOING);
    }
  }
  LOG_INFO_STREAM("[Atomic-action] Column_pos_detect: cmd_side: "
                  << cmd_sideshift << ", actual_side: " << sideshift
                  << ", speed: " << setting_speed_);
}

// 人工确认
void AtomicAction::ManualConfirm(
    cotek_msgs::action_cmd *msg, AgvData *Get_info, bool is_open_loop,
    bool use_diy_finish, bool use_diy_error,
    std::map<int, std::vector<Condition>> finish_map,
    std::map<int, std::vector<Condition>> error_map) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      " ========= Doing Atomic Action ----Manual_Confirm =========");
  
  if (DoingAction() == false) {
    SetDoingAction(true);
    SetActionTime();
  }
  
  static bool is_confirm = false;
  static ros::Time confirm_time = ros::Time::now() - ros::Duration(10.);

  int8_t state = Get_info->agvdata_.manual_confirm;
  if (state == 1) {
    is_confirm = true;
  }
  if (is_confirm) {
    confirm_time = ros::Time::now();
  }

  auto option = Get_info->option_.manual_confirm;

  if (option.overtime_confirm) {
    // 超时自动完成
    if (ros::Time::now() - GetActionStartTime() > ros::Duration(option.wait_time)) {
      is_confirm = true;
      LOG_INFO_STREAM_THROTTLE(1, "[Atomic-action] Manual_Confirm: over time ");
    }
  } else {
    // 必须人工确认
    LOG_INFO_STREAM_THROTTLE(1, "[Atomic-action] Manual_Confirm: wait windows confirm ");
  }

  if (ros::Time::now() - confirm_time < ros::Duration(1)) {
    if (is_open_loop) {
      Get_info->SetOpenLoopStateSucceed();
      OpenLoopDataReset();
    } else {
      Get_info->SetStateSucceed();
      DataReset();
    }
    AgvData::get()->SetConfirmState(0);
    is_confirm = false;
    confirm_time = ros::Time::now() - ros::Duration(10.);
    LOG_INFO_STREAM(
        " ======== Atomic Action Done ---- Manual_Confirm =========");
    return;
  }

  // 前端提示框
  std_msgs::Int32 info;
  if (is_confirm) {
    info.data = 0;
  } else {
    info.data = 1;
  }
  manual_confirm_pub_.publish(info);
}

void AtomicAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;

  std::map<uint32_t,
           std::function<void(cotek_msgs::action_cmd *, AgvData *, bool, bool,
                              bool, std::map<int, std::vector<Condition>>,
                              std::map<int, std::vector<Condition>>)>>::iterator
      tep_map;
  uint32_t action_type = goal.action_type;
  static uint32_t new_goal_type = 0;

  if (0 == action_type) {
    LOG_DEBUG_COND(Get_info->option_.enable_local_debug,
                   "[Atomic-action] No Action Executing");
    if (new_goal_type != action_type) {
      new_goal_type = action_type;
    }
    // 没动作清空action
    exec_action_.clear();
    ResetDoingAction();
    ResetConditionMap();
  } else {
    if (new_goal_type != action_type) {
      ResetDoingAction();
      ResetConditionMap();
      new_goal_type = action_type;
      LOG_WARN_COND(Get_info->option_.enable_local_debug,
                    "[Atomic-action] New Action Goal: %d", new_goal_type);

      nlohmann::ordered_json json_data;
      try {
        rt_delay_flag_ = false;
        json_data = nlohmann::ordered_json::parse(goal.mix_condition);
        std::string finsih_condition = json_data["finishConditions"].dump();
        LOG_WARN_STREAM("finsih_condition: " << finsih_condition);
        finish_map_ = GetCondition(finsih_condition, node_status_manager_ptr_);
        use_diy_finish_condition_ = finish_map_.empty() == false ? true : false;

        std::string error_condition = json_data["errorConditions"].dump();
        LOG_WARN_STREAM("error_condition: " << error_condition);
        error_map_ = GetCondition(error_condition, node_status_manager_ptr_);
        use_diy_error_condition_ = error_map_.empty() == false ? true : false;
      } catch (const n_excetion &ex) {
        std::cout << (ex.what()) << std::endl;
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_INFO("json format error.");
        use_diy_finish_condition_ = false;
        use_diy_error_condition_ = false;
      }
    }
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
           100) % 500 ==
              0 &&
          (goal.action_type != 0),
      "[Atomic-action] Doing Action Type: %d", goal.action_type);

  bool is_open_loop = false;
  tep_map = action_map_.find(action_type);
  if (tep_map != action_map_.end()) {
    action_map_[action_type](&atomic_action_msg_, Get_info, is_open_loop,
                             use_diy_finish_condition_,
                             use_diy_error_condition_, finish_map_, error_map_);
  } else {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    LOG_ERROR_THROTTLE(1, "[Atomic-action] Wrong Action Type: %d", action_type);
    return;
  }
}

void AtomicAction::ExecuteOpenLoopAction(AgvData *Get_info) {
  auto goal = Get_info->open_loop_goal_;
  std::map<uint32_t,
           std::function<void(cotek_msgs::action_cmd *, AgvData *, bool, bool,
                              bool, std::map<int, std::vector<Condition>>,
                              std::map<int, std::vector<Condition>>)>>::iterator
      tep_map;
  uint32_t action_type = goal.action_type;
  static uint32_t new_goal_type = 0;

  if (0 == action_type) {
    LOG_DEBUG_COND(Get_info->option_.enable_local_debug,
                   "[Atomic-action] No Open-Loop-Action Executing");
    if (new_goal_type != action_type) {
      new_goal_type = action_type;
    }
    // 没动作清空action
    exec_action_.clear();
    ResetDoingAction();
    ResetOpConditionMap();
  } else {
    if (new_goal_type != action_type) {
      ResetDoingAction();
      ResetOpConditionMap();
      new_goal_type = action_type;
      LOG_WARN_COND(Get_info->option_.enable_local_debug,
                    "[Atomic-action] New Open-Loop-Action Goal: %d",
                    new_goal_type);

      nlohmann::ordered_json json_data;
      try {
        rt_delay_flag_ = false;
        json_data = nlohmann::ordered_json::parse(goal.mix_condition);
        std::string finsih_condition = json_data["finish_condition"].dump();
        finish_map_ = GetCondition(finsih_condition, node_status_manager_ptr_);
        use_diy_finish_condition_ = finish_map_.empty() == false ? true : false;

        std::string error_condition = json_data["finish_condition"].dump();
        error_map_ = GetCondition(error_condition, node_status_manager_ptr_);
        op_use_diy_finish_condition_ =
            error_map_.empty() == false ? true : false;
      } catch (const n_excetion &ex) {
        std::cout << (ex.what()) << std::endl;
        /*   node_status_manager_ptr_->SetNodeStatus(
               ActionNodeStatus::DIY_CONDITION_ERROR);*/
        LOG_INFO("json format error.");
        use_diy_finish_condition_ = false;
        op_use_diy_error_condition_ = false;
      }
    }
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
           100) % 500 ==
              0 &&
          (goal.action_type != 0),
      "[Atomic-action] Doing Open-Loop-Action Type: %d", goal.action_type);

  bool is_open_loop = true;
  tep_map = action_map_.find(action_type);
  if (tep_map != action_map_.end()) {
    action_map_[action_type](&atomic_action_msg_, Get_info, is_open_loop,
                             use_diy_finish_condition_,
                             use_diy_error_condition_, finish_map_, error_map_);
  } else {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    LOG_ERROR_THROTTLE(1, "[Atomic-action] Wrong Open-Loop-Action Type: %d",
                       action_type);
    return;
  }
}

void AtomicAction::PublishOpenLoopActionMsg() {
  static uint32_t seq = 0;
  seq += 1;

  cotek_msgs::action_cmd msg;
  msg.header.seq = seq;
  msg.header.stamp = ros::Time::now();

  int i = 0;
  cotek_msgs::atomic_action action;
  if (exec_action_.empty()) return;

  for (auto it = exec_action_.begin(); it != exec_action_.end(); ++it) {
    action.cmd = it->second.cmd;
    action.data = it->second.data;
    if (action.cmd == static_cast<uint32_t>(AtomicActionType::IO_OPERATOR)) {
      action.key = it->second.io_key;
      action.name = it->second.io_name;
    } else {
      action.key = "";
      action.name = "";
    }

    msg.cmd.push_back(action);

    if ((it->second.data == 0.) &&
        (action.cmd != static_cast<uint32_t>(AtomicActionType::IO_OPERATOR)) &&
        (action.cmd != static_cast<uint32_t>(AtomicActionType::CHARGE))) {
      int num = clear_action_[it->first];
      num++;
      clear_action_[it->first] = num;
    } else {
      clear_action_[it->first] = 0;
    }
  }

  for (auto it = clear_action_.begin(); it != clear_action_.end(); ++it) {
    if (it->second > 20) {
      exec_action_.erase(it->first);
      clear_action_[it->first] = 0;
    }
  }

  atomic_action_pub_.publish(msg);
  // atomic_action_pub_.publish(atomic_action_msg_);
}

void AtomicAction::PublishActionMsg() {
  static uint32_t seq = 0;
  seq += 1;

  cotek_msgs::load_state state;
  state.load_state = AgvData::get()->agvdata_.load_state;
  load_state_pub_.publish(state);

  cotek_msgs::load load;
  load.load_id = "";
  load.load_type = std::to_string(AgvData::get()->agvdata_.load_state);
  load.weight = 0.;
  cotek_msgs::loads loads;
  loads.header.seq = seq;
  loads.header.stamp = ros::Time::now();
  loads.loads.push_back(load);
  loads_pub_.publish(loads);

  cotek_msgs::action_cmd msg;
  msg.header.seq = seq;
  msg.header.stamp = ros::Time::now();

  int i = 0;
  cotek_msgs::atomic_action action;
  if (exec_action_.empty()) return;

  for (auto it = exec_action_.begin(); it != exec_action_.end(); ++it) {
    action.cmd = it->second.cmd;
    action.data = it->second.data;
    if (action.cmd == static_cast<uint32_t>(AtomicActionType::IO_OPERATOR)) {
      action.key = it->second.io_key;
      action.name = it->second.io_name;
    } else {
      action.key = "";
      action.name = "";
    }

    msg.cmd.push_back(action);

    if ((it->second.data == 0.) &&
        (action.cmd != static_cast<uint32_t>(AtomicActionType::IO_OPERATOR)) &&
        (action.cmd != static_cast<uint32_t>(AtomicActionType::CHARGE))) {
      int num = clear_action_[it->first];
      num++;
      clear_action_[it->first] = num;
    } else {
      clear_action_[it->first] = 0;
    }
  }

  for (auto it = clear_action_.begin(); it != clear_action_.end(); ++it) {
    if (it->second > 20) {
      exec_action_.erase(it->first);
      clear_action_[it->first] = 0;
    }
  }

  atomic_action_pub_.publish(msg);
  // atomic_action_pub_.publish(atomic_action_msg_);
}

void AtomicAction::ActionInit() {
  // 非动作通用类
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&AtomicAction::Waiting, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&AtomicAction::Rest, this, _1, _2, _3, _4, _5, _6, _7);
  // TODO(@ssh) 临时用10代替预取货
  action_map_[static_cast<uint32_t>(10)] =
      boost::bind(&AtomicAction::Rest, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&AtomicAction::DelayAction, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&AtomicAction::Init, this, _1, _2, _3, _4, _5, _6, _7);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::IO_OPERATOR)] =
      boost::bind(&AtomicAction::IoOperator, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CALL_ELEVATOR)] =
      boost::bind(&AtomicAction::CallElevator, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CONTROL_ELEVATOR)] =
      boost::bind(&AtomicAction::ControlElevator, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CONTROL_AUTODOOR)] =
      boost::bind(&AtomicAction::ControlAutoDoor, this, _1, _2, _3, _4, _5, _6, _7);

  // 取卸货物
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&AtomicAction::LiftLoad, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&AtomicAction::UnLoad, this, _1, _2, _3, _4, _5, _6, _7);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::OPENLOOP_UP)] =
      boost::bind(&AtomicAction::OpenLiftLoad, this, _1, _2, _3, _4, _5, _6,
                  _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::OPENLOOP_DOWN)] =
      boost::bind(&AtomicAction::UnLoad, this, _1, _2, _3, _4, _5, _6, _7);

  // 插齿动作类
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_HEIGHT_MOVE)] =
      boost::bind(&AtomicAction::ForkHeightMove, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 升降
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_LATERAL_MOVE)] =
      boost::bind(&AtomicAction::ForkLateralMove, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 前后后移
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_TILT_MOVE)] =
      boost::bind(&AtomicAction::ForkTiltMove, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 倾斜
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_SIDE_MOVE)] =
      boost::bind(&AtomicAction::ForkSideMove, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 侧移
  // action_map_[static_cast<uint32_t>(
  //     AgvTaskOperationType::FORK_COMPENSATE_FORWARD)] =
  //     boost::bind(&AtomicAction::ForkCompensateForward, this, _1, _2, _3, _4,
  //                 _5, _6, _7);  // 带补偿的前移
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::WAITING_FORK_REST)] =
      boost::bind(&AtomicAction::WaitingForkRest, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 前移

  // 充电
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&AtomicAction::Charge, this, _1, _2, _3, _4, _5, _6, _7);

  // 称重
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_UP_WEIGHT)] =
      boost::bind(&AtomicAction::CheckUPWeight, this, _1, _2, _3, _4, _5, _6,
                  _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_DOWN_WEIGHT)] =
      boost::bind(&AtomicAction::CheckDownWeight, this, _1, _2, _3, _4, _5, _6,
                  _7);

  // 顶升车类动作
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_ROTATE)] =
      boost::bind(&AtomicAction::PalletRotation, this, _1, _2, _3, _4, _5, _6,
                  _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_NOMOVE)] =
      boost::bind(&AtomicAction::PalletNoMove, this, _1, _2, _3, _4, _5, _6,
                  _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_ZERO)] =
      boost::bind(&AtomicAction::PalletZero, this, _1, _2, _3, _4, _5, _6, _7);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_CORRECT)] =
      boost::bind(&AtomicAction::PalletCorrect, this, _1, _2, _3, _4, _5, _6,
                  _7);

  // 其他特殊逻辑类
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::PALLET_CENTER_DETECT)] =
      boost::bind(&AtomicAction::PalletCenterDetect, this, _1, _2, _3, _4, _5,
                  _6, _7);  // 托盘位置检测
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MEASURE_SIZE)] =
      boost::bind(&AtomicAction::UnloadDetect, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 放货库区检测
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_BACK_LIMIT)] =
      boost::bind(&AtomicAction::PalletBackLimit, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 超板检测
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::QUERY_STORAGE_INFO)] =
      boost::bind(&AtomicAction::QueryStorageInfo, this, _1, _2, _3, _4, _5, _6,
                  _7);  // 激光检测库位信息
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::COLUMN_DETECT)] =
      boost::bind(&AtomicAction::ColumnPositionDetect, this, _1, _2, _3, _4, _5,
                  _6, _7);  // 货架检测
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CONFIRM)] =
      boost::bind(&AtomicAction::ManualConfirm, this, _1, _2, _3, _4, _5,
                  _6, _7);  // 人工确认

  // action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
  //     boost::bind(&AtomicAction::LowPowerMode, this, _1, _2, _3);
  // action_map_[static_cast<uint32_t>(AgvTaskOperationType::AUDIO_LEVEL_CONTROL)]
  // =
  //     boost::bind(&AtomicAction::ControlAudioLevel, this, _1, _2, _3);
  // action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_CENTER_BEFORE_UP)]
  // =
  //     boost::bind(&AtomicAction::PalletForkUp, this, _1, _2, _3);
  // action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_DETECT_BEFORE_DOWN)]
  // =
  //     boost::bind(&AtomicAction::PalletDetectBeforeDown, this, _1, _2, _3);
}

}  // namespace cotek_action