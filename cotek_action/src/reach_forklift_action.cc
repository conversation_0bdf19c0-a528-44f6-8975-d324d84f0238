/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_model/reach_forklift_action.h"

#include <fstream>
#include <iostream>

#include "cotek_action/action_config.h"
#include "cotek_common/common.h"
#include "cotek_common/cotek_protocal.h"
namespace cotek_action {

uint32_t ReachForkliftAction::load_weight_base_ = 0;

void ReachForkliftAction::DataReset() {
  AgvData::get()->Reset();
  ResetDelayFlag();
  ResetDoingAction();
  ResetLastSpeed();
  enable_forward_ = true;
  enable_lift_ = false;
  enable_backward_ = false;
}

uint32_t ReachForkliftAction::FeedBack() {
  return AgvData::get()->state_.percent;
}

void ReachForkliftAction::UpdateData(AgvData *Get_info) {
  // to do ..
}

bool ReachForkliftAction::IsFinished() {
  return AgvData::get()->state_.is_finish;
}

bool ReachForkliftAction::DelayTime(double second) {
  if (!delay_flag_) {
    delay_flag_ = true;
    delay_time_start_ = ros::Time::now();
    return false;
  } else if (ros::Time::now() - delay_time_start_ < ros::Duration(second)) {
    LOG_INFO_THROTTLE(0.5, "delay time...");
    return false;
  } else {
    return true;
  }
}

// 双闭环PID调节，外环调速，内环调加速度，输出增量
const float ReachForkliftAction::GetForkMoveSpeed(const double &target_height,
                                                  const double &current_height,
                                                  const double &current_speed) {
  auto target_speed =
      dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed =
      target_height - current_height > 0
          ? lift_speed_pid_ptr_->getOutputFromDiff(target_speed - current_speed)
          : descent_speed_pid_ptr_->getOutputFromDiff(target_speed -
                                                      current_speed);
  return delta_speed;
}

const float ReachForkliftAction::GetLateralForkMoveSpeed(
    const double &target_height, const double &current_height,
    const double &current_speed) {
  auto target_speed =
      lateral_dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed = target_height - current_height > 0
                           ? forward_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed)
                           : backward_speed_pid_ptr_->getOutputFromDiff(
                                 target_speed - current_speed);
  return delta_speed;
}

// No.0
void ReachForkliftAction::Waiting(cotek_msgs::forklift_action *msg,
                                  AgvData *Get_info) {
  auto fork_status = Get_info->agvdata_.forklift_pallet_state;
  auto lateral = fork_status.lateral;
  auto lateral_option = Get_info->option_.forklift_option.lateral_fork;
  auto delta_lateral = lateral - lateral_option.kdown_limit;  // 后移预留长度

  lateral_flag = delta_lateral < lateral_option.kup_offset ? true : false;
  if (lateral_flag) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
  } else {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_BACKWARD);
    msg->atomic_action_value = 50;
    LOG_INFO("reach fork backward, curr_val: %d, speed: %lf", lateral,
             msg->atomic_action_value);
    DelayTime(0.5);
  }
  Get_info->state_.is_finish = false;
  Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
}

void ReachForkliftAction::ControlAudioLevel(cotek_msgs::forklift_action *msg,
                                            AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "======== HeapFork:Doing Action----Control_Audio_Level %d=========",
      Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  audio_level_ = Get_info->goal_.action_value;
  audio_level_dispatch_ = true;
  Get_info->SetStateSucceed();
  DataReset();
}

void ReachForkliftAction::Rest(cotek_msgs::forklift_action *msg,
                               AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

// No.1
void ReachForkliftAction::LowPowerMode(cotek_msgs::forklift_action *msg,
                                       AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift:Doing Action----Low_Power_Mode %d=========",
                Get_info->goal_.action_type);
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static uint8_t cnt = 0;
  cnt++;
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
  msg->atomic_action_value = 0;
  Get_info->state_.percent = cnt;
  if (cnt == 100) {
    cnt = 0;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Low_Power_Mode Set Done!!!");
    LOG_INFO_STREAM(" Done action: Low_Power_Mode");
    DataReset();
  }
}

// 倾斜
void ReachForkliftAction::TransPlantTilt(cotek_msgs::forklift_action *msg,
                                         AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Reach_Fork_Tilt =========");

  auto cmd_angle = Get_info->goal_.action_value;
  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto angle = pallet_status.angle_x;  // 当前叉腿的实时俯仰角度

  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  ros::Time height_time = pallet_status.height_stamp;
  ros::Time lateral_time = pallet_status.lateral_stamp;

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5) ||
      ros::Time::now() - lateral_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code time error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  auto delta_angle = cmd_angle - angle;
  if (std::fabs(delta_angle) < 0.3) {
    LOG_INFO("  fork tilt move arrived  ");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    msg->atomic_action_type =
        delta_angle > 0
            ? static_cast<uint32_t>(AtomicActionType::FORK_LEAN_FORWARD)
            : static_cast<uint32_t>(AtomicActionType::FORK_LEAN_BACKWARD);
    msg->atomic_action_value = 25;
    LOG_INFO_THROTTLE(1, "Current angle is : %f, cmd_angle : %f,delta_angle:%f",
                      angle, cmd_angle, delta_angle);
    LOG_INFO_THROTTLE(1, "Current action is : %d,Current speed is : %f",
                      msg->atomic_action_type, msg->atomic_action_value);
    //  if(angle < -2.5 && delta_angle < 0){
    //   LOG_INFO("tilt down protect");
    //   return;
    // }else if(angle > 1 && delta_angle > 0){
    //   LOG_INFO("tilt up protect");
    //   return;
    // }
  }
}

void ReachForkliftAction::CheckUPWeight(cotek_msgs::forklift_action *msg,
                                        AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          Get_info->option_.forklift_option.fork_common_option.check_weigh &&
          DoingAction() == false,
      "========= Forklift:Doing Action----Reach_Fork_Check_UP_Weight "
      "=========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto heap_option = Get_info->option_.forklift_option.fork_common_option;

  // 若使用重量检测
  if (heap_option.check_weigh && pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }
  // 不使用重量检测
  if (!heap_option.check_weigh) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

  LOG_DEBUG_STREAM("weight: " << pallet_status.weigh);

  // 抬货重量检测， 并记录抬货重量
  if (heap_option.check_weigh) {
    // 抬货完成等待1s，静等称重读数稳定，记录货物重量，以供掉货检测、卸货使用
    if (pallet_status.weigh > heap_option.kload_weighing_threshold &&
        DelayTime(1.0)) {
      load_weight_base_ = pallet_status.weigh;
      load_state_ = LoadState::HAVE_LOAD;

      LOG_INFO_STREAM(
          "forklift up weight checked succeed!!!  load_weight_base: "
          << load_weight_base_);

      // 发送调度重量信息
      {
        cotek_msgs::agv_info_response response;
        response.type = cotek_protocal::msg_type::kLoadInfoEvent;

        json11::Json load_info = json11::Json::object{
            {"time", common::GetCurrentTime<std::string>()},
            {"orderId", Get_info->goal_.order_id},
            {"taskId", Get_info->goal_.task_id},
            {"loadId", std::string("null")},
            {"weight", static_cast<double>(pallet_status.weigh)}};

        std::string info = load_info.dump();

        LOG_INFO_STREAM(info);

        response.data = info;
        // QOS2表示用http传输
        response.qos = static_cast<uint8_t>(cotek_protocal::Qos::QOS2);
        agv_info_pub_.publish(response);
      }

      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();

    } else if (!delay_flag_) {
      LOG_WARN_STREAM_THROTTLE(1, "checking up weight error.\nweight: "
                                      << pallet_status.weigh
                                      << ", kload_weighing_threshold: "
                                      << heap_option.kload_weighing_threshold);

      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
      load_state_ = LoadState::NO_LOAD;
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      return;
    }
  }
}

void ReachForkliftAction::CheckDownWeight(cotek_msgs::forklift_action *msg,
                                          AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          Get_info->option_.forklift_option.fork_common_option.check_weigh &&
          DoingAction() == false,
      "========= Forklift:Doing "
      "Action----Reach_Fork_Check_Down_Weight =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto heap_option = Get_info->option_.forklift_option.fork_common_option;

  // 使用重量检测
  if (heap_option.check_weigh && pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }
  // 不使用重量检测
  if (!heap_option.check_weigh) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

  LOG_DEBUG_STREAM("weight: " << pallet_status.weigh);

  // 放货重量检测
  if (heap_option.check_weigh) {
    // 卸货重量检测
    if (pallet_status.weigh < heap_option.kload_weighing_threshold) {
      load_weight_base_ = 0;
      load_state_ = LoadState::NO_LOAD;
      LOG_INFO("forklift down weight checked succeed!!!");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
    } else {
      LOG_WARN_STREAM_THROTTLE(1, "checking up weight error.\nweight: "
                                      << pallet_status.weigh
                                      << ", kload_weighing_threshold: "
                                      << heap_option.kload_weighing_threshold);

      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
      load_state_ = LoadState::HAVE_LOAD;
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      return;
    }
  }
}

// 前移车抬货
void ReachForkliftAction::ReachLiftLoad(cotek_msgs::forklift_action *msg,
                                        AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Reach_Fork_Lift_Load =========");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;

  auto lateral = pallet_status.lateral;
  auto height = pallet_status.height;

  double fork_speed = pallet_status.lift_velocity;
  double lateral_speed = pallet_status.lateral_velocity;

  auto heap_option = Get_info->option_.forklift_option.height_fork;
  auto lateral_option = Get_info->option_.forklift_option.lateral_fork;

  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  ros::Time height_time = pallet_status.height_stamp;
  ros::Time lateral_time = pallet_status.lateral_stamp;

  auto cmd_height = math::Meter2CMillimeter(Get_info->goal_.action_value);
  cmd_height =
      cmd_height > heap_option.kup_limit ? heap_option.kup_limit : cmd_height;
  auto delta_dis = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height << ", curr_height: " << height);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5) ||
      ros::Time::now() - lateral_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code time error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 前移抬升下降已经判断完成
  //   static bool enable_lift_load = true;

  if (enable_forward_) {
    auto cmd_lateral = enable_forward_
                           ? lateral_option.kup_limit
                           : lateral_option.kdown_limit;  // 防止后移碰撞预留3cm
    auto delta_lateral = cmd_lateral - lateral;
    if (Get_info->agvdata_.safety_io_state >= 5) {
      // 增加预前移的动作类型,保留叉尖io避障
      msg->atomic_action_type =
          static_cast<uint32_t>(AtomicActionType::PRE_LATERAL_FORK_FORWARD);
      msg->atomic_action_value = 0;
      LOG_INFO_THROTTLE(1, "DI5 warnning, forward stop!");
      return;
    }
    // 到达目前位置或提前检测挡板
    if (std::fabs(delta_lateral) < lateral_option.kup_offset ||
        (Get_info->option_.forklift_option.fork_common_option.check_pallet &&
         Get_info->agvdata_.forklift_pallet_state.fork_pallet_state != 0)) {
      LOG_INFO("reach fork forkward arrived  ");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      enable_forward_ = false;
      enable_lift_ = true;
      DelayTime(0.5);
    } else {
      msg->atomic_action_type =
          static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_FORWARD);
      setting_speed_ +=
          GetLateralForkMoveSpeed(cmd_lateral, lateral, lateral_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(lateral_option.kup_min_speed),
          static_cast<double>(lateral_option.kup_max_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("reach fork forward, curr_val: %d, cmd_val: %d speed: %lf",
               lateral, cmd_lateral, msg->atomic_action_value);
    }
  }

  // 前移车抬货
  if (enable_lift_) {
    //    抬货带托盘检测
    LOG_INFO("ShangGang Test : %d, %d",
             Get_info->agvdata_.forklift_pallet_state.fork_pallet_state,
             Get_info->option_.forklift_option.fork_common_option.check_pallet);

    if (0 == Get_info->agvdata_.forklift_pallet_state.fork_pallet_state &&
        Get_info->option_.forklift_option.fork_common_option.check_pallet) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
      LOG_WARN_STREAM_THROTTLE(1, " EXCEPTION: Have NO PALLET !!!");
      LOG_INFO("msg->atomic_action_type : %d", msg->atomic_action_type);
      return;
    }
    auto load_height = cmd_height;
    auto load_cmd_height = heap_option.kup_value + load_height;
    LOG_INFO("load up Test : height:%d, cmd_height:%d", (int)load_height,
             load_cmd_height);
    load_cmd_height = load_cmd_height > heap_option.kup_limit
                          ? heap_option.kup_limit
                          : load_cmd_height;

    //  高度必须大于最小机械限制高度
    load_cmd_height = load_cmd_height > heap_option.kspecial_limit
                          ? load_cmd_height
                          : heap_option.kspecial_limit;

    LOG_WARN_STREAM("load_cmd_height : " << load_cmd_height);
    if ((load_cmd_height - height) < heap_option.kup_offset) {
      // 完成
      LOG_WARN("reach fork load arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      enable_lift_ = false;
      enable_backward_ = true;
      DelayTime(0.5);
    } else {
      // 抬
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      setting_speed_ += GetForkMoveSpeed(load_cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed),
          static_cast<double>(heap_option.kup_max_speed));
      //      msg->atomic_action_value = 50;
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("reach_load up, curr_val: %d, cmd_val: %d speed: %lf", height,
               load_cmd_height, msg->atomic_action_value);
    }
  }

  // 前移车后移
  if (enable_backward_) {
    auto lateral_option = Get_info->option_.forklift_option.lateral_fork;
    auto cmd_lateral = enable_backward_
                           ? lateral_option.kdown_limit
                           : lateral_option.kup_limit;  // 防止后移碰撞预留3cm
    auto delta_lateral = cmd_lateral - lateral;
    if (std::fabs(delta_lateral) < lateral_option.kdown_offset) {
      // 完成
      LOG_INFO("reach fork backward arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      enable_backward_ = false;
      Get_info->SetStateSucceed();
      DataReset();
      DelayTime(0.5);
      return;
    } else {
      // 后移
      msg->atomic_action_type =
          static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_BACKWARD);
      setting_speed_ +=
          GetLateralForkMoveSpeed(cmd_lateral, lateral, lateral_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, -static_cast<double>(lateral_option.kdown_max_speed),
          -static_cast<double>(lateral_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      // msg->atomic_action_value = 50;
      LOG_INFO("reach fork backward, curr_val: %d, cmd_val: %d speed: %lf",
               lateral, cmd_lateral, msg->atomic_action_value);
    }
  }
  //  enable_forward_ = false;
}

// 前移车卸货
void ReachForkliftAction::ReachUnLoad(cotek_msgs::forklift_action *msg,
                                      AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Reach_Fork_Un_Load =========");

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto lateral = pallet_status.lateral;
  auto height = pallet_status.height;
  double fork_speed = pallet_status.lift_velocity;
  double lateral_speed = pallet_status.lateral_velocity;

  auto heap_option = Get_info->option_.forklift_option.height_fork;
  auto lateral_option = Get_info->option_.forklift_option.lateral_fork;

  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  ros::Time height_time = pallet_status.height_stamp;
  ros::Time lateral_time = pallet_status.lateral_stamp;

  auto cmd_height = math::Meter2CMillimeter(Get_info->goal_.action_value);
  cmd_height =
      cmd_height > heap_option.kup_limit ? heap_option.kup_limit : cmd_height;
  auto delta_dis = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual_height: " << height);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5) ||
      ros::Time::now() - lateral_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code time error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 前移
  if (enable_forward_) {
    auto cmd_lateral = enable_forward_
                           ? lateral_option.kup_limit
                           : lateral_option.kdown_limit;  // 防止后移碰撞预留3cm
    auto delta_lateral = cmd_lateral - lateral;
    if (Get_info->agvdata_.safety_io_state >= 5) {
      // 增加预前移的动作类型,保留叉尖io避障
      msg->atomic_action_type =
          static_cast<uint32_t>(AtomicActionType::PRE_LATERAL_FORK_FORWARD);
      msg->atomic_action_value = 0;
      LOG_INFO_THROTTLE(1, "DI5 warnning, forward stop!");
      return;
    }
    if (std::fabs(delta_lateral) < lateral_option.kup_offset) {
      LOG_INFO("reach fork forkward arrived  ");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      enable_forward_ = false;
      enable_lift_ = true;
      DelayTime(0.5);
    } else {
      msg->atomic_action_type =
          static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_FORWARD);
      setting_speed_ +=
          GetLateralForkMoveSpeed(cmd_lateral, lateral, lateral_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(lateral_option.kup_min_speed),
          static_cast<double>(lateral_option.kup_max_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("reach fork forward, curr_val: %d, cmd_val: %d speed: %lf",
               lateral, cmd_lateral, msg->atomic_action_value);
    }
  }
  // unload
  if (enable_lift_) {
    auto unload_height = pallet_status.height;
    auto unload_cmd_height =
        math::Meter2CMillimeter(Get_info->goal_.action_value);

    unload_cmd_height = unload_cmd_height <= 0 ? 0 : unload_cmd_height;
    LOG_WARN_STREAM("unload_cmd_height : " << unload_cmd_height);
    if (std::fabs(unload_cmd_height - unload_height) <
            heap_option.kdown_offset ||
        unload_height < heap_option.kdown_limit) {
      // 完成
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      enable_lift_ = false;
      enable_backward_ = true;
      DelayTime(0.5);
    } else {
      // 降
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
      setting_speed_ +=
          GetForkMoveSpeed(unload_cmd_height, unload_height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, -static_cast<double>(heap_option.kdown_max_speed),
          -static_cast<double>(heap_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      //      msg->atomic_action_value = 50;
      LOG_INFO("reach_load down, curr_val: %d, cmd_val: %d speed: %lf", height,
               unload_cmd_height, msg->atomic_action_value);
    }
  }

  // 前移车后移
  if (enable_backward_) {
    auto lateral_option = Get_info->option_.forklift_option.lateral_fork;
    auto cmd_lateral = enable_backward_
                           ? lateral_option.kdown_limit
                           : lateral_option.kup_limit;  // 防止后移碰撞预留3cm
    auto delta_lateral = cmd_lateral - lateral;
    if (std::fabs(delta_lateral) < lateral_option.kdown_offset) {
      // 完成
      LOG_INFO("reach fork backward arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      enable_backward_ = false;
      Get_info->SetStateSucceed();
      DataReset();
      DelayTime(0.5);
      return;
    } else {
      // 后移
      msg->atomic_action_type =
          static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_BACKWARD);
      setting_speed_ +=
          GetLateralForkMoveSpeed(cmd_lateral, lateral, lateral_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, -static_cast<double>(lateral_option.kdown_max_speed),
          -static_cast<double>(lateral_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      // msg->atomic_action_value = 50;
      LOG_INFO("reach fork backward, curr_val: %d, cmd_val: %d speed: %lf",
               lateral, cmd_lateral, msg->atomic_action_value);
    }
  }
}

// No.4
void ReachForkliftAction::ReachForkMove(cotek_msgs::forklift_action *msg,
                                        AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Reach_Fork_Move =========");

  auto fork_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = fork_status.height;
  auto angle = fork_status.angle_x;
  float fork_speed = fork_status.lift_velocity;
  float lateral_speed = fork_status.lateral_velocity;

  auto heap_option = Get_info->option_.forklift_option.height_fork;

  auto height_lower_limit = heap_option.kdown_limit;
  auto height_upper_limit = heap_option.kup_limit;

  auto lateral_error = fork_status.lateral_error_code;
  auto height_error = fork_status.hight_error_code;
  ros::Time height_time = fork_status.height_stamp;
  ros::Time lateral_time = fork_status.lateral_stamp;

  auto cmd_height = math::Meter2CMillimeter(Get_info->goal_.action_value);
  cmd_height =
      cmd_height > height_upper_limit ? height_upper_limit : cmd_height;
  auto delta_dist = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual_height: " << height);

  // 动作开始只进行一次上升、下降标志判定
  // if (DoingAction() == false) {
  up_down_flag_ = cmd_height >= height ? true : false;
  // }
  // static bool reset_angle_flag = false;
  // //angle_x < -3°时不安全
  // if(angle < -3 && !reset_angle_flag){
  //   auto cmd_angle = 2.0;
  //   auto delta_angle = cmd_angle - angle;
  //   if(std::fabs(delta_angle) < 0.2){
  //     LOG_INFO("angle  reset secussful,current angle is ", angle);
  //     reset_angle_flag = true;
  //   }else{
  //     msg->atomic_action_type =
  //     static_cast<uint32_t>(AtomicActionType::FORK_LEAN_FORWARD);
  //     msg->atomic_action_value = 50;
  //     LOG_INFO("Current angle is:%f",angle);
  //   }
  // }else{
  //   reset_angle_flag = true;
  // }

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // 编码器时间戳超时 无法执行抬降
  if (ros::Time::now() - height_time > ros::Duration(0.5) ||
      ros::Time::now() - lateral_time > ros::Duration(0.5)) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "line_code time error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  if (up_down_flag_ /*&& reset_angle_flag*/) {
    // 完成
    if (std::fabs(delta_dist) <= heap_option.kup_offset) {
      LOG_INFO("heap fork arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
      cmd_height_base_ = cmd_height;
      //      enable_forward_ = true;
      Get_info->SetStateSucceed();
      DataReset();
      return;
    } else {
      //  抬
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed),
          static_cast<double>(heap_option.kup_max_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("heap fork up, input ratio: %d",
               static_cast<int>(msg->atomic_action_value));
      Get_info->state_.is_finish = false;
    }
  } else /*if(!up_down_flag_ && reset_angle_flag)*/ {
    // 完成
    if (std::fabs(delta_dist) <= heap_option.kdown_offset ||
        height < height_lower_limit) {
      LOG_INFO("heap fork arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
      cmd_height_base_ =
          cmd_height < height_lower_limit ? height_lower_limit : cmd_height;
      Get_info->SetStateSucceed();
      DataReset();
      return;
    } else {
      // 降
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, -static_cast<double>(heap_option.kdown_max_speed),
          -static_cast<double>(heap_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("heap fork down, input ratio: %d",
               static_cast<int>(msg->atomic_action_value));
      Get_info->state_.is_finish = false;
    }
  }
}
// No.5
void ReachForkliftAction::Break(cotek_msgs::forklift_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Break %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_BRAKE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  static int actcnt = 0;
  ++actcnt;
  Get_info->state_.percent = actcnt;
  if (actcnt == 100) {
    actcnt = 0;
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    Get_info->state_.percent = 100;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    DataReset();
    LOG_INFO("Break Done!!!");
  }
}
// No.6
void ReachForkliftAction::Beep(cotek_msgs::forklift_action *msg,
                               AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "========= Forklift:Doing Action----Beep %d =========",
                Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::BEEP);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.is_finish = true;
  DataReset();
  LOG_INFO("Beep Done!!!");
}
// No.7
void ReachForkliftAction::ForkliftBodyCotrollerReset(
    cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Curtis_Reset %d =========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static ros::Time action_time;
  static bool first_flag = false;
  if (!first_flag) {
    first_flag = true;
    action_time = ros::Time::now();
  }
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_RESET);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.percent = 50;
  // 执行超过10s认为完成
  if (ros::Time::now() - action_time > ros::Duration(10)) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    LOG_INFO_STREAM(" Steering Reset!!!");
    DataReset();
  }
}

// No.8
void ReachForkliftAction::Charge(cotek_msgs::forklift_action *msg,
                                 AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Charge %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->SetState(50, false, ActionStatus::DOING);
  switch (static_cast<cotek_action::ChargeDetectionType>(
      Get_info->option_.charge_option.charge_detection)) {
    case ChargeDetectionType::CURRENT: {
      LOG_INFO("==========charge detection: current ==========");
      if ((Get_info->goal_.action_value == 1) &&
          (Get_info->agvdata_.battery_moniter.current >
           Get_info->option_.charge_option.charge_current_upper)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Open Charge Done!!!");
      } else if ((Get_info->goal_.action_value == 0) &&
                 (Get_info->agvdata_.battery_moniter.current <
                  Get_info->option_.charge_option.charge_current_lower)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Close Charge Done!!!");
      }
      break;
    }
    case ChargeDetectionType::VOLTAGE: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: voltage ==========");
      break;
    }
    case ChargeDetectionType::BOTH: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: both ==========");
      break;
    }
    default: {
      LOG_INFO("==========charge detection: none ==========");
      // 检查继电器是否打开
      if ((Get_info->agvdata_.charge_do_state) &&
          (Get_info->goal_.action_value == 1)) {
        // TODO(@ssh) 临时解决　调度需打开继电器后至少发一帧doing 此处延时
        ros::Duration(1.0).sleep();
        Get_info->SetStateSucceed();
        LOG_INFO("Open Charge Done!!!");
        DataReset();
      } else if ((!Get_info->agvdata_.charge_do_state) &&
                 (Get_info->goal_.action_value == 0)) {
        Get_info->SetStateSucceed();
        LOG_INFO("Close Charge Done!!!");
        DataReset();
      }
      break;
    }
  }
}

// delay action
void ReachForkliftAction::DelayAction(cotek_msgs::forklift_action *msg,
                                      AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Delay ==========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static bool delay_flag = false;
  static ros::Time delay_time_start = ros::Time::now();

  if (!delay_flag) {
    delay_time_start = ros::Time::now();
    delay_flag = true;
  }

  if (ros::Time::now() - delay_time_start <
      ros::Duration(Get_info->goal_.action_value)) {
    LOG_INFO_THROTTLE(1, "delay time: %lf !!!", Get_info->goal_.action_value);
    return;
  } else {
    delay_flag = false;
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }
}

void ReachForkliftAction::ActionInit() {
  // 注册动作
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&ReachForkliftAction::Waiting, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&ReachForkliftAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&ReachForkliftAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&ReachForkliftAction::ReachLiftLoad, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&ReachForkliftAction::ReachUnLoad, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_UP_WEIGHT)] =
      boost::bind(&ReachForkliftAction::CheckUPWeight, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_DOWN_WEIGHT)] =
      boost::bind(&ReachForkliftAction::CheckDownWeight, this, _1, _2);

  // 倾斜
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_TILT)] =
      boost::bind(&ReachForkliftAction::TransPlantTilt, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::HEIGHT_FORK_MOVE)] =
      boost::bind(&ReachForkliftAction::ReachForkMove, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&ReachForkliftAction::Charge, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
      boost::bind(&ReachForkliftAction::LowPowerMode, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
      boost::bind(&ReachForkliftAction::ControlAudioLevel, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&ReachForkliftAction::DelayAction, this, _1, _2);
}

void ReachForkliftAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;
  std::map<uint32_t, std::function<void(cotek_msgs::forklift_action *,
                                        AgvData *)>>::iterator tep_map;
  uint32_t i = goal.action_type;
  if (0 == i) {
    LOG_DEBUG_COND(Get_info->option_.enable_local_debug,
                   "ForkLift AGV:No Action Executing..");
    ResetDoingAction();
  } else {
    static uint32_t new_goal = 0;
    if (new_goal != i) {
      ResetDoingAction();
      new_goal = i;
      LOG_WARN_COND(Get_info->option_.enable_local_debug, "New goal...");
    }
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
           100) % 500 ==
              0 &&
          (goal.action_type != 0),
      "ForkLift AGV:Doing action No.%d", goal.action_type);
  tep_map = action_map_.find(i);
  if (tep_map != action_map_.end()) {
    action_map_[i](&msg_, Get_info);
  } else {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug,
                   "Wrong action type: %d", i);
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    return;
  }
}

void ReachForkliftAction::PublishWeightLoadingBase() {
  cotek_msgs::weighting_loaded msg;
  msg.stamp = ros::Time::now();
  msg.load_weight_base = load_weight_base_;
  msg.load_state = static_cast<uint32_t>(load_state_);
  load_weight_base_pub_.publish(msg);
}

void ReachForkliftAction::PublishActionMsg() {
  msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
  msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
  // 选择本地配置音量 | 调度下发音量
  msg_.audio_control_level =
      audio_level_dispatch_
          ? audio_level_
          : VolProcess(AgvData::get()->goal_.audio_control_type);
  msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
  // 音频模块硬件问题
  static int cnt = 0;
  if (msg_.audio_control_type == 0) {
    cnt++;
    if (cnt > 10) {
      msg_.audio_control_type = 1;
      msg_.audio_control_level = 0;
    }
  } else {
    cnt = 0;
  }

  forklift_action_pub_.publish(msg_);

  PublishWeightLoadingBase();
}

}  // namespace cotek_action