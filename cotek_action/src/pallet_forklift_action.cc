/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_model/pallet_forklift_action.h"

#include <fstream>
#include <iostream>

#include "cotek_action/action_config.h"
#include "cotek_common/cotek_protocal.h"

namespace cotek_action {

namespace {
// constexpr int kampere_factor = 10;  // 电流转换系数
// constexpr double kcharge_delay_wait_time = 1;
}  // namespace

void PalletForkliftAction::DataReset() { AgvData::get()->Reset(); }

uint32_t PalletForkliftAction::FeedBack() {
  return AgvData::get()->state_.percent;
}

void PalletForkliftAction::UpdateData(AgvData *Get_info) {
  // to do ..
}

bool PalletForkliftAction::IsFinished() {
  return AgvData::get()->state_.is_finish;
}

// No.0
void PalletForkliftAction::Waiting(cotek_msgs::forklift_action *msg,
                                   AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;
  Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
}

void PalletForkliftAction::Rest(cotek_msgs::forklift_action *msg,
                                AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

void PalletForkliftAction::QueryStorageInfo(cotek_msgs::forklift_action *msg,
                                            AgvData *Get_info) {
  cotek_msgs::query_storage_info query_info;
  query_info.request.order_id = Get_info->goal_.order_id;
  query_info.request.task_id = Get_info->goal_.task_id;
  query_info.request.storage_area_id = Get_info->goal_.action_value;
  ros::NodeHandle nh;
  ros::ServiceClient query_storage_srv =
      nh.serviceClient<cotek_msgs::query_storage_info>("query_storage");
  if (!query_storage_srv.call(query_info)) {
    LOG_ERROR("Query storage failed !!!");
  } else {
    Get_info->SetStateSucceed();
    DataReset();
  }
}

void PalletForkliftAction::PalletBackLimit(cotek_msgs::forklift_action *msg,
                                           AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift:Doing Action----PalletBackLimit %d=========",
                Get_info->goal_.action_type);

  cotek_msgs::request_pallet_back_limit request;
  request.stamp = ros::Time::now();
  request_pallet_back_limit_pub_.publish(request);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::PALLET_LIMIT_ERROR);
  }
  auto &pallet_back_limit =
      Get_info->agvdata_.forklift_pallet_state.pallet_back_limit_type;

  if (pallet_back_limit != PalletBackLimitType::FREE &&
      ros::Time::now() - GetActionStartTime() > ros::Duration(2.0)) {
    LOG_WARN_THROTTLE(1, "pallet_back_limit error!!!");
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::PALLET_LIMIT_ERROR);
  }
  if (pallet_back_limit == PalletBackLimitType::FREE) {
    Get_info->SetStateSucceed();
    DataReset();
  }
}

void PalletForkliftAction::Init(cotek_msgs::forklift_action *msg,
                                AgvData *Get_info) {
  // TODO(@ssh)
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

void PalletForkliftAction::ControlAudioLevel(cotek_msgs::forklift_action *msg,
                                             AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "======== Forklift:Doing Action----Control_Audio_Level %d=========",
      Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  audio_level_ = Get_info->goal_.action_value;
  audio_level_dispatch_ = true;
  Get_info->SetStateSucceed();
  DataReset();
}

// No.1
void PalletForkliftAction::LowPowerMode(cotek_msgs::forklift_action *msg,
                                        AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift:Doing Action----Low_Power_Mode %d=========",
                Get_info->goal_.action_type);
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static uint8_t cnt = 0;
  cnt++;
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
  msg->atomic_action_value = 0;
  Get_info->state_.percent = cnt;
  if (cnt == 100) {
    cnt = 0;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Low_Power_Mode Set Done!!!");
    LOG_INFO_STREAM(" Done action: Low_Power_Mode");
    DataReset();
  }
}

void PalletForkliftAction::DelayAction(cotek_msgs::forklift_action *msg,
                                       AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Delay ==========");
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static bool delay_flag = false;
  static ros::Time delay_time_start = ros::Time::now();

  if (!delay_flag) {
    delay_time_start = ros::Time::now();
    delay_flag = true;
  }

  if (ros::Time::now() - delay_time_start <
      ros::Duration(Get_info->goal_.action_value)) {
    LOG_INFO_THROTTLE(1, "delay time: %lf !!!", Get_info->goal_.action_value);
    return;
  } else {
    delay_flag = false;
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }
}

void PalletForkliftAction::CheckUPWeight(cotek_msgs::forklift_action *msg,
                                         AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Check_UP_Weight =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;

  if (pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
    msg->atomic_action_value = 0;
    return;
  }

  LOG_INFO_STREAM_THROTTLE(1, "weight: " << pallet_status.weigh);
  // 发送掉获检测基础信息
  {
    cotek_msgs::weighting_loaded msg;
    msg.stamp = ros::Time::now();
    msg.load_weight_base = pallet_status.weigh;
    msg.load_state = static_cast<uint32_t>(LoadState::HAVE_LOAD);
    msg.close_check_weight = true;
    load_weight_base_pub_.publish(msg);
  }
  // 发送调度重量信息
  {
    cotek_msgs::agv_info_response response;
    response.type = cotek_protocal::msg_type::kLoadInfoEvent;

    json11::Json load_info = json11::Json::object{
        {"time", common::GetCurrentTime<std::string>()},
        {"orderId", Get_info->goal_.order_id},
        {"taskId", Get_info->goal_.task_id},
        {"loadId", std::string("null")},
        {"weight", static_cast<double>(pallet_status.weigh)}};

    std::string info = load_info.dump();

    LOG_INFO_STREAM(info);

    response.data = info;
    // QOS2表示用http传输
    response.qos = static_cast<uint8_t>(cotek_protocal::Qos::QOS2);
    agv_info_pub_.publish(response);
  }

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

void PalletForkliftAction::CheckDownWeight(cotek_msgs::forklift_action *msg,
                                           AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Check_UP_Down =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;

  if (pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  LOG_INFO_STREAM_THROTTLE(1, "weight: " << pallet_status.weigh);
  // 发送掉获检测基础信息
  {
    cotek_msgs::weighting_loaded msg;
    msg.stamp = ros::Time::now();
    msg.load_weight_base = pallet_status.weigh;
    msg.load_state = static_cast<uint32_t>(LoadState::NO_LOAD);
    msg.close_check_weight = false;
    load_weight_base_pub_.publish(msg);
  }

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

// No.2
void PalletForkliftAction::PalletForkUp(cotek_msgs::forklift_action *msg,
                                        AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift: Pallet_Up =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  if (static_cast<uint8_t>(ForkPalletState::BOTH) !=
          Get_info->agvdata_.forklift_pallet_state.fork_pallet_state &&
      Get_info->option_.forklift_option.fork_common_option.check_pallet) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
    LOG_WARN_STREAM_COND(
        Get_info->option_.enable_local_debug &&
            (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
             100) % 100 ==
                0,
        " EXCEPTION:Doing Action-Pallet_Fork_Up: Have NO PALLET !!!");
    return;
  }

  static ros::Time start_time = ros::Time::now();

  if (AgvData::get()->agvdata_.forklift_pallet_state.fork_up_down_state < 2) {
    if (!start_up_flag_) {
      start_up_flag_ = true;
      start_time = ros::Time::now();
    }
    if (start_up_flag_ && ros::Time::now() - start_time > ros::Duration(6.0)) {
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = Get_info->goal_.action_value;
      Get_info->SetState(50, false, ActionStatus::DOING);
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::PALLET_MOVE_ERR);
      return;
    }
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
    msg->atomic_action_value = Get_info->goal_.action_value;
    Get_info->SetState(50, false, ActionStatus::DOING);
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    start_up_flag_ = false;
    LOG_INFO_STREAM(" Done action: Pallet_Fork_Up");
    DataReset();
  }
}
// No.3
void PalletForkliftAction::PalletForkDown(cotek_msgs::forklift_action *msg,
                                          AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action--- -Pallet_Down =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  if (AgvData::get()->agvdata_.forklift_pallet_state.fork_up_down_state > 0) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
    msg->atomic_action_value = Get_info->goal_.action_value;
    Get_info->SetState(50, false, ActionStatus::DOING);
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    LOG_INFO_STREAM(" Pallet_Fork_Down Done!!!");
    DataReset();
  }
}

void PalletForkliftAction::PalletDetectBeforeUp(
    cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift: Pallet_detect_up =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::PALLET_DETECT_ERROR);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::PALLET_DETECT);
  msg->atomic_action_value = Get_info->goal_.action_value;

  static int time_count = 0;
  auto pallet_detect_state = static_cast<PalletDetectType>(
      AgvData::get()->agvdata_.forklift_pallet_state.pallet_detect_state);

  if (pallet_detect_state == PalletDetectType::DETECTION) {
    time_count++;
  } else {
    time_count = 0;
  }

  // 连续检测到货物超过3s
  if (time_count < Get_info->option_.control_period * 3) {
    Get_info->SetState(50, false, ActionStatus::DOING);
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    time_count = 0;
    LOG_INFO_STREAM(" Done action: Pallet_detect_up");
    DataReset();
  }
}

void PalletForkliftAction::PalletDetectBeforeDown(
    cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift: Pallet_detect_down =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::PALLET_DETECT_ERROR);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::PALLET_DETECT);
  msg->atomic_action_value = Get_info->goal_.action_value;

  static int time_count = 0;
  auto pallet_detect_state = static_cast<PalletDetectType>(
      AgvData::get()->agvdata_.forklift_pallet_state.pallet_detect_state);

  if (pallet_detect_state == PalletDetectType::NO_DETECTION) {
    time_count++;
  } else {
    time_count = 0;
  }
  // 连续未检测到货物超过3s
  if (time_count < Get_info->option_.control_period * 3) {
    Get_info->SetState(50, false, ActionStatus::DOING);
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    time_count = 0;
    LOG_INFO_STREAM(" Done action: Pallet_detect_down");
    DataReset();
  }
}

// No.5
void PalletForkliftAction::Break(cotek_msgs::forklift_action *msg,
                                 AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Break %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_BRAKE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  static int actcnt = 0;
  ++actcnt;
  Get_info->state_.percent = actcnt;
  if (actcnt == 100) {
    actcnt = 0;
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    Get_info->state_.percent = 100;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    DataReset();
    LOG_INFO("Break Done!!!");
  }
}
// No.6
void PalletForkliftAction::Beep(cotek_msgs::forklift_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "========= Forklift:Doing Action----Beep %d =========",
                Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::BEEP);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.is_finish = true;
  DataReset();
  LOG_INFO("Beep Done!!!");
}
// No.7
void PalletForkliftAction::ForkliftBodyCotrollerReset(
    cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Curtis_Reset %d =========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static ros::Time action_time;
  static bool first_flag = false;
  if (!first_flag) {
    first_flag = true;
    action_time = ros::Time::now();
  }
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_RESET);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.percent = 50;
  // 执行超过10s认为完成
  if (ros::Time::now() - action_time > ros::Duration(10)) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    LOG_INFO_STREAM(" Steering Reset!!!");
    DataReset();
  }
}

// No.8
void PalletForkliftAction::Charge(cotek_msgs::forklift_action *msg,
                                  AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Charge %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->SetState(50, false, ActionStatus::DOING);
  switch (static_cast<cotek_action::ChargeDetectionType>(
      Get_info->option_.charge_option.charge_detection)) {
    case ChargeDetectionType::CURRENT: {
      LOG_INFO("==========charge detection: current ==========");
      if ((Get_info->goal_.action_value == 1) &&
          (Get_info->agvdata_.battery_moniter.current >
           Get_info->option_.charge_option.charge_current_upper)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Open Charge Done!!!");
      } else if ((Get_info->goal_.action_value == 0) &&
                 (Get_info->agvdata_.battery_moniter.current <
                  Get_info->option_.charge_option.charge_current_lower)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Close Charge Done!!!");
      }
      break;
    }
    case ChargeDetectionType::VOLTAGE: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: voltage ==========");
      break;
    }
    case ChargeDetectionType::BOTH: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: both ==========");
      break;
    }
    default: {
      LOG_INFO("==========charge detection: none ==========");
      // 检查继电器是否打开
      if ((Get_info->agvdata_.charge_do_state) &&
          (Get_info->goal_.action_value == 1)) {
        // TODO(@ssh) 临时解决　调度需打开继电器后至少发一帧doing 此处延时
        ros::Duration(1.0).sleep();
        Get_info->SetStateSucceed();
        LOG_INFO("Open Charge Done!!!");
        DataReset();
      } else if ((!Get_info->agvdata_.charge_do_state) &&
                 (Get_info->goal_.action_value == 0)) {
        Get_info->SetStateSucceed();
        LOG_INFO("Close Charge Done!!!");
        DataReset();
      }
      break;
    }
  }
}

// No.9
void PalletForkliftAction::PoweronInit(cotek_msgs::forklift_action *msg,
                                       AgvData *Get_info) {
  // TODO(@fsc) 上电初始化动作
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
}

void PalletForkliftAction::OpenloopForkUp(cotek_msgs::forklift_action *msg,
                                          AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift: OpenloopForkUp =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
  msg->atomic_action_value = Get_info->goal_.action_value;
  StartOpenloop(AtomicActionType::UP, Get_info->goal_.action_value, 5.0);
  Get_info->SetStateSucceed();
  DataReset();
  LOG_INFO_STREAM(" Done action: OpenloopForkUp");
}

void PalletForkliftAction::OpenloopForkDown(cotek_msgs::forklift_action *msg,
                                            AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift: OpenloopForkDown =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
  msg->atomic_action_value = Get_info->goal_.action_value;
  StartOpenloop(AtomicActionType::DOWN, Get_info->goal_.action_value, 5.0);
  Get_info->SetStateSucceed();
  DataReset();
  LOG_INFO_STREAM(" Done action: OpenloopForkDown");
}

void PalletForkliftAction::ActionInit() {
  // 注册动作
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&PalletForkliftAction::Waiting, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&PalletForkliftAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&PalletForkliftAction::Init, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&PalletForkliftAction::PalletForkUp, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&PalletForkliftAction::PalletForkDown, this, _1, _2);

  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::PALLET_CENTER_BEFORE_UP)] =
      boost::bind(&PalletForkliftAction::PalletForkUp, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::PALLET_DETECT_BEFORE_DOWN)] =
      boost::bind(&PalletForkliftAction::PalletDetectBeforeDown, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&PalletForkliftAction::Charge, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
      boost::bind(&PalletForkliftAction::LowPowerMode, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::AUDIO_TYPE_CONTROL)] =
      boost::bind(&PalletForkliftAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
      boost::bind(&PalletForkliftAction::ControlAudioLevel, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::QUERY_STORAGE_INFO)] =
      boost::bind(&PalletForkliftAction::QueryStorageInfo, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::OPEN_LOOP_FORK_UP)] =
      boost::bind(&PalletForkliftAction::OpenloopForkUp, this, _1, _2);

  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::OPEN_LOOP_FORK_DOWN)] =
      boost::bind(&PalletForkliftAction::OpenloopForkDown, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&PalletForkliftAction::DelayAction, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_UP_WEIGHT)] =
      boost::bind(&PalletForkliftAction::CheckUPWeight, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_BACK_LIMIT)] =
      boost::bind(&PalletForkliftAction::PalletBackLimit, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_DOWN_WEIGHT)] =
      boost::bind(&PalletForkliftAction::CheckDownWeight, this, _1, _2);
}

void PalletForkliftAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;
  std::map<uint32_t, std::function<void(cotek_msgs::forklift_action *,
                                        AgvData *)>>::iterator tep_map;
  uint32_t i = goal.action_type;
  if (0 == i) {
    LOG_DEBUG_COND(Get_info->option_.enable_local_debug,
                   "ForkLift AGV:No Action Executing..");
    start_up_flag_ = false;
    ResetDoingAction();
  } else {
    static uint32_t new_goal = 0;
    if (new_goal != i) {
      ResetDoingAction();
      new_goal = i;
      LOG_WARN_COND(Get_info->option_.enable_local_debug, "New goal...");
    }
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
           100) % 500 ==
              0 &&
          (goal.action_type != 0),
      "ForkLift AGV:Doing action No.%d", goal.action_type);
  tep_map = action_map_.find(i);
  if (tep_map != action_map_.end()) {
    action_map_[i](&msg_, Get_info);
  } else {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug,
                   "Wrong action type: %d", i);
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    return;
  }
}

void PalletForkliftAction::PublishActionMsg() {
  msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
  msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
  msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
  // 选择本地配置音量 | 调度下发音量
  msg_.audio_control_level =
      audio_level_dispatch_
          ? audio_level_
          : VolProcess(AgvData::get()->goal_.audio_control_type);

  // 音频模块硬件问题
  static int cnt = 0;
  if (msg_.audio_control_type == 0) {
    cnt++;
    if (cnt > 10) {
      msg_.audio_control_type = 1;
      msg_.audio_control_level = 0;
    }
  } else {
    cnt = 0;
  }

  if (IsOpenloop() /*是否有开环任务*/) {
    auto &&open_loop_goal = GetOpenloopGoalResult(AgvData::get()->agvdata_,
                                                  AgvData::get()->option_);
    msg_.atomic_action_type = static_cast<uint32_t>(open_loop_goal.action_type);
    msg_.atomic_action_value = open_loop_goal.action_value;
  }
  forklift_action_pub_.publish(msg_);
}

}  // namespace cotek_action
