/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_model/jackup_action.h"

#include <bitset>

#include "cotek_action/action_config.h"

namespace cotek_action {
void JackupAction::UpdateData(cotek_action::AgvData *Get_info) {
  tf::StampedTransform shelf_transform;
  LOG_DEBUG("shelf_tag_num_1: %d",
            Get_info->agvdata_.jackup_pallet_state.palletag);
  if (0 != Get_info->agvdata_.jackup_pallet_state.palletag) {
    try {
      shelf_tf_->lookupTransform(cotek_tf::kMapFrame, cotek_tf::kShelfFrame,
                                 ros::Time(0), shelf_transform);
    } catch (tf::TransformException &ex) {
      LOG_WARN_STREAM_THROTTLE(0.2, ex.what());
      LOG_WARN_STREAM_THROTTLE(0.2, "Current shelf pose Exception.");
      shelf_pose_vaild_ = false;
      pallet_tar_theta_ = 0.0;
      shelf_cur_theta_ = 0.0;
      return;
    }
    shelf_cur_theta_ =
        angles::normalize_angle(tf::getYaw(shelf_transform.getRotation()));
    auto x = shelf_transform.getOrigin().x();
    auto y = shelf_transform.getOrigin().y();
    //归一化托盘保持目标角度
    if (0 != pallet_nomove_flag_) {
      pallet_tar_theta_ = angles::normalize_angle(
          common::Direction2Angle(common::Angle2Direction(shelf_cur_theta_)));
    }
  } else {
    pallet_tar_theta_ = 0.0;
    shelf_cur_theta_ = 0.0;
  }
  if (!up_tag_loss_) {
    shelf_pose_vaild_ = true;
  }
  LOG_DEBUG_THROTTLE(0.2, "shelf_delta: %lf, pallet_tar_theta_: %f",
                     shelf_cur_theta_, pallet_tar_theta_);
}

uint32_t JackupAction::FeedBack() { return AgvData::get()->state_.percent; }
bool JackupAction::IsFinished() { return AgvData::get()->state_.is_finish; }

void JackupAction::DataReset(cotek_msgs::jack_up_action *msg) {
  AgvData::get()->Reset();
  memset(msg, 0, sizeof(cotek_msgs::jack_up_action));
}

void JackupAction::PalletStateProcess(uint8_t flag, AgvData *Get_info) {
  if (0 == pallet_nomove_flag_) {
    return;
  }
  // 托盘在底部时，直接返回
  if (Get_info->agvdata_.jackup_pallet_state.up_down_state == 0 ||
      Get_info->agvdata_.jackup_pallet_state.up_down_state >= 4 ||
      !shelf_pose_vaild_ ||
      Get_info->agvdata_.jackup_pallet_state.palletag == 0) {
    //托盘在顶部，且扫不到二维码，返回与车体反方向角速度
    if ((Get_info->agvdata_.jackup_pallet_state.up_down_state > 0 &&
         Get_info->agvdata_.jackup_pallet_state.up_down_state < 4) &&
        (!shelf_pose_vaild_ ||
         Get_info->agvdata_.jackup_pallet_state.palletag == 0)) {
      msg_.rotate_w = Get_info->state_.move_omega;
      return;
    }
    msg_.rotate_w = 0.0;
    return;
  }
  double diff =
      angles::normalize_angle(angles::normalize_angle(pallet_tar_theta_) -
                              angles::normalize_angle(shelf_cur_theta_));
  double make_up_pallet_w;
  // static PID_Handle pidhandle_move = {0};
  // static PID_Handle pidhandle_rotate = {0};

  if (up_tag_loss_) {
    LOG_ERROR_THROTTLE(1, "[PalletStateProcess] up_tag_loss_");
    msg_.rotate_w = 0.0;
    return;
  }
  //行走时的托盘保持
  else if (1 == pallet_nomove_flag_) {
    // pidhandle_rotate.clear();
    // pidhandle_move.kp = Get_info->option_.jack_up_option.pallet_nomove_kp;
    // pidhandle_move.ki = Get_info->option_.jack_up_option.pallet_nomove_ki;
    // pidhandle_move.kd = Get_info->option_.jack_up_option.pallet_nomove_kd;

    // mini pid
    mini_pid_rotate_->reset();
    mini_pid_move_->setPID(Get_info->option_.jack_up_option.pallet_nomove_kp,
                           Get_info->option_.jack_up_option.pallet_nomove_ki,
                           Get_info->option_.jack_up_option.pallet_nomove_kd);
    mini_pid_move_->setOutputLimits(4);
    mini_pid_move_->setOutputRampRate(1);
    // mini_pid_move_.setSetpointRange();
    // mini_pid_move_.setOutputFilter();

    msg_.rotate_w = -mini_pid_move_->getOutputFromDiff(diff);
    LOG_DEBUG_THROTTLE(0.1,
                       "[MiniPID-Move]  rotate_w:%f, shelf_cur_theta_:%f, "
                       "pallet_tar_theta_:%f",
                       msg_.rotate_w, shelf_cur_theta_, pallet_tar_theta_);
  }
  //车体旋转时候的托盘保持
  else if (2 == pallet_nomove_flag_) {
    // pidhandle_move.clear();
    // pidhandle_rotate.kp = 2.0;
    // pidhandle_rotate.ki = 0.0;
    // pidhandle_rotate.kd = 0.0;

    // mini pid
    mini_pid_move_->reset();
    mini_pid_rotate_->setPID(1, 0.1, 0.1);
    mini_pid_rotate_->setOutputLimits(4);
    mini_pid_rotate_->setOutputRampRate(1);
    // mini_pid_rotate_.setSetpointRange();
    // mini_pid_rotate_.setOutputFilter();

    make_up_pallet_w = mini_pid_move_->getOutputFromDiff(diff);
    msg_.rotate_w = Get_info->state_.move_omega - make_up_pallet_w;
    LOG_DEBUG_THROTTLE(0.1,
                       "[MiniPID-Rotate]  msg_.rotate_w:%f, "
                       "make_up_pallet_w:%f, shelf_cur_theta_:%f, "
                       "pallet_tar_theta_:%f",
                       msg_.rotate_w, make_up_pallet_w, shelf_cur_theta_,
                       pallet_tar_theta_);
  }

#if 1
  cotek_msgs::pallet_nomove_info pallet_info;
  pallet_info.pallet_angle_dif = math::Rad2Deg(diff);
  pallet_info.pallet_cmd_w = msg_.rotate_w;
  pallet_info.pallet_feedback_w = Get_info->state_.move_omega;
  pallet_info.PID_make_up_w = make_up_pallet_w;
  pallet_info.pallet_flag = pallet_nomove_flag_;
  pallet_nomove_pub_.publish(pallet_info);
#endif

  LOG_DEBUG_THROTTLE(0.2,
                     "[PalletStateProcess] rotate_w %.4f, "
                     "make_up_pallet_w:%.4f,diff %.4f, Type:%d, "
                     "pallet_tar_theta_:%.4f, shelf_cur_theta_:%f",
                     msg_.rotate_w, make_up_pallet_w, diff, pallet_nomove_flag_,
                     pallet_tar_theta_, shelf_cur_theta_);
}

void JackupAction::PalletCorrect(cotek_msgs::jack_up_action *msg,
                                 AgvData *Get_info) {
  if (Get_info->agvdata_.jackup_pallet_state.up_down_state == 0 ||
      Get_info->agvdata_.jackup_pallet_state.up_down_state >= 4) {
    Get_info->SetStateSucceed();
    LOG_ERROR_THROTTLE(1, "[PalletCorrect] Pallet on bottom");
    DataReset(msg);
    return;
  }
  float diff = angles::normalize_angle(pallet_tar_theta_ - shelf_cur_theta_);
  LOG_WARN_THROTTLE(0.1, "[JackupAction::PalletCorrect] diff:%f",
                    math::Rad2Deg(diff));
  pallet_nomove_flag_ = 1;
  static bool DelayStart = false;
  static ros::Time DelayStartTime;
  if (!DelayStart) {
    DelayStartTime = ros::Time::now();
    DelayStart = true;
  }
  if (ros::Time::now() - DelayStartTime > ros::Duration(4) ||
      fabs(diff) < common::Degree2rad(0.1)) {
    DelayStart = false;
    LOG_ERROR(
        "Pallet Correct finiish:%f shelf_cur_theta_:%f, pallet_tar_theta_:%f "
        "!!!",
        math::Rad2Deg(diff), shelf_cur_theta_, pallet_tar_theta_);
    Get_info->SetStateSucceed();
    pallet_nomove_flag_ = 2;
    DataReset(msg);
  }
}
// None..
void JackupAction::ActionNone(cotek_msgs::jack_up_action *msg,
                              AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;
  Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
}

// Rest..
void JackupAction::Rest(cotek_msgs::jack_up_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== JackUp:Doing Action----Rest %d=========",
                Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  pallet_nomove_flag_ = 2;
  Get_info->SetStateSucceed();
  DataReset(msg);
}

void JackupAction::ControlAudioLevel(cotek_msgs::jack_up_action *msg,
                                     AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "======== JackUp:Doing Action----Control_Audio_Level %d=========",
      Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  audio_level_ = Get_info->goal_.action_value;
  audio_level_dispatch_ = true;
  Get_info->SetStateSucceed();
  DataReset(msg);
}

//
void JackupAction::PalletNoMove(cotek_msgs::jack_up_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "============ Doing Action---Pallet_NoMove ============");
  pallet_nomove_flag_ = static_cast<uint32_t>(Get_info->goal_.action_value);
  LOG_INFO(
      "[action node]Pallet_Nomove Setting Scusess,pallet_nomove_flag:%d!!!",
      pallet_nomove_flag_);
  Get_info->SetStateSucceed();
  DataReset(msg);
}
// pallet up
void JackupAction::PalletUp(cotek_msgs::jack_up_action *msg,
                            AgvData *Get_info) {
  LOG_DEBUG("shelf_tag_num_2: %d",
            Get_info->agvdata_.jackup_pallet_state.palletag);
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && false == doing_action_,
      "============ Doing Action---Pallet_Up ============");
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  auto pallet = Get_info->agvdata_.jackup_pallet_state;

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   option.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  static bool start = false;
  static bool have_pallet = false;
  // 检测托盘
  if (!CheckPallet(option.check_pallet, Get_info) &&
      ((!option.check_pallet_once) ||
       (option.check_pallet_once && false == have_pallet))) {
    LOG_ERROR_THROTTLE(1, "[JackupAction::PalletUp] No Up Tag!!!!!!!!!!!!");
    return;
  }
  have_pallet = true;
  // 动作
  if ((pallet.up_down_state == 0 || pallet.up_down_state >= 4) &&
      (!pallet.height_init ||
       (pallet.height_init && (pallet.lift_height < option.max_height))) &&
      !acting_timeout_) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
    msg->atomic_action_value = Get_info->goal_.action_value;
    if (pallet.height_init && option.speed_optimize_control) {
      if (pallet.lift_height >= (option.max_height - option.safe_height)) {
        double ratio = fabs(pallet.lift_height -
                            (option.max_height - option.safe_height)) /
                       option.safe_height;
        double speed =
            option.max_speed - ((option.max_speed - option.safe_speed) * ratio);
        double speed_mold =
            speed < option.safe_speed ? option.safe_speed : speed;
        msg->lift_v = -speed_mold;
      } else {
        msg->lift_v = -option.max_speed;
      }
    } else {
      msg->lift_v = -option.fixed_speed;
    }
    msg->rotate_w = 0;
    Get_info->state_.percent =
        pallet.height_init ? 100 * (pallet.lift_height / option.max_height)
                           : 50;
    Get_info->state_.is_finish = false;

    // 重置动作超时
    if (!start && Get_info->option_.enable_timeout) {
      doing_action_ = false;
      start = true;
    }
    if (CheckTimeout(Get_info->option_.enable_timeout,
                     option.action_timeout_value, Get_info)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::ACTION_TIME_OUT);
      acting_timeout_ = true;
    }
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
    msg->atomic_action_value = 0;
    msg->lift_v = 0;
    msg->rotate_w = 0;
    // TODO(@ssh) 动作完成 延迟2s的保护动作
    static bool DelayStart = false;
    static ros::Time DelayStartTime;
    if (!DelayStart) {
      DelayStartTime = ros::Time::now();
      DelayStart = true;
    }
    if (ros::Time::now() - DelayStartTime < ros::Duration(2)) {
      LiftSafeBreakControl(DelayStartTime, msg, Get_info);
      ROS_DEBUG_ONCE("delay protect 2s");
      return;
    } else {
      pallet_nomove_flag_ = 2;
      DelayStart = false;
      Get_info->SetStateSucceed();
      Get_info->state_.is_finish = true;
      doing_action_ = false;
      DataReset(msg);
      ROS_INFO_STREAM(" Pallet_Up Done!!!");
      have_pallet = false;
      acting_timeout_ = false;
      start = false;
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
    }
  }
  ROS_DEBUG("Pallet [lift_v]: %f ;[up_down_state]: %d", msg->lift_v,
            pallet.up_down_state);
}
// pallet down..
void JackupAction::PalletDown(cotek_msgs::jack_up_action *msg,
                              AgvData *Get_info) {
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && doing_action_ == false,
      "============ Doing Action---Pallet_Down ============");
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  auto pallet = Get_info->agvdata_.jackup_pallet_state;

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   option.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  // 动作
  static bool start = false;

  if (pallet.up_down_state >= 0 && pallet.up_down_state <= 4 &&
      (!pallet.height_init ||
       (pallet.height_init && pallet.lift_height > -option.max_height)) &&
      !acting_timeout_) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
    msg->atomic_action_value = Get_info->goal_.action_value;

    Get_info->state_.percent =
        pallet.height_init
            ? 100 - (pallet.lift_height / option.max_height) * 100
            : 50;
    if (pallet.height_init && option.speed_optimize_control) {
      if (pallet.lift_height <= option.safe_height) {
        double ratio =
            (option.safe_height - pallet.lift_height) / option.safe_height;
        double speed =
            option.max_speed - ((option.max_speed - option.safe_speed) * ratio);
        double speed_mold =
            speed < option.safe_speed ? option.safe_speed : speed;
        msg->lift_v = speed_mold;
      } else {
        msg->lift_v = option.max_speed;
      }
    } else {
      msg->lift_v = option.fixed_speed;
    }
    msg->rotate_w = 0;
    Get_info->state_.is_finish = false;
    ROS_DEBUG("msg->lift_v: %f", msg->lift_v);

    // 重置动作超时
    if (!start && Get_info->option_.enable_timeout) {
      doing_action_ = false;
      start = true;
    }
    if (CheckTimeout(Get_info->option_.enable_timeout,
                     option.action_timeout_value, Get_info)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::ACTION_TIME_OUT);
      acting_timeout_ = true;
    }
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
    msg->atomic_action_value = 0;
    msg->lift_v = 0;
    msg->rotate_w = 0;
    static bool DelayStart = false;
    static ros::Time DelayStartTime;
    if (!DelayStart) {
      DelayStartTime = ros::Time::now();
      DelayStart = true;
    }
    if (ros::Time::now() - DelayStartTime < ros::Duration(2)) {
      LiftSafeBreakControl(DelayStartTime, msg, Get_info);
      return;
    } else {
      DelayStart = false;
      Get_info->SetStateSucceed();
      Get_info->state_.is_finish = true;
      doing_action_ = false;
      DataReset(msg);
      pallet_nomove_flag_ = 0;
      ROS_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                           " Pallet_Down Done!!!");
      acting_timeout_ = false;
      start = false;
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
    }
  }

  ROS_DEBUG("Pallet [lift_v]: %f ;[up_down_state]: %d", msg->lift_v,
            pallet.up_down_state);
}

void JackupAction::PalletRotation(cotek_msgs::jack_up_action *msg,
                                  AgvData *Get_info) {
  LOG_INFO_THROTTLE(0.5,
                    "============ Doing Action---Pallet_Rotation ============");

  pallet_nomove_flag_ = 0;
  static int cnt = 0;
  static int rotate_cnt_ = 0;
  if (Get_info->agvdata_.jackup_pallet_state.up_down_state == 0 ||
      Get_info->agvdata_.jackup_pallet_state.up_down_state >= 4 ||
      !BasicConfigHelper::Instance()
           .mechanical_option()
           .unicycle_model_option.use_rotate) {
    Get_info->SetStateSucceed();
    pallet_nomove_flag_ = 2;
    msg_.rotate_w = 0.;
    msg_.lift_v = 0.;
    Get_info->state_.is_finish = true;
    doing_action_ = false;
    rotate_cnt_ = 0;
    DataReset(msg);
    LOG_WARN("[PalletRotation] pallet is on bottom!!!");
    return;
  }
  if (Get_info->agvdata_.jackup_pallet_state.palletag == 0) {
    cnt++;
    if (cnt > 50) {
      // pallet_nomove_flag_ = 2;
      msg_.rotate_w = 0.;
      msg_.lift_v = 0.;
      LOG_ERROR_THROTTLE(1, "[JackupAction PalletRotation] SHELF_LOSS");
      up_tag_loss_ = true;
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::SHELF_LOSS);
      rotate_cnt_ = 0;
      return;
    }
    LOG_WARN_THROTTLE(0.2, "[JackupAction PalletRotation] Shelf_Num_Is_Zero");
    return;
  } else {
    up_tag_loss_ = false;
    cnt = 0;
  }

  double pallet_rotate_val_ = common::Degree2rad(Get_info->goal_.action_value);
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.jack_up_option.pallet_rotation_zero_option
                       .action_timeout_value,
                   Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::PALLET_ROTATE_ERR);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::PALLET_ROTATION);
  msg->atomic_action_value = 0;
  float diff = common::AngleSubtract(pallet_rotate_val_, shelf_cur_theta_);
  if (fabs(diff) > common::Degree2rad(0.5)) {
    rotate_cnt_++;
    msg_.rotate_w = -math::Sign(diff) *
                    std::min(std::fabs(diff), static_cast<float>(rotate_cnt_) *
                                                  math::Deg2Rad(0.1f));
    // msg_.rotate_w = math::Clamp(0.5, msg_.rotate_w, -0.5);
    msg_.rotate_w = math::Clamp(msg_.rotate_w, -0.5, 0.5);
    LOG_WARN_THROTTLE(
        0.1, "shelf_cur_theta_%f pallet_rotate_val_: %f, rotate_w:%f, diff: %f",
        common::rad2degree(shelf_cur_theta_), Get_info->goal_.action_value,
        msg_.rotate_w, common::rad2degree(diff));
  } else {
    Get_info->SetStateSucceed();
    pallet_nomove_flag_ = 2;
    msg_.rotate_w = 0.;
    msg_.lift_v = 0.;
    Get_info->state_.is_finish = true;
    doing_action_ = false;
    rotate_cnt_ = 0;
    DataReset(msg);
    LOG_WARN_COND(Get_info->option_.enable_local_debug,
                  " Pallet_Rotate Arrived!!! diff:%f",
                  common::rad2degree(diff));
  }
}

// pallet zero ..
void JackupAction::PalletZero(cotek_msgs::jack_up_action *msg,
                              AgvData *Get_info) {
  ros::Duration action_seconds(
      Get_info->option_.jack_up_option.pallet_rotation_zero_option
          .action_timeout_value,
      0);
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && doing_action_ == false,
      "============ Doing Action---Pallet_Zero ============");

  uint32_t percent = static_cast<int>(
      100 *
      static_cast<double>((ros::Time::now().toSec() - action_time_.toSec())) /
      static_cast<double>(action_seconds.toSec()));

  pallet_nomove_flag_ = 0;

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.jack_up_option.pallet_rotation_zero_option
                       .action_timeout_value,
                   Get_info)) {
    LOG_ERROR("[JackupAction PalletZero] PALLET_ZERO_ERR");
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  if (Get_info->agvdata_.jackup_pallet_state.roate_state == 0) {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::PALLET_ZERO);
    msg->atomic_action_value = Get_info->goal_.action_value;
    Get_info->state_.percent =
        percent < 100 && timeout_ == false ? percent : 99;
    msg->lift_v = 0;
    msg->rotate_w =
        Get_info->option_.jack_up_option.pallet_rotation_zero_option.safe_speed;
    msg_.rotate_w =
        fabs(msg_.rotate_w) > fabs(Get_info->option_.jack_up_option
                                       .pallet_rotation_zero_option.max_speed)
            ? Get_info->option_.jack_up_option.pallet_rotation_zero_option
                  .max_speed
            : msg_.rotate_w;
    Get_info->state_.is_finish = false;
    LOG_WARN_THROTTLE(0.2, "[Pallet_Zero Doing] rotate_w:%f percent: %d",
                      msg->rotate_w, Get_info->state_.percent);
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
    msg->atomic_action_value = 0;
    msg->lift_v = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
    LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                         " Pallet_Zero Done!!!");
  }
}
// Disable motor
void JackupAction::MotorDisable(cotek_msgs::jack_up_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "============ Doing Action---Motor_Disable ============");
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MOTOR_DISABLE);
  msg->atomic_action_value = Get_info->goal_.action_value;

  std::bitset<32> dismoto(Get_info->goal_.action_value);
  if (dismoto.test(0)) {
    msg->left_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "1:left_motor_disable");
  }
  if (dismoto.test(1)) {
    msg->right_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "2:right_motor_disable");
  }
  if (dismoto.test(2)) {
    msg->lift_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "3:lift_motor_disable");
  }
  if (dismoto.test(3)) {
    msg->rotate_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "4:rotate_motor_disable");
  }

  Get_info->state_.is_finish = true;
  DataReset(msg);
  LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                       " Motor_Disable Done!!!");
}
// Enable Motor
void JackupAction::MotorEnable(cotek_msgs::jack_up_action *msg,
                               AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "============ Doing Action---Motor_Enable ============");
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MOTOR_ENABLE);
  msg->atomic_action_value = Get_info->goal_.action_value;

  std::bitset<32> dismoto(Get_info->goal_.action_value);
  if (dismoto.test(0)) {
    msg->left_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "1:left_motor_Enable");
  }
  if (dismoto.test(1)) {
    msg->right_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "2:right_motor_Enable");
  }
  if (dismoto.test(2)) {
    msg->lift_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "3:lift_motor_Enable");
  }
  if (dismoto.test(3)) {
    msg->rotate_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "4:rotate_motor_Enable");
  }

  Get_info->state_.is_finish = true;
  DataReset(msg);
  LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                       " Motor_Enable Done!!!");
}
// charge ...
void JackupAction::ActionCharge(cotek_msgs::jack_up_action *msg,
                                AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
  msg->atomic_action_value = Get_info->goal_.action_value;

  // finish charge
  if (1.0 > msg->atomic_action_value) {
    Get_info->SetStateSucceed();
    doing_action_ = false;
    LOG_DEBUG_THROTTLE(0.2, "[ActionCharge] Finish Charge!!!");
    return;
  }

  /*charge finish estimate*/
  static uint8_t begin_percent;
  static uint16_t begin_voltage;

  if (doing_action_ == false) {
    begin_percent = Get_info->agvdata_.battery_moniter.capacity;
    begin_voltage = Get_info->agvdata_.battery_moniter.voltage;
    LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                         "============ Doing Action---Charge ============");
    LOG_INFO("begin_percent: %d, begin_voltage: %d", begin_percent,
             begin_voltage);
  }
  ros::Duration testtime(Get_info->option_.charge_option.charge_test_time * 60,
                         0);
  if (ros::Time::now() - action_time_ > testtime) {
    if (Get_info->agvdata_.battery_moniter.capacity < begin_percent ||
        Get_info->agvdata_.battery_moniter.voltage < begin_voltage) {
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::CHARGE_ERR);
      LOG_INFO_STREAM_COND(
          Get_info->option_.enable_local_debug,
          "============ Doing Action---Charge CHARGE_ERR ============");
      doing_action_ = false;
    }
  }
  if (CheckTimeout(Get_info->option_.enable_timeout * 60 * 60,
                   Get_info->option_.charge_option.charge_overout_time,
                   Get_info)) {
    // node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
    // msg->atomic_action_type =
    //     static_cast<uint32_t>(action::AtomicActionType::CHARGE);
    // msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
    // LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
    //                      "============ Doing Action---Charge CHARGE TIME "
    //                      "OVER!!! ============");

    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---Charge Open ============");
  }
  switch (Get_info->option_.charge_option.batter_type) {
    case static_cast<int>(BatteryType::BAO_E):
      Get_info->state_.percent = Get_info->agvdata_.battery_moniter.capacity;
      if (Get_info->agvdata_.battery_moniter.voltage >
              Get_info->option_.charge_option.charge_voltage_upper &&
          Get_info->agvdata_.battery_moniter.current >
              Get_info->option_.charge_option.charge_current_upper) {
        msg->atomic_action_type =
            static_cast<uint32_t>(action::AtomicActionType::CHARGE);
        msg->atomic_action_value = 0;
        Get_info->SetStateSucceed();
        doing_action_ = false;
        DataReset(msg);
        LOG_INFO_STREAM_COND(
            Get_info->option_.enable_local_debug,
            "============ Doing Action---Charge CHARGE DONE!!!============");
      }
      break;
    case static_cast<int>(BatteryType::ZHI_LI):
      Get_info->state_.percent = Get_info->agvdata_.battery_moniter.capacity;
      if (Get_info->agvdata_.battery_moniter.voltage >
          Get_info->option_.charge_option.charge_voltage_lower) {
        msg->atomic_action_type =
            static_cast<uint32_t>(action::AtomicActionType::CHARGE);
        msg->atomic_action_value = 0;
        Get_info->SetStateSucceed();
        doing_action_ = false;
        DataReset(msg);
        LOG_INFO_STREAM_COND(
            Get_info->option_.enable_local_debug,
            "============ Doing Action---Charge CHARGE DONE!!! ============");
      }
      break;

    default:
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::BAD_PARAMETER_ERR);
      LOG_ERROR_STREAM_COND(Get_info->option_.enable_local_debug,
                            "============ Doing Action---Charge "
                            "BAD_PARAMETER_ERR ============");
      break;
  }
}

// open thr door ...
void JackupAction::ActionOpenDoor(cotek_msgs::jack_up_action *msg,
                                  AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::OPEN_DOOR);
  msg->atomic_action_value = Get_info->goal_.action_value;

  // finish open door
  if (1.0 > msg->atomic_action_value) {
    msg->atomic_action_type =
        static_cast<uint32_t>(action::AtomicActionType::OPEN_DOOR);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---Close the door ============");
    return;
  }

  if (doing_action_ == false) {
    LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                         "============ Doing Action---OpenDoor ============");
  }
  ros::Duration wait_time(
      Get_info->option_.open_door_option.open_door_wait_time * 60, 0);
  if (ros::Time::now() - action_time_ > wait_time) {
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---Open the Door Finish============");
    msg->atomic_action_type =
        static_cast<uint32_t>(action::AtomicActionType::OPEN_DOOR);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
  }

  if (CheckTimeout(Get_info->option_.enable_timeout * 60 * 60,
                   Get_info->option_.charge_option.charge_overout_time,
                   Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---OpenDoor ACTION_TIME_OUT ============");
    msg->atomic_action_type =
        static_cast<uint32_t>(action::AtomicActionType::OPEN_DOOR);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
  }
}
// clear motor alarm
void JackupAction::MotorClearAlarm(cotek_msgs::jack_up_action *msg,
                                   AgvData *Get_info) {
  // LOG_INFO_COND(Get_info->option_.enable_local_debug,
  //               "Doing Action---Motor_ClearAlarm");
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MOTOR_CLEARALARM);
  msg->atomic_action_value = Get_info->goal_.action_value;

  static bool flag = false, delay_over = false;
  static ros::Time start_time = ros::Time::now();
  ros::Time Now_time = ros::Time::now();

  ros::Duration delay_seconds(
      Get_info->option_.jack_up_option.clear_alarm_delaytime, 0);
  ros::NodeHandle n;
  static ros::Timer time = n.createTimer(
      delay_seconds, [&](const ros::TimerEvent &) { delay_over = true; });
  if (flag == false) {
    time.start();
    flag = true;
    start_time = ros::Time::now();
  }
  Get_info->state_.percent = static_cast<int>(
      100 * static_cast<double>((Now_time.toSec() - start_time.toSec())) /
      static_cast<double>(delay_seconds.toSec()));

  if (delay_over == true) {
    time.stop();
    flag = false;
    delay_over = false;
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    Get_info->state_.percent = 100;
    Get_info->state_.is_finish = true;
    DataReset(msg);
  }
}
// pause action
void JackupAction::ActionWaiting(cotek_msgs::jack_up_action *msg,
                                 AgvData *Get_info) {
  LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
                "Doing Action---Action_Waiting");
  if (1 == Get_info->goal_.action_value) {
    Get_info->state_.is_finish = false;
    return;
  } else if (0 == Get_info->goal_.action_value) {
    DataReset(msg);
    Get_info->goal_ = lastgoal_;
    Get_info->state_.is_finish = true;
  } else {
    if (CheckTimeout(true, Get_info->goal_.action_value, Get_info)) {
      DataReset(msg);
      Get_info->goal_ = lastgoal_;
      Get_info->state_.is_finish = true;
    }
  }
}

void JackupAction::MagneticSwitchDir(cotek_msgs::jack_up_action *msg,
                                     AgvData *Get_info) {
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MAGNETIC_SWITCH_DIR);
  msg->atomic_action_value = Get_info->goal_.action_value;
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && false == doing_action_,
      "============ Doing Action---MagneticSwitchDir ============");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  static bool first_step_finish = false;

  // 至少识别出2跟磁条 第一阶段完成
  if (static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 3 ||
      static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) > 4) {
    first_step_finish = true;
    LOG_DEBUG("mag switch first step finished");
  }
  if (!first_step_finish) return;

  static bool second_step_finish = false;
  // 磁条数量重新变回1根 第二阶段完成
  if (static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 1 ||
      static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 2 ||
      static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 4) {
    second_step_finish = true;
    LOG_DEBUG("mag switch second step finished");
  }
  if (!second_step_finish) return;

  Get_info->SetStateSucceed();
  first_step_finish = false;
  second_step_finish = false;
  doing_action_ = false;
  timeout_ = false;
  DataReset(msg);
}

HinsonMlsData JackupAction::SelectMlsData(AgvData *Get_info) {
  return Get_info->agvdata_.move_feedback.velocity >= 0.
             ? Get_info->agvdata_.hinson_forward_mls_data
             : Get_info->agvdata_.hinson_backward_mls_data;
}

// Low_Power_Mode
void JackupAction::LowPowerMode(cotek_msgs::jack_up_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "Doing Action----Low_Power_Mode");
  static uint8_t cnt = 0;
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  ++cnt;
  msg->atomic_action_value = 0;
  Get_info->state_.percent = cnt;
  if (cnt == 100) {
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Low_Power_Mode Set Done!!!");
    LOG_INFO_STREAM(" Done action: Low_Power_Mode");
    DataReset(msg);
  }
}

bool JackupAction::CheckPallet(bool use, AgvData *Get_info) {
  if (!use) return true;
  if (0 == Get_info->agvdata_.jackup_pallet_state.palletag) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
    LOG_WARN_STREAM_COND(
        Get_info->option_.enable_local_debug &&
            (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
             100) % 100 ==
                0,
        "EXCEPTION:Doing Action-Pallet_Up: Have NO PALLET !!!");
    return false;
  }
  return true;
}

bool JackupAction::CheckTimeout(bool use, double limit_time,
                                AgvData *Get_info) {
  if (doing_action_ == false) {
    action_time_ = ros::Time::now();
    doing_action_ = true;
  }
  if (!use) return false;
  ros::Duration action_seconds(limit_time, 0);
  if ((ros::Time::now() - action_time_) > action_seconds) {
    LOG_ERROR_STREAM_COND(Get_info->option_.enable_local_debug,
                          " ERROR:...Action-Time out...");
    timeout_ = true;
    return true;
  }
  return false;
}

// delay
void JackupAction::Delay(cotek_msgs::jack_up_action *msg, AgvData *Get_info) {
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && false == doing_action_,
      "============ Doing Action---Delay ============");
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  auto pallet = Get_info->agvdata_.jackup_pallet_state;
  // TODO(@max) 后续修改成利用动作值作为延迟时间
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  msg->lift_v = 0;
  msg->rotate_w = 0;
  static bool DelayStart = false;
  static ros::Time DelayStartTime;
  if (!DelayStart) {
    DelayStartTime = ros::Time::now();
    DelayStart = true;
  }
  if (ros::Time::now() - DelayStartTime < ros::Duration(10)) {
    LOG_INFO("Delay time!!!");
    return;
  } else {
    pallet_nomove_flag_ = 0;
    DelayStart = false;
    Get_info->SetStateSucceed();
    Get_info->state_.is_finish = true;
    doing_action_ = false;
    DataReset(msg);
    ROS_INFO_STREAM(" Delay time Done!!!");
    acting_timeout_ = false;
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
  }
}

// Init action list
void JackupAction::ActionInit() {
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&JackupAction::ActionNone, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&JackupAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&JackupAction::PalletUp, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&JackupAction::PalletDown, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&JackupAction::ActionCharge, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::OPEN_DOOR)] =
      boost::bind(&JackupAction::ActionOpenDoor, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::SELECT_DIR)] =
      boost::bind(&JackupAction::MagneticSwitchDir, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_ROTATE)] =
      boost::bind(&JackupAction::PalletRotation, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_NOMOVE)] =
      boost::bind(&JackupAction::PalletNoMove, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
      boost::bind(&JackupAction::LowPowerMode, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
      boost::bind(&JackupAction::ControlAudioLevel, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_DISABLE)] =
      boost::bind(&JackupAction::MotorDisable, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_ENABLE)] =
      boost::bind(&JackupAction::MotorEnable, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_CLEARALARM)] =
      boost::bind(&JackupAction::MotorClearAlarm, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::WAIT)] =
      boost::bind(&JackupAction::ActionWaiting, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_ZERO)] =
      boost::bind(&JackupAction::PalletZero, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&JackupAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&JackupAction::Delay, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_CORRECT)] =
      boost::bind(&JackupAction::PalletCorrect, this, _1, _2);

  LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
                "Action_Init..... size: %d", action_map_.size());
}

// do action..
void JackupAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;
  std::map<int32_t, std::function<void(cotek_msgs::jack_up_action *,
                                       AgvData *)>>::iterator tep_map;
  uint32_t i = goal.action_type;
  if (0 == i) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug && 0 != goal.action_type,
                  "JackUP AGV: No Action Executing..");
    timeout_ = false;
    doing_action_ = false;
  } else {
    if (newgoal_.action_type != i) {
      timeout_ = false;
      doing_action_ = false;
      lastgoal_ = newgoal_;
      newgoal_ = goal;
      LOG_WARN_COND(Get_info->option_.enable_local_debug, "New goal...");
    }
  }
  tep_map = action_map_.find(i);

  if (tep_map != action_map_.end()) {
    // 根据任务清除no_move_flag
    if (IsResetNoMoveFlag(Get_info)) pallet_nomove_flag_ = 0;
    action_map_[i](&msg_, Get_info);
  } else {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug,
                   "Wrong action type: %d", i);
    DataReset(&msg_);
    return;
  }
}
// publish action msg to driver
void JackupAction::PublishActionMsg() {
  LOG_DEBUG_COND(AgvData::get()->option_.enable_local_debug,
                 "[action node]JackUpAGV--Publish");
  msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
  msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
  msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
  // 选择本地配置音量 | 调度下发音量
  msg_.audio_control_level =
      audio_level_dispatch_
          ? audio_level_
          : VolProcess(AgvData::get()->goal_.audio_control_type);
  // 音频模块硬件问题
  static int cnt = 0;

  if (msg_.audio_control_type == 0) {
    cnt++;
    if (cnt > 10) {
      msg_.audio_control_type = 1;
      msg_.audio_control_level = 0;
    }
  } else {
    cnt = 0;
  }

  PalletStateProcess(pallet_nomove_flag_, AgvData::get());
  if (pallet_nomove_flag_)
    msg_.atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::PALLET_NOMOVE);
  if (!AgvData::get()->agvdata_.manual_state) {
    jackup_action_pub_.publish(msg_);
  }
}

// 顶升电机抱闸保护
void JackupAction::LiftSafeBreakControl(ros::Time time,
                                        cotek_msgs::jack_up_action *msg,
                                        AgvData *Get_info) {
  if (!Get_info->option_.jack_up_option.pallet_up_down_option.motor_safe_break)
    return;
  pallet_nomove_flag_ = 0;
  if (((ros::Time::now() - time) > ros::Duration(1.0)) &&
      ((ros::Time::now() - time) < ros::Duration(1.5)) &&
      Get_info->state_.lift_break_state == false) {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::LIFT_MOTOR_BREAK_CONTROL);
    msg->atomic_action_value = 1.0;
    ROS_DEBUG("LIFT_MOTOR_BREAK_CONTROL=1");
  }
  if ((ros::Time::now() - time) > ros::Duration(1.5)) {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::LIFT_MOTOR_BREAK_CONTROL);
    msg->atomic_action_value = 0.0;
    ROS_DEBUG("LIFT_MOTOR_BREAK_CONTROL=0");
  }
}

bool JackupAction::IsResetNoMoveFlag(const AgvData *Get_info) {
  bool reset_no_move_flag = true;
  // 以下动作不清除 no_move_flag
  switch (static_cast<AgvTaskOperationType>(Get_info->goal_.action_type)) {
    case AgvTaskOperationType::NONE:
    case AgvTaskOperationType::REST:
    case AgvTaskOperationType::PALLET_NOMOVE:
    case AgvTaskOperationType::AUDIO_LEVEL_CONTROL:
    case AgvTaskOperationType::INIT:
    case AgvTaskOperationType::PALLET_ROTATE: {
      reset_no_move_flag = false;
      break;
    }
    default:
      break;
  }
  return reset_no_move_flag;
}

}  // namespace cotek_action
