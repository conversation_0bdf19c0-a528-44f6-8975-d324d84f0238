/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "node/cotek_action_node.h"

#include <ros/ros.h>

#include "cotek_common/cotek_node_name.h"

int main(int argc, char **argv) {
  ros::init(argc, argv, cotek_node::kActionNode);
  if (ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME,
                                     ros::console::levels::Info)) {
    ros::console::notifyLoggerLevelsChanged();
  }
  ros::NodeHandle nh;

  CotekActionNode node(&nh);
  // ros::AsyncSpinner s(2);
  // s.start();

  // if (!node.Init(&nh)) {
  //   LOG_INFO("cotek_action init failed.");
  //   return cotek_node::kErrorExitCode;
  // }

  // node.NodeRun();
  // LOG_INFO("cotek_action start...");
  // ros::waitForShutdown();

  ros::Rate rate(30);
  while (ros::ok()) {
    if (node.Init(&nh)) {
      // LOG_INFO("cotek_storage init sucessed");
    } else {
      // LOG_ERROR("cotek_storage init failed");
      // return 0;
    }
    rate.sleep();
    ros::spinOnce();
  }
  // return 0;
}
