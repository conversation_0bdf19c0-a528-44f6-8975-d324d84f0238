/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_model/roller_action.h"

#include <bitset>

#include "cotek_action/action_config.h"

namespace cotek_action {
void RollerAction::UpdateData(cotek_action::AgvData *Get_info) {}

uint32_t RollerAction::FeedBack() { return AgvData::get()->state_.percent; }
bool RollerAction::IsFinished() { return AgvData::get()->state_.is_finish; }

void RollerAction::DataReset(cotek_msgs::roller_action *msg) {
  AgvData::get()->Reset();
  memset(msg, 0, sizeof(cotek_msgs::roller_action));
}

void RollerAction::RollerStateProcess(uint8_t flag, AgvData *Get_info) {}

void RollerAction::RollerCorrect(cotek_msgs::roller_action *msg,
                                 AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  roller_nomove_flag_ = 0;
  Get_info->SetStateSucceed();
  DataReset(msg);
}

// None..
void RollerAction::ActionNone(cotek_msgs::roller_action *msg,
                              AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;
  Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
}

// Rest..
void RollerAction::Rest(cotek_msgs::roller_action *msg, AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  roller_nomove_flag_ = 2;
  Get_info->SetStateSucceed();
  DataReset(msg);
}

void RollerAction::ControlAudioLevel(cotek_msgs::roller_action *msg,
                                     AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Doing Action----Control_Audio_Level %d=========",
                Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  audio_level_ = Get_info->goal_.action_value;
  audio_level_dispatch_ = true;
  Get_info->SetStateSucceed();
  DataReset(msg);
}

//
void RollerAction::RollerNoMove(cotek_msgs::roller_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "============ Doing Action---Roller_NoMove ============");
  roller_nomove_flag_ = static_cast<uint32_t>(Get_info->goal_.action_value);
  LOG_INFO(
      "[action node]Roller_Nomove Setting Scusess,roller_nomove_flag_:%d!!!",
      roller_nomove_flag_);
  Get_info->SetStateSucceed();
  DataReset(msg);
}

// RollerIn
void RollerAction::RollerIn(cotek_msgs::roller_action *msg, AgvData *Get_info) {
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && false == doing_action_,
      "============ Doing Action---RollerIn ============");
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  auto roller = Get_info->agvdata_.roller_pallet_state;
  ros::Duration action_seconds(option.action_timeout_value, 0);
  static bool DelayStart = false;

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   option.action_timeout_value, Get_info)) {
    DataReset(msg);
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
    Get_info->SetStateSucceed();
  }
  LOG_INFO_ONCE("[RollerIn] start_delay_time:%f finish_delay_time:%f",
                option.start_delay_time, option.finish_delay_time);
  //延时检测滚筒光电状态
  if ((ros::Time::now().toSec() - action_time_.toSec()) <
      ros::Duration(option.start_delay_time).toSec()) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
    msg->atomic_action_value = Get_info->goal_.action_value;
    LOG_WARN_THROTTLE(0.5, "--RollerIn Action Start Delay--");
    return;
  }
  uint32_t percent = static_cast<int>(
      100 *
      static_cast<double>((ros::Time::now().toSec() - action_time_.toSec())) /
      static_cast<double>(action_seconds.toSec()));
  // 动作
  if ((roller.in_out_state <
       static_cast<uint8_t>(RollerInOutState::IN_AND_MIDDLE)) &&
      !acting_timeout_ && !DelayStart) {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::UP);  //进货
    msg->atomic_action_value = Get_info->goal_.action_value;
    Get_info->state_.percent =
        (percent < 100 && timeout_ == false) ? percent : 99;
    Get_info->state_.is_finish = false;
    LOG_WARN_THROTTLE(0.5, "--RollerIn Action Doing--In");
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
    msg->atomic_action_value = 0;
    msg->roller_v = 0;
    // 动作完成 延迟2s的保护动作
    static ros::Time DelayStartTime;
    if (!DelayStart) {
      DelayStartTime = ros::Time::now();
      DelayStart = true;
    }
    if (ros::Time::now().toSec() - DelayStartTime.toSec() <
        ros::Duration(option.finish_delay_time).toSec()) {
      LOG_WARN_THROTTLE(0.5, "--RollerIn Action Finish Delay--");
      return;
    } else {
      roller_nomove_flag_ = 2;
      DelayStart = false;
      Get_info->SetStateSucceed();
      Get_info->state_.is_finish = true;
      doing_action_ = false;
      DataReset(msg);
      ROS_INFO_STREAM(" RollerIn Done!!!");
      acting_timeout_ = false;
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
    }
  }
}

// RollerOut
void RollerAction::RollerOut(cotek_msgs::roller_action *msg,
                             AgvData *Get_info) {
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && doing_action_ == false,
      "============ Doing Action---RollerOut ============");
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  auto roller = Get_info->agvdata_.roller_pallet_state;
  ros::Duration action_seconds(option.action_timeout_value, 0);
  static bool DelayStart = false;

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   option.action_timeout_value, Get_info)) {
    DataReset(msg);
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  uint32_t percent = static_cast<int>(
      100 *
      static_cast<double>((ros::Time::now().toSec() - action_time_.toSec())) /
      static_cast<double>(action_seconds.toSec()));

  LOG_INFO_ONCE("[RollerIn] start_delay_time:%f finish_delay_time:%f",
                option.start_delay_time, option.finish_delay_time);
  //延时检测滚筒光电状态
  if ((ros::Time::now().toSec() - action_time_.toSec()) <
      ros::Duration(option.start_delay_time).toSec()) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
    msg->atomic_action_value = Get_info->goal_.action_value;
    LOG_WARN_THROTTLE(0.1, "--RollerOut Action Start Delay--");
    return;
  }
  // 动作
  if (roller.in_out_state > static_cast<uint8_t>(RollerInOutState::ALL_NONE) &&
      !acting_timeout_ && !DelayStart) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
    msg->atomic_action_value = Get_info->goal_.action_value;
    Get_info->state_.percent =
        (percent < 100 && timeout_ == false) ? percent : 99;
    msg->roller_v = 0;
    Get_info->state_.is_finish = false;
    LOG_WARN_THROTTLE(0.5, "--RollerOut Action Doing--OUT");
  } else {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
    msg->atomic_action_value = 0;
    msg->roller_v = 0;
    static ros::Time DelayStartTime;
    if (!DelayStart) {
      DelayStartTime = ros::Time::now();
      DelayStart = true;
    }
    if ((ros::Time::now().toSec() - DelayStartTime.toSec()) <
        ros::Duration(option.finish_delay_time).toSec()) {
      LOG_WARN_THROTTLE(0.1, "--RollerOut Action Finish Delay--");
      return;
    } else {
      DelayStart = false;
      Get_info->SetStateSucceed();
      Get_info->state_.is_finish = true;
      doing_action_ = false;
      DataReset(msg);
      roller_nomove_flag_ = 0;
      ROS_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                           " RollerOut Done!!!");
      acting_timeout_ = false;
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
    }
  }
}

// pallet rotate ..
void RollerAction::RollerRotate(cotek_msgs::roller_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && doing_action_ == false,
      "============ Doing Action---Roller_Zero ============");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  doing_action_ = false;
  DataReset(msg);
  LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                       " RollerRotate Done!!!");
}

// pallet zero ..
void RollerAction::RollerZero(cotek_msgs::roller_action *msg,
                              AgvData *Get_info) {
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  ros::Duration action_seconds(option.action_timeout_value, 0);

  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && doing_action_ == false,
      "============ Doing Action---Roller_Zero ============");
  uint32_t percent = static_cast<int>(
      100 *
      static_cast<double>((ros::Time::now().toSec() - action_time_.toSec())) /
      static_cast<double>(action_seconds.toSec()));

  roller_nomove_flag_ = 0;
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   option.action_timeout_value, Get_info)) {
    LOG_ERROR("[RollerAction RollerZero] Roller Zero Time Out");
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::PALLET_ZERO_ERR);
  }
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  doing_action_ = false;
  DataReset(msg);
  LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                       " Roller_Zero Done!!!");
}
// Disable motor
void RollerAction::MotorDisable(cotek_msgs::roller_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "============ Doing Action---Motor_Disable ============");
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MOTOR_DISABLE);
  msg->atomic_action_value = Get_info->goal_.action_value;

  std::bitset<32> dismoto(Get_info->goal_.action_value);
  if (dismoto.test(0)) {
    msg->left_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "1:left_motor_disable");
  }
  if (dismoto.test(1)) {
    msg->right_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "2:right_motor_disable");
  }
  if (dismoto.test(2)) {
    msg->roller_motor_enable = 1;
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "3:roller_motor_disable");
  }

  Get_info->state_.is_finish = true;
  DataReset(msg);
  LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                       " Motor_Disable Done!!!");
}
// Enable Motor
void RollerAction::MotorEnable(cotek_msgs::roller_action *msg,
                               AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "============ Doing Action---Motor_Enable ============");
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MOTOR_ENABLE);
  msg->atomic_action_value = Get_info->goal_.action_value;

  std::bitset<32> dismoto(Get_info->goal_.action_value);
  if (dismoto.test(0)) {
    msg->left_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "1:left_motor_Enable");
  }
  if (dismoto.test(1)) {
    msg->right_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug, "2:right_motor_Enable");
  }
  if (dismoto.test(2)) {
    msg->roller_motor_enable = 0;
    LOG_INFO_COND(Get_info->option_.enable_local_debug,
                  "3:roller_motor_Enable");
  }

  Get_info->state_.is_finish = true;
  DataReset(msg);
  LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                       " Motor_Enable Done!!!");
}
// charge ...
void RollerAction::ActionCharge(cotek_msgs::roller_action *msg,
                                AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
  msg->atomic_action_value = Get_info->goal_.action_value;

  // finish charge
  if (1.0 > msg->atomic_action_value) {
    Get_info->SetStateSucceed();
    doing_action_ = false;
    LOG_DEBUG_THROTTLE(0.2, "[ActionCharge] Finish Charge!!!");
    return;
  }

  /*charge finish estimate*/
  static uint8_t begin_percent;
  static uint16_t begin_voltage;

  if (doing_action_ == false) {
    begin_percent = Get_info->agvdata_.battery_moniter.capacity;
    begin_voltage = Get_info->agvdata_.battery_moniter.voltage;
    LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                         "============ Doing Action---Charge ============");
    LOG_INFO("begin_percent: %d, begin_voltage: %d", begin_percent,
             begin_voltage);
  }
  ros::Duration testtime(Get_info->option_.charge_option.charge_test_time * 60,
                         0);
  if (ros::Time::now() - action_time_ > testtime) {
    if (Get_info->agvdata_.battery_moniter.capacity < begin_percent ||
        Get_info->agvdata_.battery_moniter.voltage < begin_voltage) {
      node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::CHARGE_ERR);
      LOG_INFO_STREAM_COND(
          Get_info->option_.enable_local_debug,
          "============ Doing Action---Charge CHARGE_ERR ============");
      doing_action_ = false;
    }
  }
  if (CheckTimeout(Get_info->option_.enable_timeout * 60 * 60,
                   Get_info->option_.charge_option.charge_overout_time,
                   Get_info)) {
    // node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
    // msg->atomic_action_type =
    //     static_cast<uint32_t>(action::AtomicActionType::CHARGE);
    // msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---Charge CHARGE OPEN Relay!!! ============");
  }
  switch (Get_info->option_.charge_option.batter_type) {
    case static_cast<int>(BatteryType::BAO_E):
      Get_info->state_.percent = Get_info->agvdata_.battery_moniter.capacity;
      if (Get_info->agvdata_.battery_moniter.voltage >
              Get_info->option_.charge_option.charge_voltage_upper &&
          Get_info->agvdata_.battery_moniter.current >
              Get_info->option_.charge_option.charge_current_upper) {
        msg->atomic_action_type =
            static_cast<uint32_t>(action::AtomicActionType::CHARGE);
        msg->atomic_action_value = 0;
        Get_info->SetStateSucceed();
        doing_action_ = false;
        DataReset(msg);
        LOG_INFO_STREAM_COND(
            Get_info->option_.enable_local_debug,
            "============ Doing Action---Charge CHARGE DONE!!!============");
      }
      break;
    case static_cast<int>(BatteryType::ZHI_LI):
      Get_info->state_.percent = Get_info->agvdata_.battery_moniter.capacity;
      if (Get_info->agvdata_.battery_moniter.voltage >
          Get_info->option_.charge_option.charge_voltage_lower) {
        msg->atomic_action_type =
            static_cast<uint32_t>(action::AtomicActionType::CHARGE);
        msg->atomic_action_value = 0;
        Get_info->SetStateSucceed();
        doing_action_ = false;
        DataReset(msg);
        LOG_INFO_STREAM_COND(
            Get_info->option_.enable_local_debug,
            "============ Doing Action---Charge CHARGE DONE!!! ============");
      }
      break;

    default:
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::BAD_PARAMETER_ERR);
      LOG_ERROR_STREAM_COND(Get_info->option_.enable_local_debug,
                            "============ Doing Action---Charge "
                            "BAD_PARAMETER_ERR ============");
      break;
  }
}

// open thr door ...
void RollerAction::ActionOpenDoor(cotek_msgs::roller_action *msg,
                                  AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::OPEN_DOOR);
  msg->atomic_action_value = Get_info->goal_.action_value;

  // finish open door
  if (1.0 > msg->atomic_action_value) {
    msg->atomic_action_type =
        static_cast<uint32_t>(action::AtomicActionType::OPEN_DOOR);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---Close the door ============");
    return;
  }

  if (doing_action_ == false) {
    LOG_INFO_STREAM_COND(Get_info->option_.enable_local_debug,
                         "============ Doing Action---OpenDoor ============");
  }
  ros::Duration wait_time(
      Get_info->option_.open_door_option.open_door_wait_time * 60, 0);
  if (ros::Time::now() - action_time_ > wait_time) {
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---Open the Door Finish============");
    msg->atomic_action_type =
        static_cast<uint32_t>(action::AtomicActionType::OPEN_DOOR);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
  }

  if (CheckTimeout(Get_info->option_.enable_timeout * 60 * 60,
                   Get_info->option_.charge_option.charge_overout_time,
                   Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
    LOG_INFO_STREAM_COND(
        Get_info->option_.enable_local_debug,
        "============ Doing Action---OpenDoor ACTION_TIME_OUT ============");
    msg->atomic_action_type =
        static_cast<uint32_t>(action::AtomicActionType::OPEN_DOOR);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    doing_action_ = false;
    DataReset(msg);
  }
}
// clear motor alarm
void RollerAction::MotorClearAlarm(cotek_msgs::roller_action *msg,
                                   AgvData *Get_info) {
  // LOG_INFO_COND(Get_info->option_.enable_local_debug,
  //               "Doing Action---Motor_ClearAlarm");
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MOTOR_CLEARALARM);
  msg->atomic_action_value = Get_info->goal_.action_value;

  static bool flag = false, delay_over = false;
  static ros::Time start_time = ros::Time::now();
  ros::Time Now_time = ros::Time::now();

  ros::Duration delay_seconds(
      Get_info->option_.jack_up_option.clear_alarm_delaytime, 0);
  ros::NodeHandle n;
  static ros::Timer time = n.createTimer(
      delay_seconds, [&](const ros::TimerEvent &) { delay_over = true; });
  if (flag == false) {
    time.start();
    flag = true;
    start_time = ros::Time::now();
  }
  Get_info->state_.percent = static_cast<int>(
      100 * static_cast<double>((Now_time.toSec() - start_time.toSec())) /
      static_cast<double>(delay_seconds.toSec()));

  if (delay_over == true) {
    time.stop();
    flag = false;
    delay_over = false;
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    Get_info->state_.percent = 100;
    Get_info->state_.is_finish = true;
    DataReset(msg);
  }
}
// pause action
void RollerAction::ActionWaiting(cotek_msgs::roller_action *msg,
                                 AgvData *Get_info) {
  LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
                "Doing Action---Action_Waiting");
  if (1 == Get_info->goal_.action_value) {
    Get_info->state_.is_finish = false;
    return;
  } else if (0 == Get_info->goal_.action_value) {
    DataReset(msg);
    Get_info->goal_ = lastgoal_;
    Get_info->state_.is_finish = true;
  } else {
    if (CheckTimeout(true, Get_info->goal_.action_value, Get_info)) {
      DataReset(msg);
      Get_info->goal_ = lastgoal_;
      Get_info->state_.is_finish = true;
    }
  }
}

void RollerAction::MagneticSwitchDir(cotek_msgs::roller_action *msg,
                                     AgvData *Get_info) {
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::MAGNETIC_SWITCH_DIR);
  msg->atomic_action_value = Get_info->goal_.action_value;
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && false == doing_action_,
      "============ Doing Action---MagneticSwitchDir ============");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  static bool first_step_finish = false;

  // 至少识别出2跟磁条 第一阶段完成
  if (static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 3 ||
      static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) > 4) {
    first_step_finish = true;
    LOG_DEBUG("mag switch first step finished");
  }
  if (!first_step_finish) return;

  static bool second_step_finish = false;
  // 磁条数量重新变回1根 第二阶段完成
  if (static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 1 ||
      static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 2 ||
      static_cast<uint16_t>(SelectMlsData(Get_info).lines_info) == 4) {
    second_step_finish = true;
    LOG_DEBUG("mag switch second step finished");
  }
  if (!second_step_finish) return;

  Get_info->SetStateSucceed();
  first_step_finish = false;
  second_step_finish = false;
  doing_action_ = false;
  timeout_ = false;
  DataReset(msg);
}

HinsonMlsData RollerAction::SelectMlsData(AgvData *Get_info) {
  return Get_info->agvdata_.move_feedback.velocity >= 0.
             ? Get_info->agvdata_.hinson_forward_mls_data
             : Get_info->agvdata_.hinson_backward_mls_data;
}

// Low_Power_Mode
void RollerAction::LowPowerMode(cotek_msgs::roller_action *msg,
                                AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "Doing Action----Low_Power_Mode");
  static uint8_t cnt = 0;
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  ++cnt;
  msg->atomic_action_value = 0;
  Get_info->state_.percent = cnt;
  if (cnt == 100) {
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Low_Power_Mode Set Done!!!");
    LOG_INFO_STREAM(" Done action: Low_Power_Mode");
    DataReset(msg);
  }
}

bool RollerAction::CheckRoller(bool use, AgvData *Get_info) {
  if (!use) return true;
  return true;
}

bool RollerAction::CheckTimeout(bool use, double limit_time,
                                AgvData *Get_info) {
  if (doing_action_ == false) {
    action_time_ = ros::Time::now();
    doing_action_ = true;
  }
  if (!use) return false;
  ros::Duration action_seconds(limit_time, 0);
  if ((ros::Time::now() - action_time_) > action_seconds) {
    LOG_ERROR_STREAM_COND(Get_info->option_.enable_local_debug,
                          " ERROR:...Action-Time Out...");
    timeout_ = true;
    return true;
  }
  return false;
}

// delay
void RollerAction::Delay(cotek_msgs::roller_action *msg, AgvData *Get_info) {
  LOG_INFO_STREAM_COND(
      Get_info->option_.enable_local_debug && false == doing_action_,
      "============ Doing Action---Delay ============");
  auto option = Get_info->option_.jack_up_option.pallet_up_down_option;
  auto Roller = Get_info->agvdata_.roller_pallet_state;
  // TODO(@max) 后续修改成利用动作值作为延迟时间
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::NONE);
  msg->atomic_action_value = 0;
  msg->roller_v = 0;
  static bool DelayStart = false;
  static ros::Time DelayStartTime;
  if (!DelayStart) {
    DelayStartTime = ros::Time::now();
    DelayStart = true;
  }
  if (ros::Time::now() - DelayStartTime < ros::Duration(10)) {
    LOG_INFO("Delay time!!!");
    return;
  } else {
    roller_nomove_flag_ = 0;
    DelayStart = false;
    Get_info->SetStateSucceed();
    Get_info->state_.is_finish = true;
    doing_action_ = false;
    DataReset(msg);
    ROS_INFO_STREAM(" Delay time Done!!!");
    acting_timeout_ = false;
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
  }
}

// Init action list
void RollerAction::ActionInit() {
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&RollerAction::ActionNone, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&RollerAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&RollerAction::RollerIn, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&RollerAction::RollerOut, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&RollerAction::ActionCharge, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_ROTATE)] =
      boost::bind(&RollerAction::RollerRotate, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::OPEN_DOOR)] =
      boost::bind(&RollerAction::ActionOpenDoor, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::SELECT_DIR)] =
      boost::bind(&RollerAction::MagneticSwitchDir, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_NOMOVE)] =
      boost::bind(&RollerAction::RollerNoMove, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
      boost::bind(&RollerAction::LowPowerMode, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
      boost::bind(&RollerAction::ControlAudioLevel, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_DISABLE)] =
      boost::bind(&RollerAction::MotorDisable, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_ENABLE)] =
      boost::bind(&RollerAction::MotorEnable, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MOTOR_CLEARALARM)] =
      boost::bind(&RollerAction::MotorClearAlarm, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::WAIT)] =
      boost::bind(&RollerAction::ActionWaiting, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_ZERO)] =
      boost::bind(&RollerAction::RollerZero, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&RollerAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&RollerAction::Delay, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::PALLET_CORRECT)] =
      boost::bind(&RollerAction::RollerCorrect, this, _1, _2);

  LOG_INFO_COND(AgvData::get()->option_.enable_local_debug,
                "Action_Init..... size:%d", action_map_.size());
}

// do action..
void RollerAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;
  std::map<int32_t, std::function<void(cotek_msgs::roller_action *,
                                       AgvData *)>>::iterator tep_map;
  uint32_t i = goal.action_type;
  if (0 == i) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug && 0 != goal.action_type,
                  "JackUP AGV: No Action Executing..");
    timeout_ = false;
    doing_action_ = false;
  } else {
    if (newgoal_.action_type != i) {
      timeout_ = false;
      doing_action_ = false;
      lastgoal_ = newgoal_;
      newgoal_ = goal;
      LOG_WARN_COND(Get_info->option_.enable_local_debug, "New goal...");
    }
  }
  tep_map = action_map_.find(i);

  if (tep_map != action_map_.end()) {
    // 根据任务清除no_move_flag
    if (IsResetNoMoveFlag(Get_info)) roller_nomove_flag_ = 0;
    action_map_[i](&msg_, Get_info);
  } else {
    Get_info->SetStateSucceed();
    Get_info->state_.is_finish = true;
    doing_action_ = false;
    DataReset(&msg_);
    acting_timeout_ = false;
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::NORMAL);
    LOG_ERROR_COND(Get_info->option_.enable_local_debug,
                   "Wrong action type: %d", i);
    return;
  }
}
// publish action msg to driver
void RollerAction::PublishActionMsg() {
  LOG_DEBUG_COND(AgvData::get()->option_.enable_local_debug,
                 "[action node]RollerAGV--Publish");
  msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
  msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
  msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
  // 选择本地配置音量 | 调度下发音量
  msg_.audio_control_level =
      audio_level_dispatch_
          ? audio_level_
          : VolProcess(AgvData::get()->goal_.audio_control_type);
  // 音频模块硬件问题
  static int cnt = 0;

  if (msg_.audio_control_type == 0) {
    cnt++;
    if (cnt > 10) {
      msg_.audio_control_type = 1;
      msg_.audio_control_level = 0;
    }
  } else {
    cnt = 0;
  }

  RollerStateProcess(roller_nomove_flag_, AgvData::get());
  if (roller_nomove_flag_) {
    msg_.atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::PALLET_NOMOVE);
  }
  roller_action_pub_.publish(msg_);
}

bool RollerAction::IsResetNoMoveFlag(const AgvData *Get_info) {
  bool reset_no_move_flag = true;
  // 以下动作不清除 no_move_flag
  switch (static_cast<AgvTaskOperationType>(Get_info->goal_.action_type)) {
    case AgvTaskOperationType::NONE:
    case AgvTaskOperationType::REST:
    case AgvTaskOperationType::PALLET_NOMOVE:
    case AgvTaskOperationType::AUDIO_LEVEL_CONTROL:
    case AgvTaskOperationType::INIT:
    case AgvTaskOperationType::PALLET_ROTATE: {
      reset_no_move_flag = false;
      break;
    }
    default:
      break;
  }
  return reset_no_move_flag;
}

}  // namespace cotek_action
