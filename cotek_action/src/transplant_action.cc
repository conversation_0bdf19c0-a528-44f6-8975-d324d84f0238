/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_action/action_model/transplant_action.h"

#include <fstream>
#include <iostream>

#include "cotek_action/action_config.h"
#include "cotek_common/common.h"
namespace cotek_action {
// namespace {
// // TODO(wangjy) 高度到达偏差F
// constexpr int kheight_margin = 150;  // 1cm

// constexpr int klateral_offset = 100;  // 1cm

// constexpr int kampere_factor = 10;  // 电流转换系数

// // TODO(@ssh) 高度拉线编码器偏差

// constexpr int kheight_up_Offset = 100;

// constexpr int kheight_down_offset = 100;

// //堆高车货叉位于底部 计算拉线编码读数与实际偏差值
// constexpr int kheight_bmmsk34_base_deviation = -250;

// constexpr double kheight_base_ratio = 0.1;

// //临近地面高度
// constexpr int knear_to_land = 150;

// constexpr int klateral_to_liftload = 8660;  //  1m

// constexpr int kload_lift = 1500;  //载货高度

// constexpr int klateral_forward_limit = 5600;  //前移最大距离

// constexpr int klateral_backward_limit = 300;  //后移最大距离

// constexpr double lift_angle = 15.0;  //载货前倾角度

// constexpr double down_angle = 0.0;  //卸货叉腿角度

// constexpr double angle_offset = 1.0;  //俯仰角补偿

// //侧移拉线编码器初始长度
// constexpr int klateral_base = 2560;  // 10cm

// //最小机械限制高度
// constexpr int klimit_height = 5000;
// }  // namespace

uint32_t TransPlantAction::load_weight_base_ = 0;

void TransPlantAction::DataReset() {
  AgvData::get()->Reset();
  ResetDelayFlag();
  ResetDoingAction();
  ResetLastSpeed();
}

uint32_t TransPlantAction::FeedBack() { return AgvData::get()->state_.percent; }

void TransPlantAction::UpdateData(AgvData *Get_info) {
  // to do ..
}

bool TransPlantAction::IsFinished() { return AgvData::get()->state_.is_finish; }

bool TransPlantAction::DelayTime(double second) {
  if (!delay_flag_) {
    delay_flag_ = true;
    delay_time_start_ = ros::Time::now();
    return false;
  } else if (ros::Time::now() - delay_time_start_ < ros::Duration(second)) {
    LOG_INFO_THROTTLE(0.5, "delay time...");
    return false;
  } else {
    return true;
  }
}

// 双闭环PID调节，外环调速，内环调加速度，输出增量
const float TransPlantAction::GetForkMoveSpeed(const double &target_height,
                                               const double &current_height,
                                               const double &current_speed) {
  auto target_speed =
      dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed =
      target_height - current_height > 0
          ? lift_speed_pid_ptr_->getOutputFromDiff(target_speed - current_speed)
          : descent_speed_pid_ptr_->getOutputFromDiff(target_speed -
                                                      current_speed);
  LOG_DEBUG_STREAM("target_speed: " << target_speed
                                    << ", current speed: " << current_speed
                                    << ", delta_speed: " << delta_speed);
  return delta_speed;
}

// 卸载调速，分段PID
const float TransPlantAction::GetForkMoveSpeed(const double &target_height,
                                               const double &current_height,
                                               const double &current_speed,
                                               bool bl) {
  auto target_speed =
      dist_pid_ptr_->getOutputFromDiff(target_height - current_height);
  double delta_speed =
      unload_speed_pid_ptr_->getOutputFromDiff(target_speed - current_speed);
  LOG_INFO_STREAM("target_speed: " << target_speed
                                   << ", current speed: " << current_speed
                                   << ", delta_speed: " << delta_speed);
  return delta_speed;
}

// No.0
void TransPlantAction::Waiting(cotek_msgs::forklift_action *msg,
                               AgvData *Get_info) {
  auto fork_status = Get_info->agvdata_.forklift_pallet_state;
  auto lateral = fork_status.lateral;
  auto lateral_option = Get_info->option_.forklift_option.lateral_fork;
  auto delta_lateral = lateral - lateral_option.kdown_limit;  // 后移预留长度

  lateral_flag = delta_lateral < lateral_option.kup_offset ? true : false;
  if (lateral_flag) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
  } else {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_BACKWARD);
    msg->atomic_action_value = 50;
    LOG_INFO("reach fork backward, curr_val: %d, speed: %d", lateral,
             (int)msg->atomic_action_value);
    DelayTime(0.5);
  }
  Get_info->state_.is_finish = false;
  Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::UNSTART);
}

void TransPlantAction::ControlAudioLevel(cotek_msgs::forklift_action *msg,
                                         AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "======== HeapFork:Doing Action----Control_Audio_Level %d=========",
      Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  audio_level_ = Get_info->goal_.action_value;
  audio_level_dispatch_ = true;
  Get_info->SetStateSucceed();
  DataReset();
}

void TransPlantAction::Rest(cotek_msgs::forklift_action *msg,
                            AgvData *Get_info) {
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->SetStateSucceed();
  DataReset();
}

// No.1
void TransPlantAction::LowPowerMode(cotek_msgs::forklift_action *msg,
                                    AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "======== Forklift:Doing Action----Low_Power_Mode %d=========",
                Get_info->goal_.action_type);
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static uint8_t cnt = 0;
  cnt++;
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::LOW_POWER_MODE);
  msg->atomic_action_value = 0;
  Get_info->state_.percent = cnt;
  if (cnt == 100) {
    cnt = 0;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Low_Power_Mode Set Done!!!");
    LOG_INFO_STREAM(" Done action: Low_Power_Mode");
    DataReset();
  }
}

void TransPlantAction::CheckUPWeight(cotek_msgs::forklift_action *msg,
                                     AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          Get_info->option_.forklift_option.fork_common_option.check_weigh &&
          DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Check_UP_Weight =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto heap_option =
      Get_info->option_.forklift_option.fork_common_option;

  // 若使用重量检测
  if (heap_option.check_weigh && pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }
  // 不使用重量检测
  if (!heap_option.check_weigh) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

  LOG_DEBUG_STREAM("weight: " << pallet_status.weigh);

  // 抬货重量检测， 并记录抬货重量
  if (heap_option.check_weigh) {
    // 抬货完成等待1s，静等称重读数稳定，记录货物重量，以供掉货检测、卸货使用
    if (pallet_status.weigh > heap_option.kload_weighing_threshold &&
        DelayTime(1.0)) {
      load_weight_base_ = pallet_status.weigh;
      load_state_ = LoadState::HAVE_LOAD;

      LOG_INFO_STREAM(
          "forklift up weight checked succeed!!!  load_weight_base: "
          << load_weight_base_);

      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();

    } else if (!delay_flag_) {
      LOG_WARN_STREAM_THROTTLE(1, "checking up weight error.\nweight: "
                                      << pallet_status.weigh
                                      << ", kload_weighing_threshold: "
                                      << heap_option.kload_weighing_threshold);

      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::UP_WEIGHT_CHECK_ERROR);
      load_state_ = LoadState::NO_LOAD;
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      return;
    }
  }
}

void TransPlantAction::CheckDownWeight(cotek_msgs::forklift_action *msg,
                                       AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug &&
                    Get_info->option_.forklift_option.fork_common_option.check_weigh &&
                    DoingAction() == false,
                "========= Forklift:Doing "
                "Action----Heap_Fork_Check_Down_Weight =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
  }
  load_weight_base_ = 0;
  load_state_ = LoadState::NO_LOAD;
  PublishWeightLoadingBase();
  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto heap_option =
      Get_info->option_.forklift_option.fork_common_option;

  // 使用重量检测
  if (heap_option.check_weigh && pallet_status.weigh_errcode != 0) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "weigh error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }
  // 不使用重量检测
  if (!heap_option.check_weigh) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

  LOG_DEBUG_STREAM("weight: " << pallet_status.weigh);

  // 放货重量检测
  if (heap_option.check_weigh) {
    // 卸货重量检测
    if (pallet_status.weigh < heap_option.kload_weighing_threshold) {
      LOG_INFO("forklift down weight checked succeed!!!");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
    } else {
      LOG_WARN_STREAM_THROTTLE(1, "checking up weight error.\nweight: "
                                      << pallet_status.weigh
                                      << ", kload_weighing_threshold: "
                                      << heap_option.kload_weighing_threshold);

      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::DOWN_WEIGHT_CHECK_ERROR);
      load_state_ = LoadState::HAVE_LOAD;
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      return;
    }
  }
}

//前移
void TransPlantAction::LateralForward(cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Lift_Load =========");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  // auto vertical = pallet_status.vertical;
  auto lateral = pallet_status.lateral;
  auto heap_option =
      Get_info->option_.forklift_option.lateral_fork;
  // auto height = pallet_status.height;
  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  auto sideshift_error = pallet_status.sideshift_error_code;
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error || sideshift_error) {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug, "bmmsk34 error");
    return;
  }
  auto cmd_lateral = heap_option.kup_value;  //根据现场需修改配置
  auto delta_lateral = lateral - cmd_lateral;
  if (std::fabs(delta_lateral) < heap_option.kup_offset) {
    LOG_INFO(" fork lateral forward move arrived  ");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_FORWARD);
    msg->atomic_action_value = 50;
    LOG_INFO("CURRENT ACTION_TYPE IS : %s", msg->atomic_action_type);
    DelayTime(0.5);
  }
}

//后移
void TransPlantAction::LateralBackward(cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Lift_Load =========");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto lateral = pallet_status.lateral;
  auto heap_option =
      Get_info->option_.forklift_option.lateral_fork;
  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  auto sideshift_error = pallet_status.sideshift_error_code;
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error || sideshift_error) {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug, "bmmsk34 error");
    return;
  }
  auto cmd_lateral = heap_option.kdown_value; 
  auto delta_lateral = lateral - cmd_lateral;
  if (std::fabs(delta_lateral) < heap_option.kup_offset) {
    LOG_INFO(" fork lateral forward move arrived  ");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    msg->atomic_action_type =
        static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_BACKWARD);
    msg->atomic_action_value = 50;
    LOG_INFO("CURRENT ACTION_TYPE IS : %s", msg->atomic_action_type);
    DelayTime(0.5);
  }
}

//倾斜
void TransPlantAction::TransPlantTilt(cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Lift_Load =========");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;
  auto cmd_angle = Get_info->goal_.action_value;
  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto angle = pallet_status.angle_x;  //当前叉腿的实时俯仰角度
  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  auto sideshift_error = pallet_status.sideshift_error_code;
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error || sideshift_error) {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug, "bmmsk34 error");
    return;
  }
  
  auto delta_angle = cmd_angle - angle;
  if (std::fabs(delta_angle) < 1) {
    LOG_INFO("  fork tilt move arrived  ");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    msg->atomic_action_type =
        cmd_angle > 5
            ? static_cast<uint32_t>(AtomicActionType::FORK_LEAN_FORWARD)
            : static_cast<uint32_t>(AtomicActionType::FORK_LEAN_BACKWARD);
    msg->atomic_action_value = 5;
    LOG_INFO("CURRENT ACTION_TYPE IS : %s", msg->atomic_action_type);
    DelayTime(0.5);
  }
}

//侧移
void TransPlantAction::SideShiftMove(cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Lift_Load =========");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto sideshift = pallet_status.sideshift;
  auto cmd_sideshift = math::Meter2CMillimeter(pallet_status.delta_y);  //托盘识别后返回的托盘的y值
  auto heap_option =
      Get_info->option_.forklift_option.sideshift_fork;
  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  auto sideshift_error = pallet_status.sideshift_error_code;
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error || sideshift_error) {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug, "bmmsk34 error");
    return;
  }

  if(cmd_sideshift > heap_option.kup_limit){
    LOG_INFO("cmd_sideshift is out of rang !!! ");
    return;
  }
  //若为载货状态则二次侧移
  if (load_state_ == LoadState::HAVE_LOAD) {
    auto sideshift_base = sideshift;
    auto cmd_sideshift = 0;
    auto delta_sideshift = cmd_sideshift - sideshift;
    if (std::fabs(delta_sideshift) < heap_option.kup_offset) {
      LOG_INFO("second fork sideshift move arrived  ");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
    } else {
      msg->atomic_action_type =
          sideshift_base > 0
              ? static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_RIGHT)
              : static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_LEFT);
      msg->atomic_action_value = 50;
      LOG_INFO("CURRENT ACTION_TYPE IS : %s", msg->atomic_action_type);
      DelayTime(0.5);
    }
  } else {
    auto delta_sideshift = sideshift - cmd_sideshift;
    // auto sideshift_base = cmd_sideshift;
    if (std::fabs(delta_sideshift) < heap_option.kup_offset) {
      LOG_INFO(" first fork sideshift move arrived  ");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
    } else {
      msg->atomic_action_type =
          cmd_sideshift > 0
              ? static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_LEFT)
              : static_cast<uint32_t>(AtomicActionType::LATERAL_FORK_RIGHT);
      msg->atomic_action_value = 50;
      LOG_INFO("CURRENT ACTION_TYPE IS : %s", msg->atomic_action_type);
      DelayTime(0.5);
    }
  }
}

// 三向前移车抬货
void TransPlantAction::ReachLiftLoad(cotek_msgs::forklift_action *msg,
                                     AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }

  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Lift_Load =========");

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
  msg->atomic_action_value = 0;
  Get_info->state_.is_finish = false;

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;
  double fork_speed = pallet_status.lift_velocity;
  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  auto heap_option =
      Get_info->option_.forklift_option.height_fork;
  auto cmd_height = cmd_height_base_ + heap_option.kup_value;
  cmd_height = cmd_height > heap_option.kup_limit ? heap_option.kup_limit : cmd_height;
  auto delta_dis = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height << ", curr_height: " << height);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error) {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug, "bmmsk34 error");
    return;
  }

  // 抬货带托盘检测
  if (0 == pallet_status.fork_pallet_state &&
      Get_info->option_.forklift_option.fork_common_option.check_pallet) {
    node_status_manager_ptr_->SetNodeStatus(
        ActionNodeStatus::EXCEPTION_NO_PALLET_ERR);
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    LOG_WARN_STREAM_COND(
        Get_info->option_.enable_local_debug &&
            (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
             100) % 100 ==
                0,
        " EXCEPTION: Have NO PALLET !!!");
    return;
  }

  //前移车抬货步骤
    if (std::fabs(delta_dis) < heap_option.kup_offset) {
      // 完成
      LOG_WARN("transplant fork load_up arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
      return;
    } else {
      //抬
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed), 100.);
      //      msg->atomic_action_value = 50;
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("reach_load up, curr_val: %d, cmd_val: %d speed: %d", height,
               cmd_height, (int)msg->atomic_action_value);
      DelayTime(0.5);
    }
  
}

// 前移车卸货
void TransPlantAction::ReachUnLoad(cotek_msgs::forklift_action *msg,
                                   AgvData *Get_info) {
  // 延时0.5s，缓冲
  if (!DelayTime(0.5)) {
    return;
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_Fork_Un_Load =========");

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;
  double fork_speed = pallet_status.lift_velocity;
  auto lateral_error = pallet_status.lateral_error_code;
  auto height_error = pallet_status.hight_error_code;
  auto heap_option =
      Get_info->option_.forklift_option.height_fork;
  auto cmd_height = cmd_height_base_ - heap_option.kup_value;
  cmd_height = cmd_height < heap_option.kdown_limit ? heap_option.kdown_limit : cmd_height;
  auto delta_dis = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual_height: " << height);
  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 编码器错误 无法执行抬降
  if (height_error || lateral_error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  // unload
    if (std::fabs(delta_dis) < heap_option.kdown_offset) {
      // 完成
      LOG_INFO("transplant down arrived!!!");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      msg->atomic_action_value = 0;
      Get_info->SetStateSucceed();
      DataReset();
      return;
    } else {
      //降
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
      setting_speed_ +=
          GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ =
          math::Clamp(setting_speed_, -255.,
                      -static_cast<double>(heap_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      //      msg->atomic_action_value = 50;
      LOG_INFO("reach_load down, curr_val: %d, cmd_val: %d speed: %d", height,
               cmd_height, (int)msg->atomic_action_value);
      DelayTime(0.5);
    }
 }


// No.4
void TransPlantAction::ReachForkMove(cotek_msgs::forklift_action *msg,
                                     AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Heap_Fork_Move =========");

  auto fork_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = fork_status.height;
  float fork_speed = fork_status.lift_velocity;
  auto error = fork_status.hight_error_code;
  auto heap_option =
      Get_info->option_.forklift_option.height_fork;
  auto height_lower_limit = heap_option.kdown_limit;
  auto height_upper_limit = heap_option.kup_limit;
  auto cmd_height = math::Meter2CMillimeter(Get_info->goal_.action_value);
  cmd_height =
      cmd_height > height_upper_limit ? height_upper_limit : cmd_height;
  auto delta_dist = cmd_height - height;
  LOG_INFO_STREAM("cmd_height: " << cmd_height
                                 << ", actual_height: " << height);

  // 动作开始只进行一次上升、下降标志判定
  // if (DoingAction() == false) {
  up_down_flag_ = cmd_height >= height ? true : false;
  // }

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  // 拉线编码器错误或掉帧，无法执行抬降
  if (error) {
    LOG_WARN_COND(Get_info->option_.enable_local_debug, "height_bmmsk34 error");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    return;
  }

  if (up_down_flag_) {
    // 完成
    if (std::fabs(delta_dist) <= heap_option.kup_offset) {
      LOG_INFO("heap fork arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
      cmd_height_base_ = cmd_height;
      //      enable_forward = true;
      Get_info->SetStateSucceed();
      DataReset();
      return;
    } else {
      //  抬
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::UP);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ = math::Clamp(
          setting_speed_, static_cast<double>(heap_option.kup_min_speed), 100.);
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("heap fork up, input ratio: %d",
               static_cast<int>(msg->atomic_action_value));
      Get_info->state_.is_finish = false;
    }
  } else {
    // 完成
    if (std::fabs(delta_dist) <= heap_option.kdown_offset ||
        height < height_lower_limit) {
      LOG_INFO("heap fork arrived");
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
      // 记录堆高车叉腿移动完成时的最后高度,供抬货卸货时使用
      cmd_height_base_ =
          cmd_height < height_lower_limit ? height_lower_limit : cmd_height;
      Get_info->SetStateSucceed();
      DataReset();
      return;
    } else {
      // 降
      msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::DOWN);
      setting_speed_ += GetForkMoveSpeed(cmd_height, height, fork_speed);
      setting_speed_ =
          math::Clamp(setting_speed_, -100.,
                      -static_cast<double>(heap_option.kdown_min_speed));
      msg->atomic_action_value = std::fabs(setting_speed_);
      LOG_INFO("heap fork down, input ratio: %d",
               static_cast<int>(msg->atomic_action_value));
      Get_info->state_.is_finish = false;
    }
  }

#if 0
  // test
  Get_info->agvdata_.forklift_pallet_state.length += 1;
  int32_t length, goal_length;
  length = Get_info->option_.reg_a +
           (Get_info->option_.reg_b *
            Get_info->agvdata_.forklift_pallet_state.length);
  goal_length = Get_info->goal_.action_value;

  static ros::Time start_time = ros::Time::now();
  ros::Time Now_time = ros::Time::now();
  if (first_flag == false) {
    start_time = ros::Time::now();
    first_flag = true;
  }
  ros::Duration action_seconds(Get_info->option_.heap_fork_move_time, 0);
  uint32_t percent = static_cast<int>(
      100 * static_cast<double>((Now_time.toSec() - start_time.toSec())) /
      static_cast<double>(action_seconds.toSec()) / 2);
  if (percent > 150 || timeout == true) {
    first_flag = false;
    timeout = true;
    if (Get_info->option_.enable_timeout_err_alarm) {
      Get_info->state_.action_fault =
          static_cast<uint16_t>(ActionFault::ERROR_PALLET_MOVE);
      LOG_ERROR("Heap_Fork_Move Timeout..!!!");
      return;
    }
  } else {
    Get_info->state_.action_fault = 0;
  }
  Get_info->state_.percent =
      ((Get_info->agvdata_.forklift_pallet_state.length * 100) /
       Get_info->goal_.action_value);

  if (fabs(goal_length - length) < Get_info->option_.pallet_err_tolerant) {
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    timeout = false;
    first_flag = false;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status =
        static_cast<uint8_t>(ActionStatus::FINISH);
    LOG_INFO("Heap_Fork_Move Done!!!");
    Get_info->state_.action_fault = 0;
    DataReset();
    return;
  }
  Get_info->state_.is_finish = false;
  // moveup over then down with low speed
  if (length > goal_length) {
    msg->atomic_action_type = Get_info->goal_.action_type;
    msg->atomic_action_value = -5;
    return;
  }
  // move into margin zone
  else if ((goal_length - length) < Get_info->option_.margin) {
    msg->atomic_action_type = Get_info->goal_.action_type;
    // static float
    float t = 100. * (Get_info->option_.margin - (goal_length - length)) /
              Get_info->option_.margin;
    float b = 0.f;
    float c = 100.f;
    float d = 100.f;
    switch (Get_info->option_.dec_mode) {
      // linear dec speedcmd_height_base_after_forklift_up
      case 1:
        msg->atomic_action_value =
            ((goal_length - length) * 100) / Get_info->option_.margin;
        // msg->atomic_action_value = LinearEase(t,b,c,d);
        break;
      // fixed dec then uniform speed
      case 2:
        msg->atomic_action_value -=
            Get_info->option_.deceleration /
            Get_info->option_.control_period;
        msg->atomic_action_value =
            msg->atomic_action_value > Get_info->option_.low_speed
                ? msg->atomic_action_value
                : Get_info->option_.low_speed;
        break;
      // S 型减速运动，数值越大变加速度越大，停止截止速度越低
      case 3:
        msg->atomic_action_value = SineEaseInOut(t, b, c, d);
        break;
      case 4:
        msg->atomic_action_value = QuadEaseInOut(t, b, c, d);
        break;
      case 5:
        msg->atomic_action_value = CubicEaseInOut(t, b, c, d);
        break;
      case 6:
        msg->atomic_action_value = QuartEaseInOut(t, b, c, d);
        break;
      case 7:
        msg->atomic_action_value = QuintEaseInOut(t, b, c, d);
        break;
      case 8:
        msg->atomic_action_value = ExpoEaseInOut(t, b, c, d);
        break;
      case 9:
        msg->atomic_action_value = CircEaseInOut(t, b, c, d);
        break;
      default:
        break;
    }

  }
  // standard speed move up..
  else {
    msg->atomic_action_type = Get_info->goal_.action_type;
    msg->atomic_action_value = 100;
  }
#endif
}
// No.5
void TransPlantAction::Break(cotek_msgs::forklift_action *msg,
                             AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Break %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_BRAKE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  static int actcnt = 0;
  ++actcnt;
  Get_info->state_.percent = actcnt;
  if (actcnt == 100) {
    actcnt = 0;
    msg->atomic_action_type = 0;
    msg->atomic_action_value = 0;
    Get_info->state_.percent = 100;
    Get_info->state_.is_finish = true;
    Get_info->state_.action_status = static_cast<uint8_t>(ActionStatus::FINISH);
    DataReset();
    LOG_INFO("Break Done!!!");
  }
}
// No.6
void TransPlantAction::Beep(cotek_msgs::forklift_action *msg,
                            AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug,
                "========= Forklift:Doing Action----Beep %d =========",
                Get_info->goal_.action_type);
  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::BEEP);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.is_finish = true;
  DataReset();
  LOG_INFO("Beep Done!!!");
}
// No.7
void TransPlantAction::ForkliftBodyCotrollerReset(
    cotek_msgs::forklift_action *msg, AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========= Forklift:Doing Action----Curtis_Reset %d =========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  static ros::Time action_time;
  static bool first_flag = false;
  if (!first_flag) {
    first_flag = true;
    action_time = ros::Time::now();
  }
  msg->atomic_action_type =
      static_cast<uint32_t>(AtomicActionType::STEERING_RESET);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->state_.percent = 50;
  // 执行超过10s认为完成
  if (ros::Time::now() - action_time > ros::Duration(10)) {
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    LOG_INFO_STREAM(" Steering Reset!!!");
    DataReset();
  }
}

// No.8
void TransPlantAction::Charge(cotek_msgs::forklift_action *msg,
                              AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Charge %d===========",
                Get_info->goal_.action_type);

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::CHARGE);
  msg->atomic_action_value = Get_info->goal_.action_value;
  Get_info->SetState(50, false, ActionStatus::DOING);
  switch (static_cast<cotek_action::ChargeDetectionType>(
      Get_info->option_.charge_option.charge_detection)) {
    case ChargeDetectionType::CURRENT: {
      LOG_INFO("==========charge detection: current ==========");
      if ((Get_info->goal_.action_value == 1) &&
          (Get_info->agvdata_.battery_moniter.current >
           Get_info->option_.charge_option.charge_current_upper)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Open Charge Done!!!");
      } else if ((Get_info->goal_.action_value == 0) &&
                 (Get_info->agvdata_.battery_moniter.current <
                  Get_info->option_.charge_option.charge_current_lower)) {
        Get_info->SetStateSucceed();
        DataReset();
        LOG_INFO("Close Charge Done!!!");
      }
      break;
    }
    case ChargeDetectionType::VOLTAGE: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: voltage ==========");
      break;
    }
    case ChargeDetectionType::BOTH: {
      // TODO(@ssh)
      LOG_INFO("==========charge detection: both ==========");
      break;
    }
    default: {
      LOG_INFO("==========charge detection: none ==========");
      // 检查继电器是否打开
      if ((Get_info->agvdata_.charge_do_state) &&
          (Get_info->goal_.action_value == 1)) {
        // TODO(@ssh) 临时解决　调度需打开继电器后至少发一帧doing 此处延时
        ros::Duration(1.0).sleep();
        Get_info->SetStateSucceed();
        LOG_INFO("Open Charge Done!!!");
        DataReset();
      } else if ((!Get_info->agvdata_.charge_do_state) &&
                 (Get_info->goal_.action_value == 0)) {
        Get_info->SetStateSucceed();
        LOG_INFO("Close Charge Done!!!");
        DataReset();
      }
      break;
    }
  }
}

// delay action
void TransPlantAction::DelayAction(cotek_msgs::forklift_action *msg,
                                   AgvData *Get_info) {
  LOG_INFO_COND(Get_info->option_.enable_local_debug && DoingAction() == false,
                "========== Forklift:Doing Action----Delay ==========");
  if (CheckTimeout(1, 40, Get_info)) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }
  Get_info->state_.is_finish = false;
  if (!delay_flag_) {
    delay_time_start_ = ros::Time::now();
    delay_flag_ = true;
  }

  if (ros::Time::now() - delay_time_start_ < ros::Duration(1)) {
    LOG_INFO_THROTTLE(1, "delay time!!!");
    return;
  } else {
    delay_flag_ = false;
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  }

}  // namespace cotek_action

// 堆高车放货检测
void TransPlantAction::UnloadDetect(cotek_msgs::forklift_action *msg,
                                    AgvData *Get_info) {
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug && DoingAction() == false,
      "========= Forklift:Doing Action----Heap_UnloadDetect =========");

  if (CheckTimeout(Get_info->option_.enable_timeout,
                   Get_info->option_.action_timeout_value, Get_info) &&
      Get_info->state_.is_finish != true) {
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TIME_OUT);
  }

  auto pallet_status = Get_info->agvdata_.forklift_pallet_state;
  auto height = pallet_status.height;

  PublishRequestUnloadDetect();

  auto &unload_detect = Get_info->agvdata_.unload_detect_data;

  if ((ros::Time::now() - unload_detect.time < ros::Duration(2.0) &&
       unload_detect.permit_unload)) {
    LOG_INFO("Permit unload!!!");
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->SetStateSucceed();
    DataReset();
  } else {
    if (ros::Time::now() - GetActionStartTime() > ros::Duration(3.0)) {
      node_status_manager_ptr_->SetNodeStatus(
          ActionNodeStatus::PALLET_DETECT_ERROR);
    }
    msg->atomic_action_type = static_cast<uint32_t>(AtomicActionType::REST);
    msg->atomic_action_value = 0;
    Get_info->state_.is_finish = false;
  }
}

void TransPlantAction::PublishRequestUnloadDetect() {
  cotek_msgs::request_unload_detect msg;
  msg.stamp = ros::Time::now();
  unload_detect_pub_.publish(msg);
}

void TransPlantAction::ActionInit() {
  // 注册动作
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::NONE)] =
      boost::bind(&TransPlantAction::Waiting, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::REST)] =
      boost::bind(&TransPlantAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::INIT)] =
      boost::bind(&TransPlantAction::Rest, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::UP)] =
      boost::bind(&TransPlantAction::ReachLiftLoad, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_UP_WEIGHT)] =
      boost::bind(&TransPlantAction::CheckUPWeight, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHECK_DOWN_WEIGHT)] =
      boost::bind(&TransPlantAction::CheckDownWeight, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DOWN)] =
      boost::bind(&TransPlantAction::ReachUnLoad, this, _1, _2);
  //倾斜
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_TILT)] =
      boost::bind(&TransPlantAction::TransPlantTilt, this, _1, _2);
  //前移
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_FORWARD)] =
      boost::bind(&TransPlantAction::LateralForward, this, _1, _2);
  //后移
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_BACKWARD)] =
      boost::bind(&TransPlantAction::LateralBackward, this, _1, _2);
  //侧移
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::FORK_SIDESHIFT)] =
      boost::bind(&TransPlantAction::SideShiftMove, this, _1, _2);
  // 放货检测
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::MEASURE_SIZE)] =
      boost::bind(&TransPlantAction::UnloadDetect, this, _1, _2);

  action_map_[static_cast<uint32_t>(AgvTaskOperationType::HEIGHT_FORK_MOVE)] =
      boost::bind(&TransPlantAction::ReachForkMove, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::CHARGE)] =
      boost::bind(&TransPlantAction::Charge, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::LOW_POWER_MODE)] =
      boost::bind(&TransPlantAction::LowPowerMode, this, _1, _2);
  action_map_[static_cast<uint32_t>(
      AgvTaskOperationType::AUDIO_LEVEL_CONTROL)] =
      boost::bind(&TransPlantAction::ControlAudioLevel, this, _1, _2);
  action_map_[static_cast<uint32_t>(AgvTaskOperationType::DELAY)] =
      boost::bind(&TransPlantAction::DelayAction, this, _1, _2);
}

void TransPlantAction::ExecuteAction(AgvData *Get_info) {
  auto goal = Get_info->goal_;
  std::map<uint32_t, std::function<void(cotek_msgs::forklift_action *,
                                        AgvData *)>>::iterator tep_map;
  uint32_t i = goal.action_type;
  if (0 == i) {
    LOG_DEBUG_COND(Get_info->option_.enable_local_debug,
                   "ForkLift AGV:No Action Executing..");
    ResetDoingAction();
  } else {
    static uint32_t new_goal = 0;
    if (new_goal != i) {
      ResetDoingAction();
      new_goal = i;
      LOG_WARN_COND(Get_info->option_.enable_local_debug, "New goal...");
    }
  }
  LOG_INFO_COND(
      Get_info->option_.enable_local_debug &&
          (static_cast<int>((ros::Time::now().toSec() - 1574000000) * 10) %
           100) % 500 ==
              0 &&
          (goal.action_type != 0),
      "ForkLift AGV:Doing action No.%d", goal.action_type);
  tep_map = action_map_.find(i);
  if (tep_map != action_map_.end()) {
    action_map_[i](&msg_, Get_info);
  } else {
    LOG_ERROR_COND(Get_info->option_.enable_local_debug,
                   "Wrong action type: %d", i);
    node_status_manager_ptr_->SetNodeStatus(ActionNodeStatus::ACTION_TYPE_ERR);
    return;
  }
}

void TransPlantAction::PublishWeightLoadingBase() {
  cotek_msgs::weighting_loaded msg;
  msg.stamp = ros::Time::now();
  msg.load_weight_base = load_weight_base_;
  msg.load_state = static_cast<uint32_t>(load_state_);
  load_weight_base_pub_.publish(msg);
}

void TransPlantAction::PublishActionMsg() {
  msg_.corning_led_type = AgvData::get()->goal_.corning_led_type;
  msg_.audio_control_type = AgvData::get()->goal_.audio_control_type;
  // 选择本地配置音量 | 调度下发音量
  msg_.audio_control_level =
      audio_level_dispatch_
          ? audio_level_
          : VolProcess(AgvData::get()->goal_.audio_control_type);
  msg_.three_color_led_type = AgvData::get()->goal_.three_color_led_type;
  // 音频模块硬件问题
  static int cnt = 0;
  if (msg_.audio_control_type == 0) {
    cnt++;
    if (cnt > 10) {
      msg_.audio_control_type = 1;
      msg_.audio_control_level = 0;
    }
  } else {
    cnt = 0;
  }

  forklift_action_pub_.publish(msg_);

  PublishWeightLoadingBase();
}

}  // namespace cotek_action