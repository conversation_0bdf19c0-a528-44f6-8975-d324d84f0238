/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include <ros/ros.h>

#include <fstream>
#include <map>
#include <sstream>
#include <string>

#include "cotek_action/cotek_action_enum.h"
#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/log_porting.h"
#include "node/cotek_action_node.h"

bool CotekActionNode::LoadBasicOption(
    std::string file, cotek_action::AgvBasicOption* option) {
  try {
    nlohmann::ordered_json json;
    try {
      json = nlohmann::ordered_json::parse(file);
    } catch (const n_excetion &ex) {
      std::cout << (ex.what()) << std::endl;
      LOG_INFO("json format error.");
      return false;
    }
    
    if(!BasicConfigHelper::Instance().LoadConfig()) return false;
    option->agv_type = BasicConfigHelper::Instance().agv_type();

    LOG_INFO_STREAM("agv_type: " << static_cast<uint32_t>(option->agv_type));

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    LOG_INFO("json format error.");
    return false;
  }
  LOG_INFO("---------------------------------------------\n\n");
  return true;
}

bool CotekActionNode::LoadActionOption(
    std::string path, cotek_action::AgvActionOption* option, std::string json_str) {
  nlohmann::ordered_json j_config;
  try {
    if (json_str.empty()) {
      j_config = nlohmann::ordered_json::parse(path);
    } else {
      j_config = nlohmann::ordered_json::parse(json_str);
    }
  } catch (const n_excetion &ex) {
    std::cout << (ex.what()) << std::endl;
    LOG_INFO("json format error.");
    return false;
  }

  try {
    double number_value;

    // 通用配置选项
    {
      LOG_INFO("--------- cotek_action ---------");
      option->enable_local_debug = j_config["action_common_config"]["enable_loacl_debug"]
                                       .get<bool>();
      LOG_INFO_ONCE("enable_local_debug: %d", option->enable_local_debug);

      option->enable_timeout = j_config["action_common_config"]["enable_timeout"]
                                   .get<bool>();
      LOG_INFO_ONCE("enable_timeout: %d", option->enable_timeout);

      // todo: check
      number_value = j_config["action_common_config"]["action_timeout_value"].get<double>();
      if (number_value) {
        option->action_timeout_value = number_value;
        LOG_INFO_STREAM("warning action_timeout_value: " << option->action_timeout_value);
      } else {
        LOG_ERROR("action config json warning: action_timeout_value illegality.");
        return false;
      }
      // option->action_timeout_value = j_config["action_common_config"]
      //                                    .object_items()
      //                                    .at("action_timeout_value")
      //                                    .number_value();
      // LOG_INFO_ONCE("action_timeout_value: %f", option->action_timeout_value);

      number_value = j_config["action_common_config"]["control_period"].get<double>();
      if (number_value) {
        option->control_period = number_value;
        LOG_INFO_STREAM("warning control_period: " << option->control_period);
      } else {
        LOG_ERROR("action config json warning: control_period illegality.");
        return false;
      }
      // option->control_period = j_config["action_common_config"]
      //                              .object_items()
      //                              .at("control_period")
      //                              .number_value();
      // LOG_INFO_ONCE("control_period: %f", option->control_period);
    }
    // 充电配置选项
    {
      LOG_INFO("--------- charge_action ---------");
      option->charge_option.charge_detection = j_config["action_charge"]["charge_detection"]
                                                   .get<int>();
      LOG_INFO_ONCE("charge_detection: %f",
                    option->charge_option.charge_detection);

      option->charge_option.start_charge_current =
          j_config["action_charge"]["start_charge_current"].get<double>();
      LOG_INFO_ONCE("start_charge_current: %f",
                    option->charge_option.start_charge_current);

      option->charge_option.stop_charge_current =
          j_config["action_charge"]["stop_charge_current"].get<double>();
      LOG_INFO_ONCE("stop_charge_current: %f",
                    option->charge_option.stop_charge_current);

      option->charge_option.start_charge_voltage =
          j_config["action_charge"]["start_charge_voltage"].get<double>();
      LOG_INFO_ONCE("start_charge_voltage: %f",
                    option->charge_option.start_charge_voltage);

      option->charge_option.stop_charge_voltage =
          j_config["action_charge"]["stop_charge_voltage"].get<double>();
      LOG_INFO_ONCE("stop_charge_voltage: %f",
                    option->charge_option.stop_charge_voltage);
    }
    // 开门配置
    {
      option->open_door_option.enable_timewait = j_config["action_open_door"]["enable_timewait"]
                                                     .get<bool>();
      LOG_INFO_ONCE("enable_timewait: %f",
                    option->open_door_option.enable_timewait);

      option->open_door_option.open_door_wait_time =
          j_config["action_open_door"]["open_door_wait_time"].get<double>();
      LOG_INFO_ONCE("open_door_wait_time: %f",
                    option->open_door_option.open_door_wait_time);
    }
    // 人工确认
    {
      if (j_config.contains("manual_confirm")) {
        option->manual_confirm.overtime_confirm = j_config["manual_confirm"]["overtime_confirm"]
                                                     .get<bool>();
        option->manual_confirm.wait_time = j_config["manual_confirm"]["wait_time"]
                                                     .get<double>();        
      } else {
        option->manual_confirm.overtime_confirm = false;
        option->manual_confirm.wait_time = 30.;
      }
      LOG_INFO("--------- manual_confirm ---------");
      LOG_INFO_ONCE("overtime_confirm: %d",
                    option->manual_confirm.overtime_confirm);
      LOG_INFO_ONCE("wait_time: %f",
                    option->manual_confirm.wait_time);
    }
    // 顶升车配置选项
    // pallet_up_down
    {
      LOG_INFO("--------- jack_up pallet_up_down ---------");
      auto lift_option = option->jack_up_option.pallet_up_down_option;
      lift_option.check_pallet = j_config["jackup_action"]["pallet_up_down"]["check_pallet"]
                                     .get<bool>();
      LOG_INFO_STREAM("check_pallet: " << lift_option.check_pallet);

      lift_option.motor_safe_break = j_config["jackup_action"]["pallet_up_down"]["motor_safe_break"]
                                         .get<bool>();
      LOG_INFO_STREAM("motor_safe_break: " << lift_option.motor_safe_break);

      lift_option.speed_optimize_control = j_config["jackup_action"]["pallet_up_down"]["speed_optimize_control"]
                                               .get<bool>();
      LOG_INFO_STREAM(
          "speed_optimize_control: " << lift_option.speed_optimize_control);

      lift_option.max_speed = j_config["jackup_action"]["pallet_up_down"]["max_speed"]
                                  .get<double>();
      LOG_INFO_STREAM("max_speed: " << lift_option.max_speed);

      lift_option.safe_speed = j_config["jackup_action"]["pallet_up_down"]["safe_speed"]
                                   .get<double>();
      LOG_INFO_STREAM("safe_speed: " << lift_option.safe_speed);

      lift_option.fixed_speed = j_config["jackup_action"]["pallet_up_down"]["fixed_speed"]
                                    .get<double>();
      LOG_INFO_STREAM("fixed_speed: " << lift_option.fixed_speed);

      lift_option.max_height = j_config["jackup_action"]["pallet_up_down"]["max_height"]
                                   .get<double>();
      LOG_INFO_STREAM("max_height: " << lift_option.max_height);

      option->jack_up_option.pallet_up_down_option = lift_option;
    }
    // pallet_rotation_zero
    // {
    //   LOG_INFO("--------- jack_up pallet_rotation_zero ---------");

    //   option->jack_up_option.pallet_rotation_zero_option
    //       .speed_optimize_control =
    //       j_config["jackup_action"]["pallet_rotation_zero"]["speed_optimize_control"]
    //           .get<bool>();
    //   LOG_INFO_STREAM("speed_optimize_control: "
    //                   << option->jack_up_option.pallet_rotation_zero_option
    //                          .speed_optimize_control);

    //   option->jack_up_option.pallet_rotation_zero_option.max_speed =
    //       j_config["jackup_action"]["pallet_rotation_zero"]["max_speed"]
    //           .get<double>();
    //   LOG_INFO_STREAM(
    //       "max_speed: "
    //       << option->jack_up_option.pallet_rotation_zero_option.max_speed);

    //   option->jack_up_option.pallet_rotation_zero_option.safe_speed =
    //       j_config["jackup_action"]["pallet_rotation_zero"]["safe_speed"]
    //           .get<double>();
    //   LOG_INFO_STREAM(
    //       "safe_speed: "
    //       << option->jack_up_option.pallet_rotation_zero_option.safe_speed);

    //   option->jack_up_option.pallet_rotation_zero_option.fixed_speed =
    //       j_config["jackup_action"]["pallet_rotation_zero"]["fixed_speed"]
    //           .get<double>();
    //   LOG_INFO_STREAM(
    //       "fixed_speed: "
    //       << option->jack_up_option.pallet_rotation_zero_option.fixed_speed);
    // }
    // 顶升Nomove配置选项
    {
      LOG_INFO("--------- jack_up nomove_pid ---------");
      option->jack_up_option.nomove_pid.kp =
          j_config["jackup_action"]["pallet_nomove_pid"]["kp"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_nomove_kp: " << option->jack_up_option.nomove_pid.kp);

      option->jack_up_option.nomove_pid.ki =
          j_config["jackup_action"]["pallet_nomove_pid"]["ki"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_nomove_ki: " << option->jack_up_option.nomove_pid.ki);

      option->jack_up_option.nomove_pid.kd =
          j_config["jackup_action"]["pallet_nomove_pid"]["kd"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_nomove_kd: " << option->jack_up_option.nomove_pid.kd);

      option->jack_up_option.nomove_pid.output_limit =
          j_config["jackup_action"]["pallet_nomove_pid"]["output_limit"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_nomove_output_limit: " << option->jack_up_option.nomove_pid.output_limit);

      option->jack_up_option.nomove_pid.ramp_rate =
          j_config["jackup_action"]["pallet_nomove_pid"]["ramp_rate"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_nomove_ramp_rate: " << option->jack_up_option.nomove_pid.ramp_rate);
    }

    {
      LOG_INFO("--------- jack_up rotata_pid ---------");
      option->jack_up_option.rotate_pid.kp =
          j_config["jackup_action"]["pallet_rotate_pid"]["kp"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_rotate_kp: " << option->jack_up_option.rotate_pid.kp);

      option->jack_up_option.rotate_pid.ki =
          j_config["jackup_action"]["pallet_rotate_pid"]["ki"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_rotate_ki: " << option->jack_up_option.rotate_pid.ki);

      option->jack_up_option.rotate_pid.kd =
          j_config["jackup_action"]["pallet_rotate_pid"]["kd"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_rotate_kd: " << option->jack_up_option.rotate_pid.kd);

      option->jack_up_option.rotate_pid.output_limit =
          j_config["jackup_action"]["pallet_rotate_pid"]["output_limit"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_rotate_output_limit: " << option->jack_up_option.rotate_pid.output_limit);

      option->jack_up_option.rotate_pid.ramp_rate =
          j_config["jackup_action"]["pallet_rotate_pid"]["ramp_rate"]
              .get<double>();
      LOG_INFO_STREAM(
          "pallet_rotate_ramp_rate: " << option->jack_up_option.rotate_pid.ramp_rate);
    }
    // 叉车配置选项
    // pallet_fork_up_down
    {
      LOG_INFO("--------- pallet_forklift ---------");
      if (j_config["forklift_action"].contains("common_config")) {
        option->forklift_option.fork_common_option.check_pallet =
            j_config["forklift_action"]["common_config"]["check_pallet"]
                .get<bool>();

        if (j_config["forklift_action"]["common_config"].contains("auto_height_calibration")) {
            option->forklift_option.fork_common_option.auto_height_calibration =
                j_config["forklift_action"]["common_config"]["auto_height_calibration"]
                    .get<bool>();
        } else {
          option->forklift_option.fork_common_option.auto_height_calibration =
              false;
        }

        if (j_config["forklift_action"]["common_config"].contains("tilt_standard_angle")) {
            option->forklift_option.fork_common_option.tilt_standard_angle =
                j_config["forklift_action"]["common_config"]["tilt_standard_angle"]
                    .get<double>();
        } else {
          option->forklift_option.fork_common_option.tilt_standard_angle =
              0.;
        }

        option->forklift_option.fork_common_option.kload_weighing_threshold =
            j_config["forklift_action"]["common_config"]["kload_weighing_threshold"]
                .get<double>();
      } else {
        option->forklift_option.fork_common_option.check_pallet = true;
        option->forklift_option.fork_common_option.auto_height_calibration = false;
        option->forklift_option.fork_common_option.kload_weighing_threshold = 10;
        option->forklift_option.fork_common_option.tilt_standard_angle = 0.;
      }
      LOG_INFO_STREAM("check_pallet: " << option->forklift_option
                                              .fork_common_option.check_pallet);
      LOG_INFO_STREAM("auto_height_calibration: "
                      << option->forklift_option.fork_common_option
                             .auto_height_calibration);
      LOG_INFO_STREAM("kload_weighing_threshold: "
                      << option->forklift_option.fork_common_option
                             .kload_weighing_threshold);
      LOG_INFO_STREAM("tilt_standard_angle: "
                      << option->forklift_option.fork_common_option
                             .tilt_standard_angle);

      if (j_config["forklift_action"].contains("height_fork_move")) {
        option->forklift_option.height_fork.kdist_output_limit =
            j_config["forklift_action"]["height_fork_move"]["kdist_output_limit"]
                .get<float>();

        option->forklift_option.height_fork.kdist_ramp_rate =
            j_config["forklift_action"]["height_fork_move"]["kdist_ramp_rate"]
                .get<float>();

        option->forklift_option.height_fork.kspeed_output_limit =
            j_config["forklift_action"]["height_fork_move"]["kspeed_output_limit"]
                .get<float>();

        option->forklift_option.height_fork.kspeed_ramp_rate =
            j_config["forklift_action"]["height_fork_move"]["kspeed_ramp_rate"]
                .get<float>();

        option->forklift_option.height_fork.kdist_p =
            j_config["forklift_action"]["height_fork_move"]["kdist_p"]
                .get<float>();

        option->forklift_option.height_fork.kdist_i =
            j_config["forklift_action"]["height_fork_move"]["kdist_i"]
                .get<float>();

        option->forklift_option.height_fork.kdist_d =
            j_config["forklift_action"]["height_fork_move"]["kdist_d"]
                .get<float>();

        option->forklift_option.height_fork.kup_speed_p =
            j_config["forklift_action"]["height_fork_move"]["kup_speed_p"]
                .get<float>();

        option->forklift_option.height_fork.kup_speed_i =
            j_config["forklift_action"]["height_fork_move"]["kup_speed_i"]
                .get<float>();

        option->forklift_option.height_fork.kup_speed_d =
            j_config["forklift_action"]["height_fork_move"]["kup_speed_d"]
                .get<float>();

        option->forklift_option.height_fork.kup_min_speed =
            j_config["forklift_action"]["height_fork_move"]["kup_min_speed"]
                .get<float>();

        if (j_config["forklift_action"]["height_fork_move"].contains("kup_max_speed_no_pallet")) {
          option->forklift_option.height_fork.kup_max_speed_no_pallet =
              j_config["forklift_action"]["height_fork_move"]["kup_max_speed_no_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.height_fork.kup_max_speed_no_pallet = 100;
        }

        if (j_config["forklift_action"]["height_fork_move"].contains("kup_max_speed_with_pallet")) {
          option->forklift_option.height_fork.kup_max_speed_with_pallet =
              j_config["forklift_action"]["height_fork_move"]["kup_max_speed_with_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.height_fork.kup_max_speed_with_pallet = 100;
        }

        option->forklift_option.height_fork.kdown_speed_p =
            j_config["forklift_action"]["height_fork_move"]["kdown_speed_p"]
                .get<float>();

        option->forklift_option.height_fork.kdown_speed_i =
            j_config["forklift_action"]["height_fork_move"]["kdown_speed_i"]
                .get<float>();

        option->forklift_option.height_fork.kdown_speed_d =
            j_config["forklift_action"]["height_fork_move"]["kdown_speed_d"]
                .get<float>();

        option->forklift_option.height_fork.kdown_min_speed =
            j_config["forklift_action"]["height_fork_move"]["kdown_min_speed"]
                .get<float>();

        if (j_config["forklift_action"]["height_fork_move"].contains("kdown_max_speed_no_pallet")) {
          option->forklift_option.height_fork.kdown_max_speed_no_pallet =
              j_config["forklift_action"]["height_fork_move"]["kdown_max_speed_no_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.height_fork.kdown_max_speed_no_pallet = 100;
        }

        if (j_config["forklift_action"]["height_fork_move"].contains("kdown_max_speed_with_pallet")) {
          option->forklift_option.height_fork.kdown_max_speed_with_pallet =
              j_config["forklift_action"]["height_fork_move"]["kdown_max_speed_with_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.height_fork.kdown_max_speed_with_pallet = 100;
        }

        option->forklift_option.height_fork.offset =
            j_config["forklift_action"]["height_fork_move"]["offset"]
                .get<float>();

        option->forklift_option.height_fork.kup_value =
            j_config["forklift_action"]["height_fork_move"]["kup_value"]
                .get<float>();

        option->forklift_option.height_fork.kdown_value =
            j_config["forklift_action"]["height_fork_move"]["kdown_value"]
                .get<float>();

        option->forklift_option.height_fork.kup_limit =
            j_config["forklift_action"]["height_fork_move"]["kup_limit"]
                .get<float>();

        option->forklift_option.height_fork.kdown_limit =
            j_config["forklift_action"]["height_fork_move"]["kdown_limit"]
                .get<float>();

      } else {
        option->forklift_option.height_fork.kdist_output_limit = 150;
        option->forklift_option.height_fork.kdist_ramp_rate = 150;
        option->forklift_option.height_fork.kspeed_output_limit = 150;
        option->forklift_option.height_fork.kspeed_ramp_rate = 150;
        option->forklift_option.height_fork.kdist_p = 0.00004;
        option->forklift_option.height_fork.kdist_i = 0;
        option->forklift_option.height_fork.kdist_d = 0;
        option->forklift_option.height_fork.kup_speed_p = 45;
        option->forklift_option.height_fork.kup_speed_i = 0.03;
        option->forklift_option.height_fork.kup_speed_d = 0;
        option->forklift_option.height_fork.kup_min_speed = 0;
        option->forklift_option.height_fork.kup_max_speed_no_pallet = 100;
        option->forklift_option.height_fork.kup_max_speed_with_pallet = 100;
        option->forklift_option.height_fork.kdown_speed_p = 30;
        option->forklift_option.height_fork.kdown_speed_i = 0.01;
        option->forklift_option.height_fork.kdown_speed_d = 0;
        option->forklift_option.height_fork.kdown_min_speed = 25;
        option->forklift_option.height_fork.kdown_max_speed_no_pallet = 100;
        option->forklift_option.height_fork.kdown_max_speed_with_pallet = 100;
        option->forklift_option.height_fork.offset = 100;
        option->forklift_option.height_fork.kup_value = 1000;
        option->forklift_option.height_fork.kdown_value = 1000;
        option->forklift_option.height_fork.kup_limit = 30000;
        option->forklift_option.height_fork.kdown_limit = 180;
      }
      LOG_INFO("--------- forklift_height_move ---------");
      LOG_INFO_STREAM(
          "kdist_output_limit: " << option->forklift_option.height_fork.kdist_output_limit);
      LOG_INFO_STREAM(
          "kdist_ramp_rate: " << option->forklift_option.height_fork.kdist_ramp_rate);
      LOG_INFO_STREAM(
          "kspeed_output_limit: " << option->forklift_option.height_fork.kspeed_output_limit);
      LOG_INFO_STREAM(
          "kspeed_ramp_rate: " << option->forklift_option.height_fork.kspeed_ramp_rate);

      LOG_INFO_STREAM(
          "kdist_p: " << option->forklift_option.height_fork.kdist_p);
      LOG_INFO_STREAM(
          "kdist_i: " << option->forklift_option.height_fork.kdist_i);
      LOG_INFO_STREAM(
          "kdist_d: " << option->forklift_option.height_fork.kdist_d);

      LOG_INFO_STREAM(
          "kup_speed_p: " << option->forklift_option.height_fork.kup_speed_p);
      LOG_INFO_STREAM(
          "kup_speed_i: " << option->forklift_option.height_fork.kup_speed_i);
      LOG_INFO_STREAM(
          "kup_speed_d: " << option->forklift_option.height_fork.kup_speed_d);
      LOG_INFO_STREAM("kup_min_speed: "
                      << option->forklift_option.height_fork.kup_min_speed);
      LOG_INFO_STREAM("kup_max_speed_no_pallet: "
                      << option->forklift_option.height_fork.kup_max_speed_no_pallet);
      LOG_INFO_STREAM("kup_max_speed_with_pallet: "
                      << option->forklift_option.height_fork.kup_max_speed_with_pallet);

      LOG_INFO_STREAM("kdown_speed_p: "
                      << option->forklift_option.height_fork.kdown_speed_p);
      LOG_INFO_STREAM("kdown_speed_i: "
                      << option->forklift_option.height_fork.kdown_speed_i);
      LOG_INFO_STREAM("kdown_speed_d: "
                      << option->forklift_option.height_fork.kdown_speed_d);
      LOG_INFO_STREAM("kdown_min_speed: "
                      << option->forklift_option.height_fork.kdown_min_speed);
      LOG_INFO_STREAM("kdown_max_speed_no_pallet: "
                      << option->forklift_option.height_fork.kdown_max_speed_no_pallet);
      LOG_INFO_STREAM("kdown_max_speed_with_pallet: "
                      << option->forklift_option.height_fork.kdown_max_speed_with_pallet);

      LOG_INFO_STREAM(
          "offset: " << option->forklift_option.height_fork.offset);
      LOG_INFO_STREAM(
          "kup_value: " << option->forklift_option.height_fork.kup_value);
      LOG_INFO_STREAM(
          "kdown_value: " << option->forklift_option.height_fork.kdown_value);
      LOG_INFO_STREAM(
          "kup_value: " << option->forklift_option.height_fork.kup_value);
      LOG_INFO_STREAM(
          "kup_limit: " << option->forklift_option.height_fork.kup_limit);
      LOG_INFO_STREAM(
          "kdown_limit: " << option->forklift_option.height_fork.kdown_limit);

      LOG_INFO("---------------------------------------------\n\n");

			if (j_config["forklift_action"].contains("lateral_fork_move")) {
        option->forklift_option.lateral_fork.kdist_output_limit =
            j_config["forklift_action"]["lateral_fork_move"]["kdist_output_limit"]
                .get<float>();

        option->forklift_option.lateral_fork.kdist_ramp_rate =
            j_config["forklift_action"]["lateral_fork_move"]["kdist_ramp_rate"]
                .get<float>();

        option->forklift_option.lateral_fork.kspeed_output_limit =
            j_config["forklift_action"]["lateral_fork_move"]["kspeed_output_limit"]
                .get<float>();

        option->forklift_option.lateral_fork.kspeed_ramp_rate =
            j_config["forklift_action"]["lateral_fork_move"]["kspeed_ramp_rate"]
                .get<float>();

        option->forklift_option.lateral_fork.kdist_p =
            j_config["forklift_action"]["lateral_fork_move"]["kdist_p"]
                .get<float>();

        option->forklift_option.lateral_fork.kdist_i =
            j_config["forklift_action"]["lateral_fork_move"]["kdist_i"]
                .get<float>();

        option->forklift_option.lateral_fork.kdist_d =
            j_config["forklift_action"]["lateral_fork_move"]["kdist_d"]
                .get<float>();

        option->forklift_option.lateral_fork.kup_speed_p =
            j_config["forklift_action"]["lateral_fork_move"]["kup_speed_p"]
                .get<float>();

        option->forklift_option.lateral_fork.kup_speed_i =
            j_config["forklift_action"]["lateral_fork_move"]["kup_speed_i"]
                .get<float>();

        option->forklift_option.lateral_fork.kup_speed_d =
            j_config["forklift_action"]["lateral_fork_move"]["kup_speed_d"]
                .get<float>();

        option->forklift_option.lateral_fork.kup_min_speed =
            j_config["forklift_action"]["lateral_fork_move"]["kup_min_speed"]
                .get<float>();

        if (j_config["forklift_action"]["lateral_fork_move"].contains("kup_max_speed_no_pallet")) {
          option->forklift_option.lateral_fork.kup_max_speed_no_pallet =
              j_config["forklift_action"]["lateral_fork_move"]["kup_max_speed_no_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.lateral_fork.kup_max_speed_no_pallet = 100;
        }

        if (j_config["forklift_action"]["lateral_fork_move"].contains("kup_max_speed_with_pallet")) {
          option->forklift_option.lateral_fork.kup_max_speed_with_pallet =
              j_config["forklift_action"]["lateral_fork_move"]["kup_max_speed_with_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.lateral_fork.kup_max_speed_with_pallet = 100;
        }

        option->forklift_option.lateral_fork.kdown_speed_p =
            j_config["forklift_action"]["lateral_fork_move"]["kdown_speed_p"]
                .get<float>();

        option->forklift_option.lateral_fork.kdown_speed_i =
            j_config["forklift_action"]["lateral_fork_move"]["kdown_speed_i"]
                .get<float>();

        option->forklift_option.lateral_fork.kdown_speed_d =
            j_config["forklift_action"]["lateral_fork_move"]["kdown_speed_d"]
                .get<float>();

        option->forklift_option.lateral_fork.kdown_min_speed =
            j_config["forklift_action"]["lateral_fork_move"]["kdown_min_speed"]
                .get<float>();

        if (j_config["forklift_action"]["lateral_fork_move"].contains("kdown_max_speed_no_pallet")) {
          option->forklift_option.lateral_fork.kdown_max_speed_no_pallet =
              j_config["forklift_action"]["lateral_fork_move"]["kdown_max_speed_no_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.lateral_fork.kdown_max_speed_no_pallet = 100;
        }

        if (j_config["forklift_action"]["lateral_fork_move"].contains("kdown_max_speed_with_pallet")) {
          option->forklift_option.lateral_fork.kdown_max_speed_with_pallet =
              j_config["forklift_action"]["lateral_fork_move"]["kdown_max_speed_with_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.lateral_fork.kdown_max_speed_with_pallet = 100;
        }

        option->forklift_option.lateral_fork.offset =
            j_config["forklift_action"]["lateral_fork_move"]["offset"]
                .get<float>();

        option->forklift_option.lateral_fork.kup_value =
            j_config["forklift_action"]["lateral_fork_move"]["kup_value"]
                .get<float>();

        option->forklift_option.lateral_fork.kdown_value =
            j_config["forklift_action"]["lateral_fork_move"]["kdown_value"]
                .get<float>();

        option->forklift_option.lateral_fork.kup_limit =
            j_config["forklift_action"]["lateral_fork_move"]["kup_limit"]
                .get<float>();

        option->forklift_option.lateral_fork.kdown_limit =
            j_config["forklift_action"]["lateral_fork_move"]["kdown_limit"]
                .get<float>();

      } else {
        option->forklift_option.lateral_fork.kdist_output_limit = 150;
        option->forklift_option.lateral_fork.kdist_ramp_rate = 150;
        option->forklift_option.lateral_fork.kspeed_output_limit = 150;
        option->forklift_option.lateral_fork.kspeed_ramp_rate = 150;
        option->forklift_option.lateral_fork.kdist_p = 0.00004;
        option->forklift_option.lateral_fork.kdist_i = 0;
        option->forklift_option.lateral_fork.kdist_d = 0;
        option->forklift_option.lateral_fork.kup_speed_p = 45;
        option->forklift_option.lateral_fork.kup_speed_i = 0.03;
        option->forklift_option.lateral_fork.kup_speed_d = 0;
        option->forklift_option.lateral_fork.kup_min_speed = 0;
        option->forklift_option.lateral_fork.kup_max_speed_no_pallet = 100;
        option->forklift_option.lateral_fork.kup_max_speed_with_pallet = 100;
        option->forklift_option.lateral_fork.kdown_speed_p = 30;
        option->forklift_option.lateral_fork.kdown_speed_i = 0.01;
        option->forklift_option.lateral_fork.kdown_speed_d = 0;
        option->forklift_option.lateral_fork.kdown_min_speed = 25;
        option->forklift_option.lateral_fork.kdown_max_speed_no_pallet = 100;
        option->forklift_option.lateral_fork.kdown_max_speed_with_pallet = 100;
        option->forklift_option.lateral_fork.offset = 100;
        option->forklift_option.lateral_fork.kup_value = 1000;
        option->forklift_option.lateral_fork.kdown_value = 1000;
        option->forklift_option.lateral_fork.kup_limit = 30000;
        option->forklift_option.lateral_fork.kdown_limit = 180;
      }
      LOG_INFO("--------- forklift_lateral_move ---------");
      LOG_INFO_STREAM(
          "kdist_output_limit: " << option->forklift_option.lateral_fork.kdist_output_limit);
      LOG_INFO_STREAM(
          "kdist_ramp_rate: " << option->forklift_option.lateral_fork.kdist_ramp_rate);
      LOG_INFO_STREAM(
          "kspeed_output_limit: " << option->forklift_option.lateral_fork.kspeed_output_limit);
      LOG_INFO_STREAM(
          "kspeed_ramp_rate: " << option->forklift_option.lateral_fork.kspeed_ramp_rate);

      LOG_INFO_STREAM(
          "kdist_p: " << option->forklift_option.lateral_fork.kdist_p);
      LOG_INFO_STREAM(
          "kdist_i: " << option->forklift_option.lateral_fork.kdist_i);
      LOG_INFO_STREAM(
          "kdist_d: " << option->forklift_option.lateral_fork.kdist_d);

      LOG_INFO_STREAM(
          "kup_speed_p: " << option->forklift_option.lateral_fork.kup_speed_p);
      LOG_INFO_STREAM(
          "kup_speed_i: " << option->forklift_option.lateral_fork.kup_speed_i);
      LOG_INFO_STREAM(
          "kup_speed_d: " << option->forklift_option.lateral_fork.kup_speed_d);
      LOG_INFO_STREAM("kup_min_speed: "
                      << option->forklift_option.lateral_fork.kup_min_speed);
      LOG_INFO_STREAM("kup_max_speed_no_pallet: "
                      << option->forklift_option.lateral_fork.kup_max_speed_no_pallet);
      LOG_INFO_STREAM("kup_max_speed_with_pallet: "
                      << option->forklift_option.lateral_fork.kup_max_speed_with_pallet);

      LOG_INFO_STREAM("kdown_speed_p: "
                      << option->forklift_option.lateral_fork.kdown_speed_p);
      LOG_INFO_STREAM("kdown_speed_i: "
                      << option->forklift_option.lateral_fork.kdown_speed_i);
      LOG_INFO_STREAM("kdown_speed_d: "
                      << option->forklift_option.lateral_fork.kdown_speed_d);
      LOG_INFO_STREAM("kdown_min_speed: "
                      << option->forklift_option.lateral_fork.kdown_min_speed);
      LOG_INFO_STREAM("kdown_max_speed_no_pallet: "
                      << option->forklift_option.lateral_fork.kdown_max_speed_no_pallet);
      LOG_INFO_STREAM("kdown_max_speed_with_pallet: "
                      << option->forklift_option.lateral_fork.kdown_max_speed_with_pallet);

      LOG_INFO_STREAM(
          "offset: " << option->forklift_option.lateral_fork.offset);
      LOG_INFO_STREAM(
          "kup_value: " << option->forklift_option.lateral_fork.kup_value);
      LOG_INFO_STREAM(
          "kdown_value: " << option->forklift_option.lateral_fork.kdown_value);
      LOG_INFO_STREAM(
          "kup_value: " << option->forklift_option.lateral_fork.kup_value);
      LOG_INFO_STREAM(
          "kup_limit: " << option->forklift_option.lateral_fork.kup_limit);
      LOG_INFO_STREAM(
          "kdown_limit: " << option->forklift_option.lateral_fork.kdown_limit);

      LOG_INFO("---------------------------------------------\n\n");

			if (j_config["forklift_action"].contains("sideshift_fork_move")) {
        option->forklift_option.sideshift_fork.kdist_output_limit =
            j_config["forklift_action"]["sideshift_fork_move"]["kdist_output_limit"]
                .get<float>();

        option->forklift_option.sideshift_fork.kdist_ramp_rate =
            j_config["forklift_action"]["sideshift_fork_move"]["kdist_ramp_rate"]
                .get<float>();

        option->forklift_option.sideshift_fork.kspeed_output_limit =
            j_config["forklift_action"]["sideshift_fork_move"]["kspeed_output_limit"]
                .get<float>();

        option->forklift_option.sideshift_fork.kspeed_ramp_rate =
            j_config["forklift_action"]["sideshift_fork_move"]["kspeed_ramp_rate"]
                .get<float>();

        option->forklift_option.sideshift_fork.kdist_p =
            j_config["forklift_action"]["sideshift_fork_move"]["kdist_p"]
                .get<float>();

        option->forklift_option.sideshift_fork.kdist_i =
            j_config["forklift_action"]["sideshift_fork_move"]["kdist_i"]
                .get<float>();

        option->forklift_option.sideshift_fork.kdist_d =
            j_config["forklift_action"]["sideshift_fork_move"]["kdist_d"]
                .get<float>();

        option->forklift_option.sideshift_fork.kspeed_p =
            j_config["forklift_action"]["sideshift_fork_move"]["kspeed_p"]
                .get<float>();

        option->forklift_option.sideshift_fork.kspeed_i =
            j_config["forklift_action"]["sideshift_fork_move"]["kspeed_i"]
                .get<float>();

        option->forklift_option.sideshift_fork.kspeed_d =
            j_config["forklift_action"]["sideshift_fork_move"]["kspeed_d"]
                .get<float>();

        option->forklift_option.sideshift_fork.kmin_speed =
            j_config["forklift_action"]["sideshift_fork_move"]["kmin_speed"]
                .get<float>();

				if (j_config["forklift_action"]["sideshift_fork_move"].contains("kmax_speed_no_pallet")) {
          option->forklift_option.sideshift_fork.kmax_speed_no_pallet =
              j_config["forklift_action"]["sideshift_fork_move"]["kmax_speed_no_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.sideshift_fork.kmax_speed_no_pallet = 100;
        }

				if (j_config["forklift_action"]["sideshift_fork_move"].contains("kmax_speed_with_pallet")) {
          option->forklift_option.sideshift_fork.kmax_speed_with_pallet =
              j_config["forklift_action"]["sideshift_fork_move"]["kmax_speed_with_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.sideshift_fork.kmax_speed_with_pallet = 100;
        }

        option->forklift_option.sideshift_fork.offset =
            j_config["forklift_action"]["sideshift_fork_move"]["offset"]
                .get<float>();

        option->forklift_option.sideshift_fork.limit =
            j_config["forklift_action"]["sideshift_fork_move"]["limit"]
                .get<float>();
      } else {
        option->forklift_option.sideshift_fork.kdist_output_limit = 150;
        option->forklift_option.sideshift_fork.kdist_ramp_rate = 150;
        option->forklift_option.sideshift_fork.kspeed_output_limit = 150;
        option->forklift_option.sideshift_fork.kspeed_ramp_rate = 150;
        option->forklift_option.sideshift_fork.kdist_p = 0.00004;
        option->forklift_option.sideshift_fork.kdist_i = 0;
        option->forklift_option.sideshift_fork.kdist_d = 0;
        option->forklift_option.sideshift_fork.kspeed_p = 45;
        option->forklift_option.sideshift_fork.kspeed_i = 0.03;
        option->forklift_option.sideshift_fork.kspeed_d = 0;
        option->forklift_option.sideshift_fork.kmin_speed = 0;
        option->forklift_option.sideshift_fork.kmax_speed_no_pallet = 100;
        option->forklift_option.sideshift_fork.kmax_speed_with_pallet = 100;

        option->forklift_option.sideshift_fork.offset = 100;
        option->forklift_option.sideshift_fork.limit = 180;
      }
      LOG_INFO("--------- forklift_sideshift_move ---------");
      LOG_INFO_STREAM(
          "kdist_output_limit: " << option->forklift_option.sideshift_fork.kdist_output_limit);
      LOG_INFO_STREAM(
          "kdist_ramp_rate: " << option->forklift_option.sideshift_fork.kdist_ramp_rate);
      LOG_INFO_STREAM(
          "kspeed_output_limit: " << option->forklift_option.sideshift_fork.kspeed_output_limit);
      LOG_INFO_STREAM(
          "kspeed_ramp_rate: " << option->forklift_option.sideshift_fork.kspeed_ramp_rate);

      LOG_INFO_STREAM(
          "kdist_p: " << option->forklift_option.sideshift_fork.kdist_p);
      LOG_INFO_STREAM(
          "kdist_i: " << option->forklift_option.sideshift_fork.kdist_i);
      LOG_INFO_STREAM(
          "kdist_d: " << option->forklift_option.sideshift_fork.kdist_d);

      LOG_INFO_STREAM("kspeed_p: "
                      << option->forklift_option.sideshift_fork.kspeed_p);
      LOG_INFO_STREAM("kspeed_i: "
                      << option->forklift_option.sideshift_fork.kspeed_i);
      LOG_INFO_STREAM("kspeed_d: "
                      << option->forklift_option.sideshift_fork.kspeed_d);
      LOG_INFO_STREAM("kmin_speed: "
                      << option->forklift_option.sideshift_fork.kmin_speed);
      LOG_INFO_STREAM("kmax_speed_no_pallet: "
                      << option->forklift_option.sideshift_fork.kmax_speed_no_pallet);
      LOG_INFO_STREAM("kmax_speed_with_pallet: "
                      << option->forklift_option.sideshift_fork.kmax_speed_with_pallet);

      LOG_INFO_STREAM("offset: " << option->forklift_option.sideshift_fork.offset);
      LOG_INFO_STREAM("limit: " << option->forklift_option.sideshift_fork.limit);

      LOG_INFO("---------------------------------------------\n\n");

      if (j_config["forklift_action"].contains("tilt_fork_move")) {
        option->forklift_option.tilt_fork.kdist_output_limit =
            j_config["forklift_action"]["tilt_fork_move"]["kdist_output_limit"]
                .get<float>();

        option->forklift_option.tilt_fork.kdist_ramp_rate =
            j_config["forklift_action"]["tilt_fork_move"]["kdist_ramp_rate"]
                .get<float>();

        option->forklift_option.tilt_fork.kspeed_output_limit =
            j_config["forklift_action"]["tilt_fork_move"]["kspeed_output_limit"]
                .get<float>();

        option->forklift_option.tilt_fork.kspeed_ramp_rate =
            j_config["forklift_action"]["tilt_fork_move"]["kspeed_ramp_rate"]
                .get<float>();

        option->forklift_option.tilt_fork.kdist_p =
            j_config["forklift_action"]["tilt_fork_move"]["kdist_p"]
                .get<float>();

        option->forklift_option.tilt_fork.kdist_i =
            j_config["forklift_action"]["tilt_fork_move"]["kdist_i"]
                .get<float>();

        option->forklift_option.tilt_fork.kdist_d =
            j_config["forklift_action"]["tilt_fork_move"]["kdist_d"]
                .get<float>();

        option->forklift_option.tilt_fork.kspeed_p =
            j_config["forklift_action"]["tilt_fork_move"]["kspeed_p"]
                .get<float>();

        option->forklift_option.tilt_fork.kspeed_i =
            j_config["forklift_action"]["tilt_fork_move"]["kspeed_i"]
                .get<float>();

        option->forklift_option.tilt_fork.kspeed_d =
            j_config["forklift_action"]["tilt_fork_move"]["kspeed_d"]
                .get<float>();

        option->forklift_option.tilt_fork.kmin_speed =
            j_config["forklift_action"]["tilt_fork_move"]["kmin_speed"]
                .get<float>();

				if (j_config["forklift_action"]["tilt_fork_move"].contains("kmax_speed_no_pallet")) {
          option->forklift_option.tilt_fork.kmax_speed_no_pallet =
              j_config["forklift_action"]["tilt_fork_move"]["kmax_speed_no_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.tilt_fork.kmax_speed_no_pallet = 100;
        }

				if (j_config["forklift_action"]["tilt_fork_move"].contains("kmax_speed_with_pallet")) {
          option->forklift_option.tilt_fork.kmax_speed_with_pallet =
              j_config["forklift_action"]["tilt_fork_move"]["kmax_speed_with_pallet"]
                  .get<float>();
        } else {
          option->forklift_option.tilt_fork.kmax_speed_with_pallet = 100;
        }

        option->forklift_option.tilt_fork.offset =
            j_config["forklift_action"]["tilt_fork_move"]["offset"]
                .get<float>();
      } else {
        option->forklift_option.tilt_fork.kdist_output_limit = 150;
        option->forklift_option.tilt_fork.kdist_ramp_rate = 150;
        option->forklift_option.tilt_fork.kspeed_output_limit = 150;
        option->forklift_option.tilt_fork.kspeed_ramp_rate = 150;
        option->forklift_option.tilt_fork.kdist_p = 0.00004;
        option->forklift_option.tilt_fork.kdist_i = 0;
        option->forklift_option.tilt_fork.kdist_d = 0;
        option->forklift_option.tilt_fork.kspeed_p = 45;
        option->forklift_option.tilt_fork.kspeed_i = 0.03;
        option->forklift_option.tilt_fork.kspeed_d = 0;
        option->forklift_option.tilt_fork.kmin_speed = 0;
        option->forklift_option.tilt_fork.kmax_speed_no_pallet = 100;
        option->forklift_option.tilt_fork.kmax_speed_with_pallet = 100;

        option->forklift_option.tilt_fork.offset = 100;
      }
      LOG_INFO("--------- forklift_tilt_move ---------");

      LOG_INFO_STREAM(
          "kdist_output_limit: " << option->forklift_option.tilt_fork.kdist_output_limit);
      LOG_INFO_STREAM(
          "kdist_ramp_rate: " << option->forklift_option.tilt_fork.kdist_ramp_rate);
      LOG_INFO_STREAM(
          "kspeed_output_limit: " << option->forklift_option.tilt_fork.kspeed_output_limit);
      LOG_INFO_STREAM(
          "kspeed_ramp_rate: " << option->forklift_option.tilt_fork.kspeed_ramp_rate);

      LOG_INFO_STREAM(
          "kdist_p: " << option->forklift_option.tilt_fork.kdist_p);
      LOG_INFO_STREAM(
          "kdist_i: " << option->forklift_option.tilt_fork.kdist_i);
      LOG_INFO_STREAM(
          "kdist_d: " << option->forklift_option.tilt_fork.kdist_d);

      LOG_INFO_STREAM("kspeed_p: "
                      << option->forklift_option.tilt_fork.kspeed_p);
      LOG_INFO_STREAM("kspeed_i: "
                      << option->forklift_option.tilt_fork.kspeed_i);
      LOG_INFO_STREAM("kspeed_d: "
                      << option->forklift_option.tilt_fork.kspeed_d);
      LOG_INFO_STREAM("kmin_speed: "
                      << option->forklift_option.tilt_fork.kmin_speed);
      LOG_INFO_STREAM("kmax_speed_no_pallet: "
                      << option->forklift_option.tilt_fork.kmax_speed_no_pallet);
      LOG_INFO_STREAM("kmax_speed_with_pallet: "
                      << option->forklift_option.tilt_fork.kmax_speed_with_pallet);

      LOG_INFO_STREAM("offset: " << option->forklift_option.tilt_fork.offset);

      LOG_INFO("---------------------------------------------\n\n");
      LOG_INFO("Option config refresh finish!!");
      LOG_INFO("---------------------------------------------\n\n");
    }
  } catch (const std::exception& e) {
    LOG_WARN_STREAM(e.what());
    return false;
  }

  return true;
}
