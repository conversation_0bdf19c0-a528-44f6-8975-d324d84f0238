# 节点名:cotek_action
- 节点说明:
  执行decision_maker发送的目标动作，将控制数据发送至embedded节点
## 功能列表
|       | 功能         | 功能描述         |
| ----- | ------------ | ---------------- |
| 1.    | 低功耗       |                  |
| 2.    | 上升         | 托盘/插腿上升    |
| 3.    | 下降         | 托盘/插腿下降    |
| 4.    | 堆高         | 堆高车举升       |
| 5.    | 电机报闸     | 叉车电机抱闸     |
| ６.   | 柯蒂斯复位   | 柯蒂斯复位继电器 |
| ７.   | 自动充电     | 自动充电继电器   |
| ８.   | 托盘对地静止 | 车动货不动       |
| ９.   | 托盘旋转     | 托盘旋转         |
| １０. | 电机失能     | 电机断电         |
| １１. | 电机使能     | 电机上电         |
| １２. | 托盘找零     | 零点复位         |

## 相关硬件
- 叉车插腿 - 顶升车托盘 - 三色灯 - 音频播放器
### 节点依赖
--> cotek_embedded node
--> diagnostic_node

--< decision_maker node
--< embedded_node
### 通信依赖
#### 话题:
- 发布:
    /forklift_action　      (消息内容:动作类型、动作值、音频 消息文件:forklift_action.msg)
    /jack_up_action　　     (消息内容:动作类型、动作值、LED、音频、电机使/失能 消息文件:jack_up_action.msg)
    /node_action           (消息内容:错误码+时间戳 消息文件:node_diagnostic.msg)
- 订阅:
    /battery_baoe_monitor　(消息内容:电池电量、电压、电流 消息文件:battery_baoe_feedback.msg)
    /pgv100_up             (消息内容:tag 消息文件:pgv100_feedback.msg)
    /move_cmd              (消息内容:v、w 消息文件:move_cmd.msg)
    /syntron_rotate_motor  (消息内容:转速 消息文件:motor_syntro_feedback.msg)
    /syntron_lift_motor    (消息内容:转速 消息文件:motor_syntro_feedback.msg)
    /jack_up_io_state      (消息内容:顶升车升降IO状态码 消息文件:jack_up_io_state.msg)
    /pallet_fork_io_state  (消息内容:托盘车io状态与托盘状态码 消息文件:pallet_fork_io_state.msg)
    /led_control           (消息内容:LED类型 消息文件:led_control.msg)
    /audio_control         (消息内容:音频类型/音量 消息文件:audio_control.msg)
    /bmmsk34_encoder       (消息内容:拉线长度 消息文件:bmmsk34_encoder.msg)
#### 动作:
actionserver
```cpp
uint32 action_type      // goal
float64 action_value
---
bool is_finish          // result
---
uint32 percent          // feedback message
```
## 包结构
```cpp
--- cotek_action
 |--config
 |--include
 |  |--cotek_action
 |  |  |- action_config.h          // ActionConfig类定义 解析参数配置
 |  |  |- action_interface.h       // ActionInterface接口类定义
 |  |  |- action_server.h          // ActionServer类定义 处理动作
 |  |  |- data_center.h            // AgvData类定义 数据存储
 |  |  |- forklift_action_case.h   // ForkliftAction类定义 托盘车动作类
 |  |  |- jackup_action_case.h     // JackupAction类定义 顶升车动作类
 |  |  |- transplant_action_case.h // TranspalntAction类定义 移载车动作类
 |  |  |- type_enum.h              // 枚举数据
 |  └--node
 |    └- cotek_action_node.h       // CotekActionNode类定义
 |--launch 
 |  └- cotek_action.launch         
 |--src
 |  |- action_config.cc            // 配置文件解析
 |  |- action_server.cc            // 动作服务器，执行动作，发送反馈
 |  |- cotek_action_node.cc        
 |  |- forklift_action_case.cc     // 托盘车动作
 |  └- jackup_action_case.cc       // 顶升车动作
 |--CMakeList.txt
 └--package.xml

```
## 参数配置

```cpp
enable_local_debug: 0                 // 使能日志打印
enable_timeout_err_alarm: false       // 使能动作超时报警
config_filename:  ""                  // 配置文件路径
agvtype_option: 1                     // agv 类型
battery type：1                       //（curtis=1 baoe=2 zhilibattery=3）电池类型
control_period: 30                     // ms控制周期

<--充电相关--> 
charge_threshold_voltage: 800         // mA　充电电流阈值
charge_threshold_current: 54000       // mv充电电压阈值
charge_overout_time : 5               // hour　充电超时时间
charged_wait_time: 1                  // min充电测试时间

<--托盘车控制参数-->   
curtis_reset_delaytime: 5             // s  curtis复位延时时间
forkpallet_up_time: 10　　             // 抬升时间
forkpallet_down_time: 10              // 下降时间
pallet_err_tolerant: 3　　             // 誤差限
dec_mode: 6　　　　　　　　　            // 減速模式
low_speed: 20　　　　　　　　            // 低速限
margin: 60                            // 安全高度cm
deceleration: 100                     // 减速-加速度m/s2
reg_a: 0                              // regression equation slope 回归斜率
reg_b: 1                              // regression coefficient 回归系数

<--顶升车控制参数-->    
pallet_up_v: -2800                    // rad/min 托盘上升速度
pallet_down_v: 2800                   // rad/min 托盘下降速度
pallet_w: 180                         // rad/min 托盘旋转速度
pallet_height: 0.05                   // m 托盘上升高度
Jpallet_UP_overout_t: 10              // s 托盘上升超时时间
Jpallet_DOWN_overout_t: 10            // s 托盘下降超时时间
Jpallet_ROTATE_overout_t: 30          // s 托盘旋转超时时间 
clear_alarm_delaytime: 5              // s 清除错误延时时间 
pallet_angle_deviation: 0.3           // (degree) 托盘旋转角度阈值
pallet_max_w: 4.5                     // 托盘最大转速
pallet_w_kp: 1.2                      // 托盘旋转比例参数
pallet_w_min_acc: 0.024               // 托盘旋转最小加速度
pallet_w_max_acc: 1.8                 // 0.6 * 3.14托盘旋转最大加速度
pallet_lifting_ratio: 280.0           // 顶升减速比
pallet_rotate_ratio: 64.0             // 旋转减速比

* PID 控制参数
pallet_nomove_kp: 0.8                // PID 比例系数
pallet_nomove_ki: 0.0                // PID 积分系数
pallet_nomove_kd: 0.0                // PID 微分系数
```
### 源码说明
* CotekActionNode类
1. NodeRun(): 启动程序循环运行线程
2. Init()： 初始化消息订阅回调函数

* ActionInterface类 不同车型动作基类
1. UpdateData():更新数据
2. FeedBack():返回动作完成百分比
3. IsFinished():返回动作完成状态
4. ExecuteAction():动作执行
5. PublishActionMsg():发布动作执行控制量

* ForkliftAction类 托盘车动作
* JackupAction类   顶升车动作
1. ActionInit()：初始化动作列表
2. ExecuteAction(): 根据输入目标动作查询动作列表，并执行相应动作
3. PublishActionMsg(): 发布动作计算过程控制量及结果到嵌入式节点

###### 动作列表
```cpp
> 托盘车动作
enum class Forklift_Action_Type {
  WAITING = 0,
  REST = 1,
  PALLET_FORK_UP = 2,                 // 托盘车抬货  (operation_value 默认填0)
  PALLET_FORK_DOWN = 3,               // 托盘车放货  (operation_value 默认填0)
  HEAP_FORK_MOVE = 4,                 // 堆高车叉腿移动  (operation_value 填 控制高度)
  BRAKE = 5,                          // 电机报闸  (operation_value 填0(关), 1(开))
  BEEP = 6,                           // 喇叭  (operation_value 填0(关), 1(开))
  CURTIS_RESET = 7,                   // 柯蒂斯复位继电器打开  (operation_value 填0(关), 1(开))
  CHARGE = 8,                         // 自动充电 (operation_value 填0(关), 1(开))
  LOW_POWER_MODE = 9                  // 低功耗模式
};

> 顶升车动作
enum class JackupActionType {
  ACTION_NONE = 0,                    
  LOW_POWER_MODE = 1,                 // 低功耗模式
  PALLET_UP = 2,                      // 托盘上升
  PALLET_DOWN = 3,                    // 托盘下降
  PALLET_ROTATION = 4,                // 托盘旋转
  PALLET_ZERO = 5,                    // 托盘找零
  MOTOR_DISABLE = 6,                  // 电机失能
  MOTOR_ENABLE = 7,                   // 电机使能
  AGV_CHARGE = 8,                     // 充电
  MOTOR_CLEARALARM = 9,               // 电机清除报警
  ACTION_WAITING = 10,                // 等待
  PALET_NOMOVE = 11                   // 车动货不动
};

> 错误码
enum class ActionFault {
  AGV_NO_ERROR = 0,                   
  ERROR_WRONG_AGV_TYPE = 1,           // 车型错误
  ERROR_WRONG_ACTION_TYPE = 2,        // 动作类型错误
  ERROR_BAD_PARAMETER = 3,            // 参数错误
  ERROR_PALLET_UP = 4,                // 托盘上升错误-超时
  ERROR_PALLET_DOWN = 5,              // 托盘下降错误-超时
  ERROR_PALLET_ROTATE = 6,            // 托盘旋转错误-超时
  ERROR_CHARGE = 7,                   // 充电错误
  ERROR_CHARGE_TIMEOUT = 8,           // 充电超时错误
  ERROR_ACTION_NODE_TIMEOUT = 9,      // 节点超时
  ERROR_PALLET_ZERO = 10,             // 托盘找零错误-超时
  ERROR_PALLET_MOVE = 11,             // 堆高车举升错误-超时
  EXCEPTION_NO_PALLET = 12,           // 无托盘异常
  ERROR_BREAK = 13,                   // 抱闸错误-超时
};
注：移载车暂时未开发..
```





