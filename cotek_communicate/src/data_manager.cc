/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate/data_manager.h"

#include <Poco/Net/NetworkInterface.h>
#include <Poco/Path.h>
#include <tf/transform_listener.h>

#include <chrono>
#include <cstdint>
#include <ctime>
#include <exception>
#include <memory>
#include <ostream>
#include <stdexcept>
#include <string>
#include <vector>

#include "cotek_common/common.h"
#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/io_index.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/math.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_vda5050.h"
#include "cotek_msgs/action.h"
#include "cotek_msgs/action_cmd.h"
#include "cotek_msgs/action_command.h"
#include "cotek_msgs/area.h"
#include "cotek_msgs/battery_feedback.h"
#include "cotek_msgs/battery_state.h"
#include "cotek_msgs/control_point.h"
#include "cotek_msgs/debug.h"
#include "cotek_msgs/edge.h"
#include "cotek_msgs/fault_report.h"
#include "cotek_msgs/instant_action.h"
#include "cotek_msgs/io_feedback.h"
#include "cotek_msgs/motor_feedback.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/node.h"
#include "cotek_msgs/node_position.h"
#include "cotek_msgs/order.h"
#include "cotek_msgs/query_info.h"
#include "cotek_msgs/safety_state.h"
#include "cotek_msgs/self_check.h"
#include "cotek_msgs/std_cmd.h"
#include "cotek_msgs/task_cal.h"
#include "cotek_msgs/task_cmd.h"
#include "cotek_msgs/task_order.h"
#include "cotek_msgs/task_update.h"
#include "cotek_msgs/edge_update.h"
#include "cotek_msgs/node_update.h"
#include "cotek_msgs/trajectory.h"
#include "cotek_msgs/update_basic_config.h"
#include "cotek_msgs/update_config.h"
#include "cotek_msgs/velocity.h"
#include "cotek_msgs/wire_encoder_feedback.h"
#include "eigen3/Eigen/Dense"
#include "ros/duration.h"
#include "ros/forwards.h"
#include "ros/node_handle.h"
#include "sensor_msgs/LaserScan.h"
#include "std_msgs/Int32.h"
#include "std_msgs/String.h"

namespace cotek_communicate {

static std::vector<std::string> kImages{"start",   "end",   "load",    "unload",
                                        "parking", "robot", "location", 
                                        "calldt",  "takedt",  "leavedt",
                                        "closedoor",  "opendoor", "lieunload",
                                        "charge",  "confirm", "kuwei"};

static std::string EncoderTranslate(const std::string &frame) {
  std::string name;
  if (frame == cotek_topic::kHeightEncoderTopic) {
    name = "*(高度拉线编码器/heightEncoder)";
  }
  if (frame == cotek_topic::kLateralEncoderTopic) {
    name = "*(前移拉线编码器/lateralEncoder)";
  }
  if (frame == cotek_topic::kSideEncoderTopic) {
    name = "*(侧移拉线编码器/sideEncoder)";
  }
  return name;
}

static std::string intToHex(int num) {
  std::stringstream ss;
  ss << std::uppercase << std::hex << num;
  return ss.str();
}

static void transform(const cotek_msgs::task_info &info,
                      common::task_info_t &task_info) {
  task_info.current_odom_time = info.current_odom_time;
  task_info.create_odom_time = info.create_odom_time;
  task_info.current_odom = info.current_odom;
  task_info.total_odom = info.total_odom;
  task_info.total_duration = info.total_duration;
  task_info.avoid_duration = info.avoid_duration;
  task_info.current_task_count = info.current_task_count;
  task_info.total_task_count = info.total_task_count;
  task_info.error_task_count = info.error_task_count;
}

DataManager::DataManager()
    : resource_agent_ptr_(std::make_shared<ResourceAgent>()) {}

bool DataManager::Init(const CommunicateOption &option) {
  option_ = option;
  bool ret = true;
  SetJson("learningState", Json());
  SetJson(cotek_topic::kAgvPositionTopic, Json());
  ret &= resource_agent_ptr_->Init(option);
  ret &= system_info_.Init(option.server_ip);
  ret &= RosInit();
  ret &= RelocationInit();
  ret &= ImagesInit();
  ret &= FloorInit();

  Json json;
  json["system_version"] = option_.system_version;
  SetJson("system_version", json);

  return ret;
}

bool DataManager::UpdateOption(const CommunicateOption &option) {
  option_ = option;
  return system_info_.Update(option_.server_ip);
}

bool DataManager::CreateSyncAmr(const AmrSyncInfo &info) {
  if (amr_sync_manager_ptr_) {
    return amr_sync_manager_ptr_->CreateSyncAmr(info);
  }
  return false;
}

bool DataManager::RelocationInit() {
  try {
    relocations_.clear();
    std::string file_path = BasicConfigHelper::Instance().GetTargetConfigPath(
        cotek_config::ConfigType::RELOCATION_LIST);

    nlohmann::ordered_json json =
        nlohmann::ordered_json::parse(std::ifstream(file_path));
    if (json.contains("reLocationList")) {
      auto relocations = json["reLocationList"];
      for (auto iter = relocations.begin(); iter != relocations.end(); iter++) {
        relocation_t data;
        data.id = iter->at("id").get<std::string>();
        data.name = iter->at("name").get<std::string>();
        data.x = iter->at("x").get<double>();
        data.y = iter->at("y").get<double>();
        data.angle = iter->at("yaw").get<double>();
        data.map_id = iter->at("mapId").get<std::string>();
        data.zone_id = iter->at("zoneSetid").get<std::string>();
        data.is_default = false;
        relocations_[data.id] = data;
      }
    }
    if (json.contains("default")) {
      auto relocations = json["default"];
      for (auto iter = relocations.begin(); iter != relocations.end(); iter++) {
        relocation_t data;
        data.id = iter->at("id").get<std::string>();
        data.name = iter->at("name").get<std::string>();
        data.x = iter->at("x").get<double>();
        data.y = iter->at("y").get<double>();
        data.angle = iter->at("yaw").get<double>();
        data.map_id = iter->at("mapId").get<std::string>();
        data.zone_id = iter->at("zoneSetid").get<std::string>();
        data.is_default = true;
        relocations_[data.id] = data;
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR_STREAM("load relocation config exeption: " << e.what());
    // return false;
  }
  return true;
}

bool DataManager::FloorInit() {
  try {
    floors_.clear();
    std::string file_path = BasicConfigHelper::Instance().GetTargetConfigPath(
        cotek_config::ConfigType::FLOOR_LIST);

    nlohmann::ordered_json json =
        nlohmann::ordered_json::parse(std::ifstream(file_path));
    if (json.contains("list")) {
      auto floors = json["list"];
      for (auto iter = floors.begin(); iter != floors.end(); iter++) {
        Floor floor;
        floor.id = iter->at("id").get<std::string>();
        floor.zone_id = iter->at("zone_id").get<std::string>();
        floor.map_id = iter->at("map_id").get<std::string>();
        floor.floor = iter->at("floor").get<std::string>();
        floors_[floor.id] = floor;
      }
    }
  } catch (std::exception &e) {
    LOG_ERROR_STREAM("load floor config exeption: " << e.what());
    // return false;
  }
  return true;
}

bool DataManager::ImagesInit() {
  std::string file_dir =
      BasicConfigHelper::Instance().GetConfigPath() + "/images";
  for (auto &image : kImages) {
    std::string path = file_dir + "/" + image + ".png";
    try {
      std::ifstream image_file(path, std::ios::binary);
      if (!image_file) {
        LOG_ERROR_STREAM("load image file failed: " << path.c_str());
      }
      std::vector<unsigned char> image_data(
          (std::istreambuf_iterator<char>(image_file)),
          std::istreambuf_iterator<char>());
      images_[image] = image_data;
    } catch (std::exception &e) {
      LOG_ERROR_STREAM("load image exeption: " << e.what());
    }
  }
  return true;
}

bool DataManager::RosInit() {
  ros::NodeHandle nh;

  /*---------状态类api---------*/

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::agv_position>(
      cotek_topic::kAgvPositionTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::AgvPositionCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::move_feedback>(
      cotek_topic::kMoveFeedbackTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::VelocityCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::move_cmd>(
      cotek_topic::kMoveCmdTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::CmdVelocityCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::safety_states>(
      cotek_topic::kSafetyStateTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::SafetyStateCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::fault_report>(
      cotek_topic::kFaultReportTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::FaultReportCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::battery_feedback>(
      cotek_topic::kBatteryStateTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::BatteryCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<std_msgs::Int32>(
      cotek_topic::kLimitSwitchStateTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::LimitSwitchCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::loads>(
      cotek_topic::kLoadsTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::LoadsCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::path_state>(
      "path_state", kTopicReciveCacheSize,
      boost::bind(&DataManager::PathStateCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::odom_info>(
      cotek_topic::kGlobalOdomInfoTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::OdomInfoCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::self_check>(
      cotek_topic::kSelfCheckTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::SelfCheckCallback, this, _1)));

  // TODO(@ssh) 剩余叉齿状态类api

  /*---------传感器api---------*/

  sub_vec_.emplace_back(nh.subscribe<nav_msgs::Odometry>(
      cotek_topic::kOdomTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::OdomCallback, this, _1)));

  std::string motor = "motor";
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<cotek_msgs::motor_feedback>(
        motor + std::to_string(i), kTopicReciveCacheSize,
        boost::bind(&DataManager::MotorCallback, this, _1)));
  }

  std::string io = "io";
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<cotek_msgs::io_feedback>(
        io + std::to_string(i), kTopicReciveCacheSize,
        boost::bind(&DataManager::IoCallback, this, _1)));
  }

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::wire_encoder_feedback>(
      cotek_topic::kHeightEncoderTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::EncoderCallback, this, _1)));

  // TODO(@ssh) 剩余其他传感器类api

  /*---------匹配点云类api---------*/
  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::points_2d>(
      cotek_topic::kScanMatchedPointsTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::ScanMatchPointsCallback, this, _1)));
  match_points_map_[cotek_topic::kScanMatchedPointsTopic] =
      std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::points_2d>(
      cotek_topic::kReflectorMapPointsTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::ReflectorMapPointsCallback, this, _1)));
  match_points_map_[cotek_topic::kReflectorMapPointsTopic] =
      std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::points_2d>(
      cotek_topic::kMatchedLandmarkPointsTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::ReflectorMatchPointsCallback, this, _1)));
  match_points_map_[cotek_topic::kMatchedLandmarkPointsTopic] =
      std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::points_2d>(
      cotek_topic::kLostCheckMapPointsTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::LostCheckMapPointsCallback, this, _1)));
  match_points_map_[cotek_topic::kLostCheckMapPointsTopic] =
      std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::points_2d>(
      cotek_topic::kMatchedLostCheckPointsTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::LostCheckMatchPointsCallback, this, _1)));
  match_points_map_[cotek_topic::kMatchedLostCheckPointsTopic] =
      std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();

  sub_vec_.emplace_back(nh.subscribe<sensor_msgs::PointCloud2>(
      cotek_topic::kMatchedPointCloudTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::MatchPointsCloudCallback, this, _1)));
  match_3d_points_map_[cotek_topic::kMatchedPointCloudTopic] =
      std::make_unique<TimedDataQueue<sensor_msgs::PointCloud2>>();

  /*---------点云类api---------*/
  // 避障激光
  std::string avoid_laser = "avoidLaser";
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<sensor_msgs::LaserScan>(
        avoid_laser + std::to_string(i), kTopicReciveCacheSize,
        boost::bind(&DataManager::LaserPointsCallback, this, _1)));
  }

  // 导航激光
  std::string navi_laser = "naviLaser";
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<sensor_msgs::LaserScan>(
        navi_laser + std::to_string(i), kTopicReciveCacheSize,
        boost::bind(&DataManager::LaserPointsCallback, this, _1)));
  }

  // 3d 点云
  std::string navi_3d_laser = "navi3dLaser";
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<sensor_msgs::PointCloud2>(
        navi_3d_laser + std::to_string(i), kTopicReciveCacheSize,
        boost::bind(&DataManager::Laser3dPointsCallback, this, _1)));
  }

  /*---------stream类api---------*/

  // slam实时地图
  sub_vec_.emplace_back(nh.subscribe<nav_msgs::OccupancyGrid>(
      cotek_topic::kMapWebTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::MapCallback, this, _1)));
  image_strem_map_[cotek_topic::kMapWebTopic] =
      std::make_shared<MapStream>(cotek_topic::kMapWebTopic);

  sub_vec_.emplace_back(nh.subscribe<sensor_msgs::PointCloud2>(
      cotek_topic::kMap3dFrontTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::Map3dCallback, this, _1)));
  map3d_points_map_[cotek_topic::kMap3dFrontTopic] =
      std::make_unique<TimedDataQueue<sensor_msgs::PointCloud2>>();

  // area区域
  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::area>(
      cotek_topic::kStrongAreaInfoTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::WeightAreaCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::area>(
      cotek_topic::kHighDynamicAreaInfoTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::HighAreaCallback, this, _1)));

  // 原始相机图片
  std::string orign_image = "orignCameraImage";
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<sensor_msgs::CompressedImage>(
        orign_image + std::to_string(i) + "/compressed", kTopicReciveCacheSize,
        boost::bind(&DataManager::ImageCallback, this, _1)));

    image_strem_map_[orign_image + std::to_string(i)] =
        std::make_shared<RosCompressedStream>(orign_image + std::to_string(i));
  }

  std::string avoid_image = "avoidCameraImage";
  // 相机避障处理结果图片
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<sensor_msgs::CompressedImage>(
        avoid_image + std::to_string(i) + "/compressed", kTopicReciveCacheSize,
        boost::bind(&DataManager::ImageCallback, this, _1)));

    image_strem_map_[avoid_image + std::to_string(i)] =
        std::make_shared<RosCompressedStream>(avoid_image + std::to_string(i));
  }

  std::string stab_image = "stablizerCameraImage";
  // 相机整定处理结果图片
  for (int i = 0; i < 6; i++) {
    sub_vec_.emplace_back(nh.subscribe<sensor_msgs::CompressedImage>(
        stab_image + std::to_string(i) + "/compressed", kTopicReciveCacheSize,
        boost::bind(&DataManager::ImageCallback, this, _1)));

    image_strem_map_[stab_image + std::to_string(i)] =
        std::make_shared<RosCompressedStream>(stab_image + std::to_string(i));
  }

  // 发布
  pub_map_["debug"] = nh.advertise<cotek_msgs::debug>("debug", 5);
  pub_map_["points_debug"] = nh.advertise<cotek_msgs::debug>("points_debug", 5);

  // 任务请求发布
  msg_publishers_[cotek_topic::kOrderRequestTopic] =
      nh.advertise<cotek_msgs::order>(cotek_topic::kOrderRequestTopic, 10);
  // 直接动作发布
  msg_publishers_[cotek_topic::kInstantActionRequestTopic] =
      nh.advertise<cotek_msgs::instant_action>(
          cotek_topic::kInstantActionRequestTopic, 10);

  msg_publishers_[cotek_topic::kUpdateBasicConfigTopic] =
      nh.advertise<cotek_msgs::update_basic_config>(
          cotek_topic::kUpdateBasicConfigTopic, 10);
  // 直接控制
  msg_publishers_[cotek_topic::kMoveCmdTopic] =
      nh.advertise<cotek_msgs::move_cmd>(cotek_topic::kMoveCmdTopic, 10);
  msg_publishers_[cotek_topic::kActionCmdTopic] =
      nh.advertise<cotek_msgs::action_cmd>(cotek_topic::kActionCmdTopic, 10);

  // 数据service查询
  srv_clients_[cotek_services::kQueryStorageInfoService] =
      nh.serviceClient<cotek_msgs::query_info>(
          cotek_services::kQueryStorageInfoService);

  // 数据service查询
  srv_clients_[cotek_services::kOdomInfoService] =
      nh.serviceClient<cotek_msgs::query_info>(
          cotek_services::kOdomInfoService);

  srv_clients_[cotek_services::kUpdateStorageMapService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateStorageMapService);

  // 配置更新
  srv_clients_[cotek_services::kUpdateNavigationConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateNavigationConfigService);

  srv_clients_[cotek_services::kUpdateActionConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateActionConfigService);

  srv_clients_[cotek_services::kUpdateAvoidConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateAvoidConfigService);

  srv_clients_[cotek_services::kUpdateAvoidAreaConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateAvoidAreaConfigService);

  srv_clients_[cotek_services::kUpdateBasicConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateBasicConfigService);

  srv_clients_[cotek_services::kUpdateLocalizerConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateLocalizerConfigService);

  srv_clients_[cotek_services::kUpdateLogicConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateLogicConfigService);

  srv_clients_[cotek_services::kUpdateRunnerConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateRunnerConfigService);

  srv_clients_[cotek_services::kUpdatestorageConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdatestorageConfigService);

  srv_clients_[cotek_services::kUpdatevisualConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdatevisualConfigService);

  srv_clients_[cotek_services::kUpdateSlamConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateSlamConfigService);

  srv_clients_[cotek_services::kUpdateVoiceConfigService] =
      nh.serviceClient<cotek_msgs::update_config>(
          cotek_services::kUpdateVoiceConfigService);

  // 极简版XP1 api
  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::traffic_state>(
      "traffic_state", kTopicReciveCacheSize,
      boost::bind(&DataManager::TtrafficAreaCallback, this, _1)));
  msg_publishers_["others_traffic"] =
      nh.advertise<cotek_msgs::traffic_state>("others_traffic", 10);

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::learning_state>(
      "learningState", kTopicReciveCacheSize,
      boost::bind(&DataManager::LearnStateCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<cotek_msgs::task_info>(
      cotek_topic::kTaskInfoTopic, kTopicReciveCacheSize,
      boost::bind(&DataManager::TaskInfoCallback, this, _1)));

  sub_vec_.emplace_back(nh.subscribe<std_msgs::Int32>(
      "maualMode", 10, [&](const std_msgs::Int32::ConstPtr &msg) {
        if (msg) {
          popup_ = msg->data;
        }
      }));

  sub_vec_.emplace_back(nh.subscribe<std_msgs::Int32>(
      "manualConfirm", 10, [&](const std_msgs::Int32::ConstPtr &msg) {
        if (msg) {
          confirm_time_ = ros::Time::now();
          manual_confirm_ = msg->data;
        }
      }));
  
  sub_vec_.emplace_back(nh.subscribe<std_msgs::Int32>(
      "traffic_data", 10, [&](const std_msgs::Int32::ConstPtr &msg) {
        if (msg) {
          traffic_ = msg->data;
        }
      }));

  srv_clients_["taskCal"] =
      nh.serviceClient<cotek_msgs::task_cal>("taskCal");
  srv_clients_["taskOrder"] =
      nh.serviceClient<cotek_msgs::task_order>("taskOrder");
  srv_clients_["taskCmd"] = nh.serviceClient<cotek_msgs::task_cmd>("taskCmd");
  srv_clients_["actionCommand"] =
      nh.serviceClient<cotek_msgs::action_command>("actionCommand");
  srv_clients_["taskUpdate"] =
      nh.serviceClient<cotek_msgs::task_update>("taskUpdate");
  srv_clients_["edgeUpdate"] =
      nh.serviceClient<cotek_msgs::edge_update>("edgeUpdate");
  srv_clients_["nodeUpdate"] =
      nh.serviceClient<cotek_msgs::node_update>("nodeUpdate");

  srv_clients_["resetDB"] = nh.serviceClient<cotek_msgs::std_cmd>("resetDB");

  // 地图编辑服务
  srv_clients_[cotek_services::kEditSlam3DMapService] =
      nh.serviceClient<cotek_msgs::edit_slam_3d_map>(
          cotek_services::kEditSlam3DMapService);

  return true;
}

static std::string getMacAddress(const std::string &interfaceName) {
  ifreq ifr{};
  std::string ret;
  int sock = socket(AF_INET, SOCK_DGRAM, 0);

  if (sock == -1) {
    std::cerr << "Error: Unable to open socket" << std::endl;
    return ret;
  }

  // 设置要查询的网卡名称
  std::memset(&ifr, 0, sizeof(ifr));
  std::strncpy(ifr.ifr_name, interfaceName.c_str(), IFNAMSIZ - 1);

  if (ioctl(sock, SIOCGIFHWADDR, &ifr) == -1) {
    std::cerr << "Error: Unable to get MAC address for " << interfaceName
              << std::endl;
    close(sock);
    return ret;
  }

  close(sock);

  // 提取MAC地址
  unsigned char *mac =
      reinterpret_cast<unsigned char *>(ifr.ifr_hwaddr.sa_data);

  // 将MAC地址格式化为字符串
  char macStr[18];

  std::sprintf(macStr, "%02x%02x%02x%02x%02x%02x", mac[0], mac[1], mac[2],
               mac[3], mac[4], mac[5]);
  ret = macStr;

  return ret;
}

std::string DataManager::GetSerialNum() const {
  static bool init = false;
  static std::string serial_num;
  if (option_.lora_option.lora_id <= 0) {
    if (!init) {
      // 指定要查找的网卡名称
      std::string targetInterfaceName = "enp2s0";  // 替换为你要查找的网卡名称

      Poco::Net::NetworkInterface::List interfaces =
          Poco::Net::NetworkInterface::list();
      serial_num = "AMR" + getMacAddress(targetInterfaceName);
      init = true;
    }
  } else {
    serial_num = std::to_string(option_.lora_option.lora_id);
  }

  return serial_num;
}

bool DataManager::CreatStremRequest(const std::string &type) {
  try {
    if (image_strem_map_.count(type) > 0) {
      cotek_msgs::debug dg;
      dg.debug_type = type;
      dg.switch_type = 1;
      pub_map_["debug"].publish(dg);
    }
    if (match_points_map_.count(type) > 0) {
      cotek_msgs::debug dg;
      dg.debug_type = type;
      dg.switch_type = 1;
      pub_map_["points_debug"].publish(dg);
    }
  } catch (const std::out_of_range *ex) {
    LOG_ERROR(ex->what());
    return false;
  }
  return true;
}

ImageInfo DataManager::GetImageInfo(const std::string &type) {
  if (image_strem_map_.count(type) == 0) {
    return ImageInfo();
  }

  return image_strem_map_[type]->GetImageInfo();
}

bool DataManager::ClearImageInfo(const std::string &type) {
  if (image_strem_map_.count(type) == 0) {
    return false;
  }

  return image_strem_map_[type]->ClearData();
}

std::string DataManager::GetImageStreamData(const std::string &type) {
  if (image_strem_map_.count(type) == 0) {
    return std::string();
  }
  // 为避免各节点实时传输图像损耗cpu，图像流由外部请求触发，单个请求触发60s图像流传输
  CreatStremRequest(type);

  return image_strem_map_[type]->GetData();
}

Json DataManager::ImageDataToJson(const std::string &data) {
  Json json;
  if (!data.empty()) {
    json["data"] = data;
  }
  return json;
}

Json DataManager::MotorDataToJson(const std::string &type) {
  Json json, motor_json;
  try {
    for (auto &&motor : motor_map_) {
      Json tmp;
      tmp = motor.second->GetData();
      if (!tmp.empty()) {
        motor_json.push_back(tmp);
      }
    }
    json["motors"] = motor_json;
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return json;
}

Json DataManager::IoDataToJson(const std::string &type) {
  Json json, io_json;
  try {
    for (auto &&io : io_map_) {
      Json tmp;
      tmp = io.second->GetData();
      if (!tmp.empty()) {
        io_json.push_back(tmp);
      }
    }
    json["io"] = io_json;
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return json;
}

Json DataManager::EncoderToJson(const std::string &type) {
  Json json, encoder_json;
  try {
    for (auto &&encoder : encoder_map_) {
      Json tmp;
      tmp = encoder.second->GetData();
      if (!tmp.empty()) {
        encoder_json.push_back(tmp);
      }
    }
    json["wireCoder"] = encoder_json;
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return json;
}

Json DataManager::ForkStatusToJson(const std::string &type) {
  Json json, fork_json;
  try {
    for (auto &&fork : fork_status_map_) {
      Json tmp;
      tmp = fork.second->GetData();
      if (!tmp.empty()) {
        fork_json.push_back(tmp);
      }
    }
    json["forkStatus"] = fork_json;
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return json;
}

bool DataManager::DeleteJson(const std::string &type) {
  try {
    if (json_map_.count(type) > 0) {
      return json_map_[type]->ClearData();
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return false;
  }
  LOG_ERROR_STREAM_THROTTLE(1, "cant delete json with " << type);
  return false;
}

Json DataManager::GetJson(const std::string &type, const std::string &id) {
  try {
    if (json_map_.count(type) > 0) {
      return json_map_[type]->GetData();
    }

    if (type == "forkStatus") {
      return ForkStatusToJson(type);
    }

    if (type == "systemInfo") {
      return QuarySystemInfo(type);
    }

    if (type == "motors") {
      return MotorDataToJson(type);
    }

    if (type == "io") {
      return IoDataToJson(type);
    }

    if (type == "wireCoder") {
      return EncoderToJson(type);
    }

    if (type == "taskList") {
      return TaskListToJson(type);
    }

    if (type == "taskListPoint") {
      return TaskListToJson(type, true);
    }

    if (type == "taskDetail") {
      return TaskListToJson(type, true, id);
    }

    if (type == "taskInfo") {
      return TaskInfoToJson(type);
    }

    if (type == "pathInfo") {
      return PathInfoToJson(id);
    }

    if (type == "taskRunning") {
      return TaskRunningToJson(id);
    }

    if (laser_points_map_.count(type) > 0) {
      return LaserPointsToJson(type);
    }

    if (laser_3d_points_map_.count(type) > 0) {
      return Laser3dPointsToJson(type);
    }

    if (match_points_map_.count(type) > 0) {
      CreatStremRequest(type);
      return MatchPointsToJson(type);
    }

    if (match_3d_points_map_.count(type) > 0) {
      CreatStremRequest(type);
      return Match3dPointsToJson(type);
    }

    if (image_strem_map_.count(type) > 0) {
      return ImageDataToJson(GetImageStreamData(type));
    }

    if (map3d_points_map_.count(type) > 0) {
      return Map3dPointsToJson(type);
    }

    return Json();
  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return Json();
  }
}

bool DataManager::SetJson(const std::string &type, const Json &json) {
  try {
    if (json.empty()) {
      return false;
    }

    if (json_map_.count(type) == 0) {
      json_map_[type] = std::make_unique<TimedDataQueue<Json>>();
    }

    json_map_[type]->PushData(json);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return false;
  }

  return true;
}

void DataManager::AgvPositionCallback(
    const cotek_msgs::agv_position::ConstPtr &msg) {
  Json json, agv_position;
  agv_position["positionInitialized"] = msg->position_initialized;
  agv_position["localizationScore"] = msg->localization_score;
  agv_position["deviationRange"] = msg->deviation_range;
  agv_position["x"] = msg->x;
  agv_position["y"] = msg->y;
  agv_position["z"] = msg->z;
  if (msg->x >= 888888. || msg->y >= 888888.) {
    agv_position["localizationScore"] = 0;
  }
  agv_position["theta"] = msg->theta;
  agv_position["zoneId"] = msg->zone_id;
  agv_position["mapId"] = msg->map_id;
  agv_position["mapDescription"] = msg->map_description;
  agv_position["mapping"] = msg->mapping;
  agv_position["landmark_num"] = msg->landmark_num;

  if (msg->position_initialized && !msg->mapping) {
    pose_ = Pose(msg->x, msg->y, msg->theta);
    try {
      if (!msg->map_id.empty() && !msg->zone_id.empty() &&
          (map_id_ != msg->map_id || zone_id_ != msg->zone_id)) {
        std::string user_dir = Poco::Path::home();
        std::string map_png = user_dir + "/config/map/" + msg->zone_id + "/" +
                              msg->map_id + "/cotek.pgm";

        cv::Mat gray_image = cv::imread(map_png, cv::IMREAD_GRAYSCALE);
        if (gray_image.empty()) {
          throw std::runtime_error(std::string("read map image exception"));
        }

        map_width_ = gray_image.cols;
        map_height_ = gray_image.rows;

        std::string map_name;
        //try {
          std::string map_index = user_dir + "/config/map/" + msg->zone_id +
                                  "/" + msg->map_id + "/index.json";
          std::ifstream filename(map_index, std::ios::binary);
          if (!filename) {
            throw std::runtime_error("Failed to open file: " + map_index);
          }
          map_name = Json::parse(filename)["name"].get<std::string>();
        //} catch (...) {
        //}

        map_name_ = map_name;
        map_id_ = msg->map_id;
        zone_id_ = msg->zone_id;
      }
    } catch (const std::exception &e) {
      LOG_ERROR_STREAM(e.what());
    }
  } else {
    pose_ = Pose(0, 0, 0);
  }
  agv_position["mapWidth"] = map_width_;
  agv_position["mapHeight"] = map_height_;
  json["agvPosition"] = agv_position;
  SetJson(cotek_topic::kAgvPositionTopic, json);
}

void DataManager::VelocityCallback(
    const cotek_msgs::move_feedback::ConstPtr &msg) {
  Json json, v_json;
  static std::deque<double> rec_v;
  v_json["vx"] = msg->vx;
  v_json["vy"] = msg->vy;
  v_json["omega"] = msg->omega;
  json["moveFeedback"] = v_json;
  SetJson(cotek_topic::kMoveFeedbackTopic, json);

  rec_v.push_back(msg->vx);
  if (rec_v.size() > 5) rec_v.pop_front();
  double sum = 0.0;
  for (const auto &value : rec_v) {
    sum += value;
  }
  vel_ = sum/(double)rec_v.size();

  // vel_ = msg->vx;
}

void DataManager::CmdVelocityCallback(
    const cotek_msgs::move_cmd::ConstPtr &msg) {
  Json json, v_json;
  v_json["vx"] = msg->vx;
  v_json["vy"] = msg->vy;
  v_json["omega"] = msg->omega;
  v_json["angle"] = msg->angle;
  json["moveCmd"] = v_json;
  SetJson(cotek_topic::kMoveCmdTopic, json);
}

void DataManager::SafetyStateCallback(
    const cotek_msgs::safety_states::ConstPtr &msg) {
  Json json, state_json;
  state_json["eStop"] = "";
  state_json["fieldViolation"] = false;
  Json avoid_json;
  for (auto &avoid : msg->safety_states) {
    Json state;
    state["avoidState"] = avoid.avoid_state;
    state["avoidType"] = avoid.avoid_type;
    Json point;
    point["x"] = avoid.avoid_point.x;
    point["y"] = avoid.avoid_point.y;
    state["avoidPoint"] = point;
    avoid_json.push_back(state);
  }
  state_json["avoid"] = avoid_json;
  json["safetyState"] = state_json;
  SetJson(cotek_topic::kSafetyStateTopic, json);
}

void DataManager::FaultReportCallback(
    const cotek_msgs::fault_report::ConstPtr &msg) {
  Json json, errors_json;

  for (auto &error : msg->errors) {
    Json error_json;
    error_json["errorType"] = error.error_type;
    error_json["errorDescription"] = error.error_description;
    error_json["errorLevel"] = error.error_level;
    Json error_references_json;
    for (auto &reference : error.error_references) {
      Json reference_json;
      reference_json["referenceKey"] = std::to_string(reference.reference_key);
      // reference_json["referenceKey"] = reference.reference_value;
      error_references_json.push_back(reference_json);
    }
    error_json["errorReferences"] = error_references_json;
    errors_json.push_back(error_json);
  }
  json["errors"] = errors_json;

  SetJson(cotek_topic::kErrorsTopic, json);
}

void DataManager::BatteryCallback(
    const cotek_msgs::battery_feedback::ConstPtr &msg) {
  Json json, battery_json;
  battery_json["batteryCharge"] = msg->percentage;
  battery_json["batteryVoltage"] = msg->voltage;
  battery_json["batteryHealth"] = 100;
  battery_json["charging"] = msg->charge;
  // TODO(@ssh)
  battery_json["reach"] = false;
  json["batteryState"] = battery_json;

  SetJson(cotek_topic::kBatteryStateTopic, json);
}

void DataManager::LimitSwitchCallback(const std_msgs::Int32::ConstPtr &msg) {
  try {
    std::string type = cotek_topic::kLimitSwitchStateTopic;
    if (fork_status_map_.count(type) == 0) {
      fork_status_map_[type] = std::make_unique<TimedDataQueue<Json>>();
    }

    Json json;
    json["id"] = type;
    json["value"] = static_cast<double>(msg->data);
    fork_status_map_.at(type)->PushData(json);
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

void DataManager::OdomInfoCallback(const cotek_msgs::odom_info::ConstPtr &msg) {
  Json json, odom_info_json;

  odom_info_json["time"] = common::GetCurrentTime<std::string>();
  odom_info_json["odom"] = msg->odom;
  odom_info_json["totalDuration"] = msg->total_duration;
  odom_info_json["avoidDuration"] = msg->avoid_duration;

  json["globalOdomInfo"] = odom_info_json;
  SetJson(cotek_topic::kGlobalOdomInfoTopic, json);
}

void DataManager::LoadsCallback(const cotek_msgs::loads::ConstPtr &msg) {
  Json json, loads_json;
  for (auto &load : msg->loads) {
    Json load_json;
    load_json["loadId"] = load.load_id;
    load_json["loadType"] = load.load_type;
    load_json["loadPosition"] = "";
    load_json["boundingBoxReference"] = Json();
    load_json["loadDimensions"] = Json();
    load_json["weight"] = load.weight;
    loads_json.push_back(load_json);
  }
  json["loads"] = loads_json;
  SetJson(cotek_topic::kLoadsTopic, json);
}

void DataManager::PathStateCallback(
    const cotek_msgs::path_state::ConstPtr &msg) {
  // todo
}

void DataManager::SelfCheckCallback(
    const cotek_msgs::self_check::ConstPtr &msg) {
  Json json, check_json, info_json;
  for (auto &info : msg->infos) {
    Json module_json;
    module_json["type"] = info.type;
    module_json["result"] = info.result;
    for (auto &temp : info.info) {
      module_json["info"].push_back(temp);
    }
    if (info.info.empty()) {
      module_json["info"] = std::string();
    }

    info_json.push_back(module_json);
  }
  check_json["result"] = msg->result;
  check_json["info"] = info_json;

  json["selfCheck"] = check_json;
  SetJson(cotek_topic::kSelfCheckTopic, json);
}

void DataManager::OdomCallback(const nav_msgs::Odometry::ConstPtr &msg) {
  Json json, odom_json;
  odom_json["positionX"] = msg->pose.pose.position.x;
  odom_json["positionY"] = msg->pose.pose.position.y;
  odom_json["positionZ"] = msg->pose.pose.position.z;

  odom_json["linearX"] = msg->twist.twist.linear.x;
  odom_json["linearY"] = msg->twist.twist.linear.y;
  odom_json["linearZ"] = msg->twist.twist.linear.z;

  odom_json["angularX"] = msg->twist.twist.angular.x;
  odom_json["angularY"] = msg->twist.twist.angular.y;
  odom_json["angularZ"] = msg->twist.twist.angular.z;

  json["odom"] = odom_json;

  SetJson(cotek_topic::kOdomTopic, json);
}

void DataManager::MotorCallback(
    const cotek_msgs::motor_feedback::ConstPtr &msg) {
  try {
    if (motor_map_.count(msg->header.frame_id) == 0) {
      motor_map_[msg->header.frame_id] =
          std::make_unique<TimedDataQueue<Json>>();
    }

    Json json;
    json["id?"] = "*(电机名称/motor)%(string)";
    json["id"] = msg->header.frame_id;
    json["type?"] = "*(类型/type)%(string)";
    json["type"] = msg->type;
    json["model?"] =
        "*(电机模式/motor mode)#(速度/speed:0;位置/position:1)%(int)";
    json["model"] = msg->model;
    json["manual?"] = "*(手自动模式/auto)#(自动/auto:0;手动/manual:1)%(int)";
    json["manual"] = msg->manual;
    json["brake?"] = "*(抱闸/brake)%(bool)";
    json["brake"] = msg->brake;
    json["speed?"] = "*(电机转速/rpm)%(double)$(rpm)";
    json["speed"] = msg->speed;
    json["velocity?"] = "*(电机速度/velocity)%(double)$(m/s)";
    json["velocity"] = msg->velocity;
    json["angle?"] = "*(电机转角/anlge)%(double)$(rad)";
    json["angle"] = msg->angle;
    json["voltage?"] = "*(电压/voltage)%(double)$(V)";
    json["voltage"] = msg->voltage;
    json["current?"] = "*(电流/current)%(double)$(A)";
    json["current"] = msg->current;
    json["position?"] = "*(电机位置/position)%(int)";
    json["position"] = msg->position;
    std::string error =
        msg->error_code != 0 ? intToHex(msg->error_code) : std::string();
    json["error?"] = "*(错误码/error code)%(string)";
    json["error"] = error;
    motor_map_.at(msg->header.frame_id)->PushData(json);
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

void DataManager::IoCallback(const cotek_msgs::io_feedback::ConstPtr &msg) {
  try {
    if (io_map_.count(msg->header.frame_id) == 0) {
      io_map_[msg->header.frame_id] = std::make_unique<TimedDataQueue<Json>>();
    }

    Json json, io_object;
    json["id?"] = "*(名称/id)%(string)";
    json["id"] = msg->header.frame_id;
    json["type?"] = "*(类型/type)%(string)";
    json["type"] = msg->type;
    json["data?"] = "*(数据/data)";
    for (auto &&io : msg->io) {
      io_object["key?"] = "*(标识/key)%(string)";
      io_object["key"] = io.key;
      io_object["name?"] = "*(映射/mapping)%(string)";
      io_object["name"] = std::string("*(") + io::GetIoTranlator(io.name) +
                          "/" + io.name + std::string(")");
      io_object["value?"] = "*(数值/value)%(int)";
      io_object["value"] = io.value;
      json["data"].push_back(io_object);
    }
    std::string error =
        msg->error_code != 0 ? intToHex(msg->error_code) : std::string();
    json["error?"] = "*(错误码/error code)%(string)";
    json["error"] = error;

    io_map_.at(msg->header.frame_id)->PushData(json);
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

void DataManager::EncoderToForkStatus(const std::string &frame,
                                      const double &value) {
  try {
    std::string type;
    if (frame == cotek_topic::kHeightEncoderTopic) {
      type = "forkHeight";
    }
    if (frame == cotek_topic::kLateralEncoderTopic) {
      type = "lateralHeight";
    }
    if (frame == cotek_topic::kSideEncoderTopic) {
      type = "sideHeight";
    }

    if (fork_status_map_.count(type) == 0) {
      fork_status_map_[type] = std::make_unique<TimedDataQueue<Json>>();
    }

    Json json;
    json["id"] = type;
    json["value"] = value;
    fork_status_map_.at(type)->PushData(json);

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

void DataManager::EncoderCallback(
    const cotek_msgs::wire_encoder_feedback::ConstPtr &msg) {
  try {
    if (encoder_map_.count(msg->header.frame_id) == 0) {
      encoder_map_[msg->header.frame_id] =
          std::make_unique<TimedDataQueue<Json>>();
    }

    Json json, io_object;
    json["id?"] = "*(名称/id)%(string)";
    json["id"] = EncoderTranslate(msg->header.frame_id);
    json["type?"] = "*(类型/type)%(string)";
    json["type"] = msg->type;
    json["data?"] = "*(原始数据/orign_data)%(int)$(0.1mm)";
    json["data"] = msg->orign_data;
    json["length?"] = "*(矫正高度/correct height)%(double)$(m)";
    json["length"] = math::ZeroPointMM2Meter<double>(msg->data);

    std::string error =
        msg->error_code != 0 ? intToHex(msg->error_code) : std::string();
    json["error?"] = "*(错误码/error code)%(string)";
    json["error"] = error;
    // 添加拉线编码器传感器接口
    encoder_map_.at(msg->header.frame_id)->PushData(json);

    // 添加叉齿状态接口
    double fork_value = msg->error_code == 0
                            ? math::ZeroPointMM2Meter<double>(msg->data)
                            : 888888;
    EncoderToForkStatus(msg->header.frame_id, fork_value);

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

void DataManager::ScanMatchPointsCallback(
    const cotek_msgs::points_2d::ConstPtr &msg) {
  if (match_points_map_.count(cotek_topic::kScanMatchedPointsTopic) == 0) {
    match_points_map_[cotek_topic::kScanMatchedPointsTopic] =
        std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();
  }
  match_points_map_[cotek_topic::kScanMatchedPointsTopic]->PushData(*msg);
}

void DataManager::ReflectorMapPointsCallback(
    const cotek_msgs::points_2d::ConstPtr &msg) {
  if (match_points_map_.count(cotek_topic::kReflectorMapPointsTopic) == 0) {
    match_points_map_[cotek_topic::kReflectorMapPointsTopic] =
        std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();
  }
  match_points_map_[cotek_topic::kReflectorMapPointsTopic]->PushData(*msg);
}

void DataManager::ReflectorMatchPointsCallback(
    const cotek_msgs::points_2d::ConstPtr &msg) {
  if (match_points_map_.count(cotek_topic::kMatchedLandmarkPointsTopic) == 0) {
    match_points_map_[cotek_topic::kMatchedLandmarkPointsTopic] =
        std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();
  }
  match_points_map_[cotek_topic::kMatchedLandmarkPointsTopic]->PushData(*msg);
}

void DataManager::LostCheckMapPointsCallback(
    const cotek_msgs::points_2d::ConstPtr &msg) {
  if (match_points_map_.count(cotek_topic::kLostCheckMapPointsTopic) == 0) {
    match_points_map_[cotek_topic::kLostCheckMapPointsTopic] =
        std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();
  }
  match_points_map_[cotek_topic::kLostCheckMapPointsTopic]->PushData(*msg);
}
void DataManager::LostCheckMatchPointsCallback(
    const cotek_msgs::points_2d::ConstPtr &msg) {
  if (match_points_map_.count(cotek_topic::kMatchedLostCheckPointsTopic) == 0) {
    match_points_map_[cotek_topic::kMatchedLostCheckPointsTopic] =
        std::make_unique<TimedDataQueue<cotek_msgs::points_2d>>();
  }
  match_points_map_[cotek_topic::kMatchedLostCheckPointsTopic]->PushData(*msg);
}

void DataManager::MatchPointsCloudCallback(
    const sensor_msgs::PointCloud2::ConstPtr &msg) {
  if (match_3d_points_map_.count(cotek_topic::kMatchedPointCloudTopic) == 0) {
    match_3d_points_map_[cotek_topic::kMatchedPointCloudTopic] =
        std::make_unique<TimedDataQueue<sensor_msgs::PointCloud2>>();
  }
  match_3d_points_map_[cotek_topic::kMatchedPointCloudTopic]->PushData(*msg);
}

Json DataManager::MatchPointsToJson(const std::string &type) {
  Json json;
  try {
    if (match_points_map_.count(type) > 0) {
      cotek_msgs::points_2d &&points =
          match_points_map_.at(type)->GetData(10.0);
      if (points.points.empty()) {
        std::vector<double> x, y;
        json["x"] = x;
        json["y"] = y;
        return json;
      }
      std::vector<double> x, y;
      for (auto &&point : points.points) {
        x.push_back(point.x);
        y.push_back(point.y);
      }
      json["x"] = std::move(x);
      json["y"] = std::move(y);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    std::vector<double> x, y;
    json["x"] = x;
    json["y"] = y;
  }

  return json;
}

Json DataManager::Match3dPointsToJson(const std::string &type) {
  Json json;
  try {
    if (match_3d_points_map_.count(type) > 0) {
      sensor_msgs::PointCloud2 &&points =
          match_3d_points_map_.at(type)->GetData(10.0);
      std::vector<double> x, y, z;
      if (0 == points.width * points.height) {
        json["x"] = x;
        json["y"] = y;
        json["z"] = z;
        return json;
      }

      pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(
          new pcl::PointCloud<pcl::PointXYZ>);
      pcl::fromROSMsg(points, *cloud);

      // 遍历点云并打印每个点的x, y, z坐标
      for (const auto &point : *cloud) {
        x.push_back(point.x);
        y.push_back(point.y);
        z.push_back(point.z);
      }

      json["x"] = std::move(x);
      json["y"] = std::move(y);
      json["z"] = std::move(z);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    std::vector<double> x, y, z;
    json["x"] = x;
    json["y"] = y;
    json["z"] = z;
  }

  return json;
}

Json DataManager::Map3dPointsToJson(const std::string &type) {
  Json json;
  try {
    if (map3d_points_map_.count(type) > 0) {
      sensor_msgs::PointCloud2 &&points =
          map3d_points_map_.at(type)->GetData(10.0);
      std::vector<double> x, y, z;
      if (0 == points.width * points.height) {
        json["x"] = x;
        json["y"] = y;
        json["z"] = z;
        return json;
      }

      pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(
          new pcl::PointCloud<pcl::PointXYZ>);
      pcl::fromROSMsg(points, *cloud);

      // 遍历点云并打印每个点的x, y, z坐标
      for (const auto &point : *cloud) {
        x.push_back(point.x);
        y.push_back(point.y);
        z.push_back(point.z);
      }

      json["x"] = std::move(x);
      json["y"] = std::move(y);
      json["z"] = std::move(z);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    std::vector<double> x, y, z;
    json["x"] = x;
    json["y"] = y;
    json["z"] = z;
  }

  return json;
}

Json DataManager::LaserPointsToJson(const std::string &type) {
  Json json;

  try {
    // 结合配置将激光点云转换到车体坐标系
    if (laser_points_map_.count(type) > 0) {
      sensor_msgs::LaserScan &&scan = laser_points_map_.at(type)->GetData();

      if (scan.ranges.empty()) {
        return json;
      }

      std::vector<double> x, y;

      Transform laser_tf =
          BasicConfigHelper::Instance().transform_map().at(type);

      LaserScanOption laser_option =
          BasicConfigHelper::Instance().laser_map().at(type);

      Pose orign = Pose(laser_tf.x, laser_tf.y, laser_tf.yaw);
      Eigen::Vector2d orign_temp = Eigen::Vector2d(orign.x(), orign.y());

      for (std::size_t i = 0; i < scan.ranges.size();
           i += laser_option.scan_sample_step) {
        auto angle =
            laser_option.filter.positive
                ? angles::normalize_angle(laser_option.angle_min +
                                          i * laser_option.angle_increment)
                : angles::normalize_angle(laser_option.angle_max -
                                          i * laser_option.angle_increment);

        if (scan.ranges[i] > scan.range_max ||
            scan.ranges[i] < scan.range_min) {
          continue;
        }

        Eigen::Rotation2Dd point_rotator(orign.theta());
        Eigen::Vector2d point_cloud =
            point_rotator.toRotationMatrix() *
                Eigen::Vector2d(scan.ranges[i] * std::cos(angle),
                                scan.ranges[i] * std::sin(angle)) +
            orign_temp;

        x.push_back(point_cloud.x());
        y.push_back(point_cloud.y());
      }
      Json points;
      points["x"] = std::move(x);
      points["y"] = std::move(y);
      json["points"] = std::move(points);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }

  return json;
}

Json DataManager::Laser3dPointsToJson(const std::string &type) {
  Json json;

  try {
    if (laser_3d_points_map_.count(type) > 0) {
      sensor_msgs::PointCloud2 &&points =
          laser_3d_points_map_.at(type)->GetData();

      std::vector<double> x, y, z;
      if (0 == points.width * points.height) {
        Json json_points;
        json_points["x"] = x;
        json_points["y"] = y;
        json_points["z"] = z;
        json["points"] = std::move(json_points);
        return json;
      }

      pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(
          new pcl::PointCloud<pcl::PointXYZ>);
      pcl::fromROSMsg(points, *cloud);

      // 遍历点云并打印每个点的x, y, z坐标
      for (const auto &point : *cloud) {
        x.push_back(point.x);
        y.push_back(point.y);
        z.push_back(point.z);
      }
      Json json_points;
      json_points["x"] = std::move(x);
      json_points["y"] = std::move(y);
      json_points["z"] = std::move(z);
      json["points"] = std::move(json_points);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }

  return json;
}

void DataManager::LaserPointsCallback(
    const sensor_msgs::LaserScan::ConstPtr &msg) {
  if (laser_points_map_.count(msg->header.frame_id) == 0) {
    laser_points_map_[msg->header.frame_id] =
        std::make_unique<TimedDataQueue<sensor_msgs::LaserScan>>();
  }
  laser_points_map_[msg->header.frame_id]->PushData(*msg);
}

void DataManager::Laser3dPointsCallback(
    const sensor_msgs::PointCloud2::ConstPtr &msg) {
  if (laser_3d_points_map_.count(cotek_topic::k3dNaviLaser0Topic) == 0) {
    laser_3d_points_map_[cotek_topic::k3dNaviLaser0Topic] =
        std::make_unique<TimedDataQueue<sensor_msgs::PointCloud2>>();
  }
  laser_3d_points_map_[cotek_topic::k3dNaviLaser0Topic]->PushData(*msg);
}

void DataManager::ImageCallback(
    const sensor_msgs::CompressedImageConstPtr &msg) {
  if (image_strem_map_.count(msg->header.frame_id) > 0) {
    image_strem_map_[msg->header.frame_id]->SetData(
        std::vector<int8_t>(msg->data.begin(), msg->data.end()));
  }
}

void DataManager::MapCallback(const nav_msgs::OccupancyGrid::ConstPtr &msg) {
  ImageInfo info{};
  info.width = msg->info.width;
  info.height = msg->info.height;
  info.resolution = msg->info.resolution;
  info.orign_x = msg->info.origin.position.x;
  info.orign_y = msg->info.origin.position.y;
  image_strem_map_[cotek_topic::kMapWebTopic]->SetImageInfo(info);
  image_strem_map_[cotek_topic::kMapWebTopic]->SetData(msg->data);
}

void DataManager::Map3dCallback(const sensor_msgs::PointCloud2::ConstPtr &msg) {
  if (map3d_points_map_.count(cotek_topic::kMap3dFrontTopic) == 0) {
    map3d_points_map_[cotek_topic::kMap3dFrontTopic] =
        std::make_unique<TimedDataQueue<sensor_msgs::PointCloud2>>();
  }
  map3d_points_map_[cotek_topic::kMap3dFrontTopic]->PushData(*msg);
}

void DataManager::WeightAreaCallback(const cotek_msgs::area::ConstPtr &msg) {
  try {
    Json json, area_json;
    int i = 1;
    for (auto &&polygon : msg->polygons) {
      Json polygon_json;
      std::vector<double> x, y;
      polygon_json["id"] = std::to_string(i++);
      for (auto &&point : polygon.points) {
        x.push_back(point.x);
        y.push_back(point.y);
      }
      polygon_json["polygon"]["x"] = std::move(x);
      polygon_json["polygon"]["y"] = std::move(y);
      area_json.push_back(polygon_json);
    }

    json["type"] = "weight";
    json["areas"] = area_json;

    SetJson(cotek_topic::kStrongAreaInfoTopic, json);

  } catch (const std::exception &ex) {
    LOG_ERROR_THROTTLE(1, ex.what());
  }
}

void DataManager::HighAreaCallback(const cotek_msgs::area::ConstPtr &msg) {
  try {
    Json json, area_json;
    int i = 1;
    for (auto &&polygon : msg->polygons) {
      Json polygon_json;
      std::vector<double> x, y;
      polygon_json["id"] = std::to_string(i++);
      for (auto &&point : polygon.points) {
        x.push_back(point.x);
        y.push_back(point.y);
      }
      polygon_json["polygon"]["x"] = std::move(x);
      polygon_json["polygon"]["y"] = std::move(y);
      area_json.push_back(polygon_json);
    }

    json["type"] = "dynamic";
    json["areas"] = area_json;

    SetJson(cotek_topic::kHighDynamicAreaInfoTopic, json);

  } catch (const std::exception &ex) {
    LOG_ERROR_THROTTLE(1, ex.what());
  }
}

struct CpuUsage {
  int64_t idle;
  int64_t total;
};

Json DataManager::QuarySystemInfo(const std::string &type) {
  if (type != "systemInfo") {
    return Json();
  }
  try {
    Json json, system_info_json;
    system_info_json["cpuUsage"] =
        std::to_string(system_info_.GetCpuUsage()) + std::string("%");
    system_info_json["memoryUsage"] =
        std::to_string(system_info_.GetMemUsage()) + std::string("%");
    system_info_json["diskUsage"] =
        std::to_string(system_info_.GetDiskUsage()) + std::string("%");
    system_info_json["ping"] =
        std::to_string(system_info_.GetPingLatency()) + std::string("ms");
    json["systemInfo"] = system_info_json;
    return json;
  } catch (...) {
    LOG_ERROR("Quary system info failed!!!");
    return Json();
  }
  return Json();
}

static const char *kActionType[] = {"", "liftLoad", "unload", "rest", "confirm",
                                    "charge", "lieUnLoad", "openDoor", "closeDoor",
                                    "callDT", "takeDT", "leaveDT", "switchMap"};
static const char *kNodePos[] = {"none", "start", "end", ""};
Json DataManager::TaskPointToJson(const std::vector<common::node_t> &points) {
  Json json;
  try {
    Json points_json, special_points_json;

    // 初始化最大值和最小值
    double min_x = std::numeric_limits<double>::max();
    double max_x = std::numeric_limits<double>::lowest();
    double min_y = std::numeric_limits<double>::max();
    double max_y = std::numeric_limits<double>::lowest();

    std::vector<double> x, y;
    x.reserve(points.size());
    y.reserve(points.size());
    for (auto &point : points) {
      x.push_back(point.x);
      y.push_back(point.y);
      if (point.x < min_x) min_x = point.x;
      if (point.x > max_x) max_x = point.x;
      if (point.y < min_y) min_y = point.y;
      if (point.y > max_y) max_y = point.y;
      bool is_append = false;
      if (point.node >= 1 && point.node <= 2) {
        Json sp;
        sp["id"] = point.id;
        // sp["pos"] = kNodePos[point.node];
        sp["type"] = kActionType[point.action];
        if (sp["type"] != "" && sp["type"] != "rest") {
          sp["pos"] = "none";
        } else {
          sp["pos"] = kNodePos[point.node];
        }
        sp["x"] = point.x;
        sp["y"] = point.y;
        special_points_json.push_back(sp);
        is_append = true;
      }

      if (!is_append && point.type == 1) {
        if (point.action >= 0 && point.action < 13) {
          Json sp;
          sp["id"] = point.id;
          // sp["pos"] = kNodePos[point.node];
          sp["type"] = kActionType[point.action];
          if (sp["type"] != "" && sp["type"] != "rest") {
            sp["pos"] = "none";
          } else {
            sp["pos"] = kNodePos[point.node];
          }
          sp["x"] = point.x;
          sp["y"] = point.y;
          special_points_json.push_back(sp);
        }
      }
    }
    points_json["x"] = x;
    points_json["y"] = y;

    json["points"] = points_json;
    json["specialPoints"] = special_points_json;
    json["size"]["width"] = max_x - min_x;
    json["size"]["height"] = max_y - min_y;
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return json;
}

Json DataManager::TaskListToJson(const std::string &type, bool detail,
                                 const std::string &id) {
  Json json, task_lists_json;
  try {
    auto task_lists = GetTaskList(id);
    for (auto task : task_lists) {
      Json task_json;
      task_json["id"] = task.id;
      task_json["name"] = task.name;
      task_json["speed"] = task.velocity_level;
      task_json["safety"] = task.avoid_level;
      task_json["loop"] = task.loop;
      task_json["zone_id"] = task.zone_id;
      task_json["map_id"] = task.map_id;
      task_json["number"] = std::to_string(task.sn);
      task_json["storage"] = task.kuqu_id != -1 ? true : false;
      if (detail) {
        if (task.kuqu_id == -1) {
          task_json["vertexPoints"] = Json::array();
          task_json["storagePoints"] = Json::array();
          task_json["kwsize"] = Json::array();
          task_json["direction"] = "";

          auto &&pts = TaskPointToJson(task.nodes);
          if (!pts.empty()) {
            task_json.merge_patch(pts);
          }
          if (!id.empty() && id == task.id) {
            task_lists_json.push_back(task_json);
            break;
          }
        } else {
          task_json["points"]["x"] = Json::array();
          task_json["points"]["y"] = Json::array();
          task_json["specialPoints"] = Json::array();
          
          Json vertexs, storages;
          Json points;
          for (const auto &data : task.kuqu.vertexs) {
            points["id"] = data.id;
            points["x"] = data.x;
            points["y"] = data.y;
            vertexs.push_back(points);
          }
          for (const auto &data : task.kuqu.storage) {
            points["id"] = data.center.id;
            points["x"] = data.center.x;
            points["y"] = data.center.y;
            storages.push_back(points);
          }
          Json size;
          size["width"] = task.kuqu.kwsize.width;
          size["length"] = task.kuqu.kwsize.length;
          size["rowspace"] = task.kuqu.kwsize.rowspace;
          size["columnspace"] = task.kuqu.kwsize.columnspace;

          task_json["vertexPoints"] = vertexs;
          task_json["storagePoints"] = storages;
          task_json["kwsize"] = size;
          task_json["direction"] = task.kuqu.direction;
          if (!id.empty() && id == task.id) {
            task_lists_json.push_back(task_json);
            break;
          }          
        }

      }
      if (!(detail && !id.empty())) {
        task_lists_json.push_back(task_json);
      }
    }
    json["teachTaskList"] = task_lists_json;

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return Json();
  }

  return json;
}

Json DataManager::PathInfoToJson(const std::string &id) {
  Json json;
  try {
    auto task_lists = GetTaskList(id);
    Json task_json;

    for (auto task : task_lists) {
      task_json["id"] = task.id;
      task_json["name"] = task.name;
      task_json["speed"] = task.velocity_level;
      task_json["safety"] = task.avoid_level;
      task_json["loop"] = task.loop;
      task_json["number"] = std::to_string(task.sn);
      for (auto &edge : task.edges) {
        Json ed;
        ed["id"] = edge.id;
        ed["speed"] = edge.velocity_level;
        ed["safety"] = edge.avoid_level;
        
        for (int i = 0; i < edge.points.size(); i+=2) {
          Json point;
          point["x"] = edge.points[i].x;
          point["y"] = edge.points[i].y;
          ed["points"].push_back(point);
        }
        task_json["edgelist"].push_back(ed);
      }

      for (auto &point : task.nodes) {
        bool is_append = false;
        if (point.node >= 1 && point.node <= 2) {
          is_append = true;
          Json sp;
          sp["id"] = point.id;
          // sp["pos"] = kNodePos[point.node];
          sp["type"] = kActionType[point.action];
          if (sp["type"] != "" && sp["type"] != "rest") {
            sp["pos"] = "none";
          } else {
            sp["pos"] = kNodePos[point.node];
          }
          sp["x"] = point.x;
          sp["y"] = point.y;
          task_json["nodelist"].push_back(sp);
        }

        if (!is_append && point.type == 1) {
          if (point.action >= 0 && point.action < 13) {
            Json sp;
            sp["id"] = point.id;
            // sp["pos"] = kNodePos[point.node];
            sp["type"] = kActionType[point.action];
            if (sp["type"] != "" && sp["type"] != "rest") {
              sp["pos"] = "none";
            } else {
              sp["pos"] = kNodePos[point.node];
            }
            sp["x"] = point.x;
            sp["y"] = point.y;
            task_json["nodelist"].push_back(sp);
          }
        }
      }
    }
    json["teachTask"] = task_json;

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return Json();
  }

  return json;
}

std::vector<task_t> DataManager::GetTaskList(const std::string &task_id,
                                             int refresh) {
  std::vector<task_t> tasks;
  std::map<std::string, task_t> temp_tasks;
  try {
    {
      std::lock_guard<std::mutex> lck(task_mtx_);
      temp_tasks = tasks_;
    }

    if (!task_id.empty()) {
      if (temp_tasks.find(task_id) != temp_tasks.end()) {
        tasks.push_back(temp_tasks[task_id]);
        return tasks;
      }
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(std::string("load task by task_id exception: ") + ex.what());
  }

  for (auto &task : temp_tasks) {
    tasks.push_back(task.second);
  }
  return tasks;
}

std::vector<task_t> DataManager::GetTasks(const std::string &map_id) {
  std::vector<task_t> tasks;
  try {
    std::map<std::string, task_t> temp_tasks;
    {
      std::lock_guard<std::mutex> lck(task_mtx_);
      temp_tasks = tasks_;
    }

    for (auto &cit : temp_tasks) {
      if (!map_id.empty() && cit.second.map_id == map_id) {
        tasks.push_back(cit.second);
      }
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(std::string("load task by map_id exception: ") + ex.what());
  }

  return tasks;
}

bool IsTimestampToday(uint64_t ns) {
  auto compare_tp =
      std::chrono::system_clock::time_point(std::chrono::nanoseconds(ns));
  std::time_t compare_time_t = std::chrono::system_clock::to_time_t(compare_tp);

  struct tm compare_tm = {};
  localtime_r(&compare_time_t, &compare_tm);

  // now time
  auto now = std::chrono::system_clock::now();
  std::time_t now_time_t = std::chrono::system_clock::to_time_t(now);
  struct tm now_tm = {};
  localtime_r(&now_time_t, &now_tm);

  // compare year, month, and day
  return (compare_tm.tm_year == now_tm.tm_year &&
          compare_tm.tm_mon == now_tm.tm_mon &&
          compare_tm.tm_mday == now_tm.tm_mday);
}

Json DataManager::TaskInfoToJson(const std::string &type) {
  Json json, json_info;
  try {
    json_info["todayCount"] = task_info_.current_task_count;
    json_info["taskCount"] = task_info_.total_task_count;
    json_info["taskError"] = task_info_.error_task_count;
    json_info["currentOdomTime"] = task_info_.current_odom_time;
    json_info["createOdomTime"] = task_info_.create_odom_time;
    json_info["currentOdom"] = task_info_.current_odom;
    json_info["totalOdom"] = task_info_.total_odom;
    json["taskInfo"] = json_info;

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return Json();
  }

  return json;
}

int GetClosestPoint(const Pose &pt, const std::vector<node_t> &pts,
                    double &min_distance, int index = 0) {
  int closest_index = 0;
  min_distance = std::numeric_limits<double>::max();
  size_t i = 0;
  if (index > 0) {
    i = index;
  }
  for (; i < pts.size(); ++i) {
    double dist = std::hypot(pts[i].x - pt.x(), pts[i].y - pt.y());
    if (dist < min_distance && pts[i].v * pt.yaw() > 0) {
      min_distance = dist;
      closest_index = i;
    }
  }
  return closest_index;
}

double GetDistance(const std::vector<node_t> &pts) {
  double distance = 0;
  for (size_t i = 0; i < pts.size() - 1; ++i) {
    distance += std::hypot(pts[i + 1].x - pts[i].x, pts[i + 1].y - pts[i].y);
  }
  return distance;
}

double GetDistance(const Pose &pt, const std::vector<node_t> &pts, int &index) {
  double distance = 0.0;
  double min_distance = 0.0;
  int start_index = GetClosestPoint(pt, pts, min_distance, index);
  for (size_t i = start_index; i < pts.size() - 1; ++i) {
    distance += std::hypot(pts[i + 1].x - pts[i].x, pts[i + 1].y - pts[i].y);
  }
  index = start_index;
  return distance;
}

Json DataManager::TaskRunningToJson(const std::string &id) {
  Json json;
  double task_distance{0}, remain_distance{0}, remain_time{0}, process{0};
  if (!id.empty()) {
    auto tasks = GetTaskList(id);
    if (!tasks.empty()) {
      auto task = tasks.front();
      if (last_id_ != id || point_index_ >= task.nodes.size() - 1) {
        point_index_ = 0;
        remain_distance_ = 8888.0;
        remain_time_ = 8888.0;
      }
      std::vector<node_t> pts(task.nodes);
      Pose curr_pos(pose_);
      curr_pos.set_yaw(vel_);
      task_distance = GetDistance(pts);
      if (std::fabs(vel_) > 0.05) {
        int index = point_index_;
        remain_distance_ = GetDistance(curr_pos, pts, index);
        point_index_ = index;
        remain_time_ = remain_distance_ / std::fabs(vel_);
      }
      remain_time = remain_time_;
      if (remain_time < 1) remain_time = 1;
      remain_distance = remain_distance_;
      process = (task_distance - remain_distance) * 100 / task_distance;
      if (process <= 0) process = 0;
      if (process >= 100) process = 100;
      last_id_ = id;
    }
  }
  json["taskTotalDistance"] = task_distance;
  json["taskRemainDistance"] = remain_distance;
  json["taskRemainTime"] = remain_time;
  json["taskRunningProcess"] = process;
  return json;
}

std::string DataManager::GetBlueMac() { return system_info_.GetBlueMac(); }

void DataManager::UpdateEventCallback(
    const cotek_msgs::update_event::ConstPtr &msg) {
  GetResourceAgent()->SendUpdateEvent(msg);
}

void DataManager::TtrafficAreaCallback(
    const cotek_msgs::traffic_state::ConstPtr &msg) {
  // 1s 消费一次
  static ros::Time last_time = ros::Time::now();
  static bool init = false;
  if (!init) {
    last_time = ros::Time::now();
    init = true;
    GetResourceAgent()->SendTrafficArea(msg);
  }

  if (ros::Time::now() - last_time > ros::Duration(1.)) {
    last_time = ros::Time::now();
    GetResourceAgent()->SendTrafficArea(msg);
  }
}

static std::string GetLearningState(const int &state) {
  if (state == 1) return "starting";
  if (state == 2) return "preExtend";
  if (state == 3) return "extending";
  if (state == 4) return "preCover";
  if (state == 5) return "covering";
  return "none";
}

void DataManager::LearnStateCallback(
    const cotek_msgs::learning_state::ConstPtr &msg) {
  try {
    Json json, points_json, special_points_json;

    std::vector<double> x, y;
    x.reserve(msg->points.size());
    y.reserve(msg->points.size());
    for (auto &point : msg->points) {
      x.push_back(point.x);
      y.push_back(point.y);
      bool is_append = false;
      if (point.node >= 1 && point.node <= 2) {
        Json sp;
        sp["id"] = point.id;
        sp["pos"] = kNodePos[point.node];
        sp["type"] = kActionType[point.action];
        sp["x"] = point.x;
        sp["y"] = point.y;
        special_points_json.push_back(sp);
        is_append = true;
      }

      if (!is_append && point.type == 1) {
        if (point.action >= 0 && point.action < 13) {
          Json sp;
          sp["id"] = point.id;
          sp["pos"] = kNodePos[point.node];
          sp["type"] = kActionType[point.action];
          sp["x"] = point.x;
          sp["y"] = point.y;
          special_points_json.push_back(sp);
        }
      }
    }
    points_json["x"] = x;
    points_json["y"] = y;

    json["state"] = GetLearningState(msg->state);
    json["allowExtend"] = msg->allow_extend;
    json["points"] = points_json;
    json["specialPoints"] = special_points_json;
    json["taskId"] = msg->task_id;
    json["taskLoop"] = msg->task_loop;
    int total_loop = 0;
    if (!msg->task_id.empty()) {
      std::lock_guard<std::mutex> lck(task_mtx_);
      if (tasks_.find(msg->task_id) != tasks_.end()) {
        total_loop = tasks_[msg->task_id].loop;
      }
    }
    json["totalLoop"] = total_loop;

    if (msg->state > 0) {
      SetJson("learningState", json);
    } else {
      SetJson("pathStream", json);
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

void DataManager::TaskInfoCallback(const cotek_msgs::task_info::ConstPtr &msg) {
  if (!msg) {
    return;
  }
  std::map<std::string, task_t> temp_tasks;
  for (auto &task_info : msg->tasks) {
    task_t task;
    task.id = task_info.task_id;
    task.name = task_info.task_name;
    task.velocity_level = task_info.velocity_level;
    task.avoid_level = task_info.avoid_level;
    task.loop = task_info.loop;
    task.zone_id = task_info.zone_id;
    task.map_id = task_info.map_id;
    task.sn = task_info.sn;
    task.kuqu_id = task_info.kuqu_id;
    for (auto &node : task_info.points) {
      node_t pt;
      pt.id = node.id;
      pt.name = node.name;
      pt.x = node.x;
      pt.y = node.y;
      pt.angle = node.angle;
      pt.v = node.v;
      pt.cmd = node.cmd;
      pt.action = node.action;
      pt.type = node.type;
      pt.node = node.node;
      task.nodes.push_back(pt);
    }
    for (auto &node : task_info.actions) {
      node_t pt;
      pt.id = node.id;
      pt.name = node.name;
      pt.x = node.x;
      pt.y = node.y;
      pt.angle = node.angle;
      pt.v = node.v;
      pt.cmd = node.cmd;
      pt.action = node.action;
      pt.type = node.type;
      pt.node = node.node;
      task.actions.push_back(pt);
    }
    for (auto &edge : task_info.edges) {
      common::edge_t ed;
      ed.id = edge.id;
      ed.name = edge.name;
      ed.velocity_level = edge.velocity_level;
      ed.avoid_level = edge.avoid_level;
      for (const auto &point : edge.points) {
        node_t node;
        node.x = point.x;
        node.y = point.y;
        ed.points.push_back(node);
      }
      task.edges.push_back(ed);
    }
    for (auto &data : task_info.vertexs) {
      common::vertex_t vertex;
      vertex.id = data.id;
      vertex.x = data.x;
      vertex.y = data.y;
      task.kuqu.vertexs.push_back(vertex);
    }
    task.kuqu.storage.reserve(task_info.kuwei.size());
    for (int i = 0; i < task_info.kuwei.size(); i++) {
      task.kuqu.storage[i].center.id = task_info.kuwei[i].id;
      task.kuqu.storage[i].center.x = task_info.kuwei[i].x;
      task.kuqu.storage[i].center.y = task_info.kuwei[i].y;
    }
    task.kuqu.name = task_info.kuqu_name;
    task.kuqu.direction = task_info.kuqu_dir;
    task.kuqu.kwsize.width = task_info.width;
    task.kuqu.kwsize.length = task_info.length;
    task.kuqu.kwsize.rowspace = task_info.rowspace;
    task.kuqu.kwsize.columnspace = task_info.columnspace;
    temp_tasks[task.id] = task;
  }
  {
    std::lock_guard<std::mutex> lck(task_mtx_);
    tasks_ = temp_tasks;
    transform(*msg, task_info_);
  }
}

void DataManager::ResetRelocation() { relocations_.clear(); }

// 0-update 1-insert 2-delete 3-default
bool DataManager::SetRelocation(const relocation_t &data, int type /*= 0*/) {
  try {
    std::string file_path = BasicConfigHelper::Instance().GetTargetConfigPath(
        cotek_config::ConfigType::RELOCATION_LIST);

    switch (type) {
      case kRelocationUpdate: {
        if (relocations_.find(data.id) != relocations_.end()) {
          bool is_default = relocations_[data.id].is_default;
          relocations_[data.id] = data;
          relocations_[data.id].is_default = is_default;
        }
        break;
      }
      case kRelocationInsert: {
        relocations_[data.id] = data;
        break;
      }
      case kRelocationDelete: {
        auto iter = relocations_.find(data.id);
        if (iter != relocations_.end()) {
          relocations_.erase(iter);
        }
        break;
      }
      case kRelocationDefault: {
        if (relocations_.find(data.id) != relocations_.end()) {
          relocations_[data.id].is_default = true;
          for (auto &iter : relocations_) {
            if (iter.second.id != data.id) {
              iter.second.is_default = false;
            }
          }
        }
        break;
      }
      default:
        break;
    }
    nlohmann::ordered_json json;
    for (auto relocation : relocations_) {
      nlohmann::ordered_json relocation_json;
      relocation_json["id"] = relocation.second.id;
      relocation_json["name"] = relocation.second.name;
      relocation_json["x"] = relocation.second.x;
      relocation_json["y"] = relocation.second.y;
      relocation_json["z"] = relocation.second.z;
      relocation_json["yaw"] = relocation.second.angle;
      relocation_json["mapId"] = relocation.second.map_id;
      relocation_json["zoneSetid"] = relocation.second.zone_id;
      if (relocation.second.is_default) {
        json["default"].push_back(relocation_json);
      }
      json["reLocationList"].push_back(relocation_json);
    }
    if (relocations_.empty()) {
      json["reLocationList"] = nlohmann::json::array();
      json["default"] = nlohmann::json::array();
    }

    std::ofstream out_file(file_path);
    out_file << json.dump(4);
    LOG_INFO_STREAM("save relocation config(" << file_path << ") success");

    if (type == kRelocationDefault) {
      // last robot pose
      nlohmann::ordered_json json, pose_json;
      pose_json["x"] = relocations_[data.id].x;
      pose_json["y"] = relocations_[data.id].y;
      pose_json["z"] = relocations_[data.id].z;
      pose_json["yaw"] = angles::normalize_angle(relocations_[data.id].angle);
      json["last_robot_pose"] = pose_json;
      BasicConfigHelper::Instance().SaveLocal(
          cotek_config::ConfigType::LAST_ROBOT_POSE, json.dump(4));
      LOG_INFO_STREAM("save last robot pose(" << relocations_[data.id].x << ","
                                              << relocations_[data.id].y << ","
                                              << relocations_[data.id].angle
                                              << ") config success");

      // zone_id map_id
      std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::AGV_BASIC_CONFIG);

      std::regex zone_pattern(R"("current_map_id":\s*"\d+")");
      std::stringstream zone_id_stream;
      zone_id_stream << "\"current_map_id\": \"" << relocations_[data.id].map_id
                     << "\"";
      std::string new_config_json = std::regex_replace(
          basic_json_str, zone_pattern, zone_id_stream.str());

      std::regex map_pattern(R"("current_zone_id":\s*"\d+")");
      std::stringstream map_id_stream;
      map_id_stream << "\"current_zone_id\": \""
                    << relocations_[data.id].zone_id << "\"";
      new_config_json =
          std::regex_replace(new_config_json, map_pattern, map_id_stream.str());

      LOG_INFO_STREAM("agv_basic_config: " << new_config_json);

      BasicConfigHelper::Instance().SaveLocal(
          cotek_config::ConfigType::AGV_BASIC_CONFIG, new_config_json);
      LOG_INFO_STREAM("save agv_basic_config(map_id:"
                      << relocations_[data.id].map_id << ", zone_id:"
                      << relocations_[data.id].zone_id << ") success");
    }
  } catch (std::exception &e) {
    LOG_ERROR_STREAM("set relocation data exeption: " << e.what());
    return false;
  }
  return true;
}

// 0-update 1-insert 2-delete
bool DataManager::SetFloor(const Floor &data, int type /*= 0*/) {
  try {
    std::string file_path = BasicConfigHelper::Instance().GetTargetConfigPath(
        cotek_config::ConfigType::FLOOR_LIST);

    switch (type) {
      case kRelocationUpdate: {
        // if (floors_.find(data.id) != floors_.end()) {
        //   floors_[data.id] = data;
        // }
        Floor fl;
        std::string target_id = "";
        for (const auto& floor : floors_) {
          if (data.map_id == floor.second.map_id && 
              data.zone_id == floor.second.zone_id) {
            std::string id = floor.second.id;
            fl = data;
            fl.id = id;
            target_id = floor.second.id;
            break;
            // floors_[floor.second.id] = data;
            // floors_[floor.second.id].id = id;
          }
        }
        floors_[target_id] = fl;
        break;
      }
      case kRelocationInsert: {
        floors_[data.id] = data;
        break;
      }
      case kRelocationDelete: {
        auto iter = floors_.find(data.id);
        if (iter != floors_.end()) {
          LOG_WARN("erase id(%s) map_id(%s) floor(%s)", 
                   iter->second.id.c_str(), iter->second.map_id.c_str(), iter->second.floor.c_str());
          floors_.erase(iter);
        }
        break;
      }
      default:
        break;
    }
    nlohmann::ordered_json json;
    for (auto floor : floors_) {
      nlohmann::ordered_json floor_json;
      floor_json["id"] = floor.second.id;
      floor_json["floor"] = floor.second.floor;
      floor_json["map_id"] = floor.second.map_id;
      floor_json["zone_id"] = floor.second.zone_id;

      json["list"].push_back(floor_json);
    }
    if (floors_.empty()) {
      json["list"] = nlohmann::json::array();
    }

    std::ofstream out_file(file_path);
    out_file << json.dump(4);
    LOG_INFO_STREAM("save floor config(" << file_path << ") success");

  } catch (std::exception &e) {
    LOG_ERROR_STREAM("set floor data exeption: " << e.what());
    return false;
  }
  return true;
}

bool DataManager::GetRelocation(const std::string &id, relocation_t &data) {
  bool ret = false;
  if (id.empty()) {
    ret = RelocationInit();
  } else {
    if (relocations_.find(id) != relocations_.end()) {
      data = relocations_[id];
      ret = true;
    }
  }
  return ret;
}

bool DataManager::GetRelocation(const std::string &map_id,
                                std::vector<relocation_t> &datas) {
  if (map_id.empty()) {
    return false;
  } else {
    for (auto &relocation : relocations_) {
      if (relocation.second.map_id == map_id) {
        datas.push_back(relocation.second);
      }
    }
  }
  return true;
}

nlohmann::ordered_json DataManager::GetRelocation() {
  nlohmann::ordered_json json;
  for (auto relocation : relocations_) {
    nlohmann::ordered_json relocation_json;
    relocation_json["id"] = relocation.second.id;
    relocation_json["name"] = relocation.second.name;
    relocation_json["x"] = relocation.second.x;
    relocation_json["y"] = relocation.second.y;
    relocation_json["z"] = relocation.second.z;
    relocation_json["yaw"] = relocation.second.angle;
    relocation_json["mapId"] = relocation.second.map_id;
    relocation_json["zoneSetid"] = relocation.second.zone_id;
    if (relocation.second.is_default) {
      json["default"].push_back(relocation_json);
    }
    json["reLocationList"].push_back(relocation_json);
  }
  return json;
}

bool DataManager::GetFloor(const std::string &map_id,
                           std::vector<Floor> &datas) {
  if (map_id.empty()) {
    return false;
  } else {
    auto ret = FloorInit();
    for (auto &floor : floors_) {
      if (floor.second.map_id == map_id) {
        datas.push_back(floor.second);
      }
    }
  }
  return true;
}

// nlohmann::ordered_json DataManager::GetFloor() {
//   nlohmann::ordered_json json;
//   for (auto floor : floors_) {
//     nlohmann::ordered_json floor_json;
//     floor_json["mapId"] = floor.second.map_id;
//     floor_json["zoneSetid"] = floor.second.zone_id;
//     floor_json["floor"] = floor.second.zone_id;
//     floor_json["id"] = floor.second.id;

//     json["list"].push_back(floor_json);
//   }
//   return json;
// }

std::map<std::string, Floor> DataManager::GetFloor() {
  auto ret = FloorInit();
  return floors_;
}

void place_image(cv::Mat &dst, const cv::Mat &src, int x, int y) {
#if 1
  // 确保src可以放入dst中而不越界
  int rowsToAdd = std::max(0, y + src.rows - dst.rows);
  int colsToAdd = std::max(0, x + src.cols - dst.cols);
  cv::Rect roi = cv::Rect(x, y, std::min(src.cols, dst.cols - x),
                          std::min(src.rows, dst.rows - y));

  // 如果src比dst小或完全在dst内部，则复制src到dst的指定位置
  if (rowsToAdd == 0 && colsToAdd == 0) {
    src.copyTo(dst(roi));
  }
  // 注意：这里没有处理src大于dst的情况，因为那通常不是一个简单的“放置”操作
#else
  // 确保 PNG 图像有 alpha 通道（透明度）
  if (src.channels() == 4) {
    // 设置要贴图的位置
    cv::Point position(100, 100);  // 你想放置图像的位置
    cv::Rect roi(position.x, position.y, overlay.cols, overlay.rows);

    // 检查 ROI 是否在目标图像范围内
    if (roi.x >= 0 && roi.y >= 0 && roi.x + roi.width <= image.cols &&
        roi.y + roi.height <= image.rows) {
      for (int y = 0; y < overlay.rows; ++y) {
        for (int x = 0; x < overlay.cols; ++x) {
          uchar alpha = overlay.at<cv::Vec4b>(y, x)[3];  // 获取 alpha 值
          if (alpha > 0) {  // 如果不是完全透明
            // 将 RGB 值直接绘制到目标图像
            image.at<cv::Vec3b>(roi.y + y, roi.x + x) =
                overlay.at<cv::Vec4b>(y, x)(cv::Range(0, 3));
          }
        }
      }
    }
  } else {
    // 如果没有 alpha 通道，直接贴图
    cv::Point position(100, 100);  // 你想放置图像的位置
    overlay.copyTo(
        image(cv::Rect(position.x, position.y, overlay.cols, overlay.rows)));
  }
#endif
}

std::vector<uint8_t> DataManager::GetImage(const std::string &image,
                                           int type /*= kImageLocal*/) {
  if (images_.find(image) != images_.end()) {
    return images_[image];
  }
  std::vector<uint8_t> data = {0};
  switch (type) {
    case kImageLocal: {
      std::string file_dir =
          BasicConfigHelper::Instance().GetConfigPath() + "/images";
      std::string path = file_dir + "/" + image + ".png";
      try {
        std::ifstream image_file(path, std::ios::binary);
        if (!image_file) {
          LOG_ERROR("load image file(%s)failed!", path.c_str());
        }
        data =
            std::vector<uint8_t>((std::istreambuf_iterator<char>(image_file)),
                                 std::istreambuf_iterator<char>());
        images_[image] = data;
      } catch (std::exception &e) {
        LOG_ERROR("load image exeption: %s", e.what());
      }
      break;
    }
    case kImageMap: {
      try {
        // 1. 读取pgm图像
        cv::Mat gray_image = cv::imread(image, cv::IMREAD_GRAYSCALE);
        if (gray_image.empty()) {
          throw std::runtime_error(std::string("read map image exception"));
        }

        // 2. 将图像转换为 BGRA 以便有一个 alpha 通道
        cv::Mat bgra_image;
        cv::cvtColor(gray_image, bgra_image, cv::COLOR_GRAY2BGRA);

        // 3. 遍历图像的每个像素并更新 alpha 通道
        uchar kGrayValue = 128;
        for (int y = 0; y < bgra_image.rows; ++y) {
          for (int x = 0; x < bgra_image.cols; ++x) {
            cv::Vec4b &pixel = bgra_image.at<cv::Vec4b>(y, x);
            if (pixel[0] == kGrayValue) {
              pixel[3] = 0;  // 设置 alpha 通道为 0（完全透明）
            }
          }
        }

        // 4. 将 cv::Mat 转换为 PNG 格式的内存缓冲区 PNG 格式，9最高压缩
        std::vector<int> params = {cv::IMWRITE_PNG_COMPRESSION, 1};
        cv::imencode(".png", bgra_image, data, params);
        images_[image] = data;
      } catch (const std::exception &ex) {
        LOG_ERROR_STREAM("load map image exception: " << ex.what());
      }
      break;
    }
    case kImageRelocation:
    case kImagePath: {
      try {
        // 1. 获取任务点 和 计算最大/最小的x/y坐标
        int scale = 100;
        float img_scale = 1;
        float max_int = std::numeric_limits<float>::max();     // 最大值
        float min_int = std::numeric_limits<float>::lowest();  // 最小值
        double min_x(max_int), max_x(min_int), min_y(max_int), max_y(min_int);
        bool is_kuqu = false;
        std::vector<common::node_t> nodes;
        std::vector<common::vertex_t> vertexs;
        std::vector<common::vertex_t> kuwei;
        for (auto &task : tasks_) {
          if (type == kImageRelocation ||
              (type == kImagePath && image == task.first)) {
            if (task.second.kuqu_id == -1) {
              for (auto &node : task.second.nodes) {
                if (node.x < min_x) min_x = node.x;
                if (node.x > max_x) max_x = node.x;
                if (node.y < min_y) min_y = node.y;
                if (node.y > max_y) max_y = node.y;
                if (&node == &task.second.nodes.front()) {
                  node.node = 1;
                }
                if (&node == &task.second.nodes.back()) {
                  node.node = 2;
                }
                nodes.push_back(node);
              }
            } else {
              is_kuqu = true;
              vertexs = task.second.kuqu.vertexs;
              for (const auto &vertex : task.second.kuqu.vertexs) {
                if (vertex.x < min_x) min_x = vertex.x;
                if (vertex.x > max_x) max_x = vertex.x;
                if (vertex.y < min_y) min_y = vertex.y;
                if (vertex.y > max_y) max_y = vertex.y;                
              }

              for (const auto &storage : task.second.kuqu.storage) {
                common::vertex_t vertex;
                vertex.id = storage.center.id;
                vertex.x = storage.center.x;
                vertex.y = storage.center.y;             
                kuwei.push_back(vertex);
              }
            }
          }
        }

        if (type == kImageRelocation && !image.empty()) {
          common::node_t node;
          relocation_t pt;
          if (DataManager::Instance().GetRelocation(image, pt)) {
            node.id = pt.id;
            node.name = pt.name;
            node.x = pt.x;
            node.y = pt.y;
            node.angle = pt.angle;
            node.type = 3;  //定位点 下面绘制使用
            if (node.x < min_x) min_x = node.x;
            if (node.x > max_x) max_x = node.x;
            if (node.y < min_y) min_y = node.y;
            if (node.y > max_y) max_y = node.y;
            nodes.push_back(node);
          }
        }

        // 4. 绘制图像
        // 计算缩放比例
        int target_w = 164, target_h = 164;
        int margin = 0;
        float scaleX = (target_w - 2 * margin) / (max_x - min_x);
        float scaleY = (target_h - 2 * margin) / (max_y - min_y);
        scale = std::min(scaleX, scaleY) * 10;
        img_scale = scale / 10.0;
        // printf("(min_x, max_x, min_y, max_y) = (%.1f, %.1f, %.1f, %.1f)\n",
        // min_x, max_x, min_y, max_y); printf("(scaleX, scaleX, scale) = (%.1f,
        // %.1f, %d)\n", scaleX, scaleY, scale);
        int width = 800, height = 800;
        height = scale * (max_x - min_x) + 120;
        width = scale * (max_y - min_y) + 120;
        // printf("(w, h) = (%d, %d)\n", width, height);
        cv::Mat task_image(width, height, CV_8UC4,
                            cv::Scalar(0, 0, 0, 0));  // 初始为全透明

        if (!is_kuqu) {
          int index = 0, begin = 0;
          for (auto &node : nodes) {
            index++;
            double x = scale * (node.x - min_x) + 40;
            double y = scale * (node.y - min_y) + 40;
            cv::Point2d pos(x, y);
            // printf("(%.3f, %.3f)->(%.3f, %.3f)\n", node.x, node.y, x, y);
            if (node.type == 3) {
              std::vector<uchar> buffer = images_["location"];
              if (!buffer.empty()) {
                cv::Mat overlay = cv::imdecode(buffer, cv::IMREAD_UNCHANGED);
                cv::flip(overlay, overlay, 0);  // 0 表示垂直翻转
                cv::resize(overlay, overlay,
                          cv::Size(overlay.cols / 2, overlay.rows / 2));
                if (!overlay.empty()) {
                  place_image(task_image, overlay, pos.x - overlay.cols / 4,
                              pos.y - overlay.rows / 4);
                }
              }
            } else if (node.type == 1 || node.node > 0) {
              std::vector<uchar> buffer{0};
              if (node.action == 1) {
                buffer = images_["load"];
              } else if (node.action == 2) {
                buffer = images_["unload"];
              } else if (node.action == 3) {
                buffer = images_["parking"];
              } else if (node.action == 4) {
                buffer = images_["confirm"];
              } else if (node.action == 5) {
                buffer = images_["charge"];
              } else if (node.action == 6) {
                buffer = images_["lieunload"];
              } else if (node.action == 7) {
                buffer = images_["opendoor"];
              } else if (node.action == 8) {
                buffer = images_["closedoor"];
              } else if (node.action == 9) {
                buffer = images_["calldt"];
              } else if (node.action == 10) {
                buffer = images_["takedt"];
              } else if (node.action == 11) {
                buffer = images_["leavedt"];
              }

              if (node.action == 3 || node.action == 0) {
                if ((node.node == 1) && (&node == &nodes.front())) {
                  buffer = images_["start"];
                } else if ((node.node == 2)) {
                  buffer = images_["end"];
                }
              }
              if (!buffer.empty()) {
                cv::Mat overlay = cv::imdecode(buffer, cv::IMREAD_UNCHANGED);
                cv::flip(overlay, overlay, 0);  // 0 表示垂直翻转
                cv::resize(overlay, overlay,
                          cv::Size(overlay.cols / 2, overlay.rows / 2));
                if (!overlay.empty()) {
                  place_image(task_image, overlay, pos.x - overlay.cols / 4,
                              pos.y - overlay.rows / 4);
                }
              }
              if ((node.type == 1 || node.node == 2) && type == kImagePath) {
                double angle = 0;
                cv::Point2d start(pos), end(pos);
                if (index - begin >= 4) {
                  int mid = (index + begin) / 2;
                  start = cv::Point2d(scale * (nodes[mid - 1].x - min_x) + 40,
                                      scale * (nodes[mid - 1].y - min_y) + 40);
                  end = cv::Point2d(scale * (nodes[mid + 1].x - min_x) + 40,
                                    scale * (nodes[mid + 1].y - min_y) + 40);
                  angle = 1.57 - nodes[mid + 1].angle;
                } else {
                  start = cv::Point2d(scale * (nodes[begin - 1].x - min_x) + 40,
                                      scale * (nodes[begin - 1].y - min_y) + 40);
                  end = pos;
                }
                cv::Scalar fill_clr(222, 170, 18, 255);
                // cv::Scalar fill_clr(18, 0, 222, 255);
                // cv::line(task_image, start, end, fill_clr, 8);
                if (index - begin > 10) {
                  cv::arrowedLine(task_image, start, end, fill_clr, 8, cv::LINE_8,
                                  0, 3);
                }

#if 0
                // 绘制实心箭头（三角形部分）
                double arrowLength = 100; // 箭头长度
                double arrowWidth = 8;   // 箭头宽度
                cv::Point arrow_tip0(
                  start.x + std::cos(angle) * 100,
                  start.y + std::sin(angle) * 100); // 200是箭头的长度
                //cv::Point arrow_tip0(end.x, end.y);
                cv::Point arrow_tip1(
                  end.x - arrowLength * std::cos(angle - CV_PI / 6),
                  end.y - arrowLength * std::sin(angle - CV_PI / 6));
                cv::Point arrow_tip2(
                  end.x - arrowLength * std::cos(angle - CV_PI / 6),
                  end.y + arrowLength * std::sin(angle - CV_PI / 6));
                std::vector<cv::Point> triangle = {arrow_tip0, arrow_tip1, arrow_tip2};
                cv::fillConvexPoly(task_image, triangle, fill_clr);
#endif

                begin = index;
              }
            } else {
              // 蓝色圆点 半径5 alpha=255
              cv::Scalar fill_clr(222, 170, 18, 255);
              cv::circle(task_image, pos, 6, fill_clr, cv::FILLED);
            }
          }
          // 4. 将 cv::Mat 转换为 PNG 格式的内存缓冲区 PNG 格式，9最高压缩
          std::vector<int> params = {cv::IMWRITE_PNG_COMPRESSION, 9};
          cv::flip(task_image, task_image, 0);  // 0 表示垂直翻转
          cv::imencode(".png", task_image, data, params);

        } else {
          for (int i=1; i<vertexs.size(); i++) {
            double x1 = scale * (vertexs[i].x - min_x) + 40;
            double y1 = scale * (vertexs[i].y - min_y) + 40;
            cv::Point2d pos1(x1, y1);

            double x2 = scale * (vertexs[i-1].x - min_x) + 40;
            double y2 = scale * (vertexs[i-1].y - min_y) + 40;
            cv::Point2d pos2(x2, y2);

            cv::Scalar fill_clr(222, 170, 18, 255);
            cv::line(task_image, pos1, pos2, fill_clr, 2, cv::LINE_AA);

            if (i == vertexs.size()-1) {
              x2 = scale * (vertexs[0].x - min_x) + 40;
              y2 = scale * (vertexs[0].y - min_y) + 40;
              pos2 = cv::Point2d(x2, y2);
              cv::line(task_image, pos1, pos2, fill_clr, 2, cv::LINE_AA);
            }
          }

          std::map<int, std::vector<common::storage_t>> lie_kw;

          for (const auto& data : kuwei) {
            size_t pos = data.id.find('-');
            if (pos != std::string::npos) {
              common::storage_t storage;
              std::string column = data.id.substr(0, pos);  // 获取 "-" 前部分
              std::string row = data.id.substr(pos+1);   // 获取 "-" 前部分
              storage.id = data.id;
              storage.column = std::stoi(column);
              storage.row = std::stoi(row);
              storage.center.x = data.x;
              storage.center.y = data.y;
              lie_kw[std::stoi(column)].push_back(storage);       // 分类存储
            }
          }

          for (const auto &lie : lie_kw) {
            if (lie.second.size() > 1) {
                common::storage_t start;
                common::storage_t end;
                int max_row = 1;
                for (const auto &storage : lie.second) {
                    if (storage.row > max_row) {
                        max_row = storage.row;
                        end = storage;
                    }
                    if (storage.row == 1) {
                        start = storage;
                    }
                }
                double x1 = scale * (start.center.x - min_x) + 40;
                double y1 = scale * (start.center.y - min_y) + 40;
                cv::Point2d pos1(x1, y1);

                double x2 = scale * (end.center.x - min_x) + 40;
                double y2 = scale * (end.center.y - min_y) + 40;
                cv::Point2d pos2(x2, y2);

                cv::Scalar fill_clr(39, 127, 255, 255);
                // cv::Scalar fill_clr(4, 0, 255, 255);
                cv::line(task_image, pos1, pos2, fill_clr, 5, cv::LINE_AA);
            }
          }

          for (const auto &data : kuwei) {
            double x = scale * (data.x - min_x) + 40;
            double y = scale * (data.y - min_y) + 40;
            cv::Point2d pos(x, y);

            std::vector<uchar> buffer = images_["kuwei"];
            if (!buffer.empty()) {
              cv::Mat overlay = cv::imdecode(buffer, cv::IMREAD_UNCHANGED);
              cv::flip(overlay, overlay, 0);  // 0 表示垂直翻转
              cv::resize(overlay, overlay,
                        cv::Size(overlay.cols / 2, overlay.rows / 2));
              if (!overlay.empty()) {
                place_image(task_image, overlay, pos.x - overlay.cols / 4,
                            pos.y - overlay.rows / 4);
              }
            }
          }

          for (int i=0; i<vertexs.size(); i++) {
            double x1 = scale * (vertexs[i].x - min_x) + 40;
            double y1 = scale * (vertexs[i].y - min_y) + 40;
            // 在点周围画一个半径为 5 的红色圆圈
            cv::circle(task_image, 
                        cv::Point(static_cast<int>(x1), static_cast<int>(y1)), 
                        20, 
                        cv::Scalar(255, 0, 0, 255),
                        5);  // 线宽
          }

          // 4. 将 cv::Mat 转换为 PNG 格式的内存缓冲区 PNG 格式，9最高压缩
          std::vector<int> params = {cv::IMWRITE_PNG_COMPRESSION, 9};
          cv::flip(task_image, task_image, 0);  // 0 表示垂直翻转

          for (int i=0; i<vertexs.size(); i++) {
            int img_height = task_image.rows;
            // 原始逻辑坐标（未翻转前）
            double x1 = scale * (vertexs[i].x - min_x) + 40;
            double y1 = scale * (vertexs[i].y - min_y) + 40;

            // 翻转后，y 坐标需要镜像处理
            cv::Point2d pos1(x1, img_height - y1); // y 轴翻转
            // 添加角标
            std::string label = vertexs[i].id;
            cv::Point2d text_pos = pos1 + cv::Point2d(10, -10);
            cv::putText(task_image, label, text_pos, cv::FONT_HERSHEY_SIMPLEX,
                        2, cv::Scalar(0, 0, 255, 255), 3, cv::LINE_AA);
          }

          cv::imencode(".png", task_image, data, params);
        }

#if 0
      //绘制机器人
      std::vector<uchar> buffer = images_["robot"];
      if (!buffer.empty()) {
        auto &node = nodes[nodes.size()/2];
        cv::Point2d pos(node.x, node.y);
        cv::Mat overlay = cv::imdecode(buffer, cv::IMREAD_UNCHANGED);
        cv::flip(overlay, overlay, 0); // 0 表示垂直翻转
        cv::resize(overlay, overlay, cv::Size(overlay.cols/2, overlay.rows/2));
        if (!overlay.empty()) {
          place_image(task_image, overlay, pos.x-overlay.cols/4, pos.y-overlay.rows/4);
        }
      }
#endif

        // 4. 将 cv::Mat 转换为 PNG 格式的内存缓冲区 PNG 格式，9最高压缩
        // std::vector<int> params = {cv::IMWRITE_PNG_COMPRESSION, 9};
        // cv::flip(task_image, task_image, 0);  // 0 表示垂直翻转
        // cv::imencode(".png", task_image, data, params);
      } catch (const std::exception &ex) {
        LOG_ERROR_STREAM("draw task/relocation image("
                         << image << ") exception: " << ex.what());
      }
      images_[image] = data;
      break;
    }
    default:
      break;
  }
  return data;
}

bool DataManager::RefreshImage(const std::string &id) {
  if (!id.empty()) {
    for (auto it = images_.begin(); it != images_.end();) {
      if (id == it->first) {
        it = images_.erase(it);
        break;
      } else {
        it++;
      }
    }
  } else {
    for (auto it = images_.begin(); it != images_.end();) {
      bool is_find = false;
      for (auto &image : kImages) {
        if (image == it->first) {
          is_find = true;
          break;
        }
      }
      if (!is_find) {
        it = images_.erase(it);
      } else {
        ++it;
      }
    }
  }
  return true;
}

}  // namespace cotek_communicate
