/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "node/cotek_communicate_node.h"

#include <ros/ros.h>

#include "cotek_common/cotek_node_name.h"

int main(int argc, char **argv) {
  ros::init(argc, argv, cotek_node::kCommunicateNode);

  // Set ros log level:
  if (ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME,
                                     ros::console::levels::Info)) {
    ros::console::notifyLoggerLevelsChanged();
  }

  CommunicateNode node;

  ros::Rate rate(0.3);
  ros::AsyncSpinner s(1);
  s.start();
  while (!node.Init() && ros::ok()) {
    LOG_ERROR_THROTTLE(1, "cotek_communicate_node init failed. retry");
    rate.sleep();
  }
  ros::waitForShutdown();

  return 0;
}
