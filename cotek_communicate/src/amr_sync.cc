/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate/amr_sync.h"

#include <Poco/Net/HTTPClientSession.h>
#include <Poco/Net/HTTPRequest.h>
#include <Poco/Net/HTTPResponse.h>
#include <Poco/Net/NetException.h>
#include <Poco/Net/WebSocket.h>
#include <sys/types.h>

#include <boost/geometry.hpp>
#include <boost/geometry/geometries/point_xy.hpp>
#include <exception>
#include <iostream>
#include <memory>
#include <mutex>
#include <ostream>
#include <string>
#include <thread>

#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_communicate/data_manager.h"
#include "cotek_msgs/front_car.h"
#include "eigen3/Eigen/Dense"
#include "ros/duration.h"

namespace cotek_communicate {

namespace {
namespace bg = boost::geometry;
namespace btf = bg::strategy::transform;

using BoostPoint = bg::model::d2::point_xy<double>;
using BoostBox = bg::model::box<BoostPoint>;
using Polygon = bg::model::polygon<BoostPoint, true, true>;

}  // namespace

static uint8_t TransformCurAmrType(const std::string& type,
                                   const std::string& agv_state) {
  LOG_INFO_STREAM_THROTTLE(
      1, "Transform cur type: " << type << "AgvState type: " << agv_state);
  if (type.empty()) return 0;
  if (type == "syncBackward") return 1;
  if (type == "syncForward") return 2;
  if (type == "limitUnLoad") return 3;
  if (type == "rest") return 3;

  return 0;
}

// static uint8_t TransformForwardAmrType(const std::string& type,
//                                        const std::string& agv_state) {
//   LOG_INFO_STREAM_THROTTLE(
//       1, "Transform  forward type: " << type << "AgvState type: " <<
//       agv_state);
//   if (type.empty()) return 0;
//   if (type == "syncBackward" && agv_state == "Doing") return 1;
//   if (type == "syncBackward" && agv_state == "Finishing") return 2;
//   if (type == "syncForward") return 2;
//   if (type == "limitUnLoad") return 3;
//   if (type == "rest") return 3;

//   return 0;
// }

static uint8_t TransformForwardAmrType(const std::string& type,
                                       const AmrSyncData& front_amr,
                                       const AmrSyncData& cur_amr) {
  LOG_INFO_STREAM_THROTTLE(1, "Transform  front action type: "
                                  << type
                                  << "AgvState type: " << front_amr.agv_state);
  if (type.empty()) return 0;
  if (type == "syncBackward" && front_amr.agv_state == "Doing") return 1;
  if (type == "syncBackward" && front_amr.agv_state == "Finishing") return 2;
  if (type == "syncForward") return 2;

  Eigen::Vector3d cur_unit_yaw;
  Eigen::Vector3d cur_to_front;
  cur_unit_yaw << std::cos(cur_amr.pose.theta()),
      std::sin(cur_amr.pose.theta()), 0.;

  cur_to_front << front_amr.pose.x() - cur_amr.pose.x(),
      front_amr.pose.y() - cur_amr.pose.y(), 0.;

  double forward_dist = std::fabs(cur_to_front.dot(cur_unit_yaw));
  LOG_INFO_STREAM_THROTTLE(1, "Forward dist" << forward_dist);

  if (type == "limitUnLoad" && forward_dist > 2.0) {
    return 3;
  } else if (type == "limitUnLoad") {
    return 2;
  }

  if (type == "rest" && forward_dist > 2.0) {
    return 3;
  } else if (type == "rest") {
    return 2;
  }

  return 0;
}

static AmrSyncData GetCurrentAmrData() {
  AmrSyncData cur_data;
  std::string action_type;
  auto&& json = DataManager::Instance().GetJson("endnode");

  if (!json["endnode"]["actions"].empty()) {
    for (auto& action : json["endnode"]["actions"]) {
      action_type = action["actionType"].get<std::string>();
    }
  }

  auto&& state = DataManager::Instance().GetJson("agvState");
  std::string agv_state = state["agvState"].get<std::string>();

  auto&& position = DataManager::Instance().GetJson("agvPosition");

  Pose agv_pose;
  int init = position["agvPosition"]["positionInitialized"].get<int>();
  agv_pose.set(position["agvPosition"]["x"].get<double>(),
               position["agvPosition"]["y"].get<double>(),
               position["agvPosition"]["theta"].get<double>());
  cur_data.vaild = init > 0 ? true : false;
  cur_data.pose = agv_pose;
  cur_data.agv_state = agv_state;
  cur_data.type = TransformCurAmrType(action_type, cur_data.agv_state);

  LOG_INFO_STREAM_THROTTLE(
      1, "Cur amr vaild: " << cur_data.vaild << " x: " << cur_data.pose.x()
                           << " y: " << cur_data.pose.y()
                           << " theta: " << cur_data.pose.theta()
                           << " type: " << cur_data.type
                           << " state: " << cur_data.agv_state);

  return cur_data;
}

AmrSync::AmrSync(const AmrSyncInfo& info)
    : sync_ing_(false), amr_info_(info), thread_pool_(1) {
  start_time_ = ros::Time::now();
  DataSync();
}

AmrSync::~AmrSync() {}

bool AmrSync::Stop() {
  stop_web_sync_ = true;
  if (!sync_ing_) return true;
  return false;
}

bool AmrSync::AmrShouldDelete() {
  bool should_delete = false;
  if (IsTimieout()) return true;
  if (IsOffline()) return true;
  if (IsNotSyncState()) return true;
  if (IsOutArea()) return true;
  return false;
}

bool AmrSync::IsNotSyncState() {
  if (amr_data_.vaild && amr_data_.agv_state == "Manual") {
    LOG_INFO_THROTTLE(1, "Manual should exit sync!!!");
    return true;
  }

  if (amr_data_.vaild && amr_data_.agv_state == "Error") {
    LOG_INFO_THROTTLE(1, "Error should exit sync!!!");
    return true;
  }

  if (amr_data_.vaild && amr_data_.agv_state == "Fault") {
    LOG_INFO_THROTTLE(1, "Fault should exit sync!!!");
    return true;
  }

  auto&& state = DataManager::Instance().GetJson("agvState");
  std::string agv_state = state["agvState"].get<std::string>();
  if (agv_state == "Manual") {
    LOG_INFO_THROTTLE(1, "Cur car Manual should exit sync!!!");
    return true;
  }

  return false;
}

bool AmrSync::IsOffline() {
  if (sync_failed_cnt_ > 5) {
    LOG_WARN_STREAM("amr " << amr_info_.ip << " offline");
    return true;
  }
  return false;
}

bool AmrSync::IsOutArea() {
  if (pose_failed_cnt_ > 40) {
    LOG_WARN_STREAM("amr " << amr_info_.ip << " out of area");
    return true;
  }
  return false;
}

bool AmrSync::IsTimieout() {
  if (ros::Time::now() - start_time_ > ros::Duration(180.)) {
    LOG_WARN_STREAM("amr " << amr_info_.ip << " timeout");
    return true;
  }
  return false;
}

void AmrSync::DataSync() {
  LOG_INFO_STREAM_THROTTLE(1, "Build data sync...");
  auto no_block = [&]() { BuildDataChannel(); };

  thread_pool_.enqueue(no_block);
}

bool AmrSync::IsInSyncArea() {
  if (!amr_data_.vaild) return false;
  BoostPoint point1(12., -83.);
  BoostPoint point2(44., -23.);
  BoostBox box1(point1, point2);

  BoostPoint point3(61., -111.);
  BoostPoint point4(120., -100.);
  BoostBox box2(point3, point4);

  BoostPoint robot_point(amr_data_.pose.x(), amr_data_.pose.y());

  if (bg::within(robot_point, box1)) {
    LOG_WARN_THROTTLE(1, "Robot within 1 sync area!!!");
    pose_failed_cnt_ = 0;
    return true;
  }

  if (bg::within(robot_point, box2)) {
    LOG_WARN_THROTTLE(1, "Robot within 2 sync area!!!");
    pose_failed_cnt_ = 0;
    return true;
  }

  LOG_WARN_STREAM_THROTTLE(1, "(" << amr_data_.pose.x() << ","
                                  << amr_data_.pose.y() << ")"
                                  << " in not in area!!!");
  pose_failed_cnt_++;
  return false;
}

void AmrSync::BuildDataChannel() {
  //   Poco::Net::HTTPClientSession session(amr_info_.ip, amr_info_.port);

  //   // 设置连接和接收超时
  //   Poco::Timespan connectTimeout(5, 0);  // 5秒连接超时
  //   Poco::Timespan receiveTimeout(5, 0);  // 5秒接收超时
  //   session.setConnectTimeout(connectTimeout);
  //   session.setReceiveTimeout(receiveTimeout);
  //   Poco::Net::HTTPRequest request(Poco::Net::HTTPRequest::HTTP_GET,
  //                                  "/agv/amrSync");
  //   Poco::Net::HTTPResponse response;

  //   { sync_ing_ = true; }

  //   try {
  //     Poco::Net::WebSocket ws(session, request, response);
  //     char buffer[1024];
  //     int flags = 0;
  //     int n = 0;
  //     // 接收数据
  //     n = ws.receiveFrame(buffer, sizeof(buffer), flags);

  //     while (ros::ok() && !stop_web_sync_ &&
  //            (flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
  //                Poco::Net::WebSocket::FRAME_OP_CLOSE) {
  //       n = ws.receiveFrame(buffer, sizeof(buffer), flags);

  //       //判断网络连接是否断开
  //       if (n == 0 && flags == 0) break;

  //       if (n > 0) {
  //         std::string receivedData(buffer, n);

  //         nlohmann::json json = nlohmann::json::parse(receivedData);

  //         // LOG_WARN_THROTTLE(1, json.dump(4));
  //         Pose agv_pose;
  //         int init = json["agvPosition"]["positionInitialized"].get<int>();
  //         agv_pose.set(json["agvPosition"]["x"].get<double>(),
  //                      json["agvPosition"]["y"].get<double>(),
  //                      json["agvPosition"]["theta"].get<double>());

  //         std::string agv_state = json["agvState"].get<std::string>();

  //         double velocity = json["moveFeedback"]["vx"].get<double>();
  //         std::string action_type;

  //         if (!json["endnode"]["actions"].empty()) {
  //           for (auto& action : json["endnode"]["actions"]) {
  //             action_type = action["actionType"].get<std::string>();
  //           }
  //         }

  //         amr_data_.agv_state = agv_state;

  //         amr_data_.pose = agv_pose;
  //         amr_data_.velocity = velocity;
  //         amr_data_.vaild = true;

  //         AmrSyncData cur_data = GetCurrentAmrData();

  //         LOG_INFO_STREAM_THROTTLE(
  //             1, "Cur amr vaild: " << cur_data.vaild
  //                                  << " x: " << cur_data.pose.x()
  //                                  << " y: " << cur_data.pose.y()
  //                                  << " theta: " << cur_data.pose.theta()
  //                                  << " type: " << cur_data.type
  //                                  << " state: " << cur_data.agv_state);

  //         amr_data_.type =
  //             TransformForwardAmrType(action_type, amr_data_, cur_data);
  //       }
  //       std::this_thread::sleep_for(std::chrono::milliseconds(50));
  //     }

  //     LOG_WARN_THROTTLE(1, "Connection closed.");

  //   }

  //   catch (Poco::Net::WebSocketException& exc) {
  //     LOG_ERROR(exc.what());
  //     switch (exc.code()) {
  //       case Poco::Net::WebSocket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
  //         response.set("Sec-PocoWebSocket-Version",
  //                      Poco::Net::WebSocket::WEBSOCKET_VERSION);
  //         // fallthrough
  //       case Poco::Net::WebSocket::WS_ERR_NO_HANDSHAKE:
  //       case Poco::Net::WebSocket::WS_ERR_HANDSHAKE_NO_VERSION:
  //       case Poco::Net::WebSocket::WS_ERR_HANDSHAKE_NO_KEY:
  //         response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
  //         break;
  //     }
  //   } catch (std::exception ex) {
  //     LOG_ERROR(ex.what());

  //   } catch (...) {
  //     LOG_ERROR("Catch fault!!!");
  //   }

  //   sync_ing_ = false;
  //   sync_failed_cnt_++;
}

AmrSyncManager::AmrSyncManager() {
  ros::NodeHandle nh;
  data_pub_ = nh.advertise<cotek_msgs::front_car>("front_car", 2);
  executor_ =
      std::make_shared<std::thread>(std::bind(&AmrSyncManager::Run, this));
}

AmrSyncManager::~AmrSyncManager() {
  if (executor_) {
    executor_->join();
    executor_ = nullptr;
  }
}

bool AmrSyncManager::CreateSyncAmr(const AmrSyncInfo& info) {
  try {
    std::unique_lock<std::mutex> lock(mutex_);

    if (sync_amrs_.count(info.ip) == 0 && sync_amrs_.empty()) {
      sync_amrs_[info.ip] = std::make_shared<AmrSync>(info);
      LOG_INFO_STREAM("Create sync amr " << info.ip);
      return true;
    }
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }

  LOG_WARN_STREAM("Amr already exist!!!" << info.ip);
  return false;
}

bool AmrSyncManager::DeleteSyncAmr(const AmrSyncInfo& info) {
  try {
    std::unique_lock<std::mutex> lock(mutex_);

    if (sync_amrs_.count(info.ip) > 0) {
      if (sync_amrs_[info.ip]->Stop()) {
        sync_amrs_.erase(info.ip);
        LOG_INFO_STREAM("Erease sync amr " << info.ip);
        return true;
      }
    }
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }

  LOG_WARN_STREAM("Amr no exist cant delete!!!" << info.ip);
  return false;
}

void AmrSyncManager::Run() {
  ros::Rate rate(20);
  while (ros::ok()) {
    AmrSyncInfo delete_amr;
    cotek_msgs::front_car final_msg;
    std::unique_lock<std::mutex> lock(mutex_);
    for (auto& amr : sync_amrs_) {
      if (!amr.second->GetSyncFlag() && !amr.second->GetStopWebSync()) {
        // 间隔1s重新建立数据同步通道
        static ros::Time last_time = ros::Time::now();
        if (ros::Time::now() - last_time > ros::Duration(1.0)) {
          amr.second->DataSync();
          last_time = ros::Time::now();
        }
      }

      if (amr.second->IsInSyncArea()) {
        {
          auto&& data = amr.second->GetData();
          cotek_msgs::front_car msg;
          msg.id = amr.second->GetInfo().ip;
          msg.x = data.pose.x();
          msg.y = data.pose.y();
          msg.theta = data.pose.theta();
          msg.v = data.velocity;
          msg.front_car_state = data.type;

          std::string action_type;

          auto&& json = DataManager::Instance().GetJson("endnode");

          if (!json["endnode"]["actions"].empty()) {
            for (auto& action : json["endnode"]["actions"]) {
              action_type = action["actionType"].get<std::string>();
            }
          }
          auto&& state = DataManager::Instance().GetJson("agvState");
          std::string agv_state = state["agvState"].get<std::string>();
          msg.current_car_state = TransformCurAmrType(action_type, agv_state);

          LOG_INFO_STREAM_THROTTLE(
              1, "ip: " << msg.id << " x: " << msg.x << " y: " << msg.y
                        << " cur_state: " << msg.current_car_state
                        << " front_car_state: " << msg.front_car_state);

          // 数据有效才发送
          if (data.vaild) final_msg = msg;
        }
      }

      if (amr.second->AmrShouldDelete()) {
        delete_amr = amr.second->GetInfo();
      }
    }
    lock.unlock();

    // 删除同步的amr
    if (!delete_amr.ip.empty()) {
      DeleteSyncAmr(delete_amr);
    }

    data_pub_.publish(final_msg);

    rate.sleep();
    rate.reset();
  }
}

}  // namespace cotek_communicate
