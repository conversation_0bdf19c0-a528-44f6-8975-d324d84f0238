/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate/communicate.h"

#include <exception>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>
#include <tf/transform_listener.h>
#include "tf/transform_broadcaster.h"
#include <std_msgs/String.h>

#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/util/remote_service.h"
#include "cotek_communicate/data_manager.h"
#include "cotek_communicate/language.h"
#include "cotek_msgs/action.h"
#include "cotek_msgs/calibration.h"
#include "cotek_msgs/control_point.h"
#include "cotek_msgs/edge.h"
#include "cotek_msgs/instant_action.h"
#include "cotek_msgs/node.h"
#include "cotek_msgs/node_position.h"
#include "cotek_msgs/order.h"
#include "cotek_msgs/query_info.h"
#include "cotek_msgs/query_storage_info.h"
#include "cotek_msgs/switch_map.h"
#include "cotek_msgs/trajectory.h"
#include "ros/duration.h"
#include "ros/init.h"
#include "ros/node_handle.h"
#include "ros/rate.h"
#include "ros/time.h"

namespace cotek_communicate {

namespace {
constexpr int kLinkCheckTime = 5;
constexpr char kBatteryInfoRemoteId[] = "BatteryInfo";

static int logLevelToString(const std::string &level) {
  if (level == "INFO") return 0;
  if (level == "WARNING") return 1;
  if (level == "ERROR") return 2;
  if (level == "FATAL") return 3;
  return 0;
}

}  // namespace

bool CycleEventCenter::Start() {
  runner_ptr_ = std::make_unique<std::thread>(
      std::bind(&CycleEventCenter::CycleEvent, this));
  return true;
}

void CycleEventCenter::CycleEvent() {
  try {
    ros::Rate rate(20);
    while (ros::ok()) {
      std::unique_lock<std::mutex> lock(mutex_);
      auto map_ = time_event_cb_map_;
      lock.unlock();

      for (auto &time_event : map_) {
        if (ros::Time::now() - time_event.second.time < ros::Duration(60.)) {
          time_event.second.cb(time_event.first);
        }
      }
      rate.sleep();
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
}

bool CycleEventCenter::RegisterTimeEventCb(const std::string &type,
                                           const TimeEventCallback &cb) {
  try {
    if (time_event_cb_map_.count(type) > 0) {
      time_event_cb_map_.at(type).time = ros::Time::now();
    } else {
      TimedEvent time_cb{ros::Time::now(), cb};
      time_event_cb_map_[type] = time_cb;
    }
    return true;
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return false;
}

Communicate::Communicate(const CommunicateOption &option,
                         std::shared_ptr<NodeStatusManager> ns)
    : node_status_(0),
      option_(option),
      thread_pool_(10),
      http_server_ptr_(nullptr),
      cycle_event_center_ptr_(nullptr) {
  node_status_manager_ptr_ = ns;
}

// 节点监控 状态上报
void Communicate::NodeDiagnostic(const ros::TimerEvent &e) {
  cotek_msgs::node_diagnostic msg;
  msg.header.stamp = ros::Time::now();
  for (auto status : node_status_manager_ptr_->GetNodeStatus()) {
    msg.status.push_back(static_cast<int>(status.second));
  }
  msg_publishers_.at(cotek_topic::kCommunicateDiagnosticTopic).publish(msg);
  node_status_manager_ptr_->PeriodClearNodeStatus(3.0);
}

void Communicate::NetWorkLinkCheck() {
  int err_cnt = 0;
  while (ros::ok()) {
    // std::this_thread::sleep_for(std::chrono::seconds(1));
    if (ping_tool_.Ping(option_.server_ip)) {
      LOG_DEBUG("network link ok.");
      err_cnt = 0;
      node_status_manager_ptr_->SetNodeStatus(
          cotek_diagnostic::CommunicateNodeStatus::NORMAL);
    } else {
      LOG_ERROR("network link error.");
      err_cnt++;
      if (err_cnt > 5) {
        node_status_manager_ptr_->SetNodeStatus(
            cotek_diagnostic::CommunicateNodeStatus::TIMEOUT);
      }
    }
  }
}

void Communicate::Get3dPoseZ() {
  tf::StampedTransform transform;
  tf::TransformListener tf;
  ros::Rate rate(2);
  while (ros::ok()) {
    try {
      tf.lookupTransform("map", "slam_3d_base_link", ros::Time(0), transform);
      if (ros::Duration(ros::Time::now() - transform.stamp_) >
          ros::Duration(0.2)) {
        LOG_WARN_STREAM("Current 3d pose is out of date.");
        // pose_3d_z_ = 888888.;
      } else {
        pose_3d_z_ = transform.getOrigin().z();
      }
      // LOG_WARN_THROTTLE(0.5, "POSE-Z: %f", pose_3d_z_);
    } catch (tf::TransformException &ex) {
      LOG_WARN_STREAM_THROTTLE(0.2, ex.what());
      // pose_3d_z_ = 888888.;
    }
    rate.sleep();
  }
}

bool Communicate::RosCommunicateInit() {
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");

  // 发布的话题
  // 3.0
  // 任务请求发布
  msg_publishers_[cotek_topic::kOrderRequestTopic] =
      nh.advertise<cotek_msgs::order>(cotek_topic::kOrderRequestTopic, 10);
  // 直接动作发布
  msg_publishers_[cotek_topic::kInstantActionRequestTopic] =
      nh.advertise<cotek_msgs::instant_action>(
          cotek_topic::kInstantActionRequestTopic, 10);

  msg_publishers_["language"] =
      nh.advertise<std_msgs::String>("language", 10);

  // 2.0
  // 任务控制请求发布
  msg_publishers_[cotek_topic::kTaskControlRequestTopic] =
      nh.advertise<cotek_msgs::task_control_request>(
          cotek_topic::kTaskControlRequestTopic, 10);
  // 标定请求发布
  msg_publishers_[cotek_topic::kTaskCalibrationRequestTopic] =
      nh.advertise<cotek_msgs::task_calibration_request>(
          cotek_topic::kTaskCalibrationRequestTopic, 10);
  // 地图上传请求
  msg_publishers_[cotek_topic::kTaskGetMapRequestTopic] =
      nh.advertise<cotek_msgs::task_get_map_request>(
          cotek_topic::kTaskGetMapRequestTopic, 10);
  // 控制音频请求
  msg_publishers_[cotek_topic::kTaskControlAudioRequestTopic] =
      nh.advertise<cotek_msgs::task_control_audio_request>(
          cotek_topic::kTaskControlAudioRequestTopic, 10);
  // 电池数据上报
  msg_publishers_[cotek_topic::kBatteryInfoRequestTopic] =
      nh.advertise<cotek_msgs::battery_info_request>(
          cotek_topic::kBatteryInfoRequestTopic, 10);

  // agv数据上报
  msg_publishers_[cotek_topic::kAgvInfoRequestTopic] =
      nh.advertise<cotek_msgs::agv_info_request>(
          cotek_topic::kAgvInfoRequestTopic, 10);

  agv_query_dispatch_server_ =
      nh.advertiseService(cotek_services::kAgvQueryDispatchService,
                          &Communicate::AgvQueryDispatch, this);

  // 3.0
  handler_[cotek_protocal::msg_type::kOrderRequest] =
      boost::bind(&Communicate::TaskRequestHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kInstantActionRequest] =
      boost::bind(&Communicate::InstantActionHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kStatusRequest] =
      boost::bind(&Communicate::RequestStatusHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kStreamRequest] =
      boost::bind(&Communicate::RequestStreamHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kStatisticsRequest] =
      boost::bind(&Communicate::RequestStatisticsHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kTaskCalibrationRequest] =
      boost::bind(&Communicate::RequestCalibrationHandler, this, _1, _2);

  // 2.0

  handler_[cotek_protocal::msg_type::kTaskGetMapRequest] =
      boost::bind(&Communicate::TaskGetMapRequestHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kTaskControlAudioRequest] =
      boost::bind(&Communicate::TaskControlAudioRequestHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kTaskQueryStorageRequest] =
      boost::bind(&Communicate::TaskQueryStorageRequestHandler, this, _1, _2);
  handler_[cotek_protocal::msg_type::kTaskQueryOdomRequest] =
      boost::bind(&Communicate::TaskQueryOdomInfoRequestHandler, this, _1, _2);

  return true;
}

bool Communicate::Init() {
  // TODO(@ssh) 防止communica过快启动并发送task
  std::this_thread::sleep_for(std::chrono::milliseconds(200));

  std::string local_ip("127.0.0.1");
  local_ip_ = local_ip;

  bool ret = true;

  ret &= DataManager::Instance().Init(option_);

  cycle_event_center_ptr_ = std::make_unique<CycleEventCenter>();

  ret &= cycle_event_center_ptr_->Start();

  switch (option_.method) {
    case CommunicateOption::CommunicateMethod::UDP: {
      socket_ = std::make_shared<UdpSocket<ProtoclMessageVda5050>>(
          option_.server_ip, option_.server_port, option_.local_port);
      ret &= socket_->Init(option_);
      break;
    }
#ifdef __x86_64__
    case CommunicateOption::CommunicateMethod::MQTT: {
      socket_ = std::make_shared<Mqtt<ProtoclMessageVda5050>>();
      ret &= socket_->Init(option_);
      break;
    }
#endif
    case CommunicateOption::CommunicateMethod::HTTP: {
      socket_ = std::make_shared<HttpClient<ProtoclMessageVda5050>>();
      ret &= socket_->Init(option_);
      break;
    }
    default: {
      LOG_ERROR("Dont support this communication_method!!!");
    }
  }

  http_server_ptr_ =
      std::make_shared<PocoHttpServer>(new HttpHandleFactory(), 9999);

  http_server_ptr_->start();

  ret &= RosCommunicateInit();

  return ret;
}

bool Communicate::Start() {
  if (option_.lora_option.lora_mode == 0) {
    network_checking_daemon_ = std::make_shared<std::thread>(
        std::bind(&Communicate::NetWorkLinkCheck, this));
  }

  loc_mode_ = DataManager::Instance().GetCommunicateOption().localizer_mode;
  if (loc_mode_ == 2) {
    pose_get_daemon_ = std::make_shared<std::thread>(
        std::bind(&Communicate::Get3dPoseZ, this));
  }

  language_ = std::make_shared<std::thread>([&]() {
    while (ros::ok()) {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      std_msgs::String data;
      data.data = DataManager::Instance().GetLanguage();
      option_.language = data.data;
      msg_publishers_["language"].publish(data);
    }
  });

  socket_->Listen(
      std::bind(&Communicate::MessageCallback, this, std::placeholders::_1));
  socket_->Run();

  PrePare();

  return true;
}

void Communicate::PrePare() {
  //  上电直接向调度获取一次
  QueryInfoFromDispatch(cotek_protocal::msg_type::kStorageMapInfoEvent);
}

bool Communicate::UpdateOption(const CommunicateOption &option) {
  bool ret = true;
  option_ = option;
  socket_->Stop();

  ret &= DataManager::Instance().UpdateOption(option_);
  // 目前ros线程为单线程，无抢占
  switch (option_.method) {
    case CommunicateOption::CommunicateMethod::UDP: {
      socket_ = std::make_shared<UdpSocket<ProtoclMessageVda5050>>(
          option_.server_ip, option_.server_port, option_.local_port);
      ret &= socket_->Init(option_);
      break;
    }
#ifdef __x86_64__
    case CommunicateOption::CommunicateMethod::MQTT: {
      socket_ = std::make_shared<Mqtt<ProtoclMessageVda5050>>();
      ret &= socket_->Init(option_);
      break;
    }
#endif
    case CommunicateOption::CommunicateMethod::HTTP: {
      socket_ = std::make_shared<HttpClient<ProtoclMessageVda5050>>();
      ret &= socket_->Init(option_);
      break;
    }
    default: {
      LOG_ERROR("Dont support this communication_method!!!");
    }
  }
  if (ret) {
    socket_->Listen(
        std::bind(&Communicate::MessageCallback, this, std::placeholders::_1));
    socket_->Run();
  }
  return ret;
}

void Communicate::MessageCallback(ProtoclMessageVda5050 msg) {
#if 0
  std::string udp_msg(msg.Data());
  LOG_INFO_STREAM("revice udp msg string:" << udp_msg);
#endif

  if (!msg.ParseMsgToJson()) {
    LOG_ERROR("receive json data formats is error.");
    return;
  }

  std::function<void(std::string, const std::string &bind_id)> handle;
  try {
    handle = handler_.at(msg.MsgType());
    node_status_manager_ptr_->SetNodeStatus(
        cotek_diagnostic::CommunicateNodeStatus::NORMAL);
  } catch (const std::out_of_range &e) {
    LOG_ERROR_STREAM("No such msg type: " << msg.MsgType());
    node_status_manager_ptr_->SetNodeStatus(
        cotek_diagnostic::CommunicateNodeStatus::MSG_TYPE_ABNORMAL);
    return;
  }
  handle(msg.MsgData(), msg.MsgBind());
}

bool Communicate::RegisterTimeEventCb(const std::string &type,
                                      const TimeEventCallback &cb) {
  return cycle_event_center_ptr_->RegisterTimeEventCb(type, cb);
}

void Communicate::InstantActionHandler(const std::string &data,
                                       const std::string &remote_bind_id) {
  LOG_INFO("receive: [instantAction]");

  try {
    Json instant_json;
    instant_json = Json::parse(data);

    cotek_msgs::instant_action instant_action_msg;

    for (auto &action : instant_json.at("actions")) {
      cotek_msgs::action action_msg;
      action_msg.action_type = action.at("actionType").get<std::string>();
      action_msg.action_id = action.at("actionId").get<std::string>();
      action_msg.action_description =
          action.at("actionDescription").get<std::string>();
      action_msg.blocking_type = action.at("blockingType").get<std::string>();
      for (auto &action_parameter : action.at("actionParameters")) {
        if (action_parameter.contains("key") &&
            action_parameter.at("key").get<std::string>() ==
                std::string("target")) {
          for (auto &value : action_parameter.at("value")) {
            action_msg.action_value_list.push_back(value.get<std::string>());
          }
        }
      }
      instant_action_msg.actions.push_back(action_msg);
    }

    msg_publishers_[cotek_topic::kInstantActionRequestTopic].publish(
        instant_action_msg);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return;
  }
}

void Communicate::RequestStatusHandler(const std::string &data,
                                       const std::string &remote_bind_id) {
  LOG_INFO("receive: [requestStatus]");

  try {
    Json request_json;
    request_json = Json::parse(data);

    std::string type = request_json["status"]["type"].get<std::string>();

    auto &&json = DataManager::Instance().GetJson(type);
    if (json.empty()) {
      LOG_WARN_STREAM("requestStatus: " << type << "not found!!!");
      return;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg()) {
      return;
    }

    std::string topic_name =
        cotek_protocal::msg_type::kStatusResponse + std::string("/") + type;

    Send(topic_name, msg.MsgJson(), Qos::QOS2);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return;
  }
}

void Communicate::RequestStreamHandler(const std::string &data,
                                       const std::string &remote_bind_id) {
  LOG_INFO("receive: [requestStream]");

  try {
    Json request_json;
    request_json = Json::parse(data);

    std::string type = request_json["stream"]["type"].get<std::string>();

    auto cb = [&](const std::string &type) {
      auto &&json = DataManager::Instance().GetJson(type);
      if (json.empty()) {
        LOG_WARN_STREAM("requestStatus: " << type << "not found!!!");
        return;
      }
      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(json);
      if (!msg.PackMsg()) {
        return;
      }

      std::string topic_name =
          cotek_protocal::msg_type::kStreamResponse + std::string("/") + type;
      Send(topic_name, msg.MsgJson(), Qos::QOS0);
    };

    RegisterTimeEventCb(type, cb);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return;
  }
}

void Communicate::RequestStatisticsHandler(const std::string &data,
                                           const std::string &remote_bind_id) {
  LOG_INFO("receive: [requestStatistics]");

  try {
    Json request_json;
    request_json = Json::parse(data);
    std::string type = request_json["statistics"]["type"].get<std::string>();

    cotek_msgs::query_info query;
    query.request.header.stamp = ros::Time::now();
    for (auto &&value : request_json["statistics"]["value"]) {
      query.request.param_list.push_back(value.get<std::string>());
    }

    Json json;
    if (DataManager::Instance().RosServiceCall(type, query)) {
      json = Json::parse(query.response.json);

      if (json.empty()) {
        LOG_WARN_STREAM("requestStatistics: " << type << "not found!!!");
        return;
      }

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(json);
      if (!msg.PackMsg()) {
        return;
      }
      std::string topic_name = cotek_protocal::msg_type::kStatisticsResponse +
                               std::string("/") + type;
      Send(topic_name, msg.MsgJson(), Qos::QOS0);

    } else {
      LOG_WARN_STREAM("requestStatistics: " << type << "not found!!!");
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return;
  }
}

std::string Communicate::CalibrationStatusToString(
    const CalibrationStatus &status) {
  switch (status) {
    case CalibrationStatus::SUCCESS: {
      return std::string("success");
    }
    case CalibrationStatus::FAILED: {
      return std::string("failed");
    }
    default: {
      return std::string("doing");
    }
  }
}

void Communicate::CreateNoBlockCalibrationHandler(
    const std::string &type, const std::vector<std::string> &params,
    const int &method) {
  if (calibration_statsus_map_.count(type) == 0) {
    calibration_statsus_map_[type] = CalibrationStatus::NONE;
  }

  if (calibration_statsus_map_[type] != CalibrationStatus::NONE) return;
  auto handle = [&](const std::string &type,
                    const std::vector<std::string> &params,
                    const int &method) -> void {
    calibration_statsus_map_[type] = CalibrationStatus::DOING;
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<cotek_msgs::calibration>(type);
    cotek_msgs::calibration request;
    request.request.type = type;
    request.request.parameters = params;
    request.request.method = method;
    CalibrationStatus response_status = CalibrationStatus::NONE;

    if (client.call(request)) {
      response_status = static_cast<CalibrationStatus>(request.response.status);
    } else {
      response_status = CalibrationStatus::FAILED;
    }

    Json response_json;
    response_json["calibration"]["type"] = type;
    response_json["calibration"]["parameters"] = params;
    response_json["calibration"]["method"] = method;
    response_json["calibration"]["status"] =
        CalibrationStatusToString(response_status);
    response_json["calibration"]["message"] = request.response.str;

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(response_json);

    if (!msg.PackMsg()) {
      return;
    }

    Send(cotek_protocal::msg_type::kTaskCalibrationRequest, msg.MsgJson(),
         Qos::QOS0);
    calibration_statsus_map_[type] = CalibrationStatus::NONE;
  };

  thread_pool_.enqueue(handle, type, params, method);
}

void Communicate::RequestCalibrationHandler(const std::string &data,
                                            const std::string &remote_bind_id) {
  LOG_INFO("receive: [requestCalibration]");

  try {
    Json request_json;
    request_json = Json::parse(data);
    std::string type = request_json["calibration"]["type"].get<std::string>();

    std::vector<std::string> parameters;
    for (auto &param : request_json["calibration"]["parameters"]) {
      parameters.push_back(param.get<std::string>());
    }
    int method = request_json["calibration"]["method"];

    CreateNoBlockCalibrationHandler(type, parameters, method);

  }

  catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return;
  }
}

void Communicate::TaskRequestHandler(const std::string &data,
                                     const std::string &remote_bind_id) {
  LOG_INFO("receive: [orderRequest]");
  cotek_msgs::order order_msg;

  try {
    Json order;
    order = Json::parse(data);
    LOG_INFO_STREAM("Order: " << order.dump(4));

    order_msg.order_id = order["orderId"].get<std::string>();
    order_msg.task_id = order["taskId"].get<std::string>();
    order_msg.order_update_id = order.at("orderUpdateId").get<int>();
    order_msg.zone_set_id = order.at("zoneSetId").get<std::string>();

    order_msg.end_point.node_id =
        order.at("endnode").at("nodeId").get<std::string>();
    order_msg.end_point.node_description =
        order.at("endnode").at("nodeDescription").get<std::string>();
    order_msg.end_point.node_position.x =
        order.at("endnode").at("nodePosition").at("x").get<double>();
    order_msg.end_point.node_position.y =
        order.at("endnode").at("nodePosition").at("y").get<double>();
    order_msg.end_point.node_position.theta =
        order.at("endnode").at("nodePosition").at("theta").get<double>();
    order_msg.end_point.node_position.allowed_deviation_xy =
        order.at("endnode")
            .at("nodePosition")
            .at("allowedDeviationXY")
            .get<double>();
    order_msg.end_point.node_position.allowed_deviation_theta =
        order.at("endnode")
            .at("nodePosition")
            .at("allowedDeviationTheta")
            .get<double>();
    order_msg.end_point.node_position.map_id =
        order.at("endnode").at("nodePosition").at("mapId").get<std::string>();
    order_msg.end_point.node_position.map_description =
        order.at("endnode")
            .at("nodePosition")
            .at("mapDescription")
            .get<std::string>();
    for (auto &action : order.at("endnode").at("actions")) {
      cotek_msgs::action action_msg;
      action_msg.action_type = action.at("actionType").get<std::string>();
      action_msg.action_id = action.at("actionId").get<std::string>();
      action_msg.action_description =
          action.at("actionDescription").get<std::string>();
      action_msg.blocking_type = action.at("blockingType").get<std::string>();
      // end_point暂时不加自定义条件
      for (auto &action_parameter : action.at("actionParameters")) {
        if (action_parameter.contains("key") &&
            action_parameter.at("key").get<std::string>() ==
                std::string("target")) {
          for (auto &value : action_parameter.at("value")) {
            action_msg.action_value_list.push_back(value.get<std::string>());
          }
        }
      }
      order_msg.end_point.actions.push_back(action_msg);
    }

    for (auto &node : order.at("nodes")) {
      cotek_msgs::node node_msg;
      node_msg.node_id = node.at("nodeId").get<std::string>();
      node_msg.sequence_id = node.at("sequenceId").get<int>();
      node_msg.node_description = node.at("nodeDescription").get<std::string>();
      node_msg.released = node.at("released").get<bool>();
      node_msg.node_position.x = node.at("nodePosition").at("x").get<double>();
      node_msg.node_position.y = node.at("nodePosition").at("y").get<double>();
      node_msg.node_position.theta =
          node.at("nodePosition").at("theta").get<double>();
      node_msg.node_position.allowed_deviation_xy =
          node.at("nodePosition").at("allowedDeviationXY").get<double>();
      node_msg.node_position.allowed_deviation_theta =
          node.at("nodePosition").at("allowedDeviationTheta").get<double>();
      node_msg.node_position.map_id =
          node.at("nodePosition").at("mapId").get<std::string>();
      node_msg.node_position.map_description =
          node.at("nodePosition").at("mapDescription").get<std::string>();

      for (auto &action : node.at("actions")) {
        cotek_msgs::action action_msg;
        action_msg.action_type = action.at("actionType").get<std::string>();
        action_msg.action_id = action.at("actionId").get<std::string>();
        action_msg.action_description =
            action.at("actionDescription").get<std::string>();
        action_msg.blocking_type = action.at("blockingType").get<std::string>();
        for (auto &action_parameter : action.at("actionParameters")) {
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("target")) {
            for (auto &value : action_parameter.at("value")) {
              action_msg.action_value_list.push_back(value.get<std::string>());
            }
          }
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("mixConditions")) {
            auto &mix_conditions = action_parameter.at("value");

            action_msg.mix_conditions = mix_conditions.dump(4);
          }
        }

        node_msg.actions.push_back(action_msg);
      }

      order_msg.nodes.push_back(node_msg);
    }

    for (auto &edge : order.at("edges")) {
      cotek_msgs::edge edge_msg;
      edge_msg.edge_id = edge.at("edgeId").get<std::string>();
      edge_msg.sequence_id = edge.at("sequenceId").get<int>();
      edge_msg.edge_descripition =
          edge.at("edgeDescription").get<std::string>();
      edge_msg.released = edge.at("released").get<bool>();
      edge_msg.start_node_id = edge.at("startNodeId").get<std::string>();
      edge_msg.end_node_id = edge.at("endNodeId").get<std::string>();
      edge_msg.max_speed = edge.at("maxSpeed").get<double>();
      edge_msg.max_height = edge.at("maxHeight").get<double>();
      edge_msg.min_height = edge.at("minHeight").get<double>();
      edge_msg.orientation = edge.at("orientation").get<double>();
      edge_msg.orientation_type = edge.at("orientationType").get<std::string>();
      edge_msg.direction = edge.at("direction").get<std::string>();
      edge_msg.rotation_allowed = edge.at("rotationAllowed").get<bool>();
      edge_msg.max_rotation_speed = edge.at("maxRotationSpeed").get<double>();
      edge_msg.avoid_map = edge.at("avoidMap").get<int>();
      for (auto strategy : edge.at("avoidStrategy")) {
        edge_msg.avoid_strategys.push_back(strategy.get<std::string>());
      }
      edge_msg.length = edge.at("length").get<double>();

      cotek_msgs::trajectory trajectory_msg;
      auto &trajectory = edge.at("trajectory");
      trajectory_msg.degree = trajectory.at("degree").get<int>();
      // TODO（@ssh） 该字段暂未用
      trajectory_msg.knot_vector = std::vector<double>();
      for (auto &control_point : trajectory.at("controlPoints")) {
        cotek_msgs::control_point control_point_msg;
        control_point_msg.x = control_point.at("x").get<double>();
        control_point_msg.y = control_point.at("y").get<double>();
        control_point_msg.weight = control_point.at("weight").get<double>();
        trajectory_msg.control_points.push_back(control_point_msg);
      }
      edge_msg.trajectory = trajectory_msg;

      for (auto &action : edge.at("actions")) {
        cotek_msgs::action action_msg;
        action_msg.action_type = action.at("actionType").get<std::string>();
        action_msg.action_id = action.at("actionId").get<std::string>();
        action_msg.action_description =
            action.at("actionDescription").get<std::string>();
        action_msg.blocking_type = action.at("blockingType").get<std::string>();
        for (auto &action_parameter : action.at("actionParameters")) {
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("target")) {
            for (auto &value : action_parameter.at("value")) {
              action_msg.action_value_list.push_back(value.get<std::string>());
            }
          }
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("mixConditions")) {
            auto &mix_conditions = action_parameter.at("value");

            action_msg.mix_conditions = mix_conditions.dump(4);
          }
        }
        edge_msg.actions.push_back(action_msg);
      }
      order_msg.edges.push_back(edge_msg);
    }

    DataManager::Instance().RosPublish(cotek_topic::kOrderRequestTopic,
                                       order_msg);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    return;
  }
}

void Communicate::TaskCalibrationRequestHandler(
    std::string data, const std::string &remote_bind_id) {
  LOG_INFO("receive: [TaskCalibrationRequest]");

  std::string err;
  const auto &task_calibration_request_json = json11::Json::parse(data, err);
  if (!err.empty()) {
    LOG_ERROR("task_calibration_request_json data formats is error.");
    return;
  }

  cotek_msgs::task_calibration_request msg;
  msg.map_id = task_calibration_request_json["mapId"].int_value();
  msg.section_id = task_calibration_request_json["sectionId"].int_value();
  msg.calibration_mode =
      task_calibration_request_json["calibrationMode"].int_value();
  msg.calibration_operation_type =
      task_calibration_request_json["calibrationOperationType"].int_value();
  msg_publishers_.at(cotek_topic::kTaskCalibrationRequestTopic).publish(msg);
}

void Communicate::TaskGetMapRequestHandler(std::string data,
                                           const std::string &remote_bind_id) {
  LOG_INFO("receive: [TaskGetMapRequest]");

  std::string err;
  const auto &task_get_map_request_json = json11::Json::parse(data, err);
  if (!err.empty()) {
    LOG_ERROR("task_get_map_request_json data data formats is error.");
    return;
  }

  cotek_msgs::task_get_map_request msg;
  msg.get_map_method = task_get_map_request_json["getMapMethod"].int_value();
  msg.get_map_type = task_get_map_request_json["getMapType"].int_value();
  msg_publishers_.at(cotek_topic::kTaskGetMapRequestTopic).publish(msg);
}

void Communicate::TaskQueryStorageRequestHandler(
    std::string data, const std::string &remote_bind_id) {
  LOG_INFO("receive: [TaskQueryStorageRequest]");
  std::string err;
  const auto &task_query_storage_request_json = json11::Json::parse(data, err);
  if (!err.empty()) {
    LOG_ERROR("task_query_storage_request_json data data formats is error.");
    return;
  }

  cotek_msgs::agv_info_request request;
  request.type = cotek_protocal::msg_type::kTaskQueryStorageRequest;
  request.order_id = task_query_storage_request_json["orderId"].string_value();
  request.task_id = task_query_storage_request_json["taskId"].string_value();
  request.value.push_back(
      task_query_storage_request_json["areaId"].string_value());
  msg_publishers_.at(cotek_topic::kAgvInfoRequestTopic).publish(request);
}

void Communicate::TaskQueryOdomInfoRequestHandler(
    std::string data, const std::string &remote_bind_id) {
  LOG_INFO("receive: [TaskQueryOdomInfoRequest]");
  std::string err;
  const auto &task_query_storage_request_json = json11::Json::parse(data, err);
  if (!err.empty()) {
    LOG_ERROR("task_query_odomrequest_json data data formats is error.");
    return;
  }
  cotek_msgs::agv_info_request agv_info;
  agv_info.type = cotek_protocal::msg_type::kTaskQueryOdomRequest;
  msg_publishers_.at(cotek_topic::kAgvInfoRequestTopic).publish(agv_info);
}

void Communicate::TaskControlAudioRequestHandler(
    std::string data, const std::string &remote_bind_id) {
  std::string err;
  const auto &task_control_audio_request_json = json11::Json::parse(data, err);
  if (!err.empty()) {
    LOG_ERROR("task_control_audio_json data data formats is error.");
    return;
  }

  cotek_msgs::task_control_audio_request msg;
  msg.audio_type = task_control_audio_request_json["audioType"].int_value();
  msg.enable = task_control_audio_request_json["enable"].bool_value();
  msg_publishers_.at(cotek_topic::kTaskControlAudioRequestTopic).publish(msg);

  LOG_INFO("receive: [TaskControlAudioRequest] type: %d enable %d ",
           msg.audio_type, msg.enable);

  json11::Json task_response_json = json11::Json::object{
      {"taskType", "TaskControlAudioRequest"}, {"state", 1}};
  std::string task_response_string(task_response_json.dump());

#if 0
  LOG_DEBUG_STREAM("task_response data len: " << task_response_string.size());
  LOG_DEBUG_STREAM("task_response: " << task_response_string);
#endif
  LOG_INFO(" send: [TaskResponse] Type:TaskControlAudioRequest");
  Send(cotek_protocal::msg_type::kTaskResponse, task_response_string,
       Qos::QOS1);
}

void Communicate::AddAgvInfoResponseMsg(
    const cotek_msgs::agv_info_response::ConstPtr &agv_info_response) {
  Send(agv_info_response->type, agv_info_response->data,
       static_cast<Qos>(agv_info_response->qos));
}

void Communicate::AddUpdateEventMsg(
    const cotek_msgs::update_event::ConstPtr &update_event) {
  Json json, task_state;
  json["orderId"] = update_event->order_id;
  json["taskId"] = update_event->task_id;
  json["orderUpdateId"] = update_event->order_update_id;
  json["zoneSetId"] = update_event->zone_set_id;
  json["lastNodeId"] = update_event->last_node_id;
  json["lastNodeSequenceId"] = update_event->last_node_sequence_id;
  json["agvState"] = update_event->agv_state;
  json["operatingMode"] = update_event->operating_mode;
  task_state.merge_patch(json);

  std::string target;
  Json node_states = std::vector<Json>();
  for (auto &node_state : update_event->node_states) {
    if( &node_state == &update_event->node_states.back()) {
      target += node_state.node_id;
    } else {
      target += node_state.node_id + ";";
    }
    Json node_json;
    node_json["nodeId"] = node_state.node_id;
    node_json["sequenceId"] = node_state.sequence_id;
    node_json["nodeDescription"] = node_state.node_description;
    node_json["released"] = node_state.released;
    Json node_position;
    node_position["x"] = node_state.node_position.x;
    node_position["y"] = node_state.node_position.y;
    node_position["theta"] = node_state.node_position.theta;
    node_position["allowedDeviationXY"] =
        node_state.node_position.allowed_deviation_xy;
    node_position["allowedDeviationTheta"] =
        node_state.node_position.allowed_deviation_theta;
    node_position["mapId"] = node_state.node_position.map_id;
    node_position["mapDescription"] = node_state.node_position.map_description;
    node_json["nodePosition"] = node_position;
    node_states.push_back(node_json);
  }
  json["nodeStates"] = node_states;
  task_state["target"] = target;

  Json edge_states = std::vector<Json>();
  for (auto &edge_state : update_event->edge_states) {
    Json edge_json;
    edge_json["edgeId"] = edge_state.edge_id;
    edge_json["sequenceId"] = edge_state.sequence_id;
    edge_json["edgeDescription"] = edge_state.edge_descripition;
    edge_json["released"] = edge_state.released;
    edge_json["avoidMap"] = edge_state.avoid_map;
    edge_json["orientation"] = edge_state.orientation;

    Json avoid_strategys = std::vector<std::string>();
    for (auto st : edge_state.avoid_strategys) {
      avoid_strategys.push_back(st);
    }
    edge_json["avoidStrategy"] = avoid_strategys;

    Json trajectory;
    trajectory["degree"] = edge_state.trajectory.degree;
    trajectory["knotVector"] = edge_state.trajectory.knot_vector;
    Json control_points = std::vector<Json>();
    for (auto &control_point : edge_state.trajectory.control_points) {
      Json contorl_points_js;
      contorl_points_js["x"] = control_point.x;
      contorl_points_js["y"] = control_point.y;
      contorl_points_js["weight"] = 1.0;
      control_points.push_back(contorl_points_js);
    }
    trajectory["controlPoints"] = control_points;
    edge_json["trajectory"] = trajectory;
    // TODO(@ssh) 自定义条件暂不上报
    edge_states.push_back(edge_json);
  }
  json["edgeStates"] = edge_states;

  Json agv_position;
  agv_position["positionInitialized"] =
      update_event->agv_position.position_initialized;
  agv_position["localizationScore"] =
      update_event->agv_position.localization_score;
  agv_position["deviationRange"] = update_event->agv_position.deviation_range;
  agv_position["x"] = update_event->agv_position.x;
  agv_position["y"] = update_event->agv_position.y;

  if (loc_mode_ == 2) {
    agv_position["z"] = pose_3d_z_;
  } else {
    agv_position["z"] = 0.0;
  }

  agv_position["theta"] = update_event->agv_position.theta;
  agv_position["mapId"] = update_event->agv_position.map_id;
  agv_position["mapDescription"] = update_event->agv_position.map_description;
  json["agvPosition"] = agv_position;

  Json velocity;
  velocity["vx"] = update_event->velocity.vx;
  velocity["vy"] = update_event->velocity.vy;
  velocity["omega"] = update_event->velocity.omega;
  json["velocity"] = velocity;

  Json loads = std::vector<Json>();
  for (auto &load : update_event->loads) {
    Json load_js;
    load_js["loadId"] = load.load_id;
    load_js["loadType"] = load.load_type;
    load_js["loadPosition"] = std::string();
    Json temp;
    load_js["boundingBoxReference"] = temp;
    load_js["loadDimensions"] = temp;
    load_js["weight"] = load.weight;
    loads.push_back(load_js);
  }
  json["loads"] = loads;

  json["driving"] = update_event->driving;
  json["pause"] = update_event->paused;
  json["newBaseRequest"] = update_event->new_base_request;
  json["distanceSinceLastNode"] = update_event->distance_since_last_node;

  Json actions_states = std::vector<Json>();
  std::vector<Json> action_vector;
  for (auto &action_state : update_event->action_states) {
    Json action_state_js;
    action_state_js["actionId"] = action_state.action_id;
    action_state_js["actionType"] = action_state.action_type;
    action_state_js["actionDescription"] = action_state.action_description;
    action_state_js["actionStatus"] = action_state.action_status;
    action_state_js["resultDescription"] = action_state.result_description;
    actions_states.push_back(action_state_js);

    std::string start_node, end_node;
    int index = action_state.action_description.find("-");
    if (index != std::string::npos) {
      start_node = action_state.action_description.substr(0, index);
      end_node = action_state.action_description.substr(index + 1);
    }
    action_state_js["startNode"] = start_node;
    action_state_js["endNode"] = end_node;
    action_vector.push_back(action_state_js);
  }
  json["actionState"] = actions_states;

  Json current_state, next_state;
  current_state["actionId"] = "";
  current_state["actionType"] = "";
  current_state["actionDescription"] = "";
  current_state["actionStatus"] = "";
  current_state["resultDescription"] = "";
  current_state["startNode"] = "";
  current_state["endNode"] = "";
  next_state.merge_patch(current_state);

  std::string current_id, next_id;
  std::string task_name;
  task_t current_task;
  if (!update_event->order_id.empty()) {
    auto tasks = DataManager::Instance().GetTaskList(update_event->order_id);
    if (!tasks.empty()) {
      current_task = tasks.front();
      task_name = current_task.name;
    }
    node_t current_node, next_node;
    int find_index = -1;
    for(int i = 0; i < current_task.actions.size(); i++) {
      if (current_task.actions[i].id == update_event->last_node_id) {
        find_index = i;
      }
    }
    if (find_index+1 < current_task.actions.size()) {
      current_node = current_task.actions[find_index+1];
    }
    if (find_index+2 < current_task.actions.size()) {
      next_node = current_task.actions[find_index+2];
    }

    current_id = update_event->last_node_id;
    if (!current_node.id.empty()) {
      current_id = current_node.id;
      current_state["actionId"] = current_id;
      current_state["actionType"] = kActionType[option_.language][current_node.action];
      current_state["actionStatus"] = kRunningState[option_.language];
      current_state["startNode"] = update_event->last_node_id;
      current_state["endNode"] = current_id;
    }
    if (!next_node.id.empty()) {
      next_id = next_node.id;
      next_state["actionId"] = next_id;
      next_state["actionType"] = kActionType[option_.language][next_node.action];
      next_state["actionStatus"] = kNoRunningState[option_.language];
      next_state["startNode"] = current_id;
      next_state["endNode"] = next_id;
    }
  }

  if (update_event->agv_state == "Doing") {
    task_state["currentAction"] = current_state;
    task_state["nextAction"] = next_state;
  }
  task_state["remainTime"] = 8888888;

  Json batter_state;
  batter_state["batteryCharge"] = update_event->battery_state.battery_charge;
  batter_state["batteryVoltage"] = update_event->battery_state.battery_voltage;
  batter_state["batteryHealth"] = update_event->battery_state.battery_health;
  batter_state["charging"] = update_event->battery_state.charging;
  batter_state["reach"] = update_event->battery_state.reach;
  json["batteryState"] = batter_state;

  std::string error_state;
  int error_level = 0;
  Json errors = std::vector<Json>();
  for (auto &error : update_event->errors) {
    int level = logLevelToString(error.error_level);
    if (level > error_level) {
      error_level = level;
    }
    error_state += "[" + kRuningState[option_.language][level] + "] ";
    Json error_js;
    error_js["errorType"] = error.error_type;
    error_js["errorDescription"] = error.error_description;
    error_js["errorLevel"] = error.error_level;
    Json error_reference_js;
    for (auto &reference : error.error_references) {
      if (kErrorCode.find(option_.language) != kErrorCode.end()) {
        auto &error_code = kErrorCode[option_.language];
        if (error_code.find(reference.reference_key) != error_code.end()) {
          error_state += error_code[reference.reference_key];
        } else {
          error_state += std::to_string(reference.reference_key);
        }
      }
      if (&reference != &error.error_references.back()) {
        error_state += ", ";
      }

      Json reference_js;
      reference_js["referenceKey"] = std::to_string(reference.reference_key);
      reference_js["referenceValue"] = reference.reference_value;
      error_reference_js.push_back(reference_js);
    }
    error_js["errorReferences"] = error_reference_js;
    errors.push_back(error_js);
    if (&error != &update_event->errors.back()) {
      error_state += "/";
    }
  }
  json["errors"] = errors;
  task_state["errors"] = error_state;
  task_state["errorLevel"] = error_level;

  std::vector<Json> empty_info_js;
  json["information"] = empty_info_js;

  std::string avoid_state;
  Json safety_state;
  safety_state["eStop"] = std::string();
  safety_state["fieldViolation"] = false;
  Json avoid = std::vector<Json>();
  for (auto &safety_state : update_event->safety_states) {
    if (kSafetyType.find(option_.language) != kSafetyType.end()) {
      auto &safety_type = kSafetyType[option_.language];
      if (safety_type.find(safety_state.avoid_type) != safety_type.end()) {
        avoid_state += safety_type[safety_state.avoid_type];
      } else {
        avoid_state += std::to_string(safety_state.avoid_type);
      }
    }
    if (kSafetyState.find(option_.language) != kSafetyState.end()) {
      auto &safe_state = kSafetyState[option_.language];
      if (safe_state.find(safety_state.avoid_state) != safe_state.end()) {
        avoid_state += safe_state[safety_state.avoid_state];
      } else {
        avoid_state += std::to_string(safety_state.avoid_state);
      }
    }
    if (&safety_state != &update_event->safety_states.back()) {
      avoid_state += "/";
    }
    Json avoid_js;
    avoid_js["avoidState"] = safety_state.avoid_state;
    avoid_js["avoidType"] = safety_state.avoid_type;
    Json avoid_point;
    avoid_point["x"] = safety_state.avoid_point.x;
    avoid_point["y"] = safety_state.avoid_point.y;
    avoid_js["avoidPoint"] = avoid_point;
    avoid.push_back(avoid_js);
  }
  safety_state["avoid"] = avoid;
  json["safetyState"] = safety_state;
  task_state["safetyState"] = avoid_state;

  Json audio;
  audio["audioType"] = update_event->audio.audio_type;
  audio["audioControl"] = update_event->audio.audio_control;
  json["audio"] = audio;

  Json mechanism_state = std::vector<Json>();
  for (auto &state : update_event->mechanism_states) {
    Json state_js;
    state_js["type"] = state.type;
    state_js["value"] = state.value;
    mechanism_state.push_back(state_js);
  }
  json["mechanismState"] = mechanism_state;

  DataManager::Instance().SetJson(cotek_topic::kUpdateEventTopic, json);
  Json agv_state_json, audio_json, end_node_json;
  agv_state_json["agvState"] = update_event->agv_state;
  DataManager::Instance().SetJson(cotek_topic::kAgvStateTopic, agv_state_json);
  audio_json = audio;
  DataManager::Instance().SetJson(cotek_topic::kAudioStateTopic, audio_json);
  
  Json running_state;
  Json runing_json = DataManager::Instance().GetJson("taskRunning", update_event->order_id);
  if (!runing_json.empty()) {
    task_state.merge_patch(runing_json);
  }
  task_state["taskName"] = task_name;
  task_state["confirm"] = DataManager::Instance().GetManualConfirm();
  task_state["popup"] = DataManager::Instance().GetPopup();
  task_state["traffic"] = DataManager::Instance().GetTraffic();
  task_state["mapName"] = DataManager::Instance().GetMapName();
  task_state["mapId"] = DataManager::Instance().GetMapId();
  task_state["zoneId"] = DataManager::Instance().GetZoneId();
  running_state["runningState"] = task_state;
  DataManager::Instance().SetJson("taskState", running_state);

  // 添加endnode
  end_node_json["endnode"]["nodeId"] = update_event->end_point.node_id;
  end_node_json["endnode"]["nodeDescription"] =
      update_event->end_point.node_description;

  end_node_json["endnode"]["nodePosition"]["x"] =
      update_event->end_point.node_position.x;
  end_node_json["endnode"]["nodePosition"]["y"] =
      update_event->end_point.node_position.y;
  end_node_json["endnode"]["nodePosition"]["theta"] =
      update_event->end_point.node_position.theta;
  end_node_json["endnode"]["nodePosition"]["allowedDeviationTheta"] =
      update_event->end_point.node_position.allowed_deviation_theta;
  end_node_json["endnode"]["nodePosition"]["allowedDeviationXY"] =
      update_event->end_point.node_position.allowed_deviation_xy;
  end_node_json["endnode"]["nodePosition"]["mapId"] =
      update_event->end_point.node_position.map_id;
  end_node_json["endnode"]["nodePosition"]["mapDescription"] =
      update_event->end_point.node_position.map_description;

  Json action_js = std::vector<Json>();
  for (auto &action : update_event->end_point.actions) {
    Json temp;
    temp["actionType"] = action.action_type;
    temp["actionId"] = action.action_id;
    temp["actionDescription"] = action.action_description;
    temp["blockingType"] = action.blocking_type;

    Json action_value_js;
    for (auto &action_parameter : action.action_value_list) {
      action_value_js.push_back(action_parameter);
    }
    temp["value"] = action_value_js;
    action_js.push_back(temp);
  }

  end_node_json["endnode"]["actions"] = action_js;

  DataManager::Instance().SetJson("endnode", end_node_json);

  LOG_INFO_STREAM_COND(!update_event->last_node_id.empty(),
                       "Send last_node_id: " << update_event->last_node_id);

#ifdef __x86_64__
  Send(cotek_protocal::msg_type::kUpdateEvent, json, Qos::QOS0);
#endif
}

void Communicate::AddTaskFinishRequestMsg(
    const cotek_msgs::task_finish_request::ConstPtr &task_finish_request) {
  Json json;
  json["orderNo"] = task_finish_request->order_id;
  json["taskNo"] = task_finish_request->task_id;
  json["amrNo"] = DataManager::Instance().GetSerialNum();

  LOG_INFO_STREAM(
      "send: [TaskFinishRequest] order_id:" << task_finish_request->order_id);
  Send(cotek_protocal::msg_type::kTaskFinishRequest, json, Qos::QOS0);
}

void Communicate::Send(const std::string &msg_type, const std::string &msg_data,
                       const Qos &qos) {
  auto &&msg = socket_->CreatEmptyMessage();
  msg.SetMsgBind(DataManager::Instance().GetSerialNum());
  msg.SetMsgType(msg_type);
  msg.SetMsgData(msg_data);
  if (!msg.PackMsg()) {
    LOG_ERROR_STREAM("msg pack failed ,cant send!!!");
    return;
  }
  socket_->Send(msg, qos);
  LOG_DEBUG("send data success");
}

void Communicate::Send(const std::string &msg_type, const Json &msg_json,
                       const Qos &qos) {
  auto &&msg = socket_->CreatEmptyMessage();
  msg.SetMsgBind(DataManager::Instance().GetSerialNum());
  msg.SetMsgType(msg_type);
  msg.SetMsgJson(msg_json);
  if (!msg.PackMsg()) {
    LOG_ERROR_STREAM("msg pack failed ,cant send!!!");
    return;
  }
  socket_->Send(msg, qos);
}

bool Communicate::AgvQueryDispatch(
    cotek_msgs::agv_query_dispatch::Request &req,
    cotek_msgs::agv_query_dispatch::Response &res) {
  if (agv_map_info_.count(req.type) > 0) {
    res.data = agv_map_info_[req.type];
    // 刷新数据
    QueryInfoFromDispatch(req.type);
    return true;
  }
  QueryInfoFromDispatch(req.type);
  return false;
}

void Communicate::QueryInfoFromDispatch(const std::string &msg_type) {
  if (option_.method == CommunicateOption::CommunicateMethod::HTTP) {
    std::string api;

    if (msg_type == cotek_protocal::msg_type::kStorageMapInfoEvent) {
      api = "/warehouse/thirdParty/allLaser";
    }

    if (api.empty()) return;

    auto no_block = [&](std::string type, std::string api) {
      std::string &&res = HttpGetString(
          DataManager::Instance().GetCommunicateOption().server_ip,
          DataManager::Instance().GetCommunicateOption().server_port, api);
      if (!res.empty()) {
        LOG_INFO_STREAM("Get " << type << " succeed!!!");
        agv_map_info_[type] = res;
      } else {
        LOG_WARN_STREAM("Get " << type << " failed!!!");
      }
    };

    thread_pool_.enqueue(no_block, msg_type, api);
    LOG_INFO_STREAM("Query: " << msg_type);
    return;
  }

  //  TODO(@ssh)剩余mqtt接口

  // json11::Json agv_query_json = json11::Json::object{{"mapType", msg_type}};
  // std::string agv_query_string(agv_query_json.dump());
  // LOG_INFO_STREAM("Send [AgvQueryInfoRequest]: " << agv_query_string);
  // Send(cotek_protocal::msg_type::kAgvQueryInfoRequest, agv_query_string,
  //      Qos::QOS2);
}

}  // namespace cotek_communicate
