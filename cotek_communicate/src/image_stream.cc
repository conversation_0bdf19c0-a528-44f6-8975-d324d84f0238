/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "cotek_communicate/image_stream.h"

#include <Poco/Any.h>
#include <Poco/Base64Encoder.h>
#include <Poco/Buffer.h>
#include <ros/ros.h>
#include <sensor_msgs/CompressedImage.h>

#include <cstddef>
#include <cstdint>
#include <exception>
#include <ios>
#include <iostream>
#include <memory>
#include <mutex>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/opencv.hpp>
#include <sstream>
#include <string>
#include <vector>

#include "cotek_common/log_porting.h"

// #include <boost/archive/iterators/base64_from_binary.hpp>
// #include <boost/archive/iterators/binary_from_base64.hpp>
// #include <boost/archive/iterators/transform_width.hpp>

namespace cotek_communicate {

std::string RosCompressedStream::GetData() {
  std::ostringstream oss;
  Poco::Base64Encoder base64_encoder(oss);   // 创建Base64编码器
  base64_encoder.rdbuf()->setLineLength(0);  // 禁用Base64编码器的行限制

  Poco::ScopedReadRWLock read_lock(rw_lock_);
  if (!CheckTimeout() && image_data_.size() > 0) {
    // 将图像数据写入编码器
    base64_encoder.write(reinterpret_cast<const char *>(image_data_.data()),
                         static_cast<std::streamsize>(image_data_.size()));

    // 完成编码并获取 Base64 数据
    base64_encoder.close();
    return oss.str();
  }

  return std::string();
}

bool MapStream::CheckTimeout() {
  return (ros::Time::now() - last_time_ > ros::Duration(5.0));
}

std::string MapStream::GetData() {
  Poco::ScopedReadRWLock read_lock(rw_lock_);
  try {
    if (!CheckTimeout() && image_data_.size() > 0) {
      std::ostringstream oss;
      Poco::Base64Encoder base64_encoder(oss);  // 创建Base64编码器
      base64_encoder.rdbuf()->setLineLength(0);  // 禁用Base64编码器的行限制

      cv::Mat &&mat = ConverOccupancyGridToPngMat(image_data_, info_);
      std::vector<uint8_t> buffer;
      // 由于地图很大，需要提前预分配空间
      buffer.reserve(2 * image_data_.size());
      cv::imencode(".jpg", mat, buffer);

      // 将图像数据写入编码器

      base64_encoder.write(reinterpret_cast<const char *>(buffer.data()),
                           static_cast<std::streamsize>(buffer.size()));

      // 完成编码并获取 Base64 数据
      base64_encoder.close();
      return oss.str();
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return std::string();
}

cv::Mat MapStream::ConverOccupancyGridToPngMat(const std::vector<int8_t> &data,
                                               const ImageInfo &info) {
  std::vector<uint8_t> temp(data.begin(), data.end());
  if (!temp.empty()) {
    return cv::Mat(info.height, info.width, CV_8UC1, temp.data());
  }
  return cv::Mat();
}

}  // namespace cotek_communicate
