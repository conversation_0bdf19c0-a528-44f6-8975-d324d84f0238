/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate/system_info.h"

#include <bluetooth/bluetooth.h>
#include <bluetooth/hci.h>
#include <bluetooth/hci_lib.h>
#include <sys/socket.h>
#include <sys/statvfs.h>
#include <unistd.h>

#include <array>
#include <cstdio>
#include <exception>
#include <fstream>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>

namespace cotek_communicate {

static std::string executeCommand(const std::string &command) {
  std::array<char, 128> buffer;
  std::string result;
  std::shared_ptr<FILE> pipe(popen(command.c_str(), "r"), pclose);
  if (!pipe) {
    std::cout << "cant execute command!!!" << std::endl;
    return std::string();
  }
  while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
    result += buffer.data();
  }
  return result;
}

SystemInfo::SystemInfo()
    : isRunning_(false),
      cpuUsage_(0.0f),
      memUsage_(0.0f),
      diskUsage_(0.0f),
      pingLatency_(999) {}

SystemInfo ::~SystemInfo() { Stop(); }

bool SystemInfo::Init(const std::string &server) {
  server_ = server;
  Start();
  return true;
}

bool SystemInfo::Update(const std::string &server) {
  server_ = server;
  return true;
}

void SystemInfo::Start() {
  if (!isRunning_) {
    isRunning_ = true;
    thread_ = std::thread(&SystemInfo::CalculateSystemInfo, this);
  }
}

void SystemInfo::Stop() {
  if (isRunning_) {
    isRunning_ = false;
    thread_.join();
  }
}

std::string SystemInfo::GetBlueMac() {
  static bool init = false;
  static std::string cached_mac;
  try {
    if (!init) {
      int dev_id = hci_get_route(nullptr);
      if (dev_id < 0) {
        std::cerr << "Error: Failed to obtain local Bluetooth device ID."
                  << std::endl;
        return cached_mac;
      }

      int sock = hci_open_dev(dev_id);
      if (sock < 0) {
        std::cerr << "Error: Failed to open local Bluetooth device."
                  << std::endl;
        return cached_mac;
      }

      bdaddr_t bdaddr;
      if (hci_read_bd_addr(sock, &bdaddr, 1000) < 0) {
        std::cerr << "Error: Failed to read local Bluetooth MAC address."
                  << std::endl;
        close(sock);
        return cached_mac;
      }

      char mac_address_str[18] = {0};
      ba2str(&bdaddr, mac_address_str);
      cached_mac = std::string(mac_address_str);
      close(sock);
      init = true;
    }
  } catch (const std::exception &ex) {
    std::cerr << ex.what() << std::endl;
  }

  return cached_mac;
}

void SystemInfo::CalculateSystemInfo() {
  CpuOccupy cpuStat1;
  CpuOccupy cpuStat2;
  MemOccupy memStat;
  while (isRunning_) {
    GetCpuOccupy(&cpuStat1);
    GetMemOccupy(&memStat);
    std::this_thread::sleep_for(std::chrono::seconds(1));
    GetCpuOccupy(&cpuStat2);
    float cpuUsage = CalculateCpuUsage(&cpuStat1, &cpuStat2);
    float memUsage = CalculateMemUsage(&memStat);
    float diskUsage = CalculateDiskUsage();
    float pingLatency = CalculatePingLatency();
    {
      std::lock_guard<std::mutex> lock(mutex_);
      cpuUsage_ = static_cast<int>(cpuUsage);
      memUsage_ = static_cast<int>(memUsage);
      diskUsage_ = static_cast<int>(diskUsage);
      pingLatency_ = pingLatency > 1 ? static_cast<int>(pingLatency) : 1;
    }
  }
}
void SystemInfo::GetCpuOccupy(CpuOccupy *cpuStat) {
  std::ifstream fd("/proc/stat");
  if (!fd.is_open()) {
    std::cerr << "Failed to open /proc/stat" << std::endl;
    return;
  }
  fd >> cpuStat->name.data() >> cpuStat->user >> cpuStat->nice >>
      cpuStat->system >> cpuStat->idle;
}
void SystemInfo::GetMemOccupy(MemOccupy *memStat) {
  std::ifstream fd("/proc/meminfo");
  if (!fd.is_open()) {
    std::cerr << "Failed to open /proc/meminfo" << std::endl;
    return;
  }
  fd.ignore(256, ' ') >> memStat->total;
  fd.ignore(256, '\n');
  fd.ignore(256, ' ') >> memStat->free;
  fd.ignore(256, '\n');
  fd.ignore(256, ' ') >> memStat->available;
}
float SystemInfo::CalculateCpuUsage(const CpuOccupy *oldStat,
                                    const CpuOccupy *newStat) {
  uint64_t oldTotal = 0, newTotal = 0;
  uint64_t idleDiff = 0, totalDiff = 0;
  float cpuUsage = 0.0f;
  oldTotal = oldStat->user + oldStat->nice + oldStat->system + oldStat->idle;
  newTotal = newStat->user + newStat->nice + newStat->system + newStat->idle;
  idleDiff = newStat->idle - oldStat->idle;
  totalDiff = newTotal - oldTotal;
  if (totalDiff != 0) {
    cpuUsage = static_cast<float>((totalDiff - idleDiff) * 100) / totalDiff;
  }
  return cpuUsage;
}
float SystemInfo::CalculateDiskUsage() {
  struct statvfs stat;
  const char *path = "/";  // Specify the disk path here
  if (statvfs(path, &stat) != 0) {
    std::cerr << "Failed to get disk usage for path: " << path << std::endl;
    return -1.0f;
  }
  uint64_t totalSpace = stat.f_blocks * stat.f_frsize;
  uint64_t freeSpace = stat.f_bfree * stat.f_frsize;
  uint64_t usedSpace = totalSpace - freeSpace;
  float usage = static_cast<float>(usedSpace) / totalSpace * 100.0f;
  return usage;
}
float SystemInfo::CalculateMemUsage(const MemOccupy *memStat) {
  float memUsage = 0.0f;
  if (memStat->total != 0) {
    memUsage = static_cast<float>(memStat->total - memStat->available) /
               memStat->total * 100;
  }
  return memUsage;
}

float SystemInfo::CalculatePingLatency() {
  std::string command = "ping -c 1 -W 1 " + server_;
  std::string output = executeCommand(command);
  std::istringstream iss(output);
  std::string line;
  while (std::getline(iss, line)) {
    if (line.find("time=") != std::string::npos) {
      std::size_t startPos = line.find("time=") + 6;
      std::size_t endPos = line.find(" ms");
      std::string latencyStr = line.substr(startPos, endPos - startPos);
      try {
        float latency = std::stof(latencyStr);
        return latency;
      } catch (const std::exception &e) {
        std::cerr << "Failed to parse latency: " << e.what() << std::endl;
        return 9999;
      }
    }
  }
  return 9999;
}

}  // namespace cotek_communicate
