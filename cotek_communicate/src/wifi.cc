#include "cotek_communicate/wifi.h"
#include <unordered_map>
#include <thread>
#include "cotek_common/log_porting.h"

static std::string netmaskToCIDR(const std::string& netmask) {
    static const std::map<std::string, std::string> table = {
        {"255.0.0.0", "8"},
        {"255.255.0.0", "16"},
        {"255.255.255.0", "24"},
        {"255.255.255.128", "25"},
        {"255.255.255.192", "26"},
        {"255.255.255.224", "27"},
        {"255.255.255.240", "28"},
        {"255.255.255.248", "29"},
        {"255.255.255.252", "30"},
        {"255.255.255.254", "31"},
        {"255.255.255.255", "32"}
    };
    auto it = table.find(netmask);
    if (it != table.end()) {
        return it->second;
    } else {
        LOG_ERROR_STREAM("not support mask: " << netmask);
        return "failed";
    } 
}

static std::string trim(const std::string& str) {
    auto start = str.find_first_not_of(" \t");
    auto end = str.find_last_not_of(" \t");
    if (start == std::string::npos) return "";
    return str.substr(start, end - start + 1);
}

// 处理转义字符的字符串分割函数
static std::vector<std::string> split_escaped(const std::string &s, char delimiter) {
    std::vector<std::string> tokens;
    std::string token;
    bool escaped = false;

    // 第一步：分割字段（处理转义分隔符）
    for (char c : s) {
        if (escaped) {
            token += c;
            escaped = false;
        } else if (c == '\\') {
            escaped = true;
        } else if (c == delimiter) {
            tokens.push_back(token);
            token.clear();
        } else {
            token += c;
        }
    }
    tokens.push_back(token);

    // 第二步：处理字段内的转义字符
    for (auto& t : tokens) {
        std::string processed;
        bool escape = false;
        for (char c : t) {
            if (escape) {
                processed += c;
                escape = false;
            } else if (c == '\\') {
                escape = true;
            } else {
                processed += c;
            }
        }
        t = processed;
    }

    return tokens;
}

static void removeDuplicateSSIDs(std::vector<WifiNetwork>& networks) {
    std::unordered_map<std::string, WifiNetwork> ssid_map;

    for (const auto& net : networks) {
        auto it = ssid_map.find(net.ssid);
        if (it == ssid_map.end()) {
            ssid_map[net.ssid] = net;
        } else {
            // 如果已有相同 SSID，保留信号强度更高的那个
            if (net.signalStrength > it->second.signalStrength) {
                ssid_map[net.ssid] = net;
            }
        }
    }

    // 将唯一的结果更新回原 vector
    networks.clear();
    for (const auto& pair : ssid_map) {
        networks.push_back(pair.second);
    }
}

static std::string getCurSSidCmd(const char* cmd) {
    std::array<char, 256> buffer{};
    std::string result;
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd, "r"), pclose);
    if (!pipe) throw std::runtime_error("popen() failed!");

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    // 去掉最后一个 '\n'
    if (!result.empty() && result.back() == '\n') result.pop_back();
    return result;
}

bool WiFi::forgetCurrentWifi() {
    try {
        std::string ssid = getCurSSidCmd("nmcli -t -f NAME connection show --active 2>/dev/null");
        if (ssid.empty()) {
            LOG_INFO_STREAM("wifi is disconnect.");
            return false;
        }
        LOG_INFO_STREAM("get wifi: " << ssid);

        std::string delCmd = "nmcli connection delete \"" + ssid + "\"";
        int ret = std::system(delCmd.c_str());
        if (ret == 0) {
            LOG_INFO("wifi(%s) forget succeed.", ssid.c_str());
            return true;
        } else {
            LOG_INFO("wifi(%s) forget failed: %d", ssid.c_str(), ret);
            return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("wifi forget err: %s", e.what());
        return false;
    }
}

std::string WiFi::execCommand(const std::string& cmd) {
    std::string result;
    char buffer[128];
    FILE* pipe = popen(cmd.c_str(), "r");
    if (!pipe) {
        LOG_ERROR_STREAM("command execute failed");
        return "failed";
    }

    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        result += buffer;
    }
    int status = pclose(pipe);
    if (status != 0) {
        LOG_ERROR_STREAM("command execute failed(" << std::to_string(status) << "), " << result);
        return "failed";
    }
    return result;
}

std::vector<WifiNetwork> WiFi::scan() {
    std::vector<WifiNetwork> networks;

    std::string password = "Cotek@123";    
    const std::string rescan_cmd = "echo \"" + password + "\" | sudo -S nmcli device wifi rescan 2>&1";
    LOG_INFO("updating wifi list");
    
    FILE* rescan_pipe = popen(rescan_cmd.c_str(), "r");
    if (!rescan_pipe) {
        LOG_ERROR_STREAM("wifi rescan command execute failed");
        return networks;
    }
    // std::this_thread::sleep_for(std::chrono::seconds(2));

    // 读取命令输出（用于错误诊断）
    char rescan_buffer[128];
    std::string rescan_output;
    while (fgets(rescan_buffer, sizeof(rescan_buffer), rescan_pipe) != nullptr) {
        rescan_output += rescan_buffer;
    }
    
    int rescan_status = pclose(rescan_pipe);
    if (rescan_status != 0) {
        LOG_WARN_STREAM("wifi rescan failed: " << rescan_output);
        return networks;
    }

    // 构造nmcli命令（明确指定输出字段）
    const std::string command = 
        "nmcli -t -f ssid,bssid,signal,security device wifi list";
    
    // 执行命令
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        LOG_ERROR("cant execute nmcli command");
        return networks;
    }

    // 读取命令输出
    char buffer[256];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        std::string line(buffer);
        
        // 移除换行符
        if (!line.empty() && line.back() == '\n') {
            line.pop_back();
        }

        // 分割字段
        std::vector<std::string> fields = split_escaped(line, ':');

        // 验证字段数量（至少4个）
        if (fields.size() >= 4) {
            WifiNetwork network;
            network.ssid = fields[0];
            network.bssid = fields[1];
            
            try {  // 转换信号强度
                network.signalStrength = std::stoi(fields[2]);
            } catch (const std::exception& e) {
                network.signalStrength = -1;  // 无效值标记
            }
            
            network.security = fields[3];
            networks.push_back(network);
        }
    }

    // 检查读取错误
    if (ferror(pipe)) {
        pclose(pipe);
        LOG_ERROR("get command result failed");
        return networks;
    }
    
    pclose(pipe);
    
    removeDuplicateSSIDs(networks); 
    int size = networks.size();
    for (int i=0; i<size; i++) {
        networks[i].vaild = true;
        LOG_INFO("ssid(%s)", networks[i].ssid.c_str());
    }

    return networks;
}

bool WiFi::DHCPConnect(const std::string& ssid, const std::string& password) {
    LOG_INFO_STREAM("dhcp connecting wifi: " << ssid);

    std::string pwd = "Cotek@123";
    std::string cmd;
    if (password != "") {
        cmd = "echo \"" + pwd + "\" | sudo -S nmcli device wifi connect \"" + ssid + "\" password \"" + password + "\" 2>&1";
    } else {
        cmd = "echo \"" + pwd + "\" | sudo -S nmcli device wifi connect \"" + ssid + "\" 2>&1";
    }

    std::string output = execCommand(cmd);
    LOG_INFO_STREAM("connect res: " << output);

    if (output == "failed") return false;
    else return true;
} 

bool WiFi::StaticConnect(const std::string& ssid,
                         const std::string& password,
                         const std::string& ip,
                         const std::string& netmask,
                         const std::string& gateway,
                         const std::string& dns) {
    std::string connName = ssid;
    std::string pwd = "Cotek@123";

    // 删除已有连接（如果存在）
    std::string deleteCmd = "echo \"" + pwd + "\" | sudo -S nmcli connection delete \"" + connName + "\" 2>/dev/null";
    system(deleteCmd.c_str());

    std::string cidr = netmaskToCIDR(netmask);
    if (cidr == "failed") return false;

    // 添加连接配置
    std::string cmd;
    if (password != "") {
      cmd = "echo \"" + pwd + "\" | sudo -S nmcli connection add type wifi ifname wlan0 con-name \"" + connName +
          "\" ssid \"" + ssid + "\" && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" wifi-sec.key-mgmt wpa-psk && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" wifi-sec.psk \"" + password + "\" && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.addresses " + ip + "/" + cidr + " && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.gateway " + gateway + " && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.dns \"" + dns + "\" && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.method manual && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection up \"" + connName + "\"";
    } else {
      cmd = "echo \"" + pwd + "\" | sudo -S nmcli connection add type wifi ifname wlan0 con-name \"" + connName +
          "\" ssid \"" + ssid + "\" && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.addresses " + ip + "/" + cidr + " && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.gateway " + gateway + " && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.dns \"" + dns + "\" && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection modify \"" + connName + "\" ipv4.method manual && " +
          "echo \"" + pwd + "\" | sudo -S nmcli connection up \"" + connName + "\"";
    }

    LOG_INFO_STREAM("static connecting wifi: " << ssid);

    std::string output = execCommand(cmd);
    LOG_INFO_STREAM("connect res: " << output);

    if (output == "failed") return false;
    else return true;
}

bool WiFi::disconnectWiFi() {
    try {
        std::string pwd = "Cotek@123";
        std::string device = "wlan0";

        const std::string cmd = "echo \"" + pwd + "\" | sudo -S nmcli device disconnect " + device;
        std::string output = execCommand(cmd);
        LOG_INFO_STREAM("connect res: " << output);

        if (output == "failed") return false;
        else return true;
    } catch (const std::exception& ex) {
        LOG_ERROR_STREAM("disconnect wifi failed: " << ex.what());
        return false;
    }
}

WifiStatus WiFi::getStatus() {
    WifiStatus status;
    const std::string cmd = "nmcli -t -f GENERAL,WIFI-PROPERTIES,IP4 device show";
    FILE* pipe = popen(cmd.c_str(), "r");
    if (!pipe) {
      LOG_ERROR("execute nmcli command failed");
      return status;
    }

    char buffer[512];
    std::unordered_map<std::string, std::string> properties;

    bool record = false;
    while (fgets(buffer, sizeof(buffer), pipe)) {
        std::string line(buffer);
        line.erase(std::remove(line.begin(), line.end(), '\n'), line.end());
        
        size_t colon = line.find(':');
        if (colon == std::string::npos) continue;

        std::string key = trim(line.substr(0, colon));
        std::string value = trim(line.substr(colon+1));
        
        if (key == "GENERAL.DEVICE") {
            record = false;
            if (value == "wlan0") {
                record = true;
            }
        }
		
        // 合并多行属性
        if (!key.empty() && !value.empty() && record) {
            properties[key] = value;
    	    // std::cout << "map: " << key << ", " << value << std::endl;
        }
    }

    int rc = pclose(pipe);
    if (rc != 0) {
        LOG_ERROR("get command result failed");
        return status;
    }
    status.vaild = true;

    // 解析属性
    status.state = properties.count("GENERAL.STATE") ? 
        [&]() {
            const std::string& state_str = properties.at("GENERAL.STATE");
            const size_t open = state_str.find('(');
            const size_t close = state_str.find(')', open);
            
            if (open != std::string::npos && 
                close != std::string::npos &&
                close > open + 1) {
                return state_str.substr(open + 1, close - open - 1);
            }
            return state_str.substr(0, state_str.find(' '));
        }()
        : "disconnected";

    if (status.state == "connected") {
      status.ssid = properties.count("GENERAL.CONNECTION") ? 
                  properties["GENERAL.CONNECTION"] : "";
      
      status.mac = properties.count("GENERAL.HWADDR") ? 
                  properties["GENERAL.HWADDR"] : "";

      std::string G2 = properties.count("WIFI-PROPERTIES.2GHZ") ?
                      properties["WIFI-PROPERTIES.2GHZ"] : "";
      std::string G5 = properties.count("WIFI-PROPERTIES.5GHZ") ?
                      properties["WIFI-PROPERTIES.5GHZ"] : "";
                      
      if (G2 == "yes") {
          status.bandwidth = "2.4";
      }
      if (G5 == "yes") {
          status.bandwidth = "5";
      }

      // 构造nmcli命令（明确指定输出字段）
      const std::string command =
          "nmcli -t -f ssid,bssid,signal,security device wifi list";

      // 执行命令
      pipe = popen(command.c_str(), "r");
      if (!pipe) {
          throw std::runtime_error("无法执行nmcli命令");
      }

      // 读取命令输出
      memset(buffer, 0, sizeof(buffer));
      while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
          std::string line(buffer);
          // 移除换行符
          if (!line.empty() && line.back() == '\n') {
              line.pop_back();
          }

          // 分割字段
          std::vector<std::string> fields = split_escaped(line, ':');

          // 验证字段数量（至少4个）
          if (fields.size() >= 4) {
              if (fields[0] == status.ssid) {
                  status.signal = std::stoi(fields[2]);	
              }
              status.security = fields[3];
          }
      }

      // 检查读取错误
      if (ferror(pipe)) {
          pclose(pipe);
          LOG_ERROR("get command result failed");
          return status;
      }

      pclose(pipe);

      // 处理IP地址（可能有多个）
      if (properties.count("IP4.ADDRESS[1]")) {
          std::string ip_line = properties["IP4.ADDRESS[1]"];
          size_t slash = ip_line.find('/');
          if (slash != std::string::npos) {
              status.ip = ip_line.substr(0, slash);
          }
      }
    }

    return status;
}