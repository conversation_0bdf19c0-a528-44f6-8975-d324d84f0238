/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "cotek_communicate/resource_agent.h"

#include <Poco/DirectoryIterator.h>
#include <Poco/File.h>
#include <Poco/FileStream.h>
#include <Poco/Path.h>
#include <Poco/Zip/Compress.h>
#include <Poco/Zip/Decompress.h>
#include <bluetooth/bluetooth.h>
#include <bluetooth/rfcomm.h>
#include <dbus/dbus.h>
#include <fcntl.h>
#include <poll.h>
#include <sys/sendfile.h>
#include <sys/socket.h>
#include <unistd.h>

#include <cstddef>
#include <cstdint>
#include <exception>
#include <fstream>
#include <iostream>
#include <memory>
#include <string>
#include <thread>

#include "cotek_common/log_porting.h"
#include "cotek_communicate/communicate_method/communicate_interface.h"
#include "cotek_communicate/data_manager.h"
#include "ros/duration.h"
#include "ros/init.h"
#include "ros/rate.h"
#include "ros/time.h"

namespace cotek_communicate {

// Utility function to initialize DBus connection
static DBusConnection *init_dbus_connection() {
  DBusError err;
  dbus_error_init(&err);

  DBusConnection *conn = dbus_bus_get(DBUS_BUS_SYSTEM, &err);
  if (dbus_error_is_set(&err)) {
    LOG_ERROR_STREAM("DBus Connection Error (" << err.name
                                               << "): " << err.message);
    dbus_error_free(&err);
    return nullptr;
  }

  if (!conn) {
    LOG_ERROR_STREAM("DBus connection is null");
  }

  return conn;
}

// Check if discovery is already in progress
static bool is_discovery_in_progress(DBusConnection *conn,
                                     const std::string &adapter_path) {
  DBusMessage *msg =
      dbus_message_new_method_call("org.bluez", adapter_path.c_str(),
                                   "org.freedesktop.DBus.Properties", "Get");

  if (!msg) {
    LOG_ERROR_STREAM("Failed to create Get message for Discovering property");
    return false;
  }

  const char *interface = "org.bluez.Adapter1";
  const char *property = "Discovering";

  if (!dbus_message_append_args(msg, DBUS_TYPE_STRING, &interface,
                                DBUS_TYPE_STRING, &property,
                                DBUS_TYPE_INVALID)) {
    LOG_ERROR_STREAM("Failed to append arguments to Get message");
    dbus_message_unref(msg);
    return false;
  }

  DBusError err;
  dbus_error_init(&err);

  DBusMessage *reply =
      dbus_connection_send_with_reply_and_block(conn, msg, -1, &err);
  dbus_message_unref(msg);

  if (dbus_error_is_set(&err)) {
    LOG_ERROR_STREAM("Get Discovering property error ("
                     << err.name << "): " << err.message);
    dbus_error_free(&err);
    return false;
  }

  if (!reply) {
    LOG_ERROR_STREAM("Get Discovering reply is null");
    return false;
  }

  DBusMessageIter iter;
  if (dbus_message_iter_init(reply, &iter)) {
    if (dbus_message_iter_get_arg_type(&iter) == DBUS_TYPE_VARIANT) {
      DBusMessageIter variant_iter;
      dbus_message_iter_recurse(&iter, &variant_iter);

      if (dbus_message_iter_get_arg_type(&variant_iter) == DBUS_TYPE_BOOLEAN) {
        bool discovering;
        dbus_message_iter_get_basic(&variant_iter, &discovering);
        dbus_message_unref(reply);
        return discovering;
      }
    }
  }

  dbus_message_unref(reply);
  return false;
}

// Start discovery to find nearby devices
static bool start_discovery(DBusConnection *conn,
                            const std::string &adapter_path) {
  // Check if discovery is already in progress
  if (is_discovery_in_progress(conn, adapter_path)) {
    LOG_INFO_STREAM(
        "Discovery is already in progress, skipping StartDiscovery");
    return true;
  }

  DBusMessage *msg =
      dbus_message_new_method_call("org.bluez", adapter_path.c_str(),
                                   "org.bluez.Adapter1", "StartDiscovery");

  if (!msg) {
    LOG_ERROR_STREAM("Failed to create StartDiscovery message");
    return false;
  }

  DBusError err;
  dbus_error_init(&err);

  DBusMessage *reply =
      dbus_connection_send_with_reply_and_block(conn, msg, -1, &err);
  dbus_message_unref(msg);

  if (dbus_error_is_set(&err)) {
    LOG_ERROR_STREAM("StartDiscovery Error (" << err.name
                                              << "): " << err.message);
    dbus_error_free(&err);
    return false;
  }

  if (!reply) {
    LOG_ERROR_STREAM("StartDiscovery reply is null");
    return false;
  }

  dbus_message_unref(reply);
  return true;
}

// Check if the device is already paired
static bool is_device_paired(DBusConnection *conn,
                             const std::string &device_path) {
  DBusMessage *msg =
      dbus_message_new_method_call("org.bluez", device_path.c_str(),
                                   "org.freedesktop.DBus.Properties", "Get");

  if (!msg) {
    LOG_ERROR_STREAM("Failed to create Get message for Paired property");
    return false;
  }

  const char *interface = "org.bluez.Device1";
  const char *property = "Paired";

  if (!dbus_message_append_args(msg, DBUS_TYPE_STRING, &interface,
                                DBUS_TYPE_STRING, &property,
                                DBUS_TYPE_INVALID)) {
    LOG_ERROR_STREAM("Failed to append arguments to Get message");
    dbus_message_unref(msg);
    return false;
  }

  DBusError err;
  dbus_error_init(&err);

  DBusMessage *reply =
      dbus_connection_send_with_reply_and_block(conn, msg, -1, &err);
  dbus_message_unref(msg);

  if (dbus_error_is_set(&err)) {
    LOG_ERROR_STREAM("Get Paired property error (" << err.name << "): ");
    dbus_error_free(&err);
    return false;
  }

  if (!reply) {
    LOG_ERROR_STREAM("Get Paired reply is null");

    return false;
  }

  DBusMessageIter iter;
  if (dbus_message_iter_init(reply, &iter)) {
    if (dbus_message_iter_get_arg_type(&iter) == DBUS_TYPE_VARIANT) {
      DBusMessageIter variant_iter;
      dbus_message_iter_recurse(&iter, &variant_iter);

      if (dbus_message_iter_get_arg_type(&variant_iter) == DBUS_TYPE_BOOLEAN) {
        bool paired;
        dbus_message_iter_get_basic(&variant_iter, &paired);
        dbus_message_unref(reply);
        return paired;
      }
    }
  }

  dbus_message_unref(reply);
  return false;
}

// Pair the Bluetooth device
static bool pair_bluetooth_device(const char *address) {
  if (!address || strlen(address) != 17) {
    LOG_ERROR_STREAM("Invalid Bluetooth address");
    return false;
  }

  DBusConnection *conn = init_dbus_connection();
  if (!conn) {
    LOG_ERROR_STREAM("init_dbus_connection error");
    return false;
  }

  std::string adapter_path = "/org/bluez/hci0";
  std::string device_path = adapter_path + "/dev_" + address;
  std::replace(device_path.begin(), device_path.end(), ':',
               '_');  // Replace ':' with '_'

  // Start discovery to ensure the device is discoverable
  if (!start_discovery(conn, adapter_path)) {
    dbus_connection_unref(conn);  // Ensure connection is released
    LOG_ERROR_STREAM("start_discovery error");
    return false;
  }

  sleep(3);  // Allow time for discovery

  // Check if the device is already paired
  if (is_device_paired(conn, device_path)) {
    LOG_ERROR_STREAM("Device is already paired: " << device_path);
    dbus_connection_unref(conn);  // Ensure connection is released
    return true;
  }

  // Call Pair method
  DBusMessage *msg = dbus_message_new_method_call(
      "org.bluez", device_path.c_str(), "org.bluez.Device1", "Pair");

  if (!msg) {
    LOG_ERROR_STREAM("Failed to create Pair message");
    dbus_connection_unref(conn);  // Ensure connection is released
    return false;
  }

  DBusError err;
  dbus_error_init(&err);

  DBusMessage *reply =
      dbus_connection_send_with_reply_and_block(conn, msg, -1, &err);
  dbus_message_unref(msg);  // Release the Pair message

  if (dbus_error_is_set(&err)) {
    LOG_ERROR_STREAM("Pair Error (" << err.name << "): " << err.message);
    dbus_error_free(&err);
    dbus_connection_unref(conn);  // Ensure connection is released
    return false;
  }

  if (!reply) {
    LOG_ERROR_STREAM("Pair reply is null");
    dbus_connection_unref(conn);  // Ensure connection is released
    return false;
  }

  dbus_message_unref(reply);    // Release the reply message
  dbus_connection_unref(conn);  // Release the connection
  return true;
}

// 移动文件夹
static void moveDirectory(const std::string &sourcePath,
                          const std::string &destinationPath) {
  Poco::File sourceDir(sourcePath);
  if (sourceDir.exists() && sourceDir.isDirectory()) {
    Poco::File destDir(destinationPath);
    if (destDir.exists()) {
      destDir.remove(true);  // 如果目标目录存在，则递归删除
    }
    sourceDir.moveTo(destinationPath);
  } else {
    LOG_ERROR_STREAM("Source directory does not exist or is not a directory: "
                     << sourcePath);
  }
}

// 解压文件
static void unzipFile(const std::string &zipFilePath,
                      const std::string &destPath) {
  std::ifstream file(zipFilePath, std::ios::binary);
  Poco::Zip::Decompress decompressor(file, Poco::Path(destPath));
  decompressor.decompressAllFiles();
}

// 去除相对路径开头的斜杠
static std::string getRelativePath(const Poco::Path &fullPath,
                                   const std::string &basePath) {
  std::string relativePath = fullPath.toString().substr(basePath.length());
  if (!relativePath.empty() && relativePath[0] == '/') {
    relativePath = relativePath.substr(1);
  }
  return relativePath;
}

// 递归添加目录及其内容到压缩包
static void addDirectoryToZip(Poco::Zip::Compress &compress,
                              const Poco::Path &path,
                              const std::string &basePath) {
  for (Poco::DirectoryIterator it(path); it != Poco::DirectoryIterator();
       ++it) {
    if (it->isDirectory()) {
      addDirectoryToZip(compress, it.path(), basePath);
    } else {
      try {
        compress.addFile(it.path().toString(),
                         getRelativePath(it.path(), basePath));
      } catch (const Poco::Exception &ex) {
        LOG_ERROR_STREAM("Error adding file: " << ex.displayText() << " ("
                                               << it.path().toString() << ")");
      }
    }
  }
}

// 压缩目录
static void compressDirectory(const std::string &dirPath,
                              const std::string &zipFilePath) {
  std::ofstream ofs(zipFilePath, std::ios::binary);
  Poco::Zip::Compress compress(ofs, true);

  Poco::Path path(dirPath);
  std::string basePath = path.toString();
  if (!basePath.empty() && basePath.back() != '/') {
    basePath += "/";
  }
  addDirectoryToZip(compress, path, basePath);

  compress.close();  // finalize the archive
  ofs.close();
}

static std::string GetBlzStateStr(const BlueZState &blz_state) {
  std::string state_str;
  switch (blz_state) {
    case BlueZState::NONE: {
      state_str = "none";
      break;
    }
    case BlueZState::PRE_SYNC: {
      state_str = "preSync";
      break;
    }
    case BlueZState::SYNCING: {
      state_str = "syncing";
      break;
    }
    case BlueZState::SYNC_SUCCEED: {
      state_str = "syncSucceed";
      break;
    }
    case BlueZState::SYNC_FAILD: {
      state_str = "syncFailed";
      break;
    }
    case BlueZState::SYNC_CANCLE: {
      state_str = "syncCancle";
      break;
    }
  }
  return state_str;
}

class Socket {
 public:
  Socket() : s(socket(AF_BLUETOOTH, SOCK_STREAM, BTPROTO_RFCOMM)) {
    if (s < 0) {
      throw std::runtime_error("Failed to create Bluetooth socket");
    }
  }
  ~Socket() {
    if (s >= 0) {
      close(s);
    }
  }
  // 禁止拷贝
  Socket(const Socket &) = delete;
  Socket &operator=(const Socket &) = delete;

  int get() const { return s; }

 private:
  int s;
};

int ResourceObj::Sync() {
  std::unique_lock<std::mutex> lock(sync_mutex_);
  if (syncing_) return 1;
  syncing_ = true;
  auto no_block_sync = [&]() { BlzSync(); };
  thread_pool_.enqueue(no_block_sync);

  return 0;
}

static void connectWithTimeout(Socket &socketObj, const sockaddr_rc &addr,
                               int timeoutSeconds) {
  pollfd pfd = {socketObj.get(), POLLOUT, 0};
  int pollResult = poll(&pfd, 1, timeoutSeconds * 1000);
  if (pollResult <= 0) {
    throw std::runtime_error("Connection attempt timed out");
  }
  if (connect(socketObj.get(), (struct sockaddr *)&addr, sizeof(addr)) < 0 &&
      errno != EISCONN) {
    throw std::runtime_error("Failed to connect to Bluetooth server");
  }
  LOG_INFO("connect bluesuccess");
}

static bool waitForResponse(Socket &socketObj, const std::string &sendStr,
                            const std::string &expectedResponse,
                            int timeoutSeconds) {
  pollfd readFd = {socketObj.get(), POLLIN, 0};
  ros::Time startTime = ros::Time::now();

  while (ros::Time::now() - startTime < ros::Duration(timeoutSeconds)) {
    if (send(socketObj.get(), sendStr.c_str(), sendStr.size(), 0) < 0) {
      throw std::runtime_error("Failed to send data");
    }

    int pollResult = poll(&readFd, 1, 5000);  // 每次等待 5 秒
    if (pollResult > 0) {
      char buffer[1024];
      ssize_t bytesReceived = recv(socketObj.get(), buffer, sizeof(buffer), 0);
      if (bytesReceived > 0) {
        std::string receivedStr(buffer, bytesReceived);
        if (receivedStr == expectedResponse) {
          LOG_INFO("Received confirmation: %s", expectedResponse.c_str());
          return true;
        }
      }
    }
  }

  return false;  // 超时
}

void ResourceObj::BlzSync() {
  int retry_cnt = 0;
  bool success = false;

  UpdateBlueState(BlueZState::SYNCING);
  UpdateCancleSync(false);

  while (retry_cnt < 3 && !success) {
    struct sockaddr_rc addr = {0};
    Socket socketObj;
    try {
      // 设置蓝牙地址和通道
      addr.rc_family = AF_BLUETOOTH;
      addr.rc_channel = 1;
      if (str2ba(resource_.bluetooth_mac.c_str(), &addr.rc_bdaddr) == -1) {
        throw std::runtime_error("Invalid Bluetooth MAC address");
      }
      LOG_INFO("create blue(%s)", resource_.bluetooth_mac.c_str());

      // 蓝牙设备配对
      if (!pair_bluetooth_device(resource_.bluetooth_mac.c_str())) {
        throw std::runtime_error("Failed to pair with Bluetooth device");
      }

      // 连接蓝牙设备，带超时处理
      connectWithTimeout(socketObj, addr, 30);

      // 等待 "FILE START" 确认
      if (!waitForResponse(socketObj, "FILE START", "CONFIRM START", 20)) {
        throw std::runtime_error("Blz start send timeout");
      }

      // 文件压缩
      std::string userDir = Poco::Path::home();
      std::string sourceDir = userDir + "/config/map";
      std::string targetFile = userDir + "/config/map.zip";
      compressDirectory(sourceDir, targetFile);
      LOG_INFO_STREAM(sourceDir << " compressed successfully to "
                                << targetFile);

      std::ifstream file(targetFile, std::ios::binary);
      if (!file) {
        throw std::runtime_error("Failed to open file");
      }

      file.seekg(0, std::ios::end);
      size_t fileSize = file.tellg();
      file.seekg(0, std::ios::beg);

      char buffer[1016] = {0};
      size_t bytesSent = 0;

      while (size_t bytesRead = file.readsome(buffer, sizeof(buffer))) {
        if (send(socketObj.get(), buffer, bytesRead, 0) < 0) {
          throw std::runtime_error("Failed to send file data");
        }
        // 添加蓝牙同步取消
        if (cancle_sync_) {
          retry_cnt = 999;
          throw std::runtime_error("Cancel sync");
        }
        bytesSent += bytesRead;
        UpdateProcessRatio(static_cast<double>(bytesSent) / fileSize * 100);
      }

      LOG_INFO("File sent successfully, total bytes: %zu", bytesSent);

      // 等待 "FILE END" 确认
      if (!waitForResponse(socketObj, "FILE END", "CONFIRM END", 20)) {
        throw std::runtime_error("Blz end send timeout");
      }

      success = true;

    } catch (const std::runtime_error &e) {
      close(socketObj.get());
      LOG_ERROR(e.what());
    }

    retry_cnt++;
  }

  if (success) {
    UpdateBlueState(BlueZState::SYNC_SUCCEED);
  } else if (cancle_sync_) {
    UpdateBlueState(BlueZState::SYNC_CANCLE);
    UpdateCancleSync(false);
  } else {
    UpdateBlueState(BlueZState::SYNC_FAILD);
  }

  // 同步结束
  {
    std::unique_lock<std::mutex> lock(sync_mutex_);
    syncing_ = false;
  }
}

bool ResourceObj::UpdateResourceInfo(const ResoureInfo &info) {
  resource_.id = info.id;
  resource_.bluetooth_mac = info.bluetooth_mac;
  resource_.wifi_mac = info.wifi_mac;
  resource_.map_ver = info.map_ver;
  resource_.path_ver = info.path_ver;
  resource_.software_ver = info.software_ver;
  return true;
}

bool ResourceObj::UpdateCancleSync(const bool &type) {
  cancle_sync_ = type;
  return true;
}

bool ResourceObj::UpdateBlueState(const BlueZState &state) {
  std::unique_lock<std::mutex> lock(mutex_);
  resource_.blz_state = state;
  return true;
}

bool ResourceObj::UpdateProcessRatio(const uint8_t &ratio) {
  std::unique_lock<std::mutex> lock(mutex_);
  resource_.update_ratio = ratio;
  return true;
}

ResourceAgent::ResourceAgent()
    : runner_ptr_(nullptr),
      lora_manager_ptr_(std::make_shared<LoraManager>()),
      thread_pool_(1) {}

bool ResourceAgent::Init(const CommunicateOption &option) {
  bool ret = false;
  option_ = option;
  runner_ptr_ =
      std::make_shared<std::thread>(&ResourceAgent::SyncWorkHandler, this);

  ret = lora_manager_ptr_->Init(option);

  return ret;
}

bool ResourceAgent::UpdateResource(const ResoureInfo &info) {
  std::unique_lock<std::mutex> lock(mutex_);
  if (resource_map_.count(info.id) == 0) {
    resource_map_[info.id] = std::make_shared<ResourceObj>(info);
    return true;
  }
  resource_map_[info.id]->UpdateResourceInfo(info);
  return true;
}

bool ResourceAgent::UpdateTargetResoureBlueState(const uint16_t &id,
                                                 const BlueZState &state) {
  if (resource_map_.count(id) == 0) {
    return false;
  }
  if (resource_map_[id]->GetResoureInfo().blz_state != BlueZState::SYNCING) {
    resource_map_[id]->UpdateBlueState(state);
  }
  return true;
}

bool ResourceAgent::UpdateTargetResoureBlueCancleState(const uint16_t &id,
                                                       const bool state) {
  if (resource_map_.count(id) == 0) {
    return false;
  }
  resource_map_[id]->UpdateCancleSync(state);
  return true;
}

Json ResourceAgent::GetResouresInfo() {
  ProtoclMessageLora msg;
  msg.set_serialnum(option_.lora_option.lora_id);
  msg.set_fun_code(LoraFunCode::QUERY_RESOURCE);
  std::vector<uint8_t> data;
  data.push_back(0XFF);
  msg.set_data(data);
  if (msg.PackMsg()) LoraSend(msg, Qos::QOS0);

  Json json, sync_json, amrs;

  // TODO(@ssh) 后续添加本机版本号，数据里获取
  sync_json["curreVersion"] = "1_2_3";

  std::unique_lock<std::mutex> lock(mutex_);
  for (auto &resource : resource_map_) {
    Json info_json;
    auto &&info = resource.second->GetResoureInfo();
    info_json["id"] = info.id;
    info_json["name"] = std::to_string(info.id) + std::string("号车");
    info_json["version"] = std::to_string(info.software_ver) + "_" +
                           std::to_string(info.map_ver) + "_" +
                           std::to_string(info.path_ver);
    info_json["state"] = GetBlzStateStr(info.blz_state);
    info_json["ratio"] = info.update_ratio;
    amrs.push_back(info_json);
  }
  sync_json["amrLists"] = amrs;
  json["sync"] = sync_json;
  return json;
}

void ResourceAgent::SyncWorkHandler() {
  ros::Rate rate(1.0);

  while (ros::ok()) {
    try {
      std::unique_lock<std::mutex> lock(mutex_);
      if (!process_obj_queue_.empty()) {
        auto obj = process_obj_queue_.front();
        lock.unlock();

        if (obj) {
          ResoureInfo &&info = obj->GetResoureInfo();
          auto state = info.blz_state;
          switch (state) {
            case BlueZState::NONE: {
              LOG_INFO_THROTTLE(1, "Blue(%s) request sync!!!",
                                info.bluetooth_mac.c_str());
              // 发送蓝牙同步请求
              ProtoclMessageLora msg;
              msg.set_serialnum(option_.lora_option.lora_id);
              msg.set_fun_code(LoraFunCode::REQUEST_SYNC);
              std::vector<uint8_t> data;
              data.push_back(info.id);
              data.push_back(option_.lora_option.lora_id);
              std::vector<uint8_t> mac_data;
              if (!convertMacStringToVector(info.bluetooth_mac, mac_data))
                break;
              data.insert(data.end(), mac_data.begin(), mac_data.end());
              msg.set_data(data);
              if (!msg.PackMsg()) break;
              LoraSend(msg, Qos::QOS0);
              break;
            }

            case BlueZState::PRE_SYNC: {
              LOG_INFO_THROTTLE(1, "Blue(%s) pre sync!!!",
                                info.bluetooth_mac.c_str());
              obj->Sync();
              UpdateTargetResoureBlueState(info.id, BlueZState::SYNCING);
              break;
            }

            case BlueZState::SYNCING: {
              LOG_INFO_THROTTLE(2, "Blue(%s) syncing(%d)...",
                                info.bluetooth_mac.c_str(),
                                (int)info.update_ratio);
              break;
            }

            case BlueZState::SYNC_SUCCEED: {
              std::unique_lock<std::mutex> lock(mutex_);
              process_obj_queue_.pop();
              LOG_INFO("Blue(%s) sync succeed!!!", info.bluetooth_mac.c_str());
              break;
            }
            case BlueZState::SYNC_FAILD: {
              std::unique_lock<std::mutex> lock(mutex_);
              process_obj_queue_.pop();
              LOG_INFO("Blue(%s) sync faild!!!", info.bluetooth_mac.c_str());
              break;
            }
            case BlueZState::SYNC_CANCLE: {
              std::unique_lock<std::mutex> lock(mutex_);
              process_obj_queue_.pop();
              LOG_INFO("Blue(%s) sync cancle!!!", info.bluetooth_mac.c_str());
              break;
            }
            default:
              break;
          }
        }
      }
    } catch (const std::exception &ex) {
      LOG_ERROR(ex.what());
    }

    rate.sleep();
  }
}

bool ResourceAgent::LoraSend(const ProtoclMessageLora &msg, const Qos &qos) {
  if (!lora_manager_ptr_) return false;
  return lora_manager_ptr_->Send(msg, qos);
}

bool ResourceAgent::HandleObjsSync(const std::vector<uint16_t> &infos) {
  std::unique_lock<std::mutex> lock(mutex_);
  if (!process_obj_queue_.empty()) return false;
  if (resource_map_.empty()) return false;
  for (auto &&info : infos) {
    if (resource_map_.count(info) > 0) {
      process_obj_queue_.push(resource_map_[info]);
      // 每次蓝牙数据同步 清空现有对象蓝牙状态
      UpdateTargetResoureBlueState(info, BlueZState::NONE);
      resource_map_[info]->UpdateProcessRatio(0);
    }
  }
  return true;
}

bool ResourceAgent::HandleObjsCancleSync(const std::vector<uint16_t> &infos) {
  std::unique_lock<std::mutex> lock(mutex_);
  if (process_obj_queue_.empty()) return false;
  if (resource_map_.empty()) return false;
  for (auto &&info : infos) {
    if (resource_map_.count(info) > 0) {
      UpdateTargetResoureBlueCancleState(info, true);
    }
  }
  return true;
}

void ResourceAgent::SendUpdateEvent(
    const cotek_msgs::update_event::ConstPtr &msg) {
  if (lora_manager_ptr_) lora_manager_ptr_->SendUpdateEvent(msg);
}

void ResourceAgent::SendTrafficArea(
    const cotek_msgs::traffic_state::ConstPtr &msg) {
  if (lora_manager_ptr_) lora_manager_ptr_->SendTrafficArea(msg);
}

int ResourceAgent::CreateBlueListenThread() {
  std::unique_lock<std::mutex> lock(listen_mutex_);
  if (listening_) return 1;

  auto no_block_listen = [&]() { BlzListen(); };
  thread_pool_.enqueue(no_block_listen);

  return 0;
}

static void ConfigureBluetoothSocket(struct sockaddr_rc &addr) {
  addr.rc_family = AF_BLUETOOTH;  // Specify the Bluetooth protocol family.
  addr.rc_channel = 1;            // Use channel 1 for communication.

  LOG_INFO("Configuring Bluetooth socket with channel: %d", addr.rc_channel);

  if (str2ba(DataManager::Instance().GetBlueMac().c_str(), &addr.rc_bdaddr) ==
      -1) {
    LOG_ERROR("Invalid Bluetooth MAC address: %s",
              DataManager::Instance().GetBlueMac().c_str());
    throw std::runtime_error("Invalid Bluetooth MAC address");
  }

  LOG_INFO("Bluetooth socket configured for address: %s",
           DataManager::Instance().GetBlueMac().c_str());
}

static int WaitForConnection(Socket &socket_obj,
                             struct sockaddr_rc &remote_addr, int timeout_sec) {
  LOG_INFO("Waiting for incoming connections with a timeout of %d seconds",
           timeout_sec);

  struct pollfd pfd = {socket_obj.get(), POLLIN, 0};
  int poll_result = poll(&pfd, 1, timeout_sec * 1000);

  if (poll_result <= 0) {
    if (poll_result == 0) {
      LOG_WARN("Connection attempt timed out");
    } else {
      LOG_ERROR("Poll error during connection wait: %s", strerror(errno));
    }
    throw std::runtime_error("Accepting connection timed out");
  }

  socklen_t addr_len =
      sizeof(remote_addr);  // Ensure correct size for the address structure.
  int client_sock = accept(socket_obj.get(),
                           reinterpret_cast<struct sockaddr *>(&remote_addr),
                           &addr_len);  // Pass the correct length pointer.

  if (client_sock < 0) {
    LOG_ERROR("Failed to accept connection: %s", strerror(errno));
    throw std::runtime_error("Failed to accept connection");
  }

  LOG_INFO("Connection accepted successfully");
  return client_sock;
}

static std::string BluetoothAddressToString(
    const struct sockaddr_rc &remote_addr) {
  char addr_buffer[1024] = {0};
  ba2str(&remote_addr.rc_bdaddr, addr_buffer);
  LOG_INFO("Parsed Bluetooth address: %s", addr_buffer);
  return std::string(addr_buffer);
}

static void SetSocketNonBlocking(int sock_fd) {
  LOG_INFO("Setting socket %d to non-blocking mode", sock_fd);
  int flags = fcntl(sock_fd, F_GETFL, 0);
  if (fcntl(sock_fd, F_SETFL, flags | O_NONBLOCK) < 0) {
    LOG_ERROR("Failed to set socket %d to non-blocking mode: %s", sock_fd,
              strerror(errno));
    throw std::runtime_error("Failed to set socket to non-blocking mode");
  }
  LOG_INFO("Socket %d set to non-blocking mode successfully", sock_fd);
}

static void WaitForSignal(int sock_fd, const std::string &expected_signal,
                          const std::string &confirm_signal, int read_timeout,
                          int max_duration) {
  LOG_INFO("Waiting for signal: '%s' with a timeout of %d seconds",
           expected_signal.c_str(), read_timeout);

  ros::Time start_time = ros::Time::now();
  struct pollfd pfd = {sock_fd, POLLIN, 0};
  char buffer[1024] = {0};

  while (ros::Time::now() - start_time < ros::Duration(max_duration)) {
    int poll_result = poll(&pfd, 1, read_timeout * 1000);

    if (poll_result > 0) {
      ssize_t bytes_received = recv(sock_fd, buffer, sizeof(buffer), 0);
      if (bytes_received > 0) {
        std::string received_signal(buffer, bytes_received);
        LOG_INFO("Received signal: '%s'", received_signal.c_str());
        if (received_signal == expected_signal) {
          LOG_INFO("Expected signal received. Sending confirmation: '%s'",
                   confirm_signal.c_str());
          if (send(sock_fd, confirm_signal.c_str(), confirm_signal.size(), 0) <
              0) {
            LOG_ERROR("Failed to send confirmation signal: %s",
                      strerror(errno));
            throw std::runtime_error("Failed to send confirmation signal");
          }
          LOG_INFO("Confirmation signal sent successfully");
          return;
        }
      }
    } else if (poll_result == 0) {
      LOG_WARN("Waiting for signal timed out, retrying...");
    } else {
      LOG_ERROR("Poll error: %s", strerror(errno));
      throw std::runtime_error("Poll error during signal wait");
    }
  }
  LOG_ERROR("Timed out waiting for signal: '%s'", expected_signal.c_str());
  throw std::runtime_error("Timed out waiting for signal");
}

static void ReceiveFile(int sock_fd, const std::string &filename,
                        int read_timeout, int max_duration, int buffer_size) {
  LOG_INFO("Starting file reception. Destination: '%s'", filename.c_str());

  std::ofstream out_file(filename, std::ios::binary);
  if (!out_file.is_open()) {
    LOG_ERROR("Failed to open file '%s' for writing", filename.c_str());
    throw std::runtime_error("Failed to open file for writing");
  }

  ros::Time start_time = ros::Time::now();
  struct pollfd pfd = {sock_fd, POLLIN, 0};
  char buffer[buffer_size];
  const std::string end_marker = "FILE END";

  while (ros::Time::now() - start_time < ros::Duration(max_duration)) {
    int poll_result = poll(&pfd, 1, read_timeout * 1000);

    if (poll_result > 0) {
      ssize_t bytes_received = recv(sock_fd, buffer, sizeof(buffer), 0);
      if (bytes_received > 0) {
        std::string received_data(buffer, bytes_received);

        // Check for the file end marker within the received data.
        size_t end_pos = received_data.find(end_marker);
        if (end_pos != std::string::npos) {
          LOG_INFO("Detected file transfer end marker");
          out_file.write(received_data.c_str(),
                         end_pos);  // Write up to the marker.
          break;  // Exit the loop after processing the end marker.
        } else {
          out_file.write(received_data.c_str(),
                         bytes_received);  // Write all data.
        }

        start_time =
            ros::Time::now();  // Reset timeout timer after receiving data.
      } else if (bytes_received == 0) {
        LOG_WARN("Client disconnected during file reception");
        break;
      } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
        LOG_ERROR("Receive error: %s", strerror(errno));
        throw std::runtime_error("Receive error");
      }
    } else if (poll_result == 0) {
      LOG_WARN("Receiving data timed out, retrying...");
    } else {
      LOG_ERROR("Poll error: %s", strerror(errno));
      throw std::runtime_error("Poll error during file reception");
    }
  }

  LOG_INFO("File reception complete. Closing file '%s'", filename.c_str());
  out_file.close();
}

static void BackupAndExtractMap() {
  LOG_INFO("Starting map backup and extraction process");

  std::string source_dir = Poco::Path::home() + "config/map";
  std::string dest_dir = Poco::Path::home() + "config/map.bak";
  moveDirectory(source_dir, dest_dir);

  LOG_INFO("Backup completed: '%s' -> '%s'", source_dir.c_str(),
           dest_dir.c_str());

  std::string zip_file = Poco::Path::home() + "back.zip";
  std::ifstream file(zip_file, std::ios::binary);
  Poco::Zip::Decompress decompress(file, source_dir);

  LOG_INFO("Extracting files from '%s' to '%s'", zip_file.c_str(),
           source_dir.c_str());
  decompress.decompressAllFiles();

  LOG_INFO("Map extraction complete");
}

static void HandleFileTransfer(int client_sock, int read_timeout,
                               int max_duration, int buffer_size) {
  LOG_INFO("Handling file transfer on socket %d", client_sock);

  SetSocketNonBlocking(client_sock);

  std::string start_signal = "FILE START";
  std::string confirm_start = "CONFIRM START";
  WaitForSignal(client_sock, start_signal, confirm_start, read_timeout,
                max_duration);

  std::string filename = Poco::Path::home() + "back.zip";
  ReceiveFile(client_sock, filename, read_timeout, max_duration, buffer_size);

  std::string end_signal = "FILE END";
  std::string confirm_end = "CONFIRM END";
  WaitForSignal(client_sock, end_signal, confirm_end, read_timeout,
                max_duration);

  LOG_INFO("File transfer process completed successfully");
}

void ResourceAgent::BlzListen() {
  constexpr int kReadTimeoutSec = 20;
  constexpr int kMaxBufferSize = 1024;
  constexpr int kMaxReceiveDurationSec = 60;

  struct sockaddr_rc addr = {0},
                     remote_addr = {
                         0};  // Initialize address structures to zero.
  Socket socket_obj;

  try {
    LOG_INFO("Starting Bluetooth listening process");
    ConfigureBluetoothSocket(addr);

    if (bind(socket_obj.get(), reinterpret_cast<const struct sockaddr *>(&addr),
             sizeof(addr)) < 0) {
      LOG_ERROR("Failed to bind Bluetooth socket: %s", strerror(errno));
      throw std::runtime_error("Failed to bind to Bluetooth socket");
    }

    LOG_INFO("Bound to Bluetooth (%s) successfully",
             DataManager::Instance().GetBlueMac().c_str());
    listening_ = true;

    LOG_INFO("Listening on Bluetooth (%s)...",
             DataManager::Instance().GetBlueMac().c_str());
    listen(socket_obj.get(), 1);

    int client_sock =
        WaitForConnection(socket_obj, remote_addr, kReadTimeoutSec);
    LOG_INFO("Accepted connection from %s",
             BluetoothAddressToString(remote_addr).c_str());

    HandleFileTransfer(client_sock, kReadTimeoutSec, kMaxReceiveDurationSec,
                       kMaxBufferSize);

    LOG_INFO("File transfer completed. Performing post-transfer operations...");
    BackupAndExtractMap();

  } catch (const std::exception &e) {
    LOG_ERROR("Error during Bluetooth listening process: %s", e.what());
  }

  std::unique_lock<std::mutex> lock(listen_mutex_);
  listening_ = false;
  LOG_INFO("Bluetooth listening process terminated");
}

}  // namespace cotek_communicate
