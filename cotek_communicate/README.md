# cotek_communicate package

## 架构说明

```xml
|- CommunicateNode     // 对Communicate类的包装
|- Communicate         // udp < -- > rostopic 的传输处理
|- ProtoclMessageV1          // 对通讯协议包的抽象和包装
 -- CotekUdpProtocal   // 已放入cotek_common 通讯协议包    
|- UdpSocket           // 实现udp传输功能
```

## 功能描述
将收到的 udp数据(字符串)反序列化为json对象，再转化为rostopic(msg)
将收到的 rostopic(msg)转化为json对象，并序列化成udp数据(字符串)发送

### 通信依赖
#### 话题:
- 发布:
```
/task_request (消息文件: task_request.msg)
/task_finish_response (消息文件: task_finish_response.msg)
/task_control_request (消息文件: task_control_request.msg)
/task_calibration_request (消息文件: task_calibration_request.msg)
/task_get_map_request (消息文件: task_get_map_request.msg)
```
- 订阅:
```
/update_event （消息文件: update_event.msg)
/task_response (消息文件: task_response)
/task_finish_request (消息文件: task_finish_request.msg)
/
```
#### 服务:
无

## 参数配置
- server_ip 服务器ip
- server_port 服务器端口