/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_NODE_COTEK_COMMUNICATE_NODE_H_
#define COTEK_COMMUNICATE_INCLUDE_NODE_COTEK_COMMUNICATE_NODE_H_
#include <ros/ros.h>
#include <stdio.h>
#include <string.h>

#include <exception>
#include <fstream>
#include <memory>
#include <string>
#include <vector>

#include "cotek_common/agv_basic_option.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_communicate/communicate.h"
#include "cotek_msgs/update_config.h"
#include "ros/forwards.h"

static bool ExecuteShell(const std::string &file, const std::string &argument) {
  bool ret = false;
  std::ifstream ifile(file);
  if (ifile) {
    FILE *fp;
    char buffer[100];
    memset(buffer, 0x00, sizeof(buffer));
    std::string command = file + " " + argument;  // 组合命令
    fp = popen(command.c_str(), "r");
    auto rt = fgets(buffer, sizeof(buffer), fp);
    printf(" [%s]\n", buffer);
    pclose(fp);
    ret = true;
  } else {
    printf("Script does not exist.\n");
  }
  return ret;
}

class CommunicateNode final {
  using Communicate = cotek_communicate::Communicate;

 public:
  CommunicateNode() {
    ros::NodeHandle nh;
    node_status_manager_ptr_ =
        std::make_shared<cotek_communicate::NodeStatusManager>();
    node_diagnostic_timer_ = nh.createTimer(
        ros::Duration(1 / 20.), &CommunicateNode::NodeDiagnostic, this);
    // 节点监控
    msg_publishers_[cotek_topic::kCommunicateDiagnosticTopic] =
        nh.advertise<cotek_msgs::node_diagnostic>(
            cotek_topic::kCommunicateDiagnosticTopic, 10);
  }

  inline bool Init() {
    CommunicateOption option;
    if (!LoadCommunicateConfig(option)) {
      LOG_ERROR("Communicate config error!!!");
      node_status_manager_ptr_->SetNodeStatus(
          cotek_communicate::CommunicateNodeStatus::CONFIG_ERROR);
      return false;
    }

    communicate_ptr_ =
        std::make_shared<Communicate>(option, node_status_manager_ptr_);

    if (!communicate_ptr_->Init()) return false;

    Run();

    ros::NodeHandle nh;

    // add subscribers
    // 输入更新事件数据 -- 心跳
    subscribers_.emplace_back(nh.subscribe<cotek_msgs::update_event>(
        "updateEvent", kTopicReciveCacheSize,
        boost::bind(&CommunicateNode::HandleUpdateEventMessage, this, _1)));

    subscribers_.emplace_back(nh.subscribe<cotek_msgs::agv_info_response>(
        cotek_topic::kAgvInfoResponseTopic, kTopicReciveCacheSize,
        boost::bind(&CommunicateNode::HandlAgvResponseMessage, this, _1)));

    subscribers_.emplace_back(nh.subscribe<cotek_msgs::task_finish_request>(
        cotek_topic::kTaskFinishRequestTopic, kTopicReciveCacheSize,
        boost::bind(&CommunicateNode::HandleTaskFinishRequestMessage, this,
                    _1)));

    update_agv_config_service_server_ =
        nh.advertiseService(cotek_services::kUpdateBasicConfigService,
                            &CommunicateNode::UpdateBasicConfig, this);

    update_local_basic_config_service_server_ = nh.advertiseService(
        "update_basic_config", &CommunicateNode::UpdateLocalBasicConfig, this);

    return true;
  }

  inline void Run() { communicate_ptr_->Start(); }

 private:
  void NodeDiagnostic(const ros::TimerEvent &e) {
    cotek_msgs::node_diagnostic msg;
    msg.header.stamp = ros::Time::now();
    for (auto status : node_status_manager_ptr_->GetNodeStatus()) {
      msg.status.push_back(static_cast<int>(status.second));
    }
    msg_publishers_.at(cotek_topic::kCommunicateDiagnosticTopic).publish(msg);
    node_status_manager_ptr_->PeriodClearNodeStatus(3.0);
  }

  bool LoadCommunicateConfig(CommunicateOption &option) {
    if (!BasicConfigHelper::Instance().LoadConfig()) return false;
    // 加载配置
    LOG_INFO("------------ communicate config -------------");

    option = BasicConfigHelper::Instance().communicate_option();

    LOG_INFO_STREAM("Communicate method: " << static_cast<int>(option.method));
    LOG_INFO_STREAM("Server ip: " << option.server_ip);
    LOG_INFO_STREAM("Server port: " << option.server_port);
    LOG_INFO_STREAM("Server http port: " << option.server_http_port);
    LOG_INFO_STREAM("Local port: " << option.local_port);
    LOG_INFO_STREAM("Local http port: " << option.local_http_port);
    LOG_INFO_STREAM("Lora mode: " << option.lora_option.lora_mode);
    LOG_INFO_STREAM("Lora channel : " << option.lora_option.lora_chanel);

    std::string localizer_config_file = BasicConfigHelper::Instance().GetConfig(
        cotek_config::ConfigType::LOCALIZER_CONFIG);

    std::string err;
    const auto& j_config = json11::Json::parse(localizer_config_file, err);
    if (!err.empty()) {
      LOG_WARN("localizer json file data formats is error.");
      return false;
    }

    bool use_carto = j_config["sensoroption"]["use_carto"].bool_value();
    LOG_INFO_STREAM("use_carto: " << use_carto);

    bool use_3d_slam = j_config["sensoroption"]["use_3d_slam"].bool_value();
    LOG_INFO_STREAM("use_3d_slam: " << use_3d_slam);

    if (use_carto) option.localizer_mode = 1;
    if (use_3d_slam) option.localizer_mode = 2;
    
    return true;
  }

  void HandleUpdateEventMessage(
      const cotek_msgs::update_event::ConstPtr &update_event) {
    communicate_ptr_->AddUpdateEventMsg(update_event);
  }

  void HandlAgvResponseMessage(
      const cotek_msgs::agv_info_response::ConstPtr &agv_info_response) {
    communicate_ptr_->AddAgvInfoResponseMsg(agv_info_response);
  }

  void HandleTaskFinishRequestMessage(
      const cotek_msgs::task_finish_request::ConstPtr &task_finish_request) {
    communicate_ptr_->AddTaskFinishRequestMsg(task_finish_request);
  }

  static std::string GetUserName() {
    uid_t userid;
    struct passwd *pwd;
    userid = getuid();
    pwd = getpwuid(userid);
    return pwd->pw_name;
  }

  static std::string modifyFields(
      std::string config,
      const std::map<std::string, std::string> &fieldToURL) {
    try {
      for (const auto &pair : fieldToURL) {
        std::regex fieldPattern("\"" + pair.first + "\":\"([^\"]*)\"");
        config = std::regex_replace(
            config, fieldPattern,
            "\"" + pair.first + "\":\"" + pair.second + "\"");
      }
    } catch (std::exception &ex) {
      LOG_ERROR(ex.what());
    }
    return config;
  }

  bool UpdateLocalBasicConfig(cotek_msgs::update_config::Request &req,
                              cotek_msgs::update_config::Response &res) {
    if (!BasicConfigHelper::Instance().ReLoadConfig()) return false;
    // 加载配置
    LOG_INFO("------------ communicate config -------------");
    CommunicateOption option =
        BasicConfigHelper::Instance().communicate_option();

    LOG_INFO_STREAM("Communicate method: " << static_cast<int>(option.method));
    LOG_INFO_STREAM("Server ip: " << option.server_ip);
    LOG_INFO_STREAM("Server port: " << option.server_port);
    LOG_INFO_STREAM("Server http port: " << option.server_http_port);
    LOG_INFO_STREAM("Local port: " << option.local_port);
    LOG_INFO_STREAM("Local http port: " << option.local_http_port);

    LOG_INFO_STREAM("Lora id : " << option.lora_option.lora_id);
    LOG_INFO_STREAM("Lora mode : " << option.lora_option.lora_mode);
    LOG_INFO_STREAM("Lora chanel : " << option.lora_option.lora_chanel);

    // TODO(@ssh)修改车载前端配置
    {
      std::string file_path =
          "/home/" + GetUserName() + "/app/static/config.js";
      std::string &&temp = util::LocalService::GetStringFromFile(file_path);

      std::map<std::string, std::string> field_to_new_url = {
          {"VITE_GLOB_API_URL",
           option.server_ip + ":" + std::to_string(option.server_port)}};
      std::string &&modify = modifyFields(temp, field_to_new_url);
      if (!modify.empty()) {
        util::LocalService::SaveStringToFile(file_path, modify);
      }
    }

    // 执行脚本
    {
      std::string file =
          "/home/" + GetUserName() + "/config/script/server_ip.sh";
      ExecuteShell(file, option.server_ip);
    }

    if (communicate_ptr_) communicate_ptr_->UpdateOption(option);

    return true;
  }

  bool UpdateBasicConfig(cotek_msgs::update_config::Request &req,
                         cotek_msgs::update_config::Response &res) {
    std::string basic_config_str = req.data;
    CommunicateOption option;
    std::string server_gateway;

    try {
      MapInfoOption map_option;
      nlohmann::ordered_json json =
          nlohmann::ordered_json::parse(basic_config_str);

      server_gateway = json["server_gateway"].get<std::string>();

      option.method = static_cast<CommunicateOption::CommunicateMethod>(
          json["communicate_method"].get<int>());
      option.server_ip = json["server_ip"].get<std::string>();
      option.server_port = json["server_port"].get<int>();
      option.server_http_port = json["server_http_port"].get<int>();
      option.local_port = json["local_port"].get<int>();
      option.local_http_port = json["local_http_port"].get<int>();

      if (json.contains("lora_id")) {
        option.lora_option.lora_id = json["lora_id"].get<int>();
      } else {
        option.lora_option.lora_id = 0;
      }

      if (json.contains("lora_mode")) {
        option.lora_option.lora_mode = json["lora_mode"].get<int>();
      } else {
        option.lora_option.lora_mode = 0;
      }

      if (json.contains("lora_chanel")) {
        option.lora_option.lora_chanel =
            json["lora_channel"].get<std::string>();
      } else {
        option.lora_option.lora_chanel = "/dev/ttyUSB0";
      }

      map_option.current_zone_id =
          json.at("current_zone_id").get<std::string>();
      map_option.current_map_id = json.at("current_map_id").get<std::string>();

    } catch (const std::exception &ex) {
      LOG_ERROR("Update basic_config failed!!!");
      res.status = cotek_config::kConfigError;
      LOG_ERROR(ex.what());
      return false;
    }

    // 修改车载前端配置
    {
      std::string file_path =
          "/home/" + GetUserName() + "/app/static/config.js";
      std::string &&temp = util::LocalService::GetStringFromFile(file_path);

      std::map<std::string, std::string> field_to_new_url = {
          {"VITE_GLOB_API_URL",
           option.server_ip + ":" + std::to_string(option.server_port)}};
      std::string &&modify = modifyFields(temp, field_to_new_url);
      if (!modify.empty()) {
        util::LocalService::SaveStringToFile(file_path, modify);
      }
    }
    // 执行脚本
    {
      std::string file =
          "/home/" + GetUserName() + "/config/script/server_ip.sh";
      ExecuteShell(file, option.server_ip);

      std::string file1 =
          "/home/" + GetUserName() + "/config/script/server_gateway.sh";
      ExecuteShell(file1, server_gateway);
    }

    if (communicate_ptr_) communicate_ptr_->UpdateOption(option);

    res.status = cotek_config::kConfigOk;
    BasicConfigHelper::Instance().SaveLocal(
        cotek_config::ConfigType::AGV_BASIC_CONFIG, basic_config_str);
    LOG_INFO("Update basic_config success!!!");
    return true;
  }

  std::shared_ptr<Communicate> communicate_ptr_;
  std::vector<ros::Subscriber> subscribers_;

  std::shared_ptr<cotek_communicate::NodeStatusManager>
      node_status_manager_ptr_;

  ros::Timer node_diagnostic_timer_;

  std::map<std::string, ros::Publisher> msg_publishers_;

  ros::ServiceServer update_agv_config_service_server_;

  ros::ServiceServer update_local_basic_config_service_server_;
};

#endif  // COTEK_COMMUNICATE_INCLUDE_NODE_COTEK_COMMUNICATE_NODE_H_
