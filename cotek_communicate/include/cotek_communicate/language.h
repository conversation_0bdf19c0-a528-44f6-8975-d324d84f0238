#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_LANGUAGE_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_LANGUAGE_H_

#include <cstdint>
#include <string>
#include <map>

static std::map<std::string, std::map<int, std::string>> kRuningState = {
  {"cn", {
    {0, "正常"},
    {1, "警告"},
    {2, "错误"},
    {3, "致命"}
  }},
  {"en", {
    {0, "INFO"},
    {1, "WARNING"},
    {2, "ERROR"},
    {3, "FATAL"}
  }}
};

static std::map<std::string, std::map<int, std::string>> kActionType = {
  {"cn", {
    {0, "rest"},
    {1, "limitLoad"},
    {2, "limitUnLoad"},
    {3, "rest"}
  }},
  {"en", {
    {0, "rest"},
    {1, "limitLoad"},
    {2, "limitUnLoad"},
    {3, "rest"}
  }}
};

static std::map<std::string, std::string> kNoRunningState = {
  {"cn", ""},
  {"en", ""}
};

static std::map<std::string, std::string> kRunningState = {
  {"cn", "RUNNING"},
  {"en", "RUNNING"}
};

static std::map<std::string, std::map<int, std::string>> kSafetyState = {
  {"cn", {
    {0, ""},
    {1, "一级减速"},
    {2, "二级减速"},
    {3, "停障"},
    {4, "避障超时"},
  }},
  {"en", {
    {0, ""},
    {1, "slowLevel1"},
    {2, "SlowLevel2"},
    {3, "AvoidStop"},
    {4, "AvoidOvertime"}
  }}
};

static std::map<std::string, std::map<int, std::string>> kSafetyType = {
  {"cn", {
    {0,  ""},
    {1,  ""},
    {2,  ""},
    {3,  ""},
    {4,  "防撞条"},           //BUMP = 4,                       // 防撞条
    {5,  "叉车左叉腿防撞"},    //FORK_LEFT_LEG = 5,              // 叉车左叉腿防撞
    {6,  "叉车右叉腿防撞"},    //FORK_RIGHT_LEG = 6,             // 叉车右叉腿防撞
  
    {7,  "叉车左右叉腿都检测到"}, //FORK_LEG_BOTH = 7,              // 叉车左右叉腿都检测到
    {9,  "顶上前向避障雷达检测"}, //UP_FORWARD_LASER_OBSTACLE = 9,  // 顶上前向避障雷达检测
    {10, "避障激光0(前左侧)"}, //AVOID_LASER_0 = 10,  // 避障激光0(前左侧)
    {11, "避障激光1(前侧)"}, //AVOID_LASER_1 = 11,  // 避障激光1(前侧)
    {12, "避障激光2(前右侧)"}, //AVOID_LASER_2 = 12,  // 避障激光2(前右侧)
    {13, "避障激光3(后中侧或托盘车右叉腿)"}, //AVOID_LASER_3 = 13,  // 避障激光3(后中侧或托盘车右叉腿)
    {14, "避障激光4(后右侧)"}, //AVOID_LASER_4 = 14,  // 避障激光4(后右侧)
    {15, "避障激光5(后左侧)"}, //AVOID_LASER_5 = 15,  // 避障激光5(后左侧)
  
    {20, "前置左侧相机"}, //AVOID_CAMERA_0 = 20,  // 前置左侧相机
    {21, "前置中间相机"}, //AVOID_CAMERA_1 = 21,  // 前置中间相机
    {22, "前置右侧相机"}, //AVOID_CAMERA_2 = 22,  // 前置右侧相机
    {23, "后置右侧叉腿相机"}, //AVOID_CAMERA_3 = 23,  // 后置右侧叉腿相机
    {24, "后置左侧叉腿相机"}, //AVOID_CAMERA_4 = 24,  // 后置左侧叉腿相机
    {25, "备用相机1"}, //AVOID_CAMERA_5 = 25,  // 备用相机1
  
    {30, "导航激光0号"}, //NAVI_LASER_0 = 30,  // 导航激光0号
    {31, "导航激光1号"}, //NAVI_LASER_1 = 31,  // 导航激光1号
    {32, "导航激光2号"}, //NAVI_LASER_2 = 32,  // 导航激光2号
  
    {41, "超声波避障"}, //AVOID_ULTRASONIC = 41,     // 超声波避障：KS104
    {42, "前向超声波检测"}, //FORWARD_ULTRASONIC = 42,   // 前向超声波检测
    {43, "后向超声波检测"}, //BACKWARD_ULTRASONIC = 43,  // 后向超声波检测
    {44, "左向超声波检测"}, //LEFT_ULTRASONIC = 44,      // 左向超声波检测
    {45, "右向超声波检测"}, //RIGHT_ULTRASONIC = 45,     // 右向超声波检测
  
    {60, "行人识别"}, //PEDERSTRIAN_DETECT = 60,  // 行人识别
  
    {90, "无可用避障传感器数据"}, //INVAILD_SENSOR_DATA = 90,  // 无可用避障传感器数据
  
    {100, "避障激光0数据超时"}, //AVOID_LASER_0_TIMEOUT = 100,  // 避障激光0 数据超时
    {101, "避障激光1数据超时"}, //AVOID_LASER_1_TIMEOUT = 101,  // 避障激光1 数据超时
    {102, "避障激光2数据超时"}, //AVOID_LASER_2_TIMEOUT = 102,  // 避障激光2 数据超时
    {103, "避障激光3数据超时"}, //AVOID_LASER_3_TIMEOUT = 103,  // 避障激光3 数据超时
    {104, "避障激光4数据超时"}, //AVOID_LASER_4_TIMEOUT = 104,  // 避障激光4 数据超时
    {105, "避障激光5数据超时"}, //AVOID_LASER_5_TIMEOUT = 105,  // 避障激光5 数据超时
  
    {110, "相机0数据超时"}, //AVOID_CAMERA_0_TIMEOUT = 110,  // 相机0 数据超时
    {111, "相机1数据超时"}, //AVOID_CAMERA_1_TIMEOUT = 111,  // 相机1 数据超时
    {112, "相机2数据超时"}, //AVOID_CAMERA_2_TIMEOUT = 112,  // 相机2 数据超时
    {113, "相机3数据超时"}, //AVOID_CAMERA_3_TIMEOUT = 113,  // 相机3 数据超时
    {114, "相机4数据超时"}, //AVOID_CAMERA_4_TIMEOUT = 114,  // 相机4 数据超时
    {115, "相机5数据超时"}, //AVOID_CAMERA_5_TIMEOUT = 115,  // 相机5 数据超时

    {120, "导航激光0数据超时"}, //LASER_0_TIMEOUT = 120,  // 导航激光0 数据超时
    {121, "导航激光1数据超时"}, //LASER_1_TIMEOUT = 121,  // 导航激光1 数据超时
    {122, "导航激光2数据超时"}, //LASER_2_TIMEOUT = 122,  // 导航激光2 数据超时
  
    {131, "避障类io数据超时"}, //SAFETY_IO_TIMEOUT = 131,   // 避障类io 数据超时
    {141, "超声波数据超时"}, //ULTRASONIC_TIMEOUT = 141,  // 超声波 数据超时
    {200, "避障超时"}, //AVOID_OVERTIME = 200,  // 避障超时
  }},
  {"en", {
    {0,  ""},
    {1,  ""},
    {2,  ""},
    {3,  ""},
    {4,  "BUMP"},               //BUMP = 4,                       // 防撞条
    {5,  "FORKLIFT LEFT LEG"},  //FORK_LEFT_LEG = 5,              // 叉车左叉腿防撞
    {6,  "FORKLIFT RIGHT LEG"}, //FORK_RIGHT_LEG = 6,             // 叉车右叉腿防撞
  
    {7,  "FORK LEG BOTH"}, //FORK_LEG_BOTH = 7,              // 叉车左右叉腿都检测到
    {9,  "UP FORWARD LASER OBSTACLE"}, //UP_FORWARD_LASER_OBSTACLE = 9,  // 顶上前向避障雷达检测
    {10, "AVOID LASER 0"}, //AVOID_LASER_0 = 10,  // 避障激光0(前左侧)
    {11, "AVOID LASER 1"}, //AVOID_LASER_1 = 11,  // 避障激光1(前侧)
    {12, "AVOID LASER 2"}, //AVOID_LASER_2 = 12,  // 避障激光2(前右侧)
    {13, "AVOID LASER 3"}, //AVOID_LASER_3 = 13,  // 避障激光3(后中侧或托盘车右叉腿)
    {14, "AVOID LASER 4"}, //AVOID_LASER_4 = 14,  // 避障激光4(后右侧)
    {15, "AVOID LASER 5"}, //AVOID_LASER_5 = 15,  // 避障激光5(后左侧)
  
    {20, "AVOID CAMERA 0"}, //AVOID_CAMERA_0 = 20,  // 前置左侧相机
    {21, "AVOID CAMERA 1"}, //AVOID_CAMERA_1 = 21,  // 前置中间相机
    {22, "AVOID CAMERA 2"}, //AVOID_CAMERA_2 = 22,  // 前置右侧相机
    {23, "AVOID CAMERA 3"}, //AVOID_CAMERA_3 = 23,  // 后置右侧叉腿相机
    {24, "AVOID CAMERA 4"}, //AVOID_CAMERA_4 = 24,  // 后置左侧叉腿相机
    {25, "AVOID CAMERA 5"}, //AVOID_CAMERA_5 = 25,  // 备用相机1
  
    {30, "NAVI LASER 0"}, //NAVI_LASER_0 = 30,  // 导航激光0号
    {31, "NAVI LASER 1"}, //NAVI_LASER_1 = 31,  // 导航激光1号
    {32, "NAVI LASER 2"}, //NAVI_LASER_2 = 32,  // 导航激光2号
  
    {41, "AVOID ULTRASONIC"}, //AVOID_ULTRASONIC = 41,     // 超声波避障：KS104
    {42, "FORWARD ULTRASONIC"}, //FORWARD_ULTRASONIC = 42,   // 前向超声波检测
    {43, "BACKWARD ULTRASONIC"}, //BACKWARD_ULTRASONIC = 43,  // 后向超声波检测
    {44, "LEFT ULTRASONIC"}, //LEFT_ULTRASONIC = 44,      // 左向超声波检测
    {45, "RIGHT ULTRASONIC"}, //RIGHT_ULTRASONIC = 45,     // 右向超声波检测
  
    {60, "PEDERSTRIAN DETECT"}, //PEDERSTRIAN_DETECT = 60,  // 行人识别
  
    {90, "INVAILD SENSOR DATA"}, //INVAILD_SENSOR_DATA = 90,  // 无可用避障传感器数据
  
    {100, "AVOID LASER 0 TIMEOUT"}, //AVOID_LASER_0_TIMEOUT = 100,  // 避障激光0 数据超时
    {101, "AVOID LASER 1 TIMEOUT"}, //AVOID_LASER_1_TIMEOUT = 101,  // 避障激光1 数据超时
    {102, "AVOID LASER 2 TIMEOUT"}, //AVOID_LASER_2_TIMEOUT = 102,  // 避障激光2 数据超时
    {103, "AVOID LASER 3 TIMEOUT"}, //AVOID_LASER_3_TIMEOUT = 103,  // 避障激光3 数据超时
    {104, "AVOID LASER 4 TIMEOUT"}, //AVOID_LASER_4_TIMEOUT = 104,  // 避障激光4 数据超时
    {105, "AVOID LASER 5 TIMEOUT"}, //AVOID_LASER_5_TIMEOUT = 105,  // 避障激光5 数据超时
  
    {110, "AVOID CAMERA 0 TIMEOUT"}, //AVOID_CAMERA_0_TIMEOUT = 110,  // 相机0 数据超时
    {111, "AVOID CAMERA 1 TIMEOUT"}, //AVOID_CAMERA_1_TIMEOUT = 111,  // 相机1 数据超时
    {112, "AVOID CAMERA 2 TIMEOUT"}, //AVOID_CAMERA_2_TIMEOUT = 112,  // 相机2 数据超时
    {113, "AVOID CAMERA 3 TIMEOUT"}, //AVOID_CAMERA_3_TIMEOUT = 113,  // 相机3 数据超时
    {114, "AVOID CAMERA 4 TIMEOUT"}, //AVOID_CAMERA_4_TIMEOUT = 114,  // 相机4 数据超时
    {115, "AVOID CAMERA 5 TIMEOUT"}, //AVOID_CAMERA_5_TIMEOUT = 115,  // 相机5 数据超时

    {120, "LASER 0 TIMEOUT"}, //LASER_0_TIMEOUT = 120,  // 导航激光0 数据超时
    {121, "LASER 1 TIMEOUT"}, //LASER_1_TIMEOUT = 121,  // 导航激光1 数据超时
    {122, "LASER 2 TIMEOUT"}, //LASER_2_TIMEOUT = 122,  // 导航激光2 数据超时
  
    {131, "SAFETY IO TIMEOUT"}, //SAFETY_IO_TIMEOUT = 131,   // 避障类io 数据超时
    {141, "ULTRASONIC TIMEOUT"}, //ULTRASONIC_TIMEOUT = 141,  // 超声波 数据超时
    {200, "AVOID OVERTIME"}, //AVOID_OVERTIME = 200,  // 避障超时
  }}
};

static std::map<std::string, std::map<int, std::string>> kErrorCode = {
  {"cn", {
    {1301,  "下发任务类型不匹配"},
    {1302,  "与调度服务器通信超时(10s),请检查网络"},
    {1601,  "配置错误, 请联系技术人员"},
    {1999,  "通讯模块掉线，请重启"},
    {2301,  "任务工单下发错误"},
    {2302,  "任务序列号下发错误"},
    {2303,  "任务类型下发错误"},
    {2304,  "请检查踏板或扶手报警"},
    {2601,  "配置错误, 请联系技术人员"},
    {2602,  "密钥错误(请更换密钥)"},
    {2603,  "掉货请注意"},
    {2999,  "任务模块掉线，请重启"},
    {4301,  "地图出错，请检查地图文件"},
    {4302,  "定位模块初始化失败"},
    {4303,  "重定位中请等待"},
    {4304,  "反光板静态定位失败，请查询周围反光板是否充足"},
    {4305,  "slam计算位姿失败，CPU卡顿保护"},
    {4601,  "配置错误, 请联系技术人员"},
    {4602,  "导航激光表面有污渍或遮挡，请检查并擦拭"},
    {4603,  "slam计算位姿失败，CPU严重卡顿故障"},
    {4604,  "环境匹配失败，请检查周围环境是否变化过大或者反光板过少"},
    {4605,  "导航激光数据丢失，请检查导航激光电气线束连接"},
    {4606,  "环境严重匹配故障，请检查周围环境是否变化过大"},
    {4611,  "非法二维码数据，请检查该二维码是否正确"},
    {4612,  "向上二维码相机通讯异常"},
    {4613,  "向下二维码相机通讯异常"},
    {4999,  "定位模块掉线，请重启"},
    {5301,  "地图出错，请检查地图文件"},
    {5302,  "标定时环境匹配失败，请适当增加反光板数量或降低建图速度"},
    {5601,  "配置错误, 请联系技术人员"},
    {5602,  "导航激光表面有污渍或遮挡，请检查并擦拭"},
    {5999,  "标定模块掉线，请重启"},
    {6301,  "矫正动作超时"},
    {6302,  "对接动作超时"},
    {6303,  "托盘超板超时"},
    {6311,  "向下二维码扫描丢失"},
    {6312,  "向上二维码扫描丢失"},
    {6313,  "货物二维码位姿计算失败"},
    {6601,  "配置错误, 请联系技术人员"},
    {6602,  "机器人偏移路线，请检查路径是否规划正常或移动至路线上重新下发任务"},
    {6603,  "车辆对接控制超时"},
    {6604,  "里程计保护，请检查车轮是否堵转或空转"},
    {6606,  "禁止倒车"},
    {6607,  "定位丢失"},
    {6608,  "任务校验异常"},
    {6609,  "插尖传感器超时异常"},
    {6901,  "自定义条件错误，请检查配置"},
    {6999,  "导航模块掉线，请重启"},
    {7301,  "防撞条避障，请检查防撞条是否异常"},
    {7302,  "避障超时"},
    {7601,  "配置错误, 请联系技术人员"},
    {7602,  "避障地图配置错误，出现此错误请联系技术人员"},
    {7700,  "0号避障激光(前置左侧)掉线，请检查电气线束"},
    {7701,  "1号避障激光(前置中间)激光掉线，请检查电气线束"},
    {7702,  "2号避障激光(前置右侧)掉线，请检查电气线束"},
    {7703,  "3号避障激光(后置中侧)掉线，请检查电气线束"},
    {7704,  "4号避障激光(后置右侧)掉线，请检查电气线束"},
    {7705,  "5号避障激光(后置左侧)掉线，请检查电气线束"},
    {7710,  "0号避障相机(前置左侧)掉线，请检查电气线束"},
    {7711,  "1号避障相机(前置中间)掉线，请检查电气线束"},
    {7712,  "2号避障相机(前置右侧)掉线，请检查电气线束"},
    {7713,  "3号避障相机(后置中间)掉线，请检查电气线束"},
    {7714,  "4号避障相机(后置右侧)掉线，请检查电气线束"},
    {7715,  "5号避障相机(后置左侧)掉线，请检查电气线束"},
    {7720,  "导航0号激光掉线，请检查电气线束"},
    {7721,  "导航1号激光掉线，请检查电气线束"},
    {7722,  "导航2号激光掉线，请检查电气线束"},
    {7731,  "io避障模块掉线，请检查底盘模块io"},
    {7741,  "超声波掉线，请检查底盘模块"},
    {7999,  "避障模块掉线，请重启"},
    {8601,  "配置错误, 请联系技术人员"},
    {8602,  "库位地图错误，请检查库位地图配置是否正确"},
    {8999,  "库位模块掉线，请重启"},
    {9601,  "配置错误, 请联系技术人员"},
    {9602,  "前置中侧相机角度未标定"},
    {9603,  "前置中侧相机背景未标定"},
    {9604,  "相机初始化失败"},
    {9605,  "前置左侧相机角度未标定"},
    {9606,  "前置左侧相机背景未标定"},
    {9607,  "前置右侧相机角度未标定"},
    {9608,  "前置右侧相机背景未标定"},
    {9701,  "apritag相机掉线"},
    {9702,  "前置中侧相机掉线，请检查电气线束"},
    {9703,  "前置左侧相机掉线，请检查电气线束"},
    {9704,  "前置右侧相机掉线，请检查电气线束"},
    {9705,  "后置中侧相机掉线，请检查电气线束"},
    {9706,  "后置左侧相机掉线，请检查电气线束"},
    {9707,  "后置右侧相机掉线，请检查电气线束"},
    {9999,  "视觉模块掉线，请重启"},
    {10301, "托盘到位检测失败，请检查托盘是否触碰挡板机构"},
    {10302, "托盘检测失败，请检查托盘是否正常放置"},
    {10303, "托盘超板，请检查托盘上货物是否超出托盘区间"},
    {10304, "货架柱子检测失败"},
    {10305, "卸货安全空间检测失败，请检查库位是否空闲"},
    {10601, "配置错误, 请联系技术人员"},
    {10602, "调度下发动作类型错误，请检查动作类型"},
    {10603, "调度下发动作数值不匹配，请检查动作数值"},
    {10604, "动作超时，请结合实际动作检查"},
    {10605, "请检查叉腿是否有异常堵转"},
    {10606, "取货后称重检测异常"},
    {10607, "卸货后称重检测异常"},
    {10608, "货架二维码丢失"},
    {10609, "堆高车上限位触发报警"},
    {10610, "叉尖避障检测报警"},
    {10701, "IMU异常，请检查"},
    {10702, "称重传感器异常，请检查"},
    {10703, "高度拉线编码器异常，请检查"},
    {10704, "前后移拉线编码异常，请检查"},
    {10705, "侧移拉线编码异常，请检查"},
    {10706, "io模块异常，请检查io模块"},
    {10901, "自定义条件错误"},
    {10999, "动作模块掉线，请重启"},
    {11301, "反光板重定位失败"},
    {11601, "配置错误, 请联系技术人员"},
    {11602, "月台安全保护触发，请核实定位或重启设备"},
    {11999, "SLAM模块掉线，请重启"},
    {12301, "配置警告, 请联系技术人员"},
    {12302, "can1警告"},
    {12303, "can2警告"},
    {12311, "电机1警告"},
    {12312, "电机2警告"},
    {12313, "电机3警告"},
    {12314, "电机4警告"},
    {12321, "拉线1警告"},
    {12322, "拉线2警告"},
    {12323, "拉线3警告"},
    {12324, "拉线4警告"},
    {12331, "IMU1警告"},
    {12332, "IMU2警告"},
    {12333, "IMU3警告"},
    {12334, "IMU4警告"},
    {12341, "称重1警告"},
    {12342, "称重2警告"},
    {12351, "IO1警告"},
    {12352, "IO2警告"},
    {12361, "音频1警告"},
    {12362, "音频2警告"},
    {12371, "电池1警告"},
    {12372, "电池2警告"},
    {12601, "配置错误, 请联系技术人员"},
    {12602, "can1错误"},
    {12603, "can2错误"},
    {12611, "电机1错误"},
    {12612, "电机2错误"},
    {12613, "电机3错误"},
    {12614, "电机4错误"},
    {12621, "拉线1错误"},
    {12622, "拉线2错误"},
    {12623, "拉线3错误"},
    {12624, "拉线4错误"},
    {12631, "IMU1错误"},
    {12632, "IMU2错误"},
    {12633, "IMU3错误"},
    {12634, "IMU4错误"},
    {12641, "称重1错误"},
    {12642, "称重2错误"},
    {12651, "IO1错误"},
    {12652, "IO2错误"},
    {12661, "音频1错误"},
    {12662, "音频2错误"},
    {12671, "电池1错误"},
    {12672, "电池2错误"},
    {12999, "底盘模块掉线，请重启"},
  }},
  {"en", {
    {1301, "Wrong type of dispatcher task"},
    {1302, "Connection which connected to dispatcher server  timed out  10s. Please  check network"},
    {1601, "Configuration error, please contact R&D"},
    {1999, "Module offline, please restart"},
    {2301, "Order id of task  error"},
    {2302, "Sequence id of task error"},
    {2303, "Task type error"},
    {2304, "Please check handrail of peadl"},
    {2601, "Configuration error, please contact R&D"},
    {2602, "Softerware keys error,  please update it"},
    {2603, "Notice  the drops"},
    {2999, "Module offline, please restart"},
    {4301, "Map error，please check"},
    {4302, "Location module  initialize error"},
    {4303, "Relocation error, please waiting"},
    {4304, "Reflect location error，please check reflection"},
    {4305, "CPU Hang Protection"},
    {4601, "Configuration error, please contact R&D"},
    {4602, "Navigation laser had dust or shade, please check laser"},
    {4603, "SLAM  pose error, please contact R&D"},
    {4604, "SLAM dismatch error, please check scene and reflection"},
    {4605, "Lidar data loss, please check lidar connect"},
    {4606, "Severe Environment Mismatch Fault, please check if the surrounding environment has undergone significant changes"},
    {4611, "Illegal QR data, please check QR camera"},
    {4612, "Up QR disconnect"},
    {4613, "Down QR disconnect"},
    {4999, "Module offline, please restart"},
    {5301, "Map error，please check"},
    {5302, "SLAM dismatch error, please reflection or slow velocity when create map"},
    {5601, "Configuration error, please contact R&D"},
    {5602, "Navigation laser had dust or shade, please check"},
    {5999, "Module offline, please restart"},
    {6301, "Stabilze timeout"},
    {6302, "Power initialize timeout"},
    {6303, "tray exceeding board timeout"},
    {6311, "Down QR loss"},
    {6312, "Up QR loss"},
    {6313, "QR pose error"},
    {6601, "Configuration error, please contact R&D"},
    {6602, "Out of route, please check global plan or restart task"},
    {6603, "Docking timeout"},
    {6604, "Please check if the wheels are jammed or spinning freely"},
    {6606, "Forbidden backward"},
    {6607, "Location loss"},
    {6608, "Task error"},
    {6609, "Sensor error"},
    {6901, "DIY condition error, plase check config"},
    {6999, "Module offline, please restart"},
    {7301, "Bump avoid obstacle, please check bump"},
    {7302, "Avoid obstacle timeout"},
    {7601, "Configuration error, please contact R&D"},
    {7602, "Avoid map config error, please R&D"},
    {7700, "Front left laser disconnect, please check hardware"},
    {7701, "Front middle Laser disconnect, please check hardware"},
    {7702, "Front right laser disconnect, please check hardware"},
    {7703, "Back middle laser disconnect, please check hardware"},
    {7704, "Back right laser disconnect, please check hardware"},
    {7705, "Back left laser disconnect, please check hardware"},
    {7710, "Front left camera disconnect, please check hardware"},
    {7711, "Front middle camera disconnect, please check hardware"},
    {7712, "Front right camera disconnect, please check hardware"},
    {7713, "Back middle camera disconnect, please check hardware"},
    {7714, "Back right camera disconnect, please check hardware"},
    {7715, "Back left camera disconnect, please check hardware"},
    {7720, "Laser0 disconnect, please check hardware"},
    {7721, "Laser1 disconnect, please check hardware"},
    {7722, "Laser2 disconnect, please check hardware"},
    {7731, "IO avoid  obstacle disconnect, please check io hardware module"},
    {7741, "Ultrasonic sensor disconnect, please check ultrasonic hardware module"},
    {7999, "Module offline, please restart"},
    {8601, "Configuration error, please contact R&D"},
    {8602, "Storage map error, please check config"},
    {8999, "Module offline, please restart"},
    {9601, "Configuration error, please contact R&D"},
    {9602, "The angle of front middle camera is not calibrated"},
    {9603, "The background of front middle camera is not calibrated"},
    {9604, "Camera initialize failed"},
    {9605, "The angle of front left camera is not calibrated"},
    {9606, "The background of front left camera is not calibrated"},
    {9607, "The angle of front right camera is not calibrated"},
    {9608, "The background of front right camera is not calibrated"},
    {9701, "Apritag camera disconnect"},
    {9702, "Front camera disconnect"},
    {9703, "Front left  camera disconnect"},
    {9704, "Front right  camera disconnect"},
    {9705, "Pallet camera disconnect"},
    {9706, "Left avoid camera disconnect"},
    {9707, "Right avoid camera disconnect"},
    {9999, "Module offline, please restart"},
    {10301, "The pallet position detection has failed. Please check if the pallet is touching the baffle mechanism"},
    {10302, "Pallet detection failed, please check if the pallet is placed correctly"},
    {10303, "Pallet overboard, please check whether the goods on the pallet are out of the pallet area"},
    {10304, "Shelf pillar detection failed"},
    {10305, "Unloading safe space detection failed, check whether the bin location is free"},
    {10601, "Configuration error, please contact R&D"},
    {10602, "There is an error in the dispatched action type, please check the action type"},
    {10603, "The dispatched action value does not match, please check the action value"},
    {10604, "The action timed out, please check it against the actual action"},
    {10605, "Please check whether there is any abnormal stalling of the fork legs"},
    {10606, "Weigh after pick-up to detect anomalies"},
    {10607, "Abnormal weight detection after unloading"},
    {10608, "The QR code of the shelf is missing"},
    {10609, "The upper limit of the stacking vehicle triggers an alarm"},
    {10610, "Fork tip obstacle avoidance detection alarm"},
    {10701, "The IMU is abnormal, please check"},
    {10702, "The load cell is abnormal, please check"},
    {10703, "The height of the cable encoder is abnormal, please check"},
    {10704, "There is an abnormality in the encoding of the back-and-forth pull cable, please check."},
    {10705, "The side shift cable encoding is abnormal, please check."},
    {10706, "IO module exception, please check the IO module"},
    {10901, "Custom condition error"},
    {10999, "Module offline, please restart"},
    {11301, "Reflect location error，please check reflection"},
    {11601, "Configuration error, please contact R&D"},
    {11602, "SLAM dismatch error, please check scene"},
    {11999, "Module offline, please restart"},
    {12301, "Configuration warning, please contact R&D"},
    {12302, "can1 warning"},
    {12303, "can2 warning"},
    {12311, "motor1 warning"},
    {12312, "motor2 warning"},
    {12313, "motor3 warning"},
    {12314, "motor4 warning"},
    {12321, "guyed1 warning"},
    {12322, "guyed2 warning"},
    {12323, "guyed3 warning"},
    {12324, "guyed4 warning"},
    {12331, "IMU1 warning"},
    {12332, "IMU2 warning"},
    {12333, "IMU3 warning"},
    {12334, "IMU4 warning"},
    {12341, "Weigh1 warning"},
    {12342, "Weigh2 warning"},
    {12351, "IO1 warning"},
    {12352, "IO2 warning"},
    {12361, "Audio1 warning"},
    {12362, "Audio2 warning"},
    {12371, "Battery1 warning"},
    {12372, "Battery2 warning"},
    {12601, "Configuration error, please contact R&D"},
    {12602, "can1 error"},
    {12603, "can2 error"},
    {12611, "motor1 error"},
    {12612, "motor2 error"},
    {12613, "motor3 error"},
    {12614, "motor4 error"},
    {12621, "guyed1 error"},
    {12622, "guyed2 error"},
    {12623, "guyed3 error"},
    {12624, "guyed4 error"},
    {12631, "IMU1 error"},
    {12632, "IMU2 error"},
    {12633, "IMU3 error"},
    {12634, "IMU4 error"},
    {12641, "Weigh1 error"},
    {12642, "Weigh2 error"},
    {12651, "IO1 error"},
    {12652, "IO2 error"},
    {12661, "Audio1 error"},
    {12662, "Audio2 error"},
    {12671, "Battery1 error"},
    {12672, "Battery2 error"},
    {12999, "Module offline, please restart"},
  }}
};

#endif // COTEK_COMMUNICATE_INCLUDE_COTEK_LANGUAGE_H_