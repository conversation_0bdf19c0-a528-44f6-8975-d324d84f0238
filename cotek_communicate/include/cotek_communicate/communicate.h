/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_COMMUNICATE_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_COMMUNICATE_H_
#include <arpa/inet.h>
#include <net/if.h>
#include <netinet/in.h>
#include <ros/ros.h>
#include <stdio.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <unistd.h>

#include <cstring>
#include <functional>
#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <numeric>
#include <string>
#include <thread>
#include <vector>

#include "cotek_common/common.h"
#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/node_diagnostic_info.h"
#include "cotek_common/node_diagnostic_manager.h"
#include "cotek_common/thread_pool.h"
#include "cotek_common/util/ping_tool.h"
#include "cotek_communicate/communicate_method/http/http_client.h"
#include "cotek_communicate/communicate_method/http/http_handle_factory.h"
#ifdef __x86_64__
#include "cotek_communicate/communicate_method/mqtt/mqtt.h"
#endif
#include "cotek_communicate/communicate_method/udp/udp_socket.h"
#include "cotek_communicate/data_manager.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_lora.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_v1.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_vda5050.h"
#include "cotek_msgs/agv_info_request.h"
#include "cotek_msgs/agv_info_response.h"
#include "cotek_msgs/agv_query_dispatch.h"
#include "cotek_msgs/battery_info_request.h"
#include "cotek_msgs/battery_info_response.h"
#include "cotek_msgs/dongle_to_dispatch.h"
#include "cotek_msgs/finish_relocation_request.h"
#include "cotek_msgs/node_diagnostic.h"
#include "cotek_msgs/order.h"
#include "cotek_msgs/storage.h"
#include "cotek_msgs/task_calibration_request.h"
#include "cotek_msgs/task_control_audio_request.h"
#include "cotek_msgs/task_control_request.h"
#include "cotek_msgs/task_finish_request.h"
#include "cotek_msgs/task_get_map_request.h"
#include "cotek_msgs/task_request.h"
#include "cotek_msgs/task_response.h"
#include "cotek_msgs/update_event.h"
#include "cotek_msgs/update_single_task.h"
#include "cotek_msgs/update_storage_config.h"
#include "cotek_msgs/update_visual_config.h"
#include "ros/time.h"

namespace cotek_communicate {

using CommunicateNodeStatus = cotek_diagnostic::CommunicateNodeStatus;
using NodeStatusManager =
    cotek_diagnostic::NodeStatusManager<CommunicateNodeStatus>;
using TimeEventCallback = std::function<void(const std::string &)>;
struct TimedEvent {
  ros::Time time;
  TimeEventCallback cb;
};

class CycleEventCenter {
 public:
  CycleEventCenter() = default;
  ~CycleEventCenter() {
    runner_ptr_->join();
    runner_ptr_ = nullptr;
  }

  bool Start();

  bool RegisterTimeEventCb(const std::string &type,
                           const TimeEventCallback &cb);

 private:
  void CycleEvent();
  std::mutex mutex_;
  std::unique_ptr<std::thread> runner_ptr_;
  std::map<std::string, TimedEvent> time_event_cb_map_;
};

class Communicate {
 public:
  enum CalibrationStatus : uint8_t {
    NONE = 0,
    SUCCESS = 1,
    FAILED = 2,
    DOING = 3
  };
  explicit Communicate(const CommunicateOption &option,
                       std::shared_ptr<NodeStatusManager> ns);

  Communicate &operator=(const Communicate &) = delete;
  ~Communicate() {
    if (network_checking_daemon_) {
      network_checking_daemon_->join();
      network_checking_daemon_ = nullptr;
    }
    if (pose_get_daemon_) {
      pose_get_daemon_->join();
      pose_get_daemon_ = nullptr;
    }

    if(language_) {
      language_->join();
    }
    language_ = nullptr;
  }

  bool Init();

  bool RosCommunicateInit();

  bool Start();

  bool UpdateOption(const CommunicateOption &option);

  // 输入其他节点数据
  void AddUpdateEventMsg(
      const cotek_msgs::update_event::ConstPtr &update_event);

  void AddTaskFinishRequestMsg(
      const cotek_msgs::task_finish_request::ConstPtr &task_finish_request);

  void AddAgvInfoResponseMsg(
      const cotek_msgs::agv_info_response::ConstPtr &agv_info_response);

  bool AgvQueryDispatch(cotek_msgs::agv_query_dispatch::Request &req,
                        cotek_msgs::agv_query_dispatch::Response &res);

  void QueryInfoFromDispatch(const std::string &msg_type);

 private:
  void PrePare();

  // 发送数据接口 输入数据包类型和数据包数据
  void Send(const std::string &msg_type, const std::string &msg_data,
            const Qos &qos);
  void Send(const std::string &msg_type, const Json &msg_json, const Qos &qos);

  // udp 数据回调终端, 校验数据 并 分发给不同的回调函数
  void MessageCallback(ProtoclMessageVda5050 msg);

  // 不同数据包数据 回调函数
  void InstantActionHandler(const std::string &data,
                            const std::string &remote_bind_id);
  void TaskRequestHandler(const std::string &data,
                          const std::string &remote_bind_id);
  void RequestStatusHandler(const std::string &data,
                            const std::string &remote_bind_id);
  void RequestStreamHandler(const std::string &data,
                            const std::string &remote_bind_id);
  void RequestStatisticsHandler(const std::string &data,
                                const std::string &remote_bind_id);

  void RequestCalibrationHandler(const std::string &data,
                                 const std::string &remote_bind_id);

  bool RegisterTimeEventCb(const std::string &type,
                           const TimeEventCallback &cb);

  void TaskCalibrationRequestHandler(std::string data,
                                     const std::string &remote_bind_id);
  void TaskGetMapRequestHandler(std::string data,
                                const std::string &remote_bind_id);
  void TaskControlAudioRequestHandler(std::string data,
                                      const std::string &remote_bind_id);
  void TaskQueryStorageRequestHandler(std::string data,
                                      const std::string &remote_bind_id);
  void TaskQueryOdomInfoRequestHandler(std::string data,
                                       const std::string &remote_bind_id);

  // 节点监控
  void NodeDiagnostic(const ros::TimerEvent &e);

  // 通讯链路保持连接,掉线检测
  void NetWorkLinkCheck();

  void Get3dPoseZ();

  void CreateNoBlockCalibrationHandler(const std::string &type,
                                       const std::vector<std::string> &params,
                                       const int &method);

  std::string CalibrationStatusToString(const CalibrationStatus &status);

  std::shared_ptr<std::thread> network_checking_daemon_;
  std::shared_ptr<std::thread> language_;
  std::shared_ptr<std::thread> pose_get_daemon_;
  int loc_mode_;
  double pose_3d_z_{0.0};

  uint16_t node_status_;
  ros::Timer node_diagnostic_timer_;
  ros::Timer network_link_timer_;

  CommunicateOption option_;
  std::map<std::string, ros::Publisher> msg_publishers_;
  std::map<std::string,
           std::function<void(std::string, const std::string &bind_id)>>
      handler_;
  std::map<std::string, std::string> agv_map_info_;

  std::shared_ptr<CommunicateInterface<ProtoclMessageVda5050>> socket_;

  std::shared_ptr<PocoHttpServer> http_server_ptr_;
  util::PingTool ping_tool_;

  // 远程参数更新的服务器与本地配置不同
  std::string remote_calibration_host_;
  std::string remote_update_config_host_;

  std::string local_ip_;
  std::map<std::string, std::string> remote_bind_list_;
  std::map<std::string, CalibrationStatus> calibration_statsus_map_;

  ros::ServiceClient update_logic_config_service_client_;
  ros::ServiceClient update_localizer_config_service_client_;
  ros::ServiceClient update_navigation_config_service_client_;
  ros::ServiceClient update_action_config_service_client_;
  ros::ServiceClient update_avoid_config_service_client_;
  ros::ServiceClient update_avoid_area_config_service_client_;
  ros::ServiceClient update_single_task_service_client_;
  ros::ServiceClient update_embedded_config_service_client_;
  ros::ServiceClient update_visual_config_service_client_;
  ros::ServiceClient update_storage_config_service_client_;

  ros::ServiceServer agv_query_dispatch_server_;
  cotek_common::ThreadPool thread_pool_;

  std::unique_ptr<CycleEventCenter> cycle_event_center_ptr_;

  std::shared_ptr<NodeStatusManager> node_status_manager_ptr_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_COMMUNICATE_H_
