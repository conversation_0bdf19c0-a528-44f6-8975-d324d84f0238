/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_RESOURCE_AGENT_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_RESOURCE_AGENT_H_
#include <array>
#include <atomic>
#include <cstdint>
#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <thread>

#include "cotek_common/thread_pool.h"
#include "cotek_communicate/communicate_method/lora/lora_manager.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"
#include "cotek_msgs/traffic_state.h"
#include "cotek_msgs/update_event.h"

namespace cotek_communicate {

enum class BlueZState : uint8_t {
  NONE = 0,
  PRE_SYNC = 1,
  SYNCING = 2,
  SYNC_FAILD = 3,
  SYNC_SUCCEED = 4,
  SY<PERSON>_CANCLE = 5
};

struct ResoureInfo {
  uint16_t id;
  uint16_t software_ver{0};
  uint16_t map_ver{0};
  uint16_t path_ver{0};
  std::string bluetooth_mac;
  std::string wifi_mac;
  BlueZState blz_state{BlueZState::NONE};
  uint8_t update_ratio{0};
};

class ResourceObj {
 public:
  explicit ResourceObj(const ResoureInfo &info)
      : resource_(info), thread_pool_(1) {}
  ~ResourceObj() {}

  ResoureInfo GetResoureInfo() const { return resource_; }
  bool UpdateResourceInfo(const ResoureInfo &info);
  bool UpdateBlueState(const BlueZState &state);
  bool UpdateCancleSync(const bool &type);

  bool UpdateProcessRatio(const uint8_t &ratio);

  void BlzSync();

  int Sync();

 protected:
 private:
  std::mutex mutex_;
  ResoureInfo resource_;

  bool cancle_sync_{false};

  std::atomic<bool> syncing_{false};  // 跟踪蓝牙同步线程的状态
  std::mutex sync_mutex_;
  cotek_common::ThreadPool thread_pool_;
};

class ResourceAgent {
 public:
  ResourceAgent();

  bool Init(const CommunicateOption &option);

  bool UpdateResource(const ResoureInfo &info);

  bool UpdateTargetResoureBlueState(const uint16_t &id,
                                    const BlueZState &state);

  bool UpdateTargetResoureBlueCancleState(const uint16_t &id,
                                          const bool state);
  Json GetResouresInfo();

  bool HandleObjsSync(const std::vector<uint16_t> &infos);
  bool HandleObjsCancleSync(const std::vector<uint16_t> &infos);

  bool LoraSend(const ProtoclMessageLora &msg, const Qos &qos);

  int CreateBlueListenThread();

  void SendUpdateEvent(const cotek_msgs::update_event::ConstPtr &msg);

  void SendTrafficArea(const cotek_msgs::traffic_state::ConstPtr &msg);

 protected:
 private:
  void SyncWorkHandler();

  void BlzListen();

  CommunicateOption option_;

  std::shared_ptr<std::thread> runner_ptr_{nullptr};
  std::shared_ptr<LoraManager> lora_manager_ptr_;
  std::map<uint16_t, std::shared_ptr<ResourceObj>> resource_map_;

  std::queue<std::shared_ptr<ResourceObj>> process_obj_queue_;

  std::mutex mutex_;

  std::atomic<bool> listening_{false};
  std::mutex listen_mutex_;
  cotek_common::ThreadPool thread_pool_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_RESOURCE_AGENT_H_