/**
 * Copyright (c) 2024 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_QUEUE_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_QUEUE_H_

#include <iostream>
#include <stdexcept>
#include <vector>

template <typename T>
class CircularQueue {
private:
  std::vector<T> data_;   // 使用 std::vector 存储队列元素
  int front_;             // 队头指针
  int back_;              // 队尾指针
  int capacity_;          // 队列的最大容量
  int size_;              // 当前队列中的元素个数

public:
  // 构造函数
  CircularQueue(int max_size = 5)
    : front_(0)
    , back_(0)
    , size_(0)
    , capacity_(max_size) {
    data_.resize(capacity_);  // 使用 vector 时，需要指定初始大小
  }

  // 析构函数
  ~CircularQueue() = default;  // std::vector 会自动管理内存

  // 判断队列是否为空
  bool empty() const {
    return size_ == 0;
  }

  // 判断队列是否为满
  bool full() const {
    return size_ == capacity_;
  }

  // 入队操作
  bool enqueue(const T& value) {
    if (full()) {
      // 队列已满，移除最旧的数据（即队头的元素）
      front_ = (front_ + 1) % capacity_;  // 更新头指针，移除最旧的数据
    } else {
      size_++;  // 队列未满，大小加一
    }
    
    // 将新元素插入到队尾
    data_[back_] = value;
    back_ = (back_ + 1) % capacity_;  // 更新队尾指针，保持环形结构
    return true;
  }

  // 出队操作
  bool dequeue(T& value) {
    if (empty()) {
      return false;  // 队列为空，返回 false
    }
    value = data_[front_];
    front_ = (front_ + 1) % capacity_;  // 更新队头指针，保持环形结构
    size_--;
    return true;
  }

  // 获取队头元素
  bool front(T& value) const {
    if (empty()) {
      return false;  // 队列为空，返回 false
    }
    value = data_[front_];
    return true;
  }

  // 获取队尾元素
  bool back(T& value) const {
    if (empty()) {
      return false;  // 队列为空，返回 false
    }
    value = data_[(back_ - 1 + capacity_) % capacity_];  // 环形结构
    return true;
  }

  // 获取队列当前大小
  int size() const {
    return size_;
  }

  // 打印队列中的所有元素
  void print() const {
    if (empty()) {
      std::cout << "Queue is empty\n";
      return;
    }

    int i = front_;
    while (i != back_) {
      std::cout << data_[i] << " ";
      i = (i + 1) % capacity_;  // 环形结构
    }
    std::cout << std::endl;
  }
};

#endif // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_QUEUE_H_