/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_LORA_MANAGER_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_LORA_MANAGER_H_

#include <ros/ros.h>

#include <cstddef>
#include <cstdint>
#include <deque>
#include <exception>
#include <functional>
#include <memory>
#include <string>
#include <thread>
#include <utility>
#include <vector>

#include "cotek_common/cotek_protocal.h"
#include "cotek_common/log_porting.h"
#include "cotek_communicate/communicate_method/communicate_interface.h"
#include "cotek_communicate/communicate_method/lora/lora.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_lora.h"
#include "cotek_msgs/traffic_state.h"
#include "cotek_msgs/update_event.h"

namespace cotek_communicate {

// 将形如 "98:2C:BC:1D:F1:F4"转换成6个无符号组成的Vector 0X98 0X2C 0XBC 0X1D
// 0XF1 0XF4
static bool convertMacStringToVector(const std::string& mac_str,
                                     std::vector<unsigned char>& macBytes) {
  macBytes.clear();

  std::istringstream iss(mac_str);
  std::string token;

  for (int i = 0; i < 6; ++i) {
    // 检查是否可以从输入字符串中获取一个有效的字节
    if (!std::getline(iss, token, ':') || token.size() != 2) {
      return false;
    }

    // 尝试将获取的字节从十六进制字符串转换为整数
    try {
      int byteValue = std::stoi(token, nullptr, 16);
      // 检查转换后的值是否在合法范围内
      if (byteValue < 0 || byteValue > 255) {
        return false;
      }
      // 将转换后的值添加到结果向量中
      macBytes.push_back(static_cast<unsigned char>(byteValue));
    } catch (const std::invalid_argument& ia) {
      // 如果token不能被解析为数字，std::stoi会抛出invalid_argument异常
      return false;
    } catch (const std::out_of_range& oor) {
      // 如果数值超出int类型范围，std::stoi会抛出out_of_range异常
      return false;
    }
  }

  // 如果所有字节都成功转换，返回true
  return true;
}

static std::string convertVectorToMacString(
    const std::vector<unsigned char>& ma_bytes) {
  std::ostringstream oss;
  for (size_t i = 0; i < ma_bytes.size(); ++i) {
    oss << std::uppercase << std::setfill('0') << std::setw(2) << std::hex
        << static_cast<int>(ma_bytes[i]);
    if (i < ma_bytes.size() - 1) oss << ":";
  }
  return oss.str();
}

class LoraManager {
 public:
  LoraManager();

  bool Init(const CommunicateOption& option);

  void MessageCallback(ProtoclMessageLora msg);

  bool Send(const ProtoclMessageLora& msg, const Qos& qos);

  void SendUpdateEvent(const cotek_msgs::update_event::ConstPtr& msg);

  void SendTrafficArea(const cotek_msgs::traffic_state::ConstPtr& msg);

  ~LoraManager() {}

 private:
  void HandleTrafficBroadCast(const ProtoclMessageLora& msg);
  void HandlQueryResource(const ProtoclMessageLora& msg);
  void HandlResourceFeedback(const ProtoclMessageLora& msg);
  void HandlRequestSystem(const ProtoclMessageLora& msg);
  void HandlSystemFeedback(const ProtoclMessageLora& msg);
  void HandlRequestSync(const ProtoclMessageLora& msg);
  void HandlSyncFeedback(const ProtoclMessageLora& msg);
  void HandlQueryTask(const ProtoclMessageLora& msg);

  std::map<LoraFunCode, std::function<void(const ProtoclMessageLora& msg)>>
      handler_;
  std::shared_ptr<CommunicateInterface<ProtoclMessageLora>> socket_;
  CommunicateOption option_;

  std::string agv_state_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_LORA_MANAGER_H_
