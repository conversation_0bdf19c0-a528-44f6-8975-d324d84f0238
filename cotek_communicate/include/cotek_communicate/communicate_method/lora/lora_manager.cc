/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate/communicate_method/lora/lora_manager.h"

#include <cstdint>
#include <exception>
#include <iomanip>
#include <iostream>
#include <sstream>

#include "cotek_common/cotek_config_helper.h"
#include "cotek_communicate/data_manager.h"
#include "cotek_msgs/traffic_state.h"
#include "cotek_msgs/task_order.h"
#include "cotek_msgs/task_cal.h"
#include "cotek_path/db/path_db.h"
#include "std_msgs/String.h"

namespace cotek_communicate {

LoraManager::LoraManager() {
  handler_[LoraFunCode::TRAFFIC] = std::bind(
      &LoraManager::HandleTrafficBroadCast, this, std::placeholders::_1);
  handler_[LoraFunCode::QUERY_RESOURCE] =
      std::bind(&LoraManager::HandlQueryResource, this, std::placeholders::_1);
  handler_[LoraFunCode::EVENT_RESOURCE] = std::bind(
      &LoraManager::HandlResourceFeedback, this, std::placeholders::_1);
  handler_[LoraFunCode::REQUEST_SYSTEM] =
      std::bind(&LoraManager::HandlRequestSystem, this, std::placeholders::_1);
  handler_[LoraFunCode::EVENT_SYSTEM] =
      std::bind(&LoraManager::HandlSystemFeedback, this, std::placeholders::_1);
  handler_[LoraFunCode::REQUEST_SYNC] =
      std::bind(&LoraManager::HandlRequestSync, this, std::placeholders::_1);
  handler_[LoraFunCode::EVENT_SYNC] =
      std::bind(&LoraManager::HandlSyncFeedback, this, std::placeholders::_1);
  handler_[LoraFunCode::QUERY_TASK] =
      std::bind(&LoraManager::HandlQueryTask, this, std::placeholders::_1);
}

bool LoraManager::Init(const CommunicateOption& option) {
  option_ = option;
  if (option.lora_option.lora_mode) {
    socket_ = std::make_shared<Lora<ProtoclMessageLora>>();
    bool ret = socket_->Init(option);
    if (!ret) return false;
    socket_->Listen(
        std::bind(&LoraManager::MessageCallback, this, std::placeholders::_1));
    socket_->Run();
  }

  agv_state_ = "Init";
  return true;
}

void LoraManager::MessageCallback(ProtoclMessageLora msg) {
  std::function<void(const ProtoclMessageLora& msg)> handle;
  try {
    handle = handler_.at(msg.fun_code());
    handle(msg);
  } catch (const std::out_of_range& e) {
    LOG_ERROR_STREAM("No such lora function type: " << msg.fun_code());

    return;
  }
}

bool LoraManager::Send(const ProtoclMessageLora& msg, const Qos& qos) {
  if (socket_) {
    socket_->Send(msg, qos);
    return true;
  }
  return false;
}

static int AgvState2Int(const std::string &type) {
  if (type == "Manual") return 1;
  if (type == "EmergencyStop") return 2;
  if (type == "Waiting") return 3;
  if (type == "Doing") return 4;
  if (type == "Finishing") return 5;
  if (type == "Pause") return 6;
  if (type == "EmergencyPause") return 7;
  if (type == "Charge") return 8;
  if (type == "Fault") return 9;
  if (type == "Error") return 10;
  if (type == "Init") return 11;

  LOG_ERROR("Not find this agvStateType: %s", type.c_str());
  return 0;
}

void LoraManager::SendUpdateEvent(
    const cotek_msgs::update_event::ConstPtr& msg) {
  agv_state_ = msg->agv_state;
}

void LoraManager::SendTrafficArea(
    const cotek_msgs::traffic_state::ConstPtr& msg) {
  ProtoclMessageLora lora_msg;
  lora_msg.set_serialnum(option_.lora_option.lora_id);
  lora_msg.set_fun_code(LoraFunCode::TRAFFIC);
  std::vector<uint8_t> data;
  data.push_back((msg->map_id & 0xff00) >> 8);
  data.push_back(msg->map_id & 0x00ff);

  if (msg->number.size() != msg->time.size()) return;

  if (msg->number.size() > 60) {
    LOG_ERROR("traffic area size cant over 60!!!");
    return;
  }

  try {
    for (int i = 0; i < msg->number.size(); i++) {
      data.push_back(static_cast<uint16_t>((msg->number.at(i)) & 0xff00) >> 8);
      data.push_back(static_cast<uint16_t>(msg->number.at(i)) & 0x00ff);
      data.push_back(static_cast<uint16_t>((msg->time.at(i)) & 0xff00) >> 8);
      data.push_back(static_cast<uint16_t>(msg->time.at(i)) & 0x00ff);
    }
  } catch (const std::exception ex) {
    LOG_ERROR(ex.what());
  }
  int16_t x = static_cast<int16_t>(msg->x*10);
  int16_t y = static_cast<int16_t>(msg->y*10);
  int16_t theta = static_cast<int16_t>(msg->theta*100);
  data.push_back((x & 0xff00) >> 8);
  data.push_back(x & 0x00ff);
  data.push_back((y & 0xff00) >> 8);
  data.push_back(y & 0x00ff);  
  data.push_back((theta & 0xff00) >> 8);
  data.push_back(theta & 0x00ff);
  data.push_back(msg->release);

  lora_msg.set_data(data);
  if (!lora_msg.PackMsg()) return;
  Send(lora_msg, Qos::QOS0);
}

void LoraManager::HandleTrafficBroadCast(const ProtoclMessageLora& msg) {
  try {
    cotek_msgs::traffic_state traffic;
    traffic.ID = msg.serialnum();
    auto&& data = msg.data();
    traffic.map_id = data[0] * 255 + data[1];
    uint32_t size = msg.data_size();
    const int kTrafficSize = 4;
    auto area_size = static_cast<uint8_t>((size - 2 - 7) / kTrafficSize);
    std::string traffic_info("Traffic Robot");
    traffic_info += std::to_string(traffic.ID) + ": ";
    for (int i = 0; i < area_size; i++) {
      uint16_t area = data[i*kTrafficSize + 2] * 255 + data[i*kTrafficSize + 3];
      uint16_t time = data[i*kTrafficSize + 4] * 255 + data[i*kTrafficSize + 5];
      traffic.number.push_back(area);
      traffic.time.push_back(time); 
      traffic_info += std::to_string(area) + "(" + std::to_string(time) + ") ";
    }

    int len = data.size();
    double x = (static_cast<int16_t>(data[len - 7] << 8 | data[len - 6]))/10.0;
    double y = (static_cast<int16_t>(data[len - 5] << 8 | data[len - 4]))/10.0;
    double theta = (static_cast<int16_t>(data[len - 3] << 8 | data[len - 2]))/100.0;    
    bool release = static_cast<bool>(data.back());
    traffic.x = x;
    traffic.y = y;
    traffic.theta = theta;
    traffic.release = release;
    traffic_info += "-- (" + std::to_string(x) + "," + 
                    std::to_string(y) + "," + 
                    std::to_string(theta) + "," +
                    std::to_string(release) + ") ";

    if (area_size > 0) {
      LOG_INFO_STREAM(" ---------- " << traffic_info << "----------");
    }

    DataManager::Instance().RosPublish("others_traffic", traffic);

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void LoraManager::HandlQueryResource(const ProtoclMessageLora& msg) {
  try {
    auto&& data = msg.data();
    auto&& query = data[0];
    // TODO(@ssh)现阶段仅有功能码0xff
    //  TODO(@ssh) 后续添加查询版本信息
    if (query == 0xFF) {
      // 查询反馈所有资源属性
      uint16_t software_ver = 0, map_ver = 0, path_ver = 0;
      uint8_t agv_id = option_.lora_option.lora_id;
      const std::string mac_str = DataManager::Instance().GetBlueMac();
      ProtoclMessageLora orign_msg;
      orign_msg.set_serialnum(agv_id);
      orign_msg.set_fun_code(LoraFunCode::EVENT_RESOURCE);
      std::vector<uint8_t> vaild_data;
      vaild_data.push_back(query);
      vaild_data.push_back(software_ver >> 8);
      vaild_data.push_back(software_ver & 0xff);
      vaild_data.push_back(map_ver >> 8);
      vaild_data.push_back(map_ver & 0xff);
      vaild_data.push_back(path_ver >> 8);
      vaild_data.push_back(path_ver & 0xff);
      std::vector<uint8_t> mac_vec;

      if (!convertMacStringToVector(mac_str, mac_vec)) return;
      for (int i = 0; i < 6; i++) {
        vaild_data.push_back(mac_vec[i]);
      }

      const std::string wifi_str = "AA:BB:CC:EE:FF:FF";

      std::vector<uint8_t> wifi_vec;
      if (!convertMacStringToVector(wifi_str, wifi_vec)) return;
      for (int i = 0; i < 6; i++) {
        vaild_data.push_back(wifi_vec[i]);
      }

      orign_msg.set_data(vaild_data);

      if (!orign_msg.PackMsg()) return;

      Send(orign_msg, Qos::QOS0);
    } else if (query == 0x21) {
      if (0xFF != msg.serialnum()) return;
      // 查询反馈车辆在线状态
      ProtoclMessageLora orign_msg;
      orign_msg.set_serialnum(option_.lora_option.lora_id);
      orign_msg.set_fun_code(LoraFunCode::EVENT_RESOURCE);

      std::vector<uint8_t> vaild_data;
      vaild_data.push_back(query);
      vaild_data.push_back(AgvState2Int(agv_state_));
      orign_msg.set_data(vaild_data);

      if (!orign_msg.PackMsg()) return;
      Send(orign_msg, Qos::QOS0);
    } else if (query == 0x22) {
      if (0xFF != msg.serialnum()) return;
      if (option_.lora_option.lora_id != data[1]) return;
      // 数据库有关信息内容查询上传
      std::vector<cotek::task_t> tasks;
      if (DB) {
        if (0 != DB->load_task(tasks)) {
          LOG_ERROR("load all task error");
        }
        LOG_INFO("load all task ok");
        int total_num = tasks.size();

        for (int i = 1; i <= total_num; i++) {
          ProtoclMessageLora orign_msg;
          orign_msg.set_serialnum(option_.lora_option.lora_id);
          orign_msg.set_fun_code(LoraFunCode::EVENT_RESOURCE);

          std::vector<uint8_t> vaild_data;
          vaild_data.push_back(query);
          vaild_data.push_back(static_cast<uint8_t>(i));
          vaild_data.push_back(static_cast<uint8_t>(total_num));
          std::string task_id = tasks[i-1].id;
          for (const char& c : task_id) {
            vaild_data.push_back(static_cast<uint8_t>(c));
          }
          vaild_data.push_back(static_cast<uint8_t>('/'));
          std::string task_name = tasks[i-1].name;
          for (const char& c : task_name) {
            vaild_data.push_back(static_cast<uint8_t>(c));
          }
          orign_msg.set_data(vaild_data);

          if (!orign_msg.PackMsg()) return;
          Send(orign_msg, Qos::QOS0);
        } 
      }

    }

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void LoraManager::HandlResourceFeedback(const ProtoclMessageLora& msg) {
  try {
    ResoureInfo info;
    info.id = msg.serialnum();
    info.software_ver = msg.data().at(1) * 255 + msg.data().at(2);
    info.map_ver = msg.data().at(3) * 255 + msg.data().at(4);
    info.path_ver = msg.data().at(5) * 255 + msg.data().at(6);
    std::vector<uint8_t> blue_mac_vec;
    blue_mac_vec.reserve(6);
    for (int i = 0; i < 6; i++) {
      blue_mac_vec.push_back(msg.data().at(i + 7));
    }

    info.bluetooth_mac = convertVectorToMacString(blue_mac_vec);

    std::vector<uint8_t> wifi_mac_vec;
    wifi_mac_vec.reserve(6);
    for (int i = 0; i < 6; i++) {
      wifi_mac_vec.push_back(msg.data().at(i + 13));
    }
    info.wifi_mac = convertVectorToMacString(wifi_mac_vec);

    LOG_DEBUG("Blue QUERY_RESOURCE: blue mac = %s wifi mac = %s",
      info.bluetooth_mac.c_str(), info.wifi_mac.c_str());

    DataManager::Instance().GetResourceAgent()->UpdateResource(info);

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void LoraManager::HandlRequestSync(const ProtoclMessageLora& msg) {
  try {
    // 目标车辆不是本车 则丢弃报文
    uint8_t target_id = msg.data().at(0);
    uint8_t current_id = option_.lora_option.lora_id;
    if (target_id != current_id) {
      LOG_WARN_COND(2, "target_id(%d) != sel_id(%d)", target_id, current_id);
      return;
    }
    auto data = msg.data();
    std::vector<uint8_t> mac_data(data.begin() + 2, data.begin() + 8);
    std::string target_mac = convertVectorToMacString(mac_data);
    std::string&& current_mac = DataManager::Instance().GetBlueMac();
    LOG_DEBUG("Blue REQUEST_SYNC: tmac = %s(%d) cmac = %s(%d)",
      target_mac.c_str(), target_id, current_mac.c_str(), current_id);
    if (target_mac != current_mac) {
      LOG_ERROR("%s != %s", target_mac.c_str(), current_mac.c_str());
      return;
    }

    ProtoclMessageLora ret_msg;

    ret_msg.set_serialnum(option_.lora_option.lora_id);
    ret_msg.set_fun_code(LoraFunCode::EVENT_SYNC);
    ret_msg.set_data(msg.data());
    if (!ret_msg.PackMsg()) return;
    Send(ret_msg, Qos::QOS0);

    // TODO(@ssh) 监听本地蓝牙服务器接收文件
    DataManager::Instance().GetResourceAgent()->CreateBlueListenThread();

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}
void LoraManager::HandlSyncFeedback(const ProtoclMessageLora& msg) {
  try {
    // 目标车辆不是本车 则丢弃报文
    uint8_t target_id = msg.data().at(0);
    uint8_t source_id = msg.data().at(1);
    uint8_t current_id = option_.lora_option.lora_id;
    LOG_DEBUG("Blue EVENT_SYNC: target_id = %d curr_id = %d",
      target_id, current_id);
    if (source_id != current_id) return;
    // 更新蓝牙对象列表内状态
    DataManager::Instance().GetResourceAgent()->UpdateTargetResoureBlueState(
        target_id, BlueZState::PRE_SYNC);

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void LoraManager::HandlRequestSystem(const ProtoclMessageLora& msg) {
  try {
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void LoraManager::HandlSystemFeedback(const ProtoclMessageLora& msg) {
  try {
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

void LoraManager::HandlQueryTask(const ProtoclMessageLora& msg) {
  try {
    if (0xFF != msg.serialnum()) return;

    auto&& data = msg.data();
    if (data[1] == 0x00) {
      if (data[0] != 0xFF && data[0] != option_.lora_option.lora_id)
        return;

      // 任务广播发送，计算任务代价
      uint16_t task = 0;
      task = data[2] << 8 | data[3];
      std::string task_id = std::to_string(task);

      cotek_msgs::task_cal order;
      order.request.task_id = task_id;
      DataManager::Instance().RosServiceCall("taskCal", order);
      int16_t data = order.response.data;
      LOG_INFO("task(%s) grade = %d", task_id.c_str(), data);
      if (data == 0xFFFF || data < 0) return;

      ProtoclMessageLora orign_msg;
      orign_msg.set_serialnum(option_.lora_option.lora_id);
      orign_msg.set_fun_code(LoraFunCode::EVENT_TASK);
      std::vector<uint8_t> vaild_data;
      int16_t grade = data;
      vaild_data.push_back(data);
      vaild_data.push_back(data >> 8);
      // vaild_data.push_back(grade >> 16);
      // vaild_data.push_back(grade >> 24);
      // vaild_data.push_back(grade >> 32);
      // vaild_data.push_back(grade >> 40);
      // vaild_data.push_back(grade >> 48);
      // vaild_data.push_back(grade >> 56);
      orign_msg.set_data(vaild_data);

      if (!orign_msg.PackMsg()) return;
      Send(orign_msg, Qos::QOS0);

    } else if (data[1] == 0x01) {
      if (data[0] != option_.lora_option.lora_id) return;
      // 反馈任务接收完成，准备执行
      ProtoclMessageLora orign_msg;
      orign_msg.set_serialnum(option_.lora_option.lora_id);
      orign_msg.set_fun_code(LoraFunCode::EVENT_TASK);

      std::vector<uint8_t> vaild_data;
      vaild_data.push_back(0xFF);
      vaild_data.push_back(0xFF);
      orign_msg.set_data(vaild_data);

      if (!orign_msg.PackMsg()) return;
      Send(orign_msg, Qos::QOS0);

      // todo: 下发任务信息
      uint16_t task = 0;
      task = data[2] << 8 | data[3];
      std::string task_id = std::to_string(task);

      LOG_INFO("ready exec sn(%s) task", task_id.c_str());
      cotek_msgs::task_order order;
      order.request.sn = task_id;
      order.request.cmd = 0;
      DataManager::Instance().RosServiceCall("taskOrder", order);
    }
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }
}

}  // namespace cotek_communicate
