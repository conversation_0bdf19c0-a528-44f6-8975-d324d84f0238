/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_LORA_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_LORA_H_

#include <ros/ros.h>
#include <serial/serial.h>

#include <cstddef>
#include <cstdint>
#include <deque>
#include <exception>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <utility>
#include <vector>
#include <random>

#include "cotek_common/cotek_protocal.h"
#include "cotek_common/log_porting.h"
#include "cotek_communicate/communicate_method/communicate_interface.h"

namespace cotek_communicate {

enum class SerialState : uint8_t { UNINITIALIZED = 0, ACTIVE = 1, STOPPED = 2 };
static int kMaxSize = 10;

template <typename MessageType>
class Lora : public CommunicateInterface<MessageType> {
 public:
  ~Lora() {}

  bool Init(const CommunicateOption &option) override {
    try {
      serial_.setPort(option.lora_option.lora_chanel);
      serial_.setBaudrate(115200);
      serial::Timeout to = serial::Timeout::simpleTimeout(100);
      serial_.setTimeout(to);
      serial_.open();
    } catch (serial::IOException &e) {
      // LOG_ERROR("Unable to open %s !!!", param_.port);
    }
    if (serial_.isOpen()) {
      // LOG_WARN_STREAM("Serial Port initialized : " << param_.port);
      state_ = SerialState::ACTIVE;
      return true;
    }
    LOG_ERROR("Lora init failed!!!");
    return false;
  }

  bool Stop() override { return true; }

  void Run() override {
    runner_ = std::make_shared<std::thread>(std::bind(&Lora::Runner, this));
    sendner_ = std::make_shared<std::thread>(std::bind(&Lora::Sendner, this));
  }

  void Close() {}

  void Listen(std::function<void(MessageType)> handler) override {
    handler_ = handler;
  }

  void Send(const MessageType &msg, const Qos &qos) override {
    try {
      // TODO(@ssh) 后续考虑构建一个超时filter的队列
      std::unique_lock<std::mutex> lock(mutex_);
      while(send_queue_.size() >= kMaxSize) {
        send_queue_.pop();
      }
      send_queue_.push(msg);
    } catch (const std::exception &ex) {
      LOG_ERROR(ex.what());
    }
  }

  MessageType CreatEmptyMessage() override { return MessageType(); }

 protected:
 private:
  void Sendner() {
    // ros::Rate rate(2);
    std::random_device rd;
    while (ros::ok()) {
      try {
        std::unique_lock<std::mutex> lock(mutex_);
        if (!send_queue_.empty() && state_ == SerialState::ACTIVE) {
          auto &&msg = send_queue_.front();
          serial_.write(msg.orign_data());
          send_queue_.pop();
        }

      } catch (serial::IOException &e) {
        LOG_ERROR(e.what());
      } catch(std::exception &e) {
        LOG_ERROR_THROTTLE(1, e.what());
      }
      // rate.sleep();
      int random_number = rd() % 401 + 800;
      std::this_thread::sleep_for(
          std::chrono::milliseconds(random_number));
    }
  }
  void Runner() {
    ros::Rate rate(20);
    while (ros::ok()) {
      try {
        size_t buffer_size = 0;
        std::vector<uint8_t> original_data;
        buffer_size = serial_.available();
        if (buffer_size) {
          serial_.read(original_data, buffer_size);
          ParseData(original_data);
        }

        rate.sleep();
      } catch (serial::IOException &e) {
        LOG_ERROR_THROTTLE(1, e.what());
      } catch(std::exception &e) {
        LOG_ERROR_THROTTLE(1, e.what());
      }
    }
  }

  void ParseData(const std::vector<uint8_t> &data) {
    try {
      auto &&packs = MessageType::ParseToMultiPack(data);

      for (auto &&pack : packs) {
        MessageType msg(pack);
        if (!msg.ParseMsg()) {
          LOG_WARN("parse pack error");
          break;
        }
        handler_(msg);
      }
    } catch (const std::exception &ex) {
      LOG_ERROR(ex.what());
    }
  }

  SerialState state_{SerialState::UNINITIALIZED};

  serial::Serial serial_;

  std::function<void(MessageType)> handler_;

  std::shared_ptr<std::thread> runner_;

  std::mutex mutex_;

  std::shared_ptr<std::thread> sendner_;

  std::queue<MessageType> send_queue_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_LORA_H_
