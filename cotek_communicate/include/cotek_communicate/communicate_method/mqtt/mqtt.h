/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_MQTT_MQTT_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_MQTT_MQTT_H_
#include <mqtt/async_client.h>
#include <ros/ros.h>
#include <stdint.h>
#include <stdio.h>

#include <cstdint>
#include <deque>
#include <exception>
#include <memory>
#include <string>
#include <thread>

#include "cotek_common/cotek_protocal.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/thread_pool.h"
#include "cotek_communicate/communicate_method/communicate_interface.h"
#include "cotek_communicate/data_manager.h"

namespace cotek_communicate {

using ThreadPool = cotek_common::ThreadPool;

enum class RunningState : std::uint16_t {
  NONE = 0,
  RUNNING = 1,
  STOP = 2,
};

using SubCallback =
    std::function<void(const std::string& topic, const std::string& playload)>;

class ActionListener : public virtual mqtt::iaction_listener {
 public:
  explicit ActionListener(const std::string& name) : name_(name) {}
  std::string name_;

  void on_failure(const mqtt::token& tok) override {
    if (tok.get_message_id() != 0) {
      LOG_ERROR_STREAM("Failure for token: [" << tok.get_message_id() << "]");
    }
  }

  void on_success(const mqtt::token& tok) override {}
};

class Callback : public virtual mqtt::callback,
                 public virtual mqtt::iaction_listener

{
 public:
  Callback(std::shared_ptr<mqtt::async_client> cli,
           const mqtt::connect_options& conn_opts,
           const CommunicateOption& option)
      : cli_ptr_(cli),
        conn_opts_(conn_opts),
        subListener_("Subscription"),
        option_(option),
        state_(RunningState::NONE) {}

  bool stop() {
    state_ = RunningState::STOP;
    return true;
  }

  void set_callback(const SubCallback& callback) { sub_call_back_ = callback; }
  void reconnect() {
    std::this_thread::sleep_for(std::chrono::milliseconds(2500));
    try {
      if (state_ == RunningState::STOP) return;
      cli_ptr_->connect(conn_opts_, nullptr, *this);
      LOG_WARN("Reconnecting...");
    } catch (const mqtt::exception& exc) {
      LOG_ERROR(exc.what());
    }
  }

  // Re-connection failure
  void on_failure(const mqtt::token& tok) override {
    if (state_ != RunningState::STOP) reconnect();
  }

  // (Re)connection success
  // Either this or connected() can be used for callbacks.
  void on_success(const mqtt::token& tok) override {}

  // (Re)connection success
  void connected(const std::string& cause) override {
    LOG_INFO("MQTT connected");

    std::string topic = std::string("/COTEK/") +
                        DataManager::Instance().GetSerialNum() +
                        std::string("/master/#");
    cli_ptr_->subscribe(topic, static_cast<int>(cotek_protocal::Qos::QOS2),
                        nullptr, subListener_);
  }

  void connection_lost(const std::string& cause) override {
    if (state_ != RunningState::STOP) reconnect();
  }

  void message_arrived(mqtt::const_message_ptr msg) override {
    if (state_ == RunningState::STOP) return;
    if (!sub_call_back_) return;
    sub_call_back_(msg->get_topic(), msg->to_string());
  }

  void delivery_complete(mqtt::delivery_token_ptr token) override {}

 private:
  // The MQTT client
  std::shared_ptr<mqtt::async_client> cli_ptr_;
  // Options to use if we need to reconnect
  mqtt::connect_options conn_opts_;
  // An action listener to display the result of actions.
  ActionListener subListener_;

  CommunicateOption option_;

  SubCallback sub_call_back_;

  RunningState state_;
};

template <typename MessageType>
class Mqtt : public CommunicateInterface<MessageType> {
 public:
  Mqtt()
      : async_client_ptr_(nullptr),
        callback_ptr_(nullptr),
        state_(RunningState::NONE) {}
  ~Mqtt() { Stop(); }

  bool Init(const CommunicateOption& option) override {
    option_ = option;

    ip_ = option_.server_ip;
    port_ = std::to_string(option_.server_port);
    client_id_ = DataManager::Instance().GetSerialNum();

    async_client_ptr_ =
        std::make_shared<mqtt::async_client>(ip_ + ":" + port_, client_id_);

    mqtt::connect_options conn_opts;
    conn_opts.set_clean_session(true);

    callback_ptr_ =
        std::make_shared<Callback>(async_client_ptr_, conn_opts, option_);

    try {
      async_client_ptr_->set_callback(*callback_ptr_);
      async_client_ptr_->connect(conn_opts, nullptr, *callback_ptr_);
    } catch (const std::exception& exc) {
      LOG_ERROR(exc.what());
      return false;
    }

    return true;
  }

  bool Stop() override {
    if (async_client_ptr_ && callback_ptr_) {
      callback_ptr_->stop();
      state_ = RunningState::STOP;
    }
    return true;
  }

  void Run() override {
    if (!async_client_ptr_ || !callback_ptr_) {
      return;
    }

    sub_topic_ = std::string("/COTEK/") +
                 DataManager::Instance().GetSerialNum() +
                 std::string("/master/#");

    SubCallback sub_call_back =
        std::bind(&Mqtt::ProtoclHandle, this, std::placeholders::_1,
                  std::placeholders::_2);
    callback_ptr_->set_callback(sub_call_back);
  }

  void Listen(std::function<void(MessageType)> handler) override {
    handler_ = handler;
  }

  void Send(const MessageType& msg, const Qos& qos) override {
    MqttSendMsg(msg, qos);
  }

  MessageType CreatEmptyMessage() override { return MessageType(); }

 private:
  void ProtoclHandle(const std::string& topic, const std::string& playload) {
    try {
      MessageType r_message;
      r_message.Clear();

      size_t pos = topic.find("/master");
      std::string type;
      if (pos != std::string::npos) {
        type = topic.substr(pos + 8);  // +8是为了跳过"/master/"的长度
        LOG_INFO_STREAM(type);
      }
      r_message.SetMsgType(type);
      r_message.SetMsgData(playload);

      if (!handler_) return;
      handler_(r_message);
    } catch (const std::exception& ex) {
      LOG_ERROR(ex.what());
    }
  }

  void MqttSendMsg(const MessageType& msg, const Qos& qos) {
    if (!async_client_ptr_) return;
    if (state_ == RunningState::STOP) return;
    try {
      std::string topic = std::string("/COTEK/") + msg.MsgBind() +
                          std::string("/agv/") + msg.MsgType();

      mqtt::message_ptr pubmsg = mqtt::make_message(topic, msg.MsgData());
      pubmsg->set_qos(static_cast<int>(qos));
      async_client_ptr_->publish(pubmsg);

      LOG_INFO_STREAM("Mqtt send [" << msg.MsgType() << "]");
    } catch (const std::exception& ex) {
      LOG_ERROR(ex.what());
    }
  }

  CommunicateOption option_;
  std::deque<MessageType> w_messageQueue_;
  MessageType r_message_;

  std::string ip_;
  std::string port_;
  std::string client_id_;

  std::string sub_topic_;
  std::string pub_topic_;

  std::function<void(MessageType)> handler_;

  std::shared_ptr<mqtt::async_client> async_client_ptr_;

  std::shared_ptr<Callback> callback_ptr_;

  RunningState state_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_MQTT_MQTT_H_
