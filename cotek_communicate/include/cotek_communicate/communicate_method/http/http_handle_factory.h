/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_HANDLE_FACTORY_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_HANDLE_FACTORY_H_

#include <Poco/Net/HTTPRequestHandlerFactory.h>
#include <Poco/Net/MessageHeader.h>
#include <ros/ros.h>

#include <cstdint>
#include <deque>
#include <functional>
#include <string>
#include <thread>
#include <utility>
#include <vector>

#include "Poco/Net/HTMLForm.h"
#include "Poco/Net/HTTPRequestHandler.h"
#include "Poco/Net/HTTPServer.h"
#include "Poco/Net/HTTPServerRequest.h"
#include "Poco/Net/HTTPServerResponse.h"
#include "Poco/Net/HTTPServerResponseImpl.h"
#include "Poco/Net/NetException.h"
#include "Poco/Net/ServerSocket.h"
#include "Poco/Net/WebSocket.h"
#include "Poco/URI.h"
#include "Poco/Util/ServerApplication.h"
#include "cotek_common/cotek_config_helper.h"
#include "cotek_common/cotek_protocal.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/thread_pool.h"
#include "cotek_common/util/httplib.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"
namespace cotek_communicate {
using PocoHttpServer = Poco::Net::HTTPServer;
using PocoHttpHandleFactory = Poco::Net::HTTPRequestHandlerFactory;
using PocoHttpRequestHandler = Poco::Net::HTTPRequestHandler;
using PocoHttpServerRequset = Poco::Net::HTTPServerRequest;
using PocoHttpServerResponse = Poco::Net::HTTPServerResponse;
using PocoWebScoket = Poco::Net::WebSocket;
using PocoHtmlForm = Poco::Net::HTMLForm;

static constexpr char kHttpOk[] = "00000001";
static constexpr char kHttpError[] = "00000000";
static constexpr char kHttpNotFound[] = "404";

struct vertex {
  std::string id{""};
  double x{0};
  double y{0};
};

struct kuweiSize {
  double width{0};
  double length{0};
  double rowspace{0};
  double columnspace{0};
};

struct storageInfo {
  std::vector<vertex> vertexs;
  kuweiSize size;
  std::string direction{""};
  std::string name{""};

  storageInfo() = default;
};

struct edgeInfo {
  std::vector<std::string> id;
  std::vector<int> speed;
  std::vector<int> safety;

  edgeInfo() = default;
};

struct nodeInfo {
  std::vector<std::string> id;
  std::vector<std::string> type;

  nodeInfo() = default;
};

class HttpResponse {
 public:
  HttpResponse(const std::string &code, const std::string &data,
               const std::string &message = std::string(""))
      : code_(code), data_(data), message_(message) {}

  static std::string CreatHttpResponse(
      const std::string &code, const std::string &data,
      const std::string &message = std::string("")) {
    Json json;
    json["code"] = code;
    json["data"] = data;
    json["message"] = message;
    return json.dump();
  }

 private:
  std::string code_;
  std::string data_;
  std::string message_;
};

class HttpHandleFactory : public PocoHttpHandleFactory {
 public:
  HttpHandleFactory() {};
  ~HttpHandleFactory() {};
  PocoHttpRequestHandler *createRequestHandler(
      const PocoHttpServerRequset &request) override;

 private:
  PocoHttpRequestHandler *createStreamRequestHandler(
      const PocoHttpServerRequset &request);
};

class RedirectHandler: public PocoHttpRequestHandler {
public:
  void handleRequest(PocoHttpServerRequset& request, 
                     PocoHttpServerResponse& response) override {
    // 设置 301 永久重定向
    std::string host = request.get("Host", "No Host Header");
    size_t index = host.find(':');
    if (index != std::string::npos) {
      host = host.substr(0, index);
    }
    std::string redirect_url = "http://" + host + request.getURI();
    //std::cout << "Redirecting from port 9999 to: " << redirect_url << std::endl;
    response.set("Access-Control-Allow-Origin", "*");
    response.setStatus(PocoHttpServerResponse::HTTP_MOVED_PERMANENTLY);
    response.set("Location", redirect_url);
    response.send();
  }
};

class NullHandle : public PocoHttpRequestHandler {
 public:
  NullHandle() {};
  ~NullHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class ConfirmSendHandle : public PocoHttpRequestHandler {
 public:
  ConfirmSendHandle()
    : thread_pool_(1) {};
  ~ConfirmSendHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void NoBlockService(const std::string &type, const int8_t &data);

  cotek_common::ThreadPool thread_pool_;

};

class DocumentHandle : public PocoHttpRequestHandler {
 public:
  DocumentHandle(){};
  ~DocumentHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void SendHelpFile(PocoHttpServerRequset &request,
                   PocoHttpServerResponse &response);
};

class StatusHandle : public PocoHttpRequestHandler {
 public:
  StatusHandle() {};
  ~StatusHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class StatusStreamHandle : public PocoHttpRequestHandler {
 public:
  StatusStreamHandle(){};
  ~StatusStreamHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class SensorHandle : public PocoHttpRequestHandler {
 public:
  SensorHandle() {};
  ~SensorHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class ControlHandle : public PocoHttpRequestHandler {
 public:
  ControlHandle() {};
  ~ControlHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class GetConfigHandle : public PocoHttpRequestHandler {
 public:
  GetConfigHandle() {};
  ~GetConfigHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  cotek_config::ConfigType GetConfigType(const std::string &type);

  void Confirm(PocoHttpServerRequset &request,
               PocoHttpServerResponse &response);

  void GetConfig(PocoHttpServerRequset &request,
                 PocoHttpServerResponse &response);
};

class UpdateConfigHandle : public PocoHttpRequestHandler {
 public:
  UpdateConfigHandle() {};
  ~UpdateConfigHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class MapFileHandle : public PocoHttpRequestHandler {
 public:
  MapFileHandle() {};
  ~MapFileHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void SendMapFile(PocoHttpServerRequset &request,
                   PocoHttpServerResponse &response);

  void SendMapList(PocoHttpServerRequset &request,
                   PocoHttpServerResponse &response);

  void SendMapImage(PocoHttpServerRequset &request,
                   PocoHttpServerResponse &response);

  void SendMapSvg(PocoHttpServerRequset &request,
                  PocoHttpServerResponse &response);

  void Send3dMapSvg(PocoHttpServerRequset &request,
                    PocoHttpServerResponse &response);

  void SendMapInfo(PocoHttpServerRequset &request,
                   PocoHttpServerResponse &response);

  void Send3dMapInfo(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response);
};

class UpdateMapFileHandle : public PocoHttpRequestHandler {
 public:
  UpdateMapFileHandle() {};
  ~UpdateMapFileHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void UpdateMapFile(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response);

  void DeleteMapFile(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response);

  void DeleteRelocation(const std::string &map_id);
  void DeleteTask(const std::string &map_id);
  void DeleteFloor(const std::string &map_id);
};

class EditMapFileHandle : public PocoHttpRequestHandler {
 public:
  EditMapFileHandle(){};
  ~EditMapFileHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class UpdateStorageMapHandle : public PocoHttpRequestHandler {
 public:
  UpdateStorageMapHandle() {};
  ~UpdateStorageMapHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;
};

class MapStreamHandle : public PocoHttpRequestHandler {
 public:
  MapStreamHandle() {};
  ~MapStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class Map3dStreamHandle : public PocoHttpRequestHandler {
 public:
  Map3dStreamHandle() {};
  ~Map3dStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class OrderHandle : public PocoHttpRequestHandler {
 public:
  OrderHandle() {};
  ~OrderHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class InstantActionHandle : public PocoHttpRequestHandler {
 public:
  InstantActionHandle() {};
  ~InstantActionHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class MasterAmrSyncHandle : public PocoHttpRequestHandler {
 public:
  MasterAmrSyncHandle() {};
  ~MasterAmrSyncHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class AmrSyncHandle : public PocoHttpRequestHandler {
 public:
  AmrSyncHandle() {};
  ~AmrSyncHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class StatisticsHandle : public PocoHttpRequestHandler {
 public:
  StatisticsHandle() {};
  ~StatisticsHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  nlohmann::ordered_json GetOdomInfoJson(PocoHttpServerRequset &request);
  nlohmann::ordered_json GetStorageInfoJson(PocoHttpServerRequset &request);
};

class ImageStreamHandle : public PocoHttpRequestHandler {
 public:
  ImageStreamHandle() {};
  ~ImageStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class LocalizerStreamHandle : public PocoHttpRequestHandler {
 public:
  LocalizerStreamHandle() {};
  ~LocalizerStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class Localizer3dStreamHandle : public PocoHttpRequestHandler {
 public:
  Localizer3dStreamHandle() {};
  ~Localizer3dStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class PointsStreamHandle : public PocoHttpRequestHandler {
 public:
  PointsStreamHandle() {};
  ~PointsStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class CalibrationHandle : public PocoHttpRequestHandler {
 public:
  enum CalibrationStatus : uint8_t { NONE = 0, SUCCESS = 1, FAILED = 2 };
  CalibrationHandle()
      : thread_pool_(1),
        response_status_(CalibrationStatus::NONE),
        has_call_(false) {};
  ~CalibrationHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  std::string CalibrationStatusToString(const CalibrationStatus &status) {
    switch (status) {
      case CalibrationStatus::SUCCESS: {
        return std::string("success");
      }
      case CalibrationStatus::FAILED: {
        return std::string("failed");
      }
      default: {
        return std::string("doing");
      }
    }
  }

  void NoBlockService(const std::string &type,
                      const std::vector<std::string> &parameters,
                      const int &method);

  cotek_common::ThreadPool thread_pool_;
  CalibrationStatus response_status_;
  std::string response_message_;
  bool has_call_;
};

class ParamsGetHandle : public PocoHttpRequestHandler {
 public:
  ParamsGetHandle(){};
  ~ParamsGetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class ParamsSetHandle : public PocoHttpRequestHandler {
 public:
  ParamsSetHandle(){};
  ~ParamsSetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class WifiGetHandle : public PocoHttpRequestHandler {
 public:
  WifiGetHandle(){};
  ~WifiGetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void WifiScan(PocoHttpServerRequset &request,
                PocoHttpServerResponse &response);
  void GetStatus(PocoHttpServerRequset &request,
                PocoHttpServerResponse &response);
};

class WifiSetHandle : public PocoHttpRequestHandler {
 public:
  WifiSetHandle(){};
  ~WifiSetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class FloorsSetHandle : public PocoHttpRequestHandler {
 public:
  FloorsSetHandle(){};
  ~FloorsSetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class FloorsGetHandle : public PocoHttpRequestHandler {
 public:
  FloorsGetHandle(){};
  ~FloorsGetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class TestStreamHandle : public PocoHttpRequestHandler {
 public:
  TestStreamHandle() {};
  ~TestStreamHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class TestHandle : public PocoHttpRequestHandler {
 public:
  TestHandle() {};
  ~TestHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class GuideGetHandle : public PocoHttpRequestHandler {
 public:
  GuideGetHandle() {};
  ~GuideGetHandle() {};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class SyncGetHandle : public PocoHttpRequestHandler {
 public:
  SyncGetHandle(){};
  ~SyncGetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class SyncUpdateHandle : public PocoHttpRequestHandler {
 public:
  SyncUpdateHandle(){};
  ~SyncUpdateHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class SystemHandle : public PocoHttpRequestHandler {
 public:
  SystemHandle(){};
  ~SystemHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void resetMap(const std::string &dir);
  void resetRelocation(const std::string &file);
  void resetFloor(const std::string &file_path);
  int resetDB();
};

class LanguageSetHandle : public PocoHttpRequestHandler {
 public:
  LanguageSetHandle(){};
  ~LanguageSetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class LanguageGetHandle : public PocoHttpRequestHandler {
 public:
  LanguageGetHandle(){};
  ~LanguageGetHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class ReLocationListHandle : public PocoHttpRequestHandler {
 public:
  ReLocationListHandle(){};
  ~ReLocationListHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class ReLocationUpdateHandle : public PocoHttpRequestHandler {
 public:
  ReLocationUpdateHandle(){};
  ~ReLocationUpdateHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void Insert(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Delete(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Update(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Default(PocoHttpServerRequset &request,
               PocoHttpServerResponse &response);
};

class TeachTaskListHandle : public PocoHttpRequestHandler {
 public:
  TeachTaskListHandle(){};
  ~TeachTaskListHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  void GetTaskListImage(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response);
};

class TeachTaskStreamHandle : public PocoHttpRequestHandler {
 public:
  TeachTaskStreamHandle(){};
  ~TeachTaskStreamHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class TeachTaskSendHandle : public PocoHttpRequestHandler {
 public:
  TeachTaskSendHandle(){};
  ~TeachTaskSendHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
};

class TeachTaskUpdateHandle : public PocoHttpRequestHandler {
 public:
  enum TaskCmd : uint8_t {
    NO = 0,
    START = 1,
    PRE_EXTEND = 2,
    EXTEND = 3,
    PRE_COVER = 4,
    COVER = 5
  };

  enum ResponseStatus : uint8_t { NONE = 0, SUCCESS = 1, FAILED = 2 };
  TeachTaskUpdateHandle()
      : thread_pool_(1),
        response_status_(ResponseStatus::NONE),
        has_call_(false){};
  ~TeachTaskUpdateHandle(){};

  void handleRequest(PocoHttpServerRequset &request,
                     PocoHttpServerResponse &response) override;

 private:
  std::string ResponseStatusToString(const ResponseStatus &status) {
    switch (status) {
      case ResponseStatus::SUCCESS: {
        return std::string("success");
      }
      case ResponseStatus::FAILED: {
        return std::string("failed");
      }
      default: {
        return std::string("doing");
      }
    }
  }
  void Start(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void PreExtend(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Extend(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void PreCover(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Cover(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Insert(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Delete(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void Edit(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void TaskEdit(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void StorageEdit(PocoHttpServerRequset &request, PocoHttpServerResponse &response);

  void NoBlockService(const std::string &method,
                      const std::string &task_id, const int &task_type, const int &task_cmd,
                      const std::string &action_type = "", const std::string &name = "",
                      const int speed = 0, const int safety = 0, const int loop = 0,
                      const int number = 0, const edgeInfo& edges = edgeInfo(),
                      const nodeInfo& nodes = nodeInfo(), const storageInfo& storage = storageInfo());

  cotek_common::ThreadPool thread_pool_;
  ResponseStatus response_status_;
  std::string response_message_;
  bool has_call_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_HANDLE_FACTORY_H_