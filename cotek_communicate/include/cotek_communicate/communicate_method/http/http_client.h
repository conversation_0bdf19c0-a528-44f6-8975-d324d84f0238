/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_CLIENT_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_CLIENT_H_

#include <Poco/Net/HTTPClientSession.h>
#include <Poco/Net/HTTPRequest.h>
#include <Poco/Net/HTTPResponse.h>
#include <Poco/StreamCopier.h>
#include <ros/ros.h>
#include <stdint.h>
#include <stdio.h>

#include <cstddef>
#include <deque>
#include <exception>
#include <string>
#include <thread>

#include "Poco/DigestStream.h"
#include "Poco/MD5Engine.h"
#include "cotek_common/common.h"
#include "cotek_common/cotek_protocal.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/thread_pool.h"
#include "cotek_communicate/communicate_method/communicate_interface.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"

namespace cotek_communicate {

using ThreadPool = cotek_common::ThreadPool;

static std::string HttpGetToken(const std::string &ip, const int &port) {
  std::string str;
  try {
    Poco::Net::HTTPClientSession session(ip, port);
    std::string path = "/sys/api/thirdParty/displaceToken";

    Poco::Net::HTTPRequest request(Poco::Net::HTTPRequest::HTTP_POST, path);

    nlohmann::ordered_json json;
    json["appKey"] = "DISPATCH";
    json["timeStamp"] = common::GetCurrentTime<std::string>();
    json["appSecret"] = "847fca3b5012ded76cc4c9a88225d3a8";

    Poco::MD5Engine md5;
    std::string input_string = "sign:DISPATCH-847fca3b5012ded76cc4c9a88225d3a8";

    md5.update(input_string);

    json["code"] = Poco::DigestEngine::digestToHex(md5.digest());

    std::string sent_msg = json.dump();

    request.setContentType("application/json");
    request.setContentLength(sent_msg.size());

    std::ostream &requestStream = session.sendRequest(request);
    requestStream << sent_msg;

    Poco::Net::HTTPResponse response;
    std::istream &response_stream = session.receiveResponse(response);

    nlohmann::ordered_json res_js(
        nlohmann::ordered_json::parse(response_stream));
    if (res_js.at("code").get<std::string>() == "00000001") {
      // 仅成功赋值token
      str = res_js["data"].get<std::string>();
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return str;
}

static std::string HttpGetString(const std::string &ip, const int &port,
                                 const std::string &path) {
  std::string str;
  try {
    std::string &&token = HttpGetToken(ip, port);
    if (token.empty()) return str;
    LOG_INFO_STREAM("Token: " << token);

    Poco::Net::HTTPClientSession session(ip, port);

    Poco::Net::HTTPRequest request(Poco::Net::HTTPRequest::HTTP_GET, path);

    request.set("Authorization", token);

    session.sendRequest(request);

    Poco::Net::HTTPResponse response;
    std::istream &response_stream = session.receiveResponse(response);

    Json json = Json::parse(response_stream);
    if (json.at("code").get<std::string>() == "00000001") {
      // 仅成功赋值token
      str = json["data"].dump();
      LOG_INFO_STREAM(str);
    }
  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
  }
  return str;
}

template <typename MessageType>
class HttpClient : public CommunicateInterface<MessageType> {
 public:
  HttpClient() : thread_pool_(5) {}
  ~HttpClient() { Stop(); }

  bool Init(const CommunicateOption &option) override {
    option_ = option;
    ip_ = option_.server_ip;
    port_ = option_.server_http_port;
    return true;
  }

  bool Stop() override { return true; }

  void Run() override {}

  void Listen(std::function<void(MessageType)> handler) override {}

  void Send(const MessageType &msg, const Qos &qos) override {
    auto no_block_send = [&](const MessageType &msg, const Qos &qos) {
      HttpSendMsg(msg, qos);
    };
    thread_pool_.enqueue(no_block_send, msg, qos);
  }

  MessageType CreatEmptyMessage() override { return MessageType(); }

 private:
  void HttpSendMsg(const MessageType &msg, const Qos &qos) {
    try {
      std::string topic = std::string("/agv/") + msg.MsgType();
      std::string ip = ip_;
      int port = port_;
      if (msg.MsgType() == cotek_protocal::msg_type::kUpdateEvent) {
        ip = "127.0.0.1";
        port = 8103;
      }

      if (msg.MsgType() == cotek_protocal::msg_type::kOdomInfoEvent) {
        topic = "/statistic/agv/odomInfo";
      }

      if (msg.MsgType() == "eventInfo") {
        topic = "/statistic/agv/eventInfo";
      }

      // 设置超时时间（单位：秒）
      Poco::Timespan timeout(2, 0);  // 2秒

      Poco::Net::HTTPClientSession session(ip, port);
      session.setTimeout(timeout);

      // 创建一个POST请求
      Poco::Net::HTTPRequest request(Poco::Net::HTTPRequest::HTTP_POST,

                                     topic, Poco::Net::HTTPMessage::HTTP_1_1);

      request.setContentType("application/json");
      request.setContentLength(msg.SendBufferSize());

      // Send the request
      std::ostream &requestStream = session.sendRequest(request);
      requestStream << msg.MsgData();
      LOG_INFO_STREAM_THROTTLE(1, "Http send [" << msg.MsgType() << "]");

      // 获取并打印响应
      Poco::Net::HTTPResponse response;
      std::istream &rs = session.receiveResponse(response);

      if (response.getStatus() != Poco::Net::HTTPResponse::HTTP_OK) {
        // 打印响应状态和头部信息
        LOG_INFO_STREAM("Response Status: " << response.getStatus()
                                            << " Response Reason:"
                                            << response.getReason());
      }

    } catch (const std::exception &ex) {
      LOG_ERROR(ex.what());
    }
  }

  CommunicateOption option_;
  std::deque<MessageType> w_messageQueue_;
  MessageType r_message_;

  std::string ip_;
  int port_;
  std::string client_id_;

  std::string sub_topic_;
  std::string pub_topic_;

  ThreadPool thread_pool_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_CLIENT_H_
