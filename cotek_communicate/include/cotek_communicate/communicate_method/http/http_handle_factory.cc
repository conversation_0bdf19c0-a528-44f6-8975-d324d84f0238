#include "cotek_communicate/communicate_method/http/http_handle_factory.h"

#include <Poco/Base64Encoder.h>
#include <Poco/DirectoryIterator.h>
#include <Poco/Environment.h>
#include <Poco/File.h>
#include <Poco/Net/HTTPClientSession.h>
#include <Poco/Net/HTTPServerResponseImpl.h>
#include <Poco/Net/PartHandler.h>
#include <Poco/Net/PartSource.h>
#include <Poco/Path.h>
#include <Poco/Random.h>
#include <Poco/StreamCopier.h>
#include <Poco/Zip/Compress.h>
#include <Poco/Zip/Decompress.h>
#include <zlib.h>

#include <dirent.h>
#include <sys/types.h>
#include <tf/transform_listener.h>

#include <chrono>
#include <cstdint>
#include <exception>
#include <fstream>
#include <ios>
#include <iostream>
#include <limits>
#include <memory>
#include <ostream>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

#include "cotek_common/common.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/math.h"
#include "cotek_communicate/communicate_method/http/http_client.h"
#include "cotek_communicate/data_manager.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_vda5050.h"
#include "cotek_communicate/wifi.h"
#include "cotek_msgs/action_cmd.h"
#include "cotek_msgs/action_command.h"
#include "cotek_msgs/calibration.h"
#include "cotek_msgs/edge_update.h"
#include "cotek_msgs/edit_slam_3d_map.h"
#include "cotek_msgs/instant_action.h"
#include "cotek_msgs/manual_confirm.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/node_update.h"
#include "cotek_msgs/order.h"
#include "cotek_msgs/query_info.h"
#include "cotek_msgs/std_cmd.h"
#include "cotek_msgs/task_cmd.h"
#include "cotek_msgs/task_order.h"
#include "cotek_msgs/task_update.h"
#include "cotek_msgs/storage_update.h"
#include "cotek_msgs/update_basic_config.h"
#include "cotek_msgs/update_config.h"
#include "cotek_msgs/vertex.h"
#include "ros/init.h"
#include "ros/node_handle.h"
#include "ros/service_client.h"
#include "ros/time.h"
#include "std_msgs/String.h"
#include "tf/transform_broadcaster.h"
namespace cotek_communicate {

static void saveAsSvg(const std::vector<std::vector<cv::Point>> &contours,
                      const std::string &filename) {
  std::ofstream svgFile(filename);
  std::string strokeColor = "hsla(240, 100%, 50%, 0.5)";
  std::string width = "1";

  svgFile << "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n";
  for (const auto &contour : contours) {
    svgFile << "<polyline points=\"";
    for (const auto &point : contour) {
      svgFile << point.x << "," << point.y << " ";
    }
    // svgFile << "\" style=\"fill:none;stroke:black;stroke-width:1\" />\n";
    svgFile << "\" style=\"fill:none;stroke:" << strokeColor
            << ";stroke-width:" << width << "\" />\n";
  }

  svgFile << "</svg>";
  svgFile.close();
}

static void CreateHttpHeadRes(PocoHttpServerRequset &request,
                              PocoHttpServerResponse &response) {
  response.set("Access-Control-Allow-Origin", "*");
  response.set("Access-Control-Allow-Methods", "*");
  response.set("Access-Control-Allow-Headers",
               " authorization,Authorization,credential,X-XSRF-TOKEN,x-"
               "requested-with,client,*,Content-Type,token,username");
  response.set("Access-Control-Allow-Credentials", "true");
}

static std::string GetVersion() {
  return DataManager::Instance().GetCommunicateOption().system_version;
}

void TestHandle::handleRequest(PocoHttpServerRequset &request,
                               PocoHttpServerResponse &response) {
  CreateHttpHeadRes(request, response);
}

void GuideGetHandle::handleRequest(PocoHttpServerRequset &request,
                                   PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/guide/get?type=" << type);

    std::string res = "";

    std::string user_dir = Poco::Path::home();
    std::string path = user_dir + "config/map/888888/";
    DIR *dir = opendir(path.c_str());

    if (dir == nullptr) {
      LOG_INFO_STREAM("Dir is empty: " << path);
      res = "yes";
    } else {
      res = "no";
    }

    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.setContentType("text/plain");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, res);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void SyncGetHandle::handleRequest(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    std::string type = format.get("type");
    LOG_INFO_STREAM("/sync/get?type=" << type);

    PocoWebScoket ws(request, response);
    while (ros::ok() && !ws.available()) {
      Json &&json =
          DataManager::Instance().GetResourceAgent()->GetResouresInfo();
      if (!json.empty()) {
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());
        msg.SetMsgJson(json);
        if (msg.PackMsg(GetVersion())) {
          ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                       static_cast<int>(msg.SendBufferSize()),
                       PocoWebScoket::FRAME_TEXT);
        }
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    }
    ws.shutdown();
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void SyncUpdateHandle::handleRequest(PocoHttpServerRequset &request,
                                     PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type = format.get("type");

    LOG_INFO_STREAM("/sync/update?type=" << type);

    Json update;
    update = Json::parse(request.stream());
    LOG_INFO_STREAM("Blz update list: " << update.dump(4));

    std::vector<uint16_t> amrs;
    for (auto &&amr : update.at("sync").at("armLists")) {
      amrs.push_back(amr.at("id").get<int>());
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);

    bool ret = false;
    if (type == "start") {
      ret = DataManager::Instance().GetResourceAgent()->HandleObjsSync(amrs);
    }

    if (type == "stop") {
      ret = DataManager::Instance().GetResourceAgent()->HandleObjsSync(amrs);
    }

    // if (DataManager::Instance().GetResourceAgent()->HandleObjsSync(amrs)) {
    if (ret) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");
    } else {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ReLocationListHandle::handleRequest(PocoHttpServerRequset &request,
                                         PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type = format.get("type");

    LOG_INFO_STREAM("/reLocation/get?type=" << type);

    std::string file_path = BasicConfigHelper::Instance().GetTargetConfigPath(
        cotek_config::ConfigType::RELOCATION_LIST);

    nlohmann::ordered_json json =
        nlohmann::ordered_json::parse(std::ifstream(file_path));

    if (json.empty()) {
      nlohmann::ordered_json null_json = nullptr;
      nlohmann::ordered_json temp_json;
      temp_json["reLocationList"] = null_json;
      json.merge_patch(temp_json);
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "json is empty");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ReLocationUpdateHandle::handleRequest(PocoHttpServerRequset &request,
                                           PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);

  try {
    std::string type = format.get("type");
    LOG_INFO_STREAM("/reLocation/update?type=" << type);

    if (type == "insert") {
      Insert(request, response);
      return;
    }

    if (type == "delete") {
      Delete(request, response);
      return;
    }

    if (type == "update") {
      Update(request, response);
      return;
    }

    if (type == "default") {
      Default(request, response);
      return;
    }

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }

  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
}

void ReLocationUpdateHandle::Insert(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  std::string name;
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
  try {
    Json &&pos_json =
        DataManager::Instance().GetJson(cotek_topic::kAgvPositionTopic);
    if (pos_json.empty() || !pos_json.contains("agvPosition")) {
      throw std::runtime_error(std::string("agv position isn't ok"));
    }

    if (!pos_json.empty() && pos_json.contains("agvPosition")) {
      bool is_pos_init = false;
      bool is_mapping = false;
      double x = 0.0, y = 0.0, z = 0.0;
      if (pos_json["agvPosition"].contains("positionInitialized")) {
        is_pos_init =
            pos_json["agvPosition"]["positionInitialized"].get<uint8_t>();
      }

      if (pos_json["agvPosition"].contains("mapping")) {
        is_mapping = pos_json["agvPosition"]["mapping"].get<uint8_t>();
      }

      if (pos_json["agvPosition"].contains("x") &&
          pos_json["agvPosition"].contains("y")) {
        x = pos_json["agvPosition"]["x"].get<double>();
        y = pos_json["agvPosition"]["y"].get<double>();
      }

      if (!is_pos_init || is_mapping) {
        throw std::runtime_error(std::string("agv position not relocate"));
      }

      if (std::fabs(x - 888888) < 0.001 && std::fabs(y - 888888) < 0.001) {
        throw std::runtime_error(std::string("agv position not relocate"));
      }
    }

    Json insert_relocation_js = Json::parse(request.stream());
    auto insert_json = insert_relocation_js["reLocationList"].at(0);

    relocation_t data;
    data.id = insert_json.at("id").get<std::string>();
    data.name = insert_json.at("name").get<std::string>();
    data.x = insert_json.at("x").get<double>();
    data.y = insert_json.at("y").get<double>();
    data.z = insert_json.at("z").get<double>();
    data.angle = insert_json.at("yaw").get<double>();
    data.map_id = insert_json.at("mapId").get<std::string>();
    data.zone_id = insert_json.at("zoneSetid").get<std::string>();
    name = data.name;

    bool could_insert =
        DataManager::Instance().SetRelocation(data, kRelocationInsert);
    if (!could_insert) {
      response.send() << HttpResponse::CreatHttpResponse(
          kHttpError, "", std::string("create relocation point failed"));
      return;
    }

    DataManager::Instance().RefreshImage(data.id);

    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "", std::string("create relocation point success"));

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(
        kHttpError, "",
        std::string("create relocation point exception: ") + ex.what());
  }
}

void ReLocationUpdateHandle::Delete(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
  try {
    relocation_t data;
    Json delete_relocation_js = Json::parse(request.stream());
    data.id =
        delete_relocation_js["reLocationList"].at(0)["id"].get<std::string>();
    bool could_delete =
        DataManager::Instance().SetRelocation(data, kRelocationDelete);
    if (!could_delete) {
      response.send() << HttpResponse::CreatHttpResponse(
          kHttpError, "", std::string("cant find ") + data.id + " relocation");
      return;
    }

    DataManager::Instance().RefreshImage(data.id);

    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "",
        std::string("delete relocation point ") + data.name + " success");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ReLocationUpdateHandle::Update(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
  try {
    Json update_relocation_js = Json::parse(request.stream());
    auto update_json = update_relocation_js["reLocationList"].at(0);

    relocation_t data;
    data.id = update_json.at("id").get<std::string>();
    data.name = update_json.at("name").get<std::string>();
    data.x = update_json.at("x").get<double>();
    data.y = update_json.at("y").get<double>();
    data.z = update_json.at("z").get<double>();
    data.angle = update_json.at("yaw").get<double>();
    data.map_id = update_json.at("mapId").get<std::string>();
    data.zone_id = update_json.at("zoneSetid").get<std::string>();

    bool could_update =
        DataManager::Instance().SetRelocation(data, kRelocationUpdate);
    if (!could_update) {
      response.send() << HttpResponse::CreatHttpResponse(
          kHttpError, "",
          std::string("can't find relocation point ") + data.name);
      return;
    }

    DataManager::Instance().RefreshImage(data.id);

    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "",
        std::string("update relocation point ") + data.name + " success");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ReLocationUpdateHandle::Default(PocoHttpServerRequset &request,
                                     PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
  try {
    relocation_t data;
    Json default_relocation_js = Json::parse(request.stream());
    data.id =
        default_relocation_js["reLocationList"].at(0)["id"].get<std::string>();
    bool could_default =
        DataManager::Instance().SetRelocation(data, kRelocationDefault);
    if (!could_default) {
      response.send() << HttpResponse::CreatHttpResponse(
          kHttpError, "",
          std::string("cant find relocation point ") + data.name);
      return;
    }

    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "",
        std::string("set default relocation point ") + data.name + " success");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void TeachTaskListHandle::handleRequest(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type, task_id, relocation_id;
    if (format.has("type")) {
      type = format.get("type");
    }
    if (format.has("taskId")) {
      task_id = format.get("taskId");
    }
    if (format.has("relocationId")) {
      relocation_id = format.get("relocationId");
    }
    LOG_INFO_STREAM("/teachTask/get?type=" << type << "&&taskId=" << task_id
                                           << "&&relocationId="
                                           << relocation_id);

    nlohmann::ordered_json json;
    if (type == "image") {
      GetTaskListImage(request, response);
      return;
    } else if (type == "task") {
      json = DataManager::Instance().GetJson("taskDetail", task_id);
    } else if (type == "list") {
      json = DataManager::Instance().GetJson("taskList");
    } else {
      json = DataManager::Instance().GetJson("pathInfo", task_id);
    }

    if (json.empty()) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void TeachTaskListHandle::GetTaskListImage(PocoHttpServerRequset &request,
                                           PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);

    // 1. 解析头部字段
    std::string type, task_id, relocation_id;
    if (format.has("type")) {
      type = format.get("type");
    }
    if (format.has("taskId")) {
      task_id = format.get("taskId");
      LOG_INFO_STREAM("type=" << type << ", taskId=" << task_id);
    }
    if (format.has("relocationId")) {
      relocation_id = format.get("relocationId");
      LOG_INFO_STREAM("type=" << type << ", relocationId=" << relocation_id);
    }

    // 5. 将 cv::Mat 转换为 PNG 格式的内存缓冲区 PNG 格式，9最高压缩
    std::vector<uchar> buffer{0};
    if (!task_id.empty()) {
      buffer = DataManager::Instance().GetImage(task_id, kImagePath);
    }

    if (!relocation_id.empty()) {
      buffer =
          DataManager::Instance().GetImage(relocation_id, kImageRelocation);
    }

    // 6. 设置响应头
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.setContentType("image/png");
    response.setContentLength(buffer.size());

    // 7. 将图像数据写入响应流
    std::ostream &ostr = response.send();
    ostr.write(reinterpret_cast<const char *>(buffer.data()), buffer.size());
  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void TeachTaskStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                          PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    std::string type = format.get("type");

    LOG_INFO_STREAM("/teachTask/stream?type=" << type);

    PocoWebScoket ws(request, response);
    // while (ros::ok() && !ws.available()) {
    while (ros::ok()) {
      Json &&agv_position_json =
          DataManager::Instance().GetJson(cotek_topic::kAgvPositionTopic);
      Json &&learning_state_json =
          DataManager::Instance().GetJson("learningState");

      if (!agv_position_json.empty() && !learning_state_json.empty()) {
        agv_position_json.merge_patch(learning_state_json);
        Json json;
        json["teachTaskStream"] = agv_position_json;
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());
        json.merge_patch(msg.GenerateHeader(GetVersion()));
        auto &&temp = json.dump();
        ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskSendHandle::handleRequest(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);

  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
  try {
    LOG_INFO_STREAM("/teachTask/send");

    nlohmann::ordered_json &&json =
        nlohmann::ordered_json::parse(request.stream());

    if (json.empty()) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(
          kHttpError, "", "json param is invalid");
      return;
    }

    cotek_msgs::task_order order;
    if (json["teachTask"].contains("ids")) {
      if (json["teachTask"]["ids"].is_array()) {
        for (auto &id : json["teachTask"]["ids"]) {
          std::cout << "task id: " << id << std::endl;
          order.request.task_ids.push_back(id);
        }
      }
    }
    std::string id;
    if (json["teachTask"].contains("id")) {
      id = json["teachTask"]["id"].get<std::string>();
    }

    if (json["teachTask"].contains("actions")) {
      if (json["teachTask"]["actions"].is_array()) {
        for (auto &action : json["teachTask"]["actions"]) {
          std::cout << "action: " << action << std::endl;
          order.request.actions.push_back(action);
        }
      }
    }

    int cmd = 0;
    if (json["teachTask"].contains("cmd")) {
      cmd = json["teachTask"]["cmd"].get<int>();
    }
    order.request.task_id = id;
    order.request.cmd = cmd;
    if (DataManager::Instance().RosServiceCall("taskOrder", order)) {
      std::string ret = std::to_string(order.response.code);
      response.send() << HttpResponse::CreatHttpResponse(
          ret, order.response.data, order.response.msg);
    } else {
      LOG_ERROR_STREAM("Call send task failed!!!");
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         order.response.msg);
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void TeachTaskUpdateHandle::handleRequest(PocoHttpServerRequset &request,
                                          PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);

  try {
    std::string type = format.get("type");
    LOG_INFO_STREAM("/teachTask/update?type=" << type);

    if (type == "start") {
      Start(request, response);
      return;
    }

    if (type == "preExtend") {
      PreExtend(request, response);
      return;
    }

    if (type == "extend") {
      Extend(request, response);
      return;
    }

    if (type == "preCover") {
      PreCover(request, response);
      return;
    }

    if (type == "cover") {
      Cover(request, response);
      return;
    }

    if (type == "insert") {
      Insert(request, response);
      return;
    }

    if (type == "delete") {
      Delete(request, response);
      return;
    }

    if (type == "edit") {
      Edit(request, response);
      return;
    }

    if (type == "taskedit" &&
        request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
      TaskEdit(request, response);
      return;
    }

    if (type == "storage" &&
        request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
      StorageEdit(request, response);
      return;
    }

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }

  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
}

void TeachTaskUpdateHandle::NoBlockService(
    const std::string &method, const std::string &task_id, const int &task_type,
    const int &task_cmd, const std::string &action_type,
    const std::string &name, const int speed, const int safety, const int loop,
    const int number, const edgeInfo &edges, const nodeInfo &nodes, const storageInfo& storage) {
  if (has_call_) return;
  auto no_block_call = [&](const std::string &method,
                           const std::string &task_id, const int &task_type,
                           const int &task_cmd, const std::string &action_type,
                           const std::string &name, const int speed,
                           const int safety, const int loop, const int number,
                           const edgeInfo &edges, const nodeInfo &nodes, 
                           const storageInfo& storage) {
    // 发送示教 新建 续建等
    if (method == "task_cmd") {
      cotek_msgs::task_cmd cmd;
      cmd.request.cmd = task_type;
      cmd.request.start = task_cmd;
      cmd.request.task_id = task_id;
      for (const auto &node : storage.vertexs) {
        cotek_msgs::vertex vertex;
        vertex.id = node.id;
        vertex.x = node.x;
        vertex.y = node.y;
        cmd.request.vertexs.push_back(vertex);
      }
      cmd.request.width = storage.size.width;
      cmd.request.length = storage.size.length;
      cmd.request.rowspace = storage.size.rowspace;
      cmd.request.columnspace = storage.size.columnspace;
      cmd.request.direction = storage.direction;
      if (DataManager::Instance().RosServiceCall("taskCmd", cmd)) {
        response_status_ = cmd.response.code == 0 ? ResponseStatus::SUCCESS
                                                  : ResponseStatus::FAILED;
        response_message_ = cmd.response.msg;
      } else {
        LOG_ERROR_STREAM("Call task taskCmd failed!!!");
        response_status_ = ResponseStatus::FAILED;
        response_message_ = cmd.response.msg;
      }
    }

    if (method == "action_cmd") {
      cotek_msgs::action_command cmd;
      if (action_type == "liftLoad") {
        cmd.request.cmd = 1;
      } else if (action_type == "unload") {
        cmd.request.cmd = 2;
      } else if (action_type == "rest") {
        cmd.request.cmd = 3;
      } else if (action_type == "confirm") {
        cmd.request.cmd = 4;
      } else if (action_type == "charge") {
        cmd.request.cmd = 5;
      } else if (action_type == "lieUnLoad") {
        cmd.request.cmd = 6;
      } else if (action_type == "openDoor") {
        cmd.request.cmd = 7;
      } else if (action_type == "closeDoor") {
        cmd.request.cmd = 8;
      } else if (action_type == "callDT") {
        cmd.request.cmd = 9;
      } else if (action_type == "takeDT") {
        cmd.request.cmd = 10;
      } else if (action_type == "leaveDT") {
        cmd.request.cmd = 11;
      } else if (action_type == "switchMap") {
        cmd.request.cmd = 12;
      } else {
      }

      if (DataManager::Instance().RosServiceCall("actionCommand", cmd)) {
        response_status_ = cmd.response.code == 0 ? ResponseStatus::SUCCESS
                                                  : ResponseStatus::FAILED;
        response_message_ = cmd.response.msg;
      } else {
        LOG_ERROR_STREAM("Call task actionCommand failed!!!");
        response_status_ = ResponseStatus::FAILED;
        response_message_ = cmd.response.msg;
      }
    }

    if (method == "task_update") {
      cotek_msgs::task_update update;
      update.request.task_id = task_id;
      update.request.task_name = name;
      update.request.update = task_cmd;
      update.request.avoid_level = safety;
      update.request.velocity_level = speed;
      update.request.loop = loop;
      update.request.sn = number;

      if (DataManager::Instance().RosServiceCall("taskUpdate", update)) {
        response_status_ = update.response.code == 0 ? ResponseStatus::SUCCESS
                                                     : ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      } else {
        LOG_ERROR_STREAM("Call task task_update failed!!!");
        response_status_ = ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      }
    }

    if (method == "edge_update") {
      cotek_msgs::edge_update update;
      for (const auto &id : edges.id) {
        update.request.id.push_back(id);
      }
      for (const auto &speed : edges.speed) {
        update.request.speed.push_back(speed);
      }
      for (const auto &safety : edges.safety) {
        update.request.safety.push_back(safety);
      }

      if (DataManager::Instance().RosServiceCall("edgeUpdate", update)) {
        response_status_ = update.response.code == 0 ? ResponseStatus::SUCCESS
                                                     : ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      } else {
        LOG_ERROR_STREAM("Call task edge_update failed!!!");
        response_status_ = ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      }
    }

    if (method == "node_update") {
      cotek_msgs::node_update update;
      for (const auto &id : nodes.id) {
        update.request.id.push_back(id);
      }
      for (const auto &action_type : nodes.type) {
        int type;
        if (action_type == "liftLoad") {
          type = 1;
        } else if (action_type == "unload") {
          type = 2;
        } else if (action_type == "rest") {
          type = 3;
        } else if (action_type == "confirm") {
          type = 4;
        } else if (action_type == "charge") {
          type = 5;
        } else if (action_type == "lieUnLoad") {
          type = 6;
        } else if (action_type == "openDoor") {
          type = 7;
        } else if (action_type == "closeDoor") {
          type = 8;
        } else if (action_type == "callDT") {
          type = 9;
        } else if (action_type == "takeDT") {
          type = 10;
        } else if (action_type == "leaveDT") {
          type = 11;
        } else if (action_type == "switchMap") {
          type = 12;
        } else {
        }
        update.request.type.push_back(type);
      }

      if (DataManager::Instance().RosServiceCall("nodeUpdate", update)) {
        response_status_ = update.response.code == 0 ? ResponseStatus::SUCCESS
                                                     : ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      } else {
        LOG_ERROR_STREAM("Call task node_update failed!!!");
        response_status_ = ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      }
    }

    if (method == "storage_update") {
      cotek_msgs::storage_update update;
      update.request.task_id = task_id;
      update.request.kuqu_name = storage.name;
      update.request.direction = storage.direction;
      update.request.width = storage.size.width;
      update.request.length = storage.size.length;
      update.request.rowspace = storage.size.rowspace;
      update.request.columnspace = storage.size.columnspace;

      if (DataManager::Instance().RosServiceCall("storageUpdate", update)) {
        response_status_ = update.response.code == 0 ? ResponseStatus::SUCCESS
                                                     : ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      } else {
        LOG_ERROR_STREAM("Call task storage_update failed!!!");
        response_status_ = ResponseStatus::FAILED;
        response_message_ = update.response.msg;
      }
    }
  };

  thread_pool_.enqueue(no_block_call, method, task_id, task_type, task_cmd,
                       action_type, name, speed, safety, loop, number, edges,
                       nodes, storage);
  has_call_ = true;
}

void TeachTaskUpdateHandle::Start(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);

      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      int task_cmd = json["teachTask"]["type"].get<int>();
      storageInfo storage;
      if (json["teachTask"].contains("storage")) {
        auto nodes = json["teachTask"]["storage"]["node"];
        for (const auto& node : nodes) {
          vertex vertex;
          vertex.id = node["id"].get<std::string>();
          vertex.x = node["x"].get<double>();
          vertex.y = node["y"].get<double>();
          storage.vertexs.push_back(vertex);
        }
        
        kuweiSize kwInfo;
        auto size = json["teachTask"]["storage"]["size"];
        for (const auto& data : size) {
          kwInfo.width = data["width"].get<double>();
          kwInfo.length = data["length"].get<double>();
          kwInfo.rowspace = data["rowspace"].get<double>();
          kwInfo.columnspace = data["columnspace"].get<double>();
        }        
        storage.size = kwInfo;

        storage.direction = json["teachTask"]["storage"]["direction"].get<std::string>();

        edgeInfo edges;
        nodeInfo nds;
        NoBlockService("task_cmd", task_id, TaskCmd::START, task_cmd,
                       "", "", 0, 0, 0, 0, edges, nds, storage);
      } else {
        NoBlockService("task_cmd", task_id, TaskCmd::START,
                      task_cmd /*开始保存或取消*/);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        if (task_cmd == 2) {
          DataManager::Instance().RefreshImage();
        }
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::PreExtend(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);
      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      int task_cmd = json["teachTask"]["type"].get<int>();
      NoBlockService("task_cmd", task_id, TaskCmd::PRE_EXTEND,
                     task_cmd /*开始保存或取消*/);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::Extend(PocoHttpServerRequset &request,
                                   PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);

      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      int task_cmd = json["teachTask"]["type"].get<int>();
      NoBlockService("task_cmd", task_id, TaskCmd::EXTEND,
                     task_cmd /*开始保存或取消*/);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        if (task_cmd == 2) {
          DataManager::Instance().RefreshImage();
        }
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::PreCover(PocoHttpServerRequset &request,
                                     PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);
      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      int task_cmd = json["teachTask"]["type"].get<int>();
      NoBlockService("task_cmd", task_id, TaskCmd::PRE_COVER,
                     task_cmd /*开始保存或取消*/);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::Cover(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);
      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      int task_cmd = json["teachTask"]["type"].get<int>();
      NoBlockService("task_cmd", task_id, TaskCmd::COVER,
                     task_cmd /*开始保存或取消*/);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        if (task_cmd == 2) {
          DataManager::Instance().RefreshImage();
        }
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::Insert(PocoHttpServerRequset &request,
                                   PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);
      Json json = Json::parse(receivedData);

      std::string task_id;
      int task_cmd = 0;
      std::string action_type = json["teachTask"]["type"].get<std::string>();
      std::string action_value = "";
      if (json["teachTask"].contains("value")) {
        action_value = json["teachTask"]["value"].get<std::string>();
      }
      NoBlockService("action_cmd", task_id, TaskCmd::NO, task_cmd, action_type);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = action_type;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::Delete(PocoHttpServerRequset &request,
                                   PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);
      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      int task_cmd = 1;  // 0-更新 1-删除
      NoBlockService("task_update", task_id, TaskCmd::NO, task_cmd);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        DataManager::Instance().RefreshImage(task_id);
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::Edit(PocoHttpServerRequset &request,
                                 PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);
      Json json = Json::parse(receivedData);

      std::string task_id = json["teachTask"]["id"].get<std::string>();
      std::string name = json["teachTask"]["name"].get<std::string>();
      int speed = json["teachTask"]["speed"].get<int>();
      int safety = json["teachTask"]["safety"].get<int>();
      int loop = 1;
      if (json["teachTask"].contains("loop")) {
        loop = json["teachTask"]["loop"].get<int>();
      }
      int number = 0;
      if (json["teachTask"].contains("number")) {
        number = std::stoi(json["teachTask"]["number"].get<std::string>());
      }
      int task_cmd = 0;  // 0-更新 1-删除
      std::string action_type;
      NoBlockService("task_update", task_id, TaskCmd::NO, task_cmd, action_type,
                     name, speed, safety, loop, number);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      Json response_json;
      response_json["teachTask"]["id"] = task_id;
      response_json["teachTask"]["type"] = task_cmd;
      response_json["teachTask"]["status"] =
          ResponseStatusToString(response_status_);
      response_json["teachTask"]["message"] = response_message_;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(response_json);

      if (msg.PackMsg(GetVersion())) {
        ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                     static_cast<int>(msg.SendBufferSize()), flags);
      }
      // 当标定任务返回结果时，结束循环
      if (static_cast<uint8_t>(response_status_) !=
          static_cast<uint8_t>(ResponseStatus::NONE)) {
        LOG_INFO_STREAM("return status: " << std::to_string(
                            static_cast<uint8_t>(response_status_)));
        has_call_ = false;
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void TeachTaskUpdateHandle::TaskEdit(PocoHttpServerRequset &request,
                                     PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type, task_id;
    if (format.has("type")) {
      type = format.get("type");
    }
    // if (format.has("taskId")) {
    //   task_id = format.get("taskId");
    // }
    // LOG_INFO_STREAM("/teachTask/update?type=" << type << "&&taskId=" <<
    // task_id);

    LOG_INFO_STREAM("/teachTask/update?type=" << type);
    std::stringstream buffer;
    buffer << request.stream().rdbuf();
    Json json = Json::parse(buffer);
    task_id = json["teachTask"]["id"].get<std::string>();
    LOG_INFO_STREAM("taskId=" << task_id);
    std::cout << buffer.str() << std::endl;

    if (!task_id.empty()) {
      // std::stringstream buffer;
      // buffer << request.stream().rdbuf();
      // Json json = Json::parse(buffer);

      // std::string id = json["teachTask"]["id"].get<std::string>();
      std::string name = json["teachTask"]["name"].get<std::string>();
      int loop = json["teachTask"]["loop"].get<int>();
      int number = std::stoi(json["teachTask"]["number"].get<std::string>());

      int task_cmd = 0;  // 0-更新 1-删除
      int speed = 0;
      int safety = 0;
      std::string action_type;
      NoBlockService("task_update", task_id, TaskCmd::NO, task_cmd, action_type,
                     name, speed, safety, loop, number);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      has_call_ = false;

      edgeInfo edge_info;
      auto edge_list = json["teachTask"]["edgelist"];
      LOG_INFO("update edge size: %d", edge_list.size());
      for (const auto &edge : edge_list) {
        std::string id = edge["id"].get<std::string>();
        int speed = edge["speed"].get<int>();
        int safety = edge["safety"].get<int>();
        edge_info.id.push_back(id);
        edge_info.speed.push_back(speed);
        edge_info.safety.push_back(safety);
        LOG_INFO("update edge(%s), sp: %d, av: %d", id.c_str(), speed, safety);
      }
      if (edge_list.size() > 0) {
        NoBlockService("edge_update", task_id, TaskCmd::NO, task_cmd,
                       action_type, name, speed, safety, loop, number,
                       edge_info);

        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
      has_call_ = false;

      nodeInfo node_info;
      auto node_list = json["teachTask"]["nodelist"];
      for (const auto &node : node_list) {
        std::string id = node["id"].get<std::string>();
        std::string type = node["type"].get<std::string>();
        node_info.id.push_back(id);
        node_info.type.push_back(type);
      }
      if (node_list.size() > 0) {
        NoBlockService("node_update", task_id, TaskCmd::NO, task_cmd,
                       action_type, name, speed, safety, loop, number,
                       edge_info, node_info);

        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
      has_call_ = false;

    } else {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError,
                                                         "no taskId");
      return;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void TeachTaskUpdateHandle::StorageEdit(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type, task_id;
    if (format.has("type")) {
      type = format.get("type");
    }

    LOG_INFO_STREAM("/teachTask/update?type=" << type);
    std::stringstream buffer;
    buffer << request.stream().rdbuf();
    Json json = Json::parse(buffer);
    task_id = json["teachTask"]["id"].get<std::string>();
    LOG_INFO_STREAM("taskId=" << task_id);
    std::cout << buffer.str() << std::endl;

    storageInfo storage;
    if (!task_id.empty()) {
      storage.name = json["teachTask"]["name"].get<std::string>();
      if (json["teachTask"].contains("direction")) {
        storage.direction = json["teachTask"]["direction"].get<std::string>();
      }
      if (json["teachTask"].contains("size")) {
        kuweiSize kwInfo;
        auto size = json["teachTask"]["storage"]["size"];
        for (const auto& data : size) {
          kwInfo.width = data["width"].get<double>();
          kwInfo.length = data["length"].get<double>();
          kwInfo.rowspace = data["rowspace"].get<double>();
          kwInfo.columnspace = data["columnspace"].get<double>();
        }        
        storage.size = kwInfo;
      }

      int task_cmd = 0;  // 0-更新 1-删除
      edgeInfo edges;
      nodeInfo nds;
      NoBlockService("storage_update", task_id, TaskCmd::NO, task_cmd, 
                     "", "", 0, 0, 0, 0, edges, nds, storage);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      has_call_ = false;

    } else {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError,
                                                         "no taskId");
      return;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void TestStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                     PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);

  if (!format.has("topic")) {
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
    return;
  }

  std::string tpoic = format.get("topic");
  PocoWebScoket ws(request, response);

  try {
    while (ros::ok() && !ws.available()) {
      // 获取Base64编码的图像数据
      std::string &&image_data =
          DataManager::Instance().GetImageStreamData(tpoic);
      if (!image_data.empty()) {
        // 发送图像数据到客户端
        ws.sendFrame(image_data.data(), image_data.size(),
                     PocoWebScoket::FRAME_TEXT);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
        // fallthrough
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY: {
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                           exc.what());
      } break;
    }
  }
}

void NullHandle::handleRequest(PocoHttpServerRequset &request,
                               PocoHttpServerResponse &response) {
  CreateHttpHeadRes(request, response);
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                     "path error");
}

void StatusHandle::handleRequest(PocoHttpServerRequset &request,
                                 PocoHttpServerResponse &response) {
  std::string type;
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/status/get?type=" << type);

    Json &&json = DataManager::Instance().GetJson(type);
    if (json.empty()) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(
          kHttpError, "", "get " + type + " data is empty");
      return;
    }
    if (type == "agvPosition") {
      Json &&state = DataManager::Instance().GetJson("state");
      json["agvPosition"]["z"] = state["agvPosition"]["z"];
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "pack message error");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(
        kHttpError, "", "get " + type + " data exception: " + ex.what());
  }
}

void StatusStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                       PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    std::string type = format.get("type");

    LOG_INFO_STREAM("/status/stream?type=" << type);

    PocoWebScoket ws(request, response);
    while (ros::ok() /*&& !ws.available()*/) {
      Json &&position_json =
          DataManager::Instance().GetJson(cotek_topic::kAgvPositionTopic);
      Json &&move_json =
          DataManager::Instance().GetJson(cotek_topic::kMoveFeedbackTopic);
      Json &&battery_json =
          DataManager::Instance().GetJson(cotek_topic::kBatteryStateTopic);
      Json &&odom_json =
          DataManager::Instance().GetJson(cotek_topic::kGlobalOdomInfoTopic);
      Json &&system_json = DataManager::Instance().GetJson("systemInfo");
      Json &&safety_json =
          DataManager::Instance().GetJson(cotek_topic::kSafetyStateTopic);
      Json &&error_json =
          DataManager::Instance().GetJson(cotek_topic::kErrorsTopic);
      Json &&task_json = DataManager::Instance().GetJson("taskListPoint");
      Json &&task_info = DataManager::Instance().GetJson("taskInfo");
      Json &&task_state = DataManager::Instance().GetJson("taskState");
      Json &&learning_state_json =
          DataManager::Instance().GetJson("pathStream");
      Json &&relocaion_json = DataManager::Instance().GetRelocation();

      Json json;
      if (!position_json.empty()) {
        json.merge_patch(position_json);
      }
      if (!move_json.empty()) {
        json.merge_patch(move_json);
      }
      if (!battery_json.empty()) {
        json.merge_patch(battery_json);
      }
      if (!odom_json.empty()) {
        json.merge_patch(odom_json);
      }
      if (!system_json.empty()) {
        json.merge_patch(system_json);
      }
      if (!task_info.empty()) {
        json.merge_patch(task_info);
      }
      if (!safety_json.empty()) {
        json.merge_patch(safety_json);
      }
      if (!error_json.empty()) {
        json.merge_patch(error_json);
      }
      if (!task_json.empty()) {
        json.merge_patch(task_json);
      }
      if (!task_state.empty()) {
        json.merge_patch(task_state);
      }
      json["pathStream"] = learning_state_json;
      json["relocationList"] = relocaion_json;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      json.merge_patch(msg.GenerateHeader(GetVersion()));
      auto &&temp = json.dump();
      ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void SensorHandle::handleRequest(PocoHttpServerRequset &request,
                                 PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type = format.get("type");

    LOG_INFO_STREAM("/sensor/get?type=" << type);

    Json &&json = DataManager::Instance().GetJson(type);
    if (json.empty()) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ControlHandle::handleRequest(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);

  try {
    PocoWebScoket ws(request, response);
    char buffer[1024];
    int flags = 0;
    int n = 0;
    // 接收数据
    do {
      n = ws.receiveFrame(buffer, sizeof(buffer), flags);
      if (n > 0) {
        std::string receivedData(buffer, n);
        Json json = Json::parse(receivedData);

        auto agv_state = DataManager::Instance()
                             .GetJson(cotek_topic::kAgvStateTopic)["agvState"]
                             .get<std::string>();
        // 仅在远程控制模式下才允许控制
        if (agv_state == "RemoteControl") {
          double speed = json["speed"].get<double>();
          double angle = json["angle"].get<double>();
          int height = json["height"].get<int>();

          speed = math::Clamp(speed, -0.5, 0.5);
          angle = math::Clamp(speed, -1.5705, 1.5707);

          cotek_msgs::move_cmd move;
          move.vx = static_cast<float>(speed);
          move.angle = static_cast<float>(angle);
          DataManager::Instance().RosPublish(cotek_topic::kMoveCmdTopic, move);

          cotek_msgs::action_cmd action;
          cotek_msgs::atomic_action ac;
          ac.cmd = static_cast<uint32_t>(action::AtomicActionType::UP_DOWN);
          ac.data = static_cast<float>(height);
          action.cmd.push_back(ac);
          DataManager::Instance().RosPublish(cotek_topic::kActionCmdTopic,
                                             action);
        }
      }
    } while (ros::ok() && !ws.available() && n > 0 &&
             ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
              Poco::Net::WebSocket::FRAME_OP_CLOSE));

    std::cout << "Connection closed." << std::endl;
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
        // fallthrough
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception ex) {
    LOG_ERROR(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  } catch (...) {
  }
}

void GetConfigHandle::GetConfig(PocoHttpServerRequset &request,
                                PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type = format.get("type");
    LOG_INFO_STREAM("/config/get?type=" << type);

    std::string config_str =
        BasicConfigHelper::Instance().GetConfig(GetConfigType(type));

    if (config_str.empty()) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    Json json;
    Json target_json = Json::parse(config_str);
    // 避障地图调度需要json格式化数据 其他配置需要原文件字符串格式
    if (type == cotek_services::kUpdateAvoidAreaConfigService) {
      json[type] = target_json;
    } else {
      json[type] = config_str;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const Poco::NotFoundException &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void GetConfigHandle::Confirm(PocoHttpServerRequset &request,
                              PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type = format.get("type");
    LOG_INFO_STREAM("/config/get?type=" << type);

    int mode = DataManager::Instance().GetCommunicateOption().localizer_mode;
    std::string mode_str;
    if (mode == 1) {
      mode_str = "2dslam";
    } else {
      mode_str = "3dslam";
    }

    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.setContentType("text/plain");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, mode_str);

  } catch (const Poco::NotFoundException &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

cotek_config::ConfigType GetConfigHandle::GetConfigType(
    const std::string &type) {
  if (type == "navigationConfig") {
    return cotek_config::ConfigType::NAVIGATION_CONFIG;
  }

  if (type == "actionConfig") {
    return cotek_config::ConfigType::ACTION_CONFIG;
  }

  if (type == "basicConfig") {
    return cotek_config::ConfigType::AGV_BASIC_CONFIG;
  }

  if (type == "avoidAreaConfig") {
    return cotek_config::ConfigType::AVOID_AREA;
  }

  if (type == "avoidConfig") {
    return cotek_config::ConfigType::AVOID_CONFIG;
  }

  if (type == "runnerConfig") {
    return cotek_config::ConfigType::RUNNER_CONFIG;
  }

  if (type == "locaizerConfig") {
    return cotek_config::ConfigType::LOCALIZER_CONFIG;
  }

  if (type == "storageConfig") {
    return cotek_config::ConfigType::STORAGE_CONFIG;
  }

  if (type == "logicConfig") {
    return cotek_config::ConfigType::LOGIC_CONFIG;
  }

  if (type == "visualConfig") {
    return cotek_config::ConfigType::VISUAL_CONFIG;
  }

  if (type == "slamConfig") {
    return cotek_config::ConfigType::SLAM_CONFIG;
  }

  if (type == "voiceConfig") {
    return cotek_config::ConfigType::VOICE_CONFIG;
  }

  if (type == "slam3dConfig") {
    return cotek_config::ConfigType::SLAM_3D_CONFIG;
  }

  LOG_ERROR_STREAM("Get " << type << " config failed!!!");

  return cotek_config::ConfigType::NONE;
}

void GetConfigHandle::handleRequest(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);

  try {
    std::string type = format.get("type");

    if (type == "confirm") {
      Confirm(request, response);
      return;
    } else {
      GetConfig(request, response);
      return;
    }

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }

  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
}

void UpdateConfigHandle::handleRequest(PocoHttpServerRequset &request,
                                       PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  try {
    std::string type = format.get("type");
    LOG_INFO_STREAM("/config/update?type=" << type);

    cotek_msgs::update_config update_config;

    std::stringstream buffer;
    buffer << request.stream().rdbuf();

    Json json = Json::parse(buffer);
    update_config.request.type = type;
    update_config.request.data = json[type].dump(4);

    if (DataManager::Instance().RosServiceCall(type, update_config)) {
      std::string ret =
          update_config.response.status == 1 ? kHttpOk : kHttpError;
      response.send() << HttpResponse::CreatHttpResponse(ret, "");
    } else {
      LOG_ERROR_STREAM("Call  " << type << "failed!!!");
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
    }

    // 更新整车基础配置
    if (update_config.response.status == 1 &&
        (type == cotek_services::kUpdateRunnerConfigService)) {
      cotek_msgs::update_basic_config msg;
      DataManager::Instance().RosPublish(cotek_topic::kUpdateBasicConfigTopic,
                                         msg);
    }

  } catch (const Poco::NotFoundException &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void MapFileHandle::SendMapImage(PocoHttpServerRequset &request,
                                 PocoHttpServerResponse &response) {
  try {
    CreateHttpHeadRes(request, response);

    // 1. 解析url
    PocoHtmlForm format(request);
    std::string zone_id, map_id;
    if (format.has("zoneSetid")) {
      zone_id = format.get("zoneSetid");
    }
    if (format.has("mapId")) {
      map_id = format.get("mapId");
    }

    LOG_INFO_STREAM("/map/get?type=image&&zoneSetid=" << zone_id
                                                      << "&&mapId=" << map_id);

    // 2. 读取地图图片
    std::string user_dir = Poco::Path::home();
    std::string map_png =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.pgm";

    cv::Mat gray_image = cv::imread(map_png, cv::IMREAD_GRAYSCALE);
    if (gray_image.empty()) {
      throw std::runtime_error(std::string("read map image exception"));
    }

    // 3. 将图像转换为 BGRA 以便有一个 alpha 通道
    cv::Mat bgra_image;
    cv::cvtColor(gray_image, bgra_image, cv::COLOR_GRAY2BGRA);

    // 4. 遍历图像的每个像素并更新 alpha 通道
    uchar kGrayValue = 128;
    for (int y = 0; y < bgra_image.rows; ++y) {
      for (int x = 0; x < bgra_image.cols; ++x) {
        cv::Vec4b &pixel = bgra_image.at<cv::Vec4b>(y, x);
        if (pixel[0] == kGrayValue) {
          pixel[3] = 0;  // 设置 alpha 通道为 0（完全透明）
        }
      }
    }

    // 5. 将 cv::Mat 转换为 PNG 格式的内存缓冲区 PNG 格式，9最高压缩
    std::vector<uchar> buffer;
    std::vector<int> params = {cv::IMWRITE_PNG_COMPRESSION, 3};
    cv::imencode(".png", bgra_image, buffer, params);

    // 6. 设置响应头
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.setContentType("image/png");
    response.setContentLength(buffer.size());

    // 7. 将图像数据写入响应流
    std::ostream &ostr = response.send();
    ostr.write(reinterpret_cast<const char *>(buffer.data()), buffer.size());
  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

void MapFileHandle::SendMapSvg(PocoHttpServerRequset &request,
                               PocoHttpServerResponse &response) {
  try {
    CreateHttpHeadRes(request, response);

    // 1. 解析url
    PocoHtmlForm format(request);
    std::string zone_id, map_id;
    if (format.has("zoneSetid")) {
      zone_id = format.get("zoneSetid");
    }
    if (format.has("mapId")) {
      map_id = format.get("mapId");
    }

    LOG_INFO_STREAM("/map/get?type=svg&&zoneSetid=" << zone_id
                                                    << "&&mapId=" << map_id);

    std::string user_dir = Poco::Path::home();
    std::string map_pgm =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.pgm";

    cv::Mat image = cv::imread(map_pgm, cv::IMREAD_GRAYSCALE);
    if (image.empty()) {
      LOG_ERROR_STREAM("Could not open the file: " << map_pgm);
      return;
    }

    cv::Mat edges;
    cv::Canny(image, edges, 100, 200, 3, true);

    std::vector<std::vector<cv::Point>> contours;
    cv::findContours(edges, contours, cv::RETR_EXTERNAL,
                     cv::CHAIN_APPROX_SIMPLE);

    std::string output_svg =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.svg";

    saveAsSvg(contours, output_svg);
    LOG_INFO_STREAM("Save svg file to " << output_svg << " succeed.");

    // 读取SVG文件内容
    std::ifstream file(output_svg, std::ios::in | std::ios::binary);
    if (!file) {
      LOG_ERROR_STREAM("Could not open the SVG file: " << output_svg);
      return;
    }

    // 获取文件大小
    file.seekg(0, std::ios::end);
    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);

    // 分配缓冲区并读取文件内容
    std::vector<char> buffer(size);
    if (file.read(buffer.data(), size)) {
      // std::cerr << "Succeed reading the SVG file: " << output_svg <<
      // std::endl;
    } else {
      LOG_ERROR_STREAM("Could not read the SVG file: " << output_svg);
    }
    file.close();

    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.setContentType("image/svg+xml");
    response.setContentLength(buffer.size());

    std::ostream &ostr = response.send();
    ostr.write(buffer.data(), size);

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

static bool gzipCompress(const std::vector<char>& input, std::vector<char>& output) {
    z_stream zs{};
    if (deflateInit2(&zs, Z_BEST_COMPRESSION, Z_DEFLATED, 15 + 16, 8, Z_DEFAULT_STRATEGY) != Z_OK)
        return false;

    zs.next_in = reinterpret_cast<Bytef*>(const_cast<char*>(input.data()));
    zs.avail_in = input.size();

    const int bufferSize = 32768;
    char tmp[bufferSize];

    do {
        zs.next_out = reinterpret_cast<Bytef*>(tmp);
        zs.avail_out = bufferSize;

        int ret = deflate(&zs, Z_FINISH);
        if (ret != Z_STREAM_END && ret != Z_OK && ret != Z_BUF_ERROR) {
            deflateEnd(&zs);
            return false;
        }

        output.insert(output.end(), tmp, tmp + (bufferSize - zs.avail_out));
    } while (zs.avail_out == 0);

    deflateEnd(&zs);
    return true;
}

void MapFileHandle::Send3dMapSvg(PocoHttpServerRequset &request,
                                 PocoHttpServerResponse &response) {
  try {
    CreateHttpHeadRes(request, response);

    // 1. 解析url
    PocoHtmlForm format(request);
    std::string zone_id, map_id;
    if (format.has("zoneSetid")) {
      zone_id = format.get("zoneSetid");
    }
    if (format.has("mapId")) {
      map_id = format.get("mapId");
    }

    LOG_INFO_STREAM("/map/get?type=3dsvg&&zoneSetid=" << zone_id
                                                      << "&&mapId=" << map_id);

    std::string user_dir = Poco::Path::home();
    std::string map_svg =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.svg";

    // 读取SVG文件内容
    std::ifstream file(map_svg, std::ios::in | std::ios::binary);
    if (!file) {
      LOG_ERROR_STREAM("Could not open the SVG file: " << map_svg);
      return;
    }

    // 获取文件大小
    file.seekg(0, std::ios::end);
    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);

    // 分配缓冲区并读取文件内容
    std::vector<char> buffer(size);
    if (file.read(buffer.data(), size)) {
      // std::cerr << "Succeed reading the SVG file: " << map_svg << std::endl;
    } else {
      LOG_ERROR_STREAM("Could not read the SVG file: " << map_svg);
    }
    file.close();

    // 压缩数据
    std::vector<char> compressed;
    if (!gzipCompress(buffer, compressed)) {
        LOG_ERROR_STREAM("Failed to compress SVG file");
        return;
    }

    // 设置 HTTP 响应头并发送数据
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.setContentType("image/svg+xml");
    response.set("Content-Encoding", "gzip");  // 告知客户端这是 gzip 压缩的内容
    response.setContentLength(compressed.size());

    std::ostream &ostr = response.send();
    ostr.write(compressed.data(), compressed.size());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

void MapFileHandle::SendMapInfo(PocoHttpServerRequset &request,
                                PocoHttpServerResponse &response) {
  try {
    CreateHttpHeadRes(request, response);

    // 1. 解析url
    PocoHtmlForm format(request);
    std::string zone_id, map_id;
    if (format.has("zoneSetid")) {
      zone_id = format.get("zoneSetid");
    }
    if (format.has("mapId")) {
      map_id = format.get("mapId");
    }

    LOG_INFO_STREAM(
        "/map/get?type=mapinfo&&zoneSetid=" << zone_id << "&&mapId=" << map_id);

    // if (DataManager::Instance().GetMapId() != map_id ||
    //     DataManager::Instance().GetZoneId() != zone_id) {
    //   LOG_ERROR("map id not fit.");
    //   return;
    // }

    std::string user_dir = Poco::Path::home();
    std::string map_info =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.yaml";
    std::string map_pgm =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.pgm";

    cv::Mat image = cv::imread(map_pgm, cv::IMREAD_GRAYSCALE);
    if (image.empty()) {
      LOG_ERROR_STREAM("Could not open the file: " << map_pgm);
      return;
    }

    int width = image.cols;
    int height = image.rows;

    std::ifstream yaml_file(map_info);
    if (!yaml_file) {
      LOG_ERROR_STREAM("open yaml file: " << map_info << " failed!");
      return;
    }
    std::stringstream buffer;
    buffer << yaml_file.rdbuf();
    std::string yaml_map_string(buffer.str());
    yaml_file.close();

    double resolution, orign_x, orign_y;

    std::smatch match;
    std::regex key1(R"(resolution:\s*([0-9.]+))");

    if (std::regex_search(yaml_map_string, match, key1)) {
      resolution = std::stod(match[1]);
      LOG_INFO("resolution: %f", resolution);
    } else {
      LOG_ERROR_STREAM("resolution not found!");
      return;
    }

    std::regex key2(R"(origin:\s*\[([-0-9.]+),\s*([-0-9.]+))");

    if (std::regex_search(yaml_map_string, match, key2)) {
      orign_x = std::stod(match[1]);
      orign_y = std::stod(match[2]);
      LOG_INFO("Extracted origin values: (%f, %f)", orign_x, orign_y);
    } else {
      LOG_ERROR_STREAM("origin not found!");
      return;
    }

    Json info_json;
    info_json["width"] = width;
    info_json["height"] = height;
    info_json["resolution"] = resolution;
    info_json["orign_x"] = orign_x;
    info_json["orign_y"] = orign_y;

    // std::string topic = "mapWeb";
    // // 获取Base64编码的图像数据
    // std::string &&image_data =
    //     DataManager::Instance().GetImageStreamData(topic);

    // ImageInfo &&info = DataManager::Instance().GetImageInfo(topic);
    // Json info_json;
    // info_json["width"] = info.width;
    // info_json["height"] = info.height;
    // info_json["resolution"] = info.resolution;
    // info_json["orign_x"] = info.orign_x;
    // info_json["orign_y"] = info.orign_y;

    Json json;
    json["info"] = info_json;

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

void MapFileHandle::Send3dMapInfo(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  try {
    CreateHttpHeadRes(request, response);

    // 1. 解析url
    PocoHtmlForm format(request);
    std::string zone_id, map_id;
    if (format.has("zoneSetid")) {
      zone_id = format.get("zoneSetid");
    }
    if (format.has("mapId")) {
      map_id = format.get("mapId");
    }

    LOG_INFO_STREAM("/map/get?type=3dmapinfo&&zoneSetid="
                    << zone_id << "&&mapId=" << map_id);

    // std::string user_dir = Poco::Path::home();
    // std::string map_info =
    //     user_dir + "config/map/" + zone_id + "/" + map_id + "/info.json";

    // std::ifstream file(map_info);
    // if (!file.is_open()) {
    //   LOG_ERROR_STREAM("Could not open the map-info file: " << map_info);
    //   return;
    // }
    // // 读取文件内容到字符串
    // std::stringstream buffer;
    // buffer << file.rdbuf();          // 将文件内容读取到 stringstream
    // std::string str = buffer.str();  // 转换为 std::string
    // file.close();
    // LOG_INFO_STREAM(str);

    // Json data = Json::parse(str);
    // auto &&j_info = data["info"];
    // int width = j_info["width"];
    // int height = j_info["height"];
    // double resolution = j_info["resolution"];
    // int orign_x = j_info["orign_x"];
    // int orign_y = j_info["orign_y"];

    std::string user_dir = Poco::Path::home();
    std::string map_info =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.yaml";
    std::string map_pgm =
        user_dir + "config/map/" + zone_id + "/" + map_id + "/cotek.pgm";

    cv::Mat image = cv::imread(map_pgm, cv::IMREAD_GRAYSCALE);
    if (image.empty()) {
      LOG_ERROR_STREAM("Could not open the file: " << map_pgm);
      return;
    }

    int width = image.cols;
    int height = image.rows;

    std::ifstream yaml_file(map_info);
    if (!yaml_file) {
      LOG_ERROR_STREAM("open yaml file: " << map_info << " failed!");
      return;
    }
    std::stringstream buffer;
    buffer << yaml_file.rdbuf();
    std::string yaml_map_string(buffer.str());
    yaml_file.close();

    double resolution, orign_x, orign_y;

    std::smatch match;
    std::regex key1(R"(resolution:\s*([0-9.]+))");

    if (std::regex_search(yaml_map_string, match, key1)) {
      resolution = std::stod(match[1]);
      LOG_INFO("resolution: %f", resolution);
    } else {
      LOG_ERROR_STREAM("resolution not found!");
      return;
    }

    std::regex key2(R"(origin:\s*\[([-0-9.]+),\s*([-0-9.]+))");

    if (std::regex_search(yaml_map_string, match, key2)) {
      orign_x = std::stod(match[1]);
      orign_y = std::stod(match[2]);
      LOG_INFO("Extracted origin values: (%f, %f)", orign_x, orign_y);
    } else {
      LOG_ERROR_STREAM("origin not found!");
      return;
    }

    LOG_INFO("width: %d", width);
    LOG_INFO("height: %d", height);
    LOG_INFO("resolution: %f", resolution);
    LOG_INFO("ox: %f", orign_x);
    LOG_INFO("oy: %f", orign_y);

    Json info;
    info["width"] = width;
    info["height"] = height;
    info["resolution"] = resolution;
    info["orign_x"] = orign_x;
    info["orign_y"] = orign_y;

    Json json;
    json["info"] = info;

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

void MapFileHandle::SendMapFile(PocoHttpServerRequset &request,
                                PocoHttpServerResponse &response) {
  CreateHttpHeadRes(request, response);
  try {
    PocoHtmlForm format(request);
    std::string zone_id = format.get("zoneSetid");
    std::string map_id = format.get("mapId");

    std::string user_dir = Poco::Path::home();
    std::string target_dir =
        user_dir + "config/map/" + zone_id + "/" + map_id;  // 指定文件夹的路径

    Poco::File directory(target_dir);
    if (directory.exists() && directory.isDirectory()) {
      Poco::DirectoryIterator it(directory);
      Poco::DirectoryIterator end;
      std::string temp_file = user_dir + "test.zip";
      std::ofstream out(temp_file, std::ios::binary);
      Poco::Zip::Compress c(out, false);
      std::string zip_name = zone_id + "_" + map_id + ".zip";

      response.set(
          "Content-Disposition",
          "attachment; filename=\"" + zip_name + "\"");  // 设置响应头部

      for (; it != end; ++it) {
        if (it->isFile()) {
          std::string filePath = it->path();

          c.addFile(filePath, it.name());
        }
      }
      c.close();

      response.sendFile(temp_file, "application/zip");
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

void MapFileHandle::SendMapList(PocoHttpServerRequset &request,
                                PocoHttpServerResponse &response) {
  CreateHttpHeadRes(request, response);
  try {
    response.setContentType("application/json");
    std::string user_dir = Poco::Path::home();
    std::string target_dir = user_dir + "config/map/";  // 指定文件夹的路径

    Poco::File directory(target_dir);
    if (directory.exists() && directory.isDirectory()) {
      Json json, zones;

      std::vector<std::string> zone_dirs;
      directory.list(zone_dirs);

      for (auto &zone_dir : zone_dirs) {
        if (zone_dir == "Scans") continue;
        Json zone;
        std::string zone_dir_path = target_dir + zone_dir;
        Poco::File zone_directory(zone_dir_path);
        if (zone_directory.isDirectory()) {
          // 忽略0号地图
          if (zone_dir == "0") continue;
          zone["zoneSetid"] = zone_dir;

          Json maps = std::vector<std::string>();
          Json names = std::vector<std::string>();
          Json urls = std::vector<std::string>();

          std::vector<std::string> map_ids;
          zone_directory.list(map_ids);
          try {
            std::sort(map_ids.begin(), map_ids.end(),
                      [](const std::string &s1, const std::string &s2) {
                        return std::stoi(s1) < std::stoi(s2);
                      });
          } catch (...) {
          }

          for (auto &map_id : map_ids) {
            maps.push_back(map_id);
            // name by index.json
            std::string map_dir(zone_dir_path + std::string("/") + map_id);
            std::string map_index = map_dir + std::string("/index.json");
            std::ifstream filename(map_index, std::ios::binary);
            if (!filename) {
              throw std::runtime_error("Failed to open file: " + map_index);
            }
            std::string name = Json::parse(filename)["name"].get<std::string>();
            if (!name.empty()) names.push_back(name);

#if 0
            //pgm->png
            std::string map_pgm = map_dir + std::string("/cotek.pgm");
            std::string map_png = map_dir + std::string("/cotek.png");
            Poco::File file(map_png);
            if (!file.exists()) {
              try {
                cv::Mat image = cv::imread(map_pgm, cv::IMREAD_GRAYSCALE);
                if (image.empty()) {
                  throw std::runtime_error("Failed to load PGM file: " + map_pgm);
                }
                bool success = cv::imwrite(map_png, image);
                if (!success) {
                  throw std::runtime_error("Failed to save PNG file: " + map_png);
                }
              } catch (const std::exception& ex) {
                throw std::runtime_error(std::string("convert PGM to PNG file exception: ") + ex.what());
              }
            }
            std::string map_url= "/map/" + zone_dir + "/" + map_id +  "/cotek.png";
            urls.push_back(map_url);
#endif
          }
          zone["mapId"] = maps;
          zone["name"] = names;
          zone["imageUrl"] = urls;
          zones.push_back(zone);
        }
      }

      if (zones.empty()) {
        response.send() << HttpResponse::CreatHttpResponse(kHttpError,
                                                           "no exists map!!!");
        return;
      }

      json["mapList"] = zones;

      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      msg.SetMsgJson(json);
      if (!msg.PackMsg(GetVersion())) {
        response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
        return;
      }
      response.setContentType("application/json");

      response.send() << HttpResponse::CreatHttpResponse(kHttpOk,
                                                         msg.MsgData());
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void MapFileHandle::handleRequest(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);

  try {
    std::string type = format.get("type");

    if (type == "target") {
      SendMapFile(request, response);
      return;
    }

    if (type == "list") {
      SendMapList(request, response);
      return;
    }

    if (type == "image" || type == "3dimage") {
      SendMapImage(request, response);
      return;
    }

    if (type == "svg") {
      SendMapSvg(request, response);
      return;
    }

    if (type == "3dsvg") {
      Send3dMapSvg(request, response);
      return;
    }

    if (type == "mapinfo") {
      SendMapInfo(request, response);
      return;
    }

    if (type == "3dmapinfo") {
      Send3dMapInfo(request, response);
      return;
    }

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }

  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
}

void UpdateMapFileHandle::UpdateMapFile(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  std::string zone_id, map_id;
  try {
    PocoHtmlForm format(request);
    zone_id = format.get("zoneSetid");
    map_id = format.get("mapId");
    // 指定文件夹的路径
    std::string target_dir =
        Poco::Path::home() + "config/map/" + zone_id + "/" + map_id;

    // 创建 Poco::Random 对象
    Poco::Random random;
    std::string filename = Poco::Path::temp() +
                           std::to_string(random.next(1000)) + "+" + zone_id +
                           "_" + map_id + "back.zip";
    std::ofstream myfile(filename, std::ios::binary);
    Poco::StreamCopier::copyStream(request.stream(), myfile);
    std::ifstream file(filename, std::ios::binary);

    Poco::Zip::Decompress decompress(file, target_dir);
    auto zip = decompress.decompressAllFiles();
    cotek_msgs::edit_slam_3d_map edit_msg;
    edit_msg.request.map_id = std::stoi(zone_id);
    edit_msg.request.section_id = std::stoi(map_id);
    DataManager::Instance().RosServiceCall(
        cotek_services::kEditSlam3DMapService, edit_msg);
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "",
        std::string("get map(") + zone_id + ":" + map_id + ") success");
  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    LOG_ERROR_STREAM(ex.message());
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpError, "",
        std::string("get map(") + zone_id + ":" + map_id + ") exception, " +
            ex.message());
  }
}

void UpdateMapFileHandle::DeleteMapFile(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  std::string zone_id, map_id;
  try {
    PocoHtmlForm format(request);
    zone_id = format.get("zoneSetid");
    map_id = format.get("mapId");
    // 指定文件夹的路径
    std::string target_dir =
        Poco::Path::home() + "config/map/" + zone_id + "/" + map_id;
    Poco::File dir(target_dir);
    if (dir.exists() && dir.isDirectory()) {
      dir.remove(true);
    }

    DeleteRelocation(map_id);
    DeleteFloor(map_id);
    DeleteTask(map_id);

    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "",
        std::string("delete map(") + zone_id + ":" + map_id + ") success");

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    LOG_ERROR_STREAM(ex.message());
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpError, "",
        std::string("delete map(") + zone_id + ":" + map_id + ") exception, " +
            ex.message());
  }
}

void UpdateMapFileHandle::DeleteRelocation(const std::string &map_id) {
  std::vector<relocation_t> datas;
  if (DataManager::Instance().GetRelocation(map_id, datas)) {
    for (auto &data : datas) {
      DataManager::Instance().SetRelocation(data, kRelocationDelete);
      DataManager::Instance().RefreshImage(data.id);
    }
  }
}

void UpdateMapFileHandle::DeleteFloor(const std::string &map_id) {
  std::vector<Floor> datas;
  if (DataManager::Instance().GetFloor(map_id, datas)) {
    for (auto &data : datas) {
      DataManager::Instance().SetFloor(data, kRelocationDelete);
    }
  }
}

void UpdateMapFileHandle::DeleteTask(const std::string &map_id) {
  std::vector<task_t> task_list = DataManager::Instance().GetTasks(map_id);
  for (auto &task : task_list) {
    try {
      cotek_msgs::task_update update;
      update.request.task_id = task.id;
      update.request.task_name = task.name;
      update.request.update = 1;  // delete
      update.request.avoid_level = task.avoid_level;
      update.request.velocity_level = task.velocity_level;
      update.request.loop = task.loop;

      if (!DataManager::Instance().RosServiceCall("taskUpdate", update)) {
        LOG_ERROR_STREAM("delete task(" << task.id << ") failed!!!");
      } else {
        DataManager::Instance().RefreshImage(task.id);
      }
    } catch (const std::exception &e) {
      LOG_ERROR_STREAM("delete task(" << task.id << ") exception!!!");
    }
  }
}

void UpdateMapFileHandle::handleRequest(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  CreateHttpHeadRes(request, response);
  try {
    PocoHtmlForm format(request);
    std::string type = format.get("type");

    if (type == "target") {
      UpdateMapFile(request, response);
      return;
    }

    if (type == "delete") {
      DeleteMapFile(request, response);
      return;
    }
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    LOG_ERROR_STREAM(ex.code());
    LOG_ERROR_STREAM(ex.message());
    LOG_ERROR_STREAM(ex.name());
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.message());
  }
}

void EditMapFileHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  LOG_INFO("Receive edit map!!!");
  CreateHttpHeadRes(request, response);
  std::string zone_id, map_id;

  try {
    Json edit_map;
    edit_map = Json::parse(request.stream());

    zone_id = edit_map["mapEdit"]["zoneSetid"].get<std::string>();
    map_id = edit_map["mapEdit"]["mapId"].get<std::string>();
    std::string name = edit_map["mapEdit"]["name"].get<std::string>();

    std::string filename = Poco::Path::home() + "config/map/" + zone_id + "/" +
                           map_id + "/index.json";

    std::ifstream file(filename, std::ios::binary);
    if (!file) {
      throw std::runtime_error("Failed to open file");
    }

    if (zone_id == DataManager::Instance().GetZoneId() &&
        map_id == DataManager::Instance().GetMapId()) {
      DataManager::Instance().setMapName(name);
    }

    Json json = Json::parse(file);
    json["name"] = name;

    std::ofstream file_path(filename);
    file_path << json;

    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpOk, "",
        std::string("edit map(") + zone_id + ":" + map_id + ") success");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(
        kHttpError, "",
        std::string("edit map(") + zone_id + ":" + map_id + ") exception, " +
            ex.what());
  }
}

void UpdateStorageMapHandle::handleRequest(PocoHttpServerRequset &request,
                                           PocoHttpServerResponse &response) {
  LOG_INFO("Receive update storage map!!!");
  CreateHttpHeadRes(request, response);

  try {
    std::string ret = HttpGetString(
        DataManager::Instance().GetCommunicateOption().server_ip,
        DataManager::Instance().GetCommunicateOption().server_port,
        "/warehouse/thirdParty/allLaser");

    cotek_msgs::update_config update_config;

    std::string type = cotek_services::kUpdateStorageMapService;

    update_config.request.type = type;
    update_config.request.data = ret;

    if (DataManager::Instance().RosServiceCall(type, update_config)) {
      std::string ret =
          update_config.response.status == 1 ? kHttpOk : kHttpError;
      response.send() << HttpResponse::CreatHttpResponse(ret, "");
    } else {
      LOG_ERROR_STREAM("Call  " << type << "failed!!!");
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
    }

  } catch (const Poco::NotFoundException &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void OrderHandle::handleRequest(PocoHttpServerRequset &request,
                                PocoHttpServerResponse &response) {
  LOG_INFO("Receive order");
  CreateHttpHeadRes(request, response);
  try {
    cotek_msgs::order order_msg;
    Json order;
    order = Json::parse(request.stream());
    LOG_INFO_STREAM("Order: " << order.dump(4));

    order_msg.order_id = order["orderId"].get<std::string>();
    order_msg.task_id = order["taskId"].get<std::string>();
    order_msg.order_update_id = order.at("orderUpdateId").get<int>();
    order_msg.zone_set_id = order.at("zoneSetId").get<std::string>();

    order_msg.end_point.node_id =
        order.at("endnode").at("nodeId").get<std::string>();
    order_msg.end_point.node_description =
        order.at("endnode").at("nodeDescription").get<std::string>();
    order_msg.end_point.node_position.x =
        order.at("endnode").at("nodePosition").at("x").get<double>();
    order_msg.end_point.node_position.y =
        order.at("endnode").at("nodePosition").at("y").get<double>();
    order_msg.end_point.node_position.theta =
        order.at("endnode").at("nodePosition").at("theta").get<double>();
    order_msg.end_point.node_position.allowed_deviation_xy =
        order.at("endnode")
            .at("nodePosition")
            .at("allowedDeviationXY")
            .get<double>();
    order_msg.end_point.node_position.allowed_deviation_theta =
        order.at("endnode")
            .at("nodePosition")
            .at("allowedDeviationTheta")
            .get<double>();
    order_msg.end_point.node_position.map_id =
        order.at("endnode").at("nodePosition").at("mapId").get<std::string>();
    order_msg.end_point.node_position.map_description =
        order.at("endnode")
            .at("nodePosition")
            .at("mapDescription")
            .get<std::string>();
    for (auto &action : order.at("endnode").at("actions")) {
      cotek_msgs::action action_msg;
      action_msg.action_type = action.at("actionType").get<std::string>();
      action_msg.action_id = action.at("actionId").get<std::string>();
      action_msg.action_description =
          action.at("actionDescription").get<std::string>();
      action_msg.blocking_type = action.at("blockingType").get<std::string>();
      // end_point暂时不加自定义条件
      for (auto &action_parameter : action.at("actionParameters")) {
        if (action_parameter.contains("key") &&
            action_parameter.at("key").get<std::string>() ==
                std::string("target")) {
          for (auto &value : action_parameter.at("value")) {
            action_msg.action_value_list.push_back(value.get<std::string>());
          }
        }
      }
      order_msg.end_point.actions.push_back(action_msg);
    }

    for (auto &node : order.at("nodes")) {
      cotek_msgs::node node_msg;
      node_msg.node_id = node.at("nodeId").get<std::string>();
      node_msg.sequence_id = node.at("sequenceId").get<int>();
      node_msg.node_description = node.at("nodeDescription").get<std::string>();
      node_msg.released = node.at("released").get<bool>();
      node_msg.node_position.x = node.at("nodePosition").at("x").get<double>();
      node_msg.node_position.y = node.at("nodePosition").at("y").get<double>();
      node_msg.node_position.theta =
          node.at("nodePosition").at("theta").get<double>();
      node_msg.node_position.allowed_deviation_xy =
          node.at("nodePosition").at("allowedDeviationXY").get<double>();
      node_msg.node_position.allowed_deviation_theta =
          node.at("nodePosition").at("allowedDeviationTheta").get<double>();
      node_msg.node_position.map_id =
          node.at("nodePosition").at("mapId").get<std::string>();
      node_msg.node_position.map_description =
          node.at("nodePosition").at("mapDescription").get<std::string>();

      for (auto &action : node.at("actions")) {
        cotek_msgs::action action_msg;
        action_msg.action_type = action.at("actionType").get<std::string>();
        action_msg.action_id = action.at("actionId").get<std::string>();
        action_msg.action_description =
            action.at("actionDescription").get<std::string>();
        action_msg.blocking_type = action.at("blockingType").get<std::string>();
        for (auto &action_parameter : action.at("actionParameters")) {
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("target")) {
            for (auto &value : action_parameter.at("value")) {
              action_msg.action_value_list.push_back(value.get<std::string>());
            }
          }
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("mixConditions")) {
            auto &mix_conditions = action_parameter.at("value");

            action_msg.mix_conditions = mix_conditions.dump(4);
          }
        }

        node_msg.actions.push_back(action_msg);
      }

      order_msg.nodes.push_back(node_msg);
    }

    for (auto &edge : order.at("edges")) {
      cotek_msgs::edge edge_msg;
      edge_msg.edge_id = edge.at("edgeId").get<std::string>();
      edge_msg.sequence_id = edge.at("sequenceId").get<int>();
      edge_msg.edge_descripition =
          edge.at("edgeDescription").get<std::string>();
      edge_msg.released = edge.at("released").get<bool>();
      edge_msg.start_node_id = edge.at("startNodeId").get<std::string>();
      edge_msg.end_node_id = edge.at("endNodeId").get<std::string>();
      edge_msg.max_speed = edge.at("maxSpeed").get<double>();
      edge_msg.max_height = edge.at("maxHeight").get<double>();
      edge_msg.min_height = edge.at("minHeight").get<double>();
      edge_msg.orientation = edge.at("orientation").get<double>();
      edge_msg.orientation_type = edge.at("orientationType").get<std::string>();
      edge_msg.direction = edge.at("direction").get<std::string>();
      edge_msg.rotation_allowed = edge.at("rotationAllowed").get<bool>();
      edge_msg.max_rotation_speed = edge.at("maxRotationSpeed").get<double>();
      edge_msg.avoid_map = edge.at("avoidMap").get<int>();
      for (auto strategy : edge.at("avoidStrategy")) {
        edge_msg.avoid_strategys.push_back(strategy.get<std::string>());
      }
      edge_msg.length = edge.at("length").get<double>();

      cotek_msgs::trajectory trajectory_msg;
      auto &trajectory = edge.at("trajectory");
      trajectory_msg.degree = trajectory.at("degree").get<int>();
      // TODO（@ssh） 该字段暂未用
      trajectory_msg.knot_vector = std::vector<double>();
      for (auto &control_point : trajectory.at("controlPoints")) {
        cotek_msgs::control_point control_point_msg;
        control_point_msg.x = control_point.at("x").get<double>();
        control_point_msg.y = control_point.at("y").get<double>();
        control_point_msg.weight = control_point.at("weight").get<double>();
        trajectory_msg.control_points.push_back(control_point_msg);
      }
      edge_msg.trajectory = trajectory_msg;

      for (auto &action : edge.at("actions")) {
        cotek_msgs::action action_msg;
        action_msg.action_type = action.at("actionType").get<std::string>();
        action_msg.action_id = action.at("actionId").get<std::string>();
        action_msg.action_description =
            action.at("actionDescription").get<std::string>();
        action_msg.blocking_type = action.at("blockingType").get<std::string>();
        for (auto &action_parameter : action.at("actionParameters")) {
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("target")) {
            for (auto &value : action_parameter.at("value")) {
              action_msg.action_value_list.push_back(value.get<std::string>());
            }
          }
          if (action_parameter.contains("key") &&
              action_parameter.at("key").get<std::string>() ==
                  std::string("mixConditions")) {
            auto &mix_conditions = action_parameter.at("value");

            action_msg.mix_conditions = mix_conditions.dump(4);
          }
        }
        edge_msg.actions.push_back(action_msg);
      }
      order_msg.edges.push_back(edge_msg);
    }

    DataManager::Instance().RosPublish(cotek_topic::kOrderRequestTopic,
                                       order_msg);
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
    return;
  }
}

void InstantActionHandle::handleRequest(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  LOG_WARN("receive: [instantAction]");
  CreateHttpHeadRes(request, response);
  try {
    Json instant_json;
    instant_json = Json::parse(request.stream());

    cotek_msgs::instant_action instant_action_msg;

    for (auto &action : instant_json.at("actions")) {
      cotek_msgs::action action_msg;
      action_msg.action_type = action.at("actionType").get<std::string>();
      action_msg.action_id = action.at("actionId").get<std::string>();
      action_msg.action_description =
          action.at("actionDescription").get<std::string>();
      action_msg.blocking_type = action.at("blockingType").get<std::string>();
      for (auto &action_parameter : action.at("actionParameters")) {
        if (action_parameter.contains("key") &&
            action_parameter.at("key").get<std::string>() ==
                std::string("target")) {
          for (auto &value : action_parameter.at("value")) {
            action_msg.action_value_list.push_back(value.get<std::string>());
          }
        }
      }
      instant_action_msg.actions.push_back(action_msg);
    }

    DataManager::Instance().RosPublish(cotek_topic::kInstantActionRequestTopic,
                                       instant_action_msg);
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
    return;
  }
}

void AmrSyncHandle::handleRequest(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  LOG_WARN("receive: [AmrSyncHandle]");
  try {
    PocoWebScoket ws(request, response);

    while (ros::ok() && !ws.available()) {
      Json &&agv_position_json =
          DataManager::Instance().GetJson(cotek_topic::kAgvPositionTopic);

      Json &&velocity =
          DataManager::Instance().GetJson(cotek_topic::kMoveFeedbackTopic);

      Json &&endnode = DataManager::Instance().GetJson("endnode");

      Json &&agv_state =
          DataManager::Instance().GetJson(cotek_topic::kAgvStateTopic);

      if (!agv_position_json.empty() && !velocity.empty()) {
        agv_position_json.merge_patch(velocity);
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());

        agv_position_json.merge_patch(endnode);

        agv_position_json.merge_patch(agv_state);

        agv_position_json.merge_patch(msg.GenerateHeader(GetVersion()));
        auto &&temp = agv_position_json.dump();
        ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
        // fallthrough
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void MasterAmrSyncHandle::handleRequest(PocoHttpServerRequset &request,
                                        PocoHttpServerResponse &response) {
  LOG_WARN("receive: [AmrSync]");
  CreateHttpHeadRes(request, response);
  try {
    Json json = Json::parse(request.stream());

    LOG_INFO_STREAM(json.dump(4));

    AmrSyncInfo info;
    info.ip = json["amrSync"]["amrIp"].get<std::string>();

    if (json["amrSync"].contains("amrPort")) {
      info.port = json["amrSync"]["amrPort"].get<int>();
    } else {
      info.port = 9999;
    }

    info.name = json["amrSync"]["amrName"].get<std::string>();

    DataManager::Instance().CreateSyncAmr(info);

    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
    return;
  }
}

Json StatisticsHandle::GetOdomInfoJson(PocoHttpServerRequset &request) {
  try {
    Json json;

    cotek_msgs::query_info query_odom;
    query_odom.request.header.stamp = ros::Time::now();

    if (DataManager::Instance().RosServiceCall(cotek_services::kOdomInfoService,
                                               query_odom)) {
      Json odom_info_json = Json::parse(query_odom.response.json);
      if (!odom_info_json.empty()) {
        json["odomInfo"] = odom_info_json;
      }
      return json;
    } else {
      LOG_ERROR("Call %s failed!!!", cotek_services::kOdomInfoService);
      return Json();
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return Json();
  }
}

Json StatisticsHandle::GetStorageInfoJson(PocoHttpServerRequset &request) {
  try {
    PocoHtmlForm format(request);
    std::string area_id = format.get("areaId");
    std::string order_id = format.get("orderId");
    std::string task_id = format.get("taskId");

    Json json;
    cotek_msgs::query_info query_storage;
    query_storage.request.header.stamp = ros::Time::now();
    query_storage.request.param_list.push_back(area_id);
    query_storage.request.param_list.push_back(order_id);
    query_storage.request.param_list.push_back(task_id);

    if (DataManager::Instance().RosServiceCall(
            cotek_services::kQueryStorageInfoService, query_storage)) {
      Json storage_info_json = Json::parse(query_storage.response.json);
      if (!storage_info_json.empty()) {
        json["storageInfo"] = storage_info_json;
      }
      return json;
    } else {
      LOG_ERROR("Call %s failed!!!", cotek_services::kQueryStorageInfoService);
      return Json();
    }

  } catch (const std::exception &ex) {
    LOG_ERROR(ex.what());
    return Json();
  }
}

// TODO(@ssh)待测试
void StatisticsHandle::handleRequest(PocoHttpServerRequset &request,
                                     PocoHttpServerResponse &response) {
  CreateHttpHeadRes(request, response);
  try {
    Json json;
    PocoHtmlForm format(request);

    std::string type = format.get("type");

    if (type == "odomInfo") {
      json = GetOdomInfoJson(request);
    }

    if (type == "storageInfo") {
      json = GetStorageInfoJson(request);
    }

    if (json.empty()) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
      return;
    }
    response.setContentType("application/json");
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const Poco::NotFoundException &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, ex.what());
  }
}

void MapStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  std::string topic = "mapWeb";
  try {
    PocoWebScoket ws(request, response);

    DataManager::Instance().ClearImageInfo(topic);

    while (ros::ok() && !ws.available()) {
      // 获取Base64编码的图像数据
      std::string &&image_data =
          DataManager::Instance().GetImageStreamData(topic);

      ImageInfo &&info = DataManager::Instance().GetImageInfo(topic);
      Json info_json;
      info_json["width"] = info.width;
      info_json["height"] = info.height;
      info_json["resolution"] = info.resolution;
      info_json["orign_x"] = info.orign_x;
      info_json["orign_y"] = info.orign_y;

      Json &&landmark_map_json = DataManager::Instance().GetJson(
          cotek_topic::kReflectorMapPointsTopic);
      if (landmark_map_json.empty()) {
        std::vector<double> x, y;
        Json json;
        json["x"] = x;
        json["y"] = y;
        landmark_map_json = json;
      }

      Json &&lost_check_json = DataManager::Instance().GetJson(
          cotek_topic::kLostCheckMapPointsTopic);
      if (lost_check_json.empty()) {
        std::vector<double> x, y;
        Json json;
        json["x"] = x;
        json["y"] = y;
        lost_check_json = json;
      }

      Json &&weight_area =
          DataManager::Instance().GetJson(cotek_topic::kStrongAreaInfoTopic);

      Json &&dynamic_area = DataManager::Instance().GetJson(
          cotek_topic::kHighDynamicAreaInfoTopic);

      if (!image_data.empty()) {
        // 发送图像数据到客户端
        Json json;
        json["data"] = image_data;
        json["info"] = info_json;
        json["landmarks"] = landmark_map_json;
        json["lost_check"] = lost_check_json;
        json["specialAreas"].push_back(weight_area);
        json["specialAreas"].push_back(dynamic_area);
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());

        json.merge_patch(msg.GenerateHeader(GetVersion()));
        auto &&temp = json.dump();
        ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
        // fallthrough
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void Map3dStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);

    PocoWebScoket ws(request, response);
    while (ros::ok() && !ws.available()) {
      Json &&points_json =
          DataManager::Instance().GetJson(cotek_topic::kMap3dFrontTopic);

      Json json;

      json["points"] = points_json;
      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      json.merge_patch(msg.GenerateHeader(GetVersion()));
      auto &&temp = json.dump();
      ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void LocalizerStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                          PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);

    PocoWebScoket ws(request, response);
    while (ros::ok() && !ws.available()) {
      Json &&agv_position_json =
          DataManager::Instance().GetJson(cotek_topic::kAgvPositionTopic);
      Json &&points_json =
          DataManager::Instance().GetJson(cotek_topic::kScanMatchedPointsTopic);
      Json &&landmark_json = DataManager::Instance().GetJson(
          cotek_topic::kMatchedLandmarkPointsTopic);
      Json &&lost_check_json = DataManager::Instance().GetJson(
          cotek_topic::kMatchedLostCheckPointsTopic);
      Json json;

      json["points"] = points_json;
      json["landmarks"] = landmark_json;
      json["lost_check"] = lost_check_json;
      if (!agv_position_json.empty()) {
        agv_position_json.merge_patch(json);
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());

        agv_position_json.merge_patch(msg.GenerateHeader(GetVersion()));
        auto &&temp = agv_position_json.dump();
        ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void Localizer3dStreamHandle ::handleRequest(PocoHttpServerRequset &request,
                                             PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);

    PocoWebScoket ws(request, response);
    while (ros::ok() && !ws.available()) {
      Json &&points_json =
          DataManager::Instance().GetJson(cotek_topic::kMatchedPointCloudTopic);

      Json json;

      json["points"] = points_json;
      ProtoclMessageVda5050 msg;
      msg.SetMsgBind(DataManager::Instance().GetSerialNum());
      json.merge_patch(msg.GenerateHeader(GetVersion()));
      auto &&temp = json.dump();
      ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void ImageStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  if (!format.has("frame")) {
    response.setStatus(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.send() << "Not Found";
    return;
  }

  std::string frame = format.get("frame");

  LOG_INFO_STREAM("/stream/get?type="
                  << "image"
                  << "&&frame=" << frame);

  PocoWebScoket ws(request, response);

  try {
    while (ros::ok() && !ws.available()) {
      // 获取Base64编码的图像数据
      std::string &&image_data =
          DataManager::Instance().GetImageStreamData(frame);
      if (!image_data.empty()) {
        // 发送图像数据到客户端
        Json json;
        json["data"] = image_data;
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());

        json.merge_patch(msg.GenerateHeader(GetVersion()));
        auto &&temp = json.dump();
        ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
        // fallthrough
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void PointsStreamHandle::handleRequest(PocoHttpServerRequset &request,
                                       PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    std::string frame = format.get("frame");
    PocoWebScoket ws(request, response);
    while (ros::ok() && !ws.available()) {
      Json &&points_json = DataManager::Instance().GetJson(frame);
      if (!points_json.empty()) {
        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());

        points_json.merge_patch(msg.GenerateHeader(GetVersion()));
        auto &&temp = points_json.dump();
        ws.sendFrame(temp.data(), temp.size(), PocoWebScoket::FRAME_TEXT);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  }
}

void CalibrationHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  try {
    Poco::Net::WebSocket ws(request, response);
    char buffer[1024];
    int flags = 0;
    bool is_calibrate = false;

    int n = ws.receiveFrame(buffer, sizeof(buffer), flags);
    while (ros::ok() && !ws.available() && n > 0 &&
           ((flags & Poco::Net::WebSocket::FRAME_OP_BITMASK) !=
            Poco::Net::WebSocket::FRAME_OP_CLOSE)) {
      std::string receivedData(buffer, n);

      Json json = Json::parse(receivedData);

      std::string type = json["calibration"]["type"].get<std::string>();
      std::string params;
      std::vector<std::string> parameters;
      for (auto &param : json["calibration"]["parameters"]) {
        parameters.push_back(param.get<std::string>());
        params += " " + param.get<std::string>();
      }
      int method = json["calibration"]["method"];

      LOG_INFO_STREAM("calibration: type=" << type << ", param=" << params
                                          << ", frame=" << method);
      is_calibrate = true;

      NoBlockService(type, parameters, method);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      if (type != "backGroundCalibration") {
        Json response_json;
        response_json["calibration"]["type"] = type;
        response_json["calibration"]["parameters"] = parameters;
        response_json["calibration"]["method"] = method;
        response_json["calibration"]["status"] =
            CalibrationStatusToString(response_status_);
        response_json["calibration"]["message"] = response_message_;

        ProtoclMessageVda5050 msg;
        msg.SetMsgBind(DataManager::Instance().GetSerialNum());
        msg.SetMsgJson(response_json);

        if (msg.PackMsg(GetVersion())) {
          ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                      static_cast<int>(msg.SendBufferSize()), flags);
        }
        // 当标定任务返回结果时，结束循环
        if (static_cast<uint8_t>(response_status_) !=
            static_cast<uint8_t>(CalibrationStatus::NONE)) {
          LOG_INFO_STREAM("return status: " << std::to_string(
                              static_cast<uint8_t>(response_status_)));
          has_call_ = false;
          if (type == "saveMap") {
            common::relocation_t data;
            DataManager::Instance().GetRelocation("", data);
          }
          break;
        }
      } else {
        while (is_calibrate) {
          LOG_WARN("type(%s) status(%d) call(%d)", type.c_str(), 
                    static_cast<uint8_t>(response_status_), has_call_);
          if (static_cast<uint8_t>(response_status_) !=
              static_cast<uint8_t>(CalibrationStatus::NONE)) {
            Json response_json;
            response_json["calibration"]["type"] = type;
            response_json["calibration"]["parameters"] = parameters;
            response_json["calibration"]["method"] = method;
            response_json["calibration"]["status"] =
                CalibrationStatusToString(response_status_);
            response_json["calibration"]["message"] = response_message_;

            ProtoclMessageVda5050 msg;
            msg.SetMsgBind(DataManager::Instance().GetSerialNum());
            msg.SetMsgJson(response_json);

            if (msg.PackMsg(GetVersion())) {
              ws.sendFrame(reinterpret_cast<void *>(msg.Data()),
                          static_cast<int>(msg.SendBufferSize()), flags);
            }
            is_calibrate = false;
            has_call_ = false;
            break;
          } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
          }
        }
        break;
      }
    }
  } catch (Poco::Net::WebSocketException &exc) {
    LOG_ERROR(exc.what());
    switch (exc.code()) {
      case PocoWebScoket::WS_ERR_HANDSHAKE_UNSUPPORTED_VERSION:
        response.set("Sec-PocoWebSocket-Version",
                     PocoWebScoket::WEBSOCKET_VERSION);
      case PocoWebScoket::WS_ERR_NO_HANDSHAKE:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_VERSION:
      case PocoWebScoket::WS_ERR_HANDSHAKE_NO_KEY:
        response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_BAD_REQUEST);
        break;
    }
  } catch (std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }
}

void CalibrationHandle::NoBlockService(
    const std::string &type, const std::vector<std::string> &parameters,
    const int &method) {
  if (has_call_) return;
  auto no_block_call = [&](const std::string &type,
                           const std::vector<std::string> &parameters,
                           const int &method) {
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<cotek_msgs::calibration>(type);
    cotek_msgs::calibration request;
    request.request.type = type;
    request.request.parameters = parameters;
    request.request.method = method;

    if (client.call(request)) {
      response_status_ =
          static_cast<CalibrationStatus>(request.response.status);
    } else {
      response_status_ = CalibrationStatus::FAILED;
    }
    response_message_ = request.response.str;
  };
  thread_pool_.enqueue(no_block_call, type, parameters, method);
  has_call_ = true;
}

PocoHttpRequestHandler *HttpHandleFactory::createStreamRequestHandler(
    const PocoHttpServerRequset &request) {
  PocoHtmlForm format(request);

  std::string type = format.get("type");
  if (type == "map") {
    return new MapStreamHandle();
  }

  if (type == "3dmap") {
    return new Map3dStreamHandle();
  }

  if (type == "localizer") {
    return new LocalizerStreamHandle();
  }

  if (type == "3dlocalizer") {
    return new Localizer3dStreamHandle();
  }

  if (type == "image") {
    return new ImageStreamHandle();
  }

  if (type == "points") {
    return new PointsStreamHandle();
  }
  LOG_ERROR("http request not found!!!");
  return nullptr;
}

PocoHttpRequestHandler *HttpHandleFactory::createRequestHandler(
    const PocoHttpServerRequset &request) {
  Poco::URI uri(request.getURI());

  LOG_INFO_STREAM("path: " << uri.getPath()
                           << ", method: " << request.getMethod());

  if (uri.getPath().find("/images") != std::string::npos) {
    // LOG_INFO("redirect from: %s", uri.getPath().c_str());
    return new RedirectHandler();
  }

  if (uri.getPath() == "/status/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new StatusHandle();
  }

  if (uri.getPath() == "/status/stream" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new StatusStreamHandle();
  }

  if (uri.getPath() == "/sensor/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new SensorHandle();
  }

  if (uri.getPath() == "/control/update") {
    return new ControlHandle();
  }

  if (uri.getPath() == "/config/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new GetConfigHandle();
  }

  if (uri.getPath() == "/config/update" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new UpdateConfigHandle();
  }

  if (uri.getPath() == "/map/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new MapFileHandle();
  }

  if (uri.getPath() == "/map/update" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new UpdateMapFileHandle();
  }

  if (uri.getPath() == "/map/edit" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new EditMapFileHandle();
  }

  if (uri.getPath() == "/map/storage/update" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new UpdateStorageMapHandle();
  }

  if (uri.getPath() == "/master/order" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new OrderHandle();
  }

  if (uri.getPath() == "/master/instantAction" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new InstantActionHandle();
  }

  if (uri.getPath() == "/stream/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return createStreamRequestHandler(request);
  }

  if (uri.getPath() == "/statistics/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new StatisticsHandle();
  }

  if (uri.getPath() == "/calibration") {
    return new CalibrationHandle();
  }

  if (uri.getPath() == "/master/amrSync" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new MasterAmrSyncHandle();
  }

  if (uri.getPath() == "/agv/amrSync" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new AmrSyncHandle();
  }

  if (uri.getPath() == "/test") {
    return new TestHandle();
  }

  // xp1 api
  if (uri.getPath() == "/guide/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new GuideGetHandle();
  }

  if (uri.getPath() == "/sync/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new SyncGetHandle();
  }

  if (uri.getPath() == "/sync/update" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new SyncUpdateHandle();
  }

  if (uri.getPath() == "/system/set" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new SystemHandle();
  }

  if (uri.getPath() == "/comfirm/send" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new ConfirmSendHandle();
  }

  if (uri.getPath() == "/language/set" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new LanguageSetHandle();
  }

  if (uri.getPath() == "/language/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new LanguageGetHandle();
  }

  if (uri.getPath() == "/reLocation/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new ReLocationListHandle();
  }

  if (uri.getPath() == "/reLocation/update" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new ReLocationUpdateHandle();
  }

  if (uri.getPath() == "/teachTask/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new TeachTaskListHandle();
  }

  if (uri.getPath() == "/teachTask/stream" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new TeachTaskStreamHandle();
  }

  if (uri.getPath() == "/teachTask/send" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new TeachTaskSendHandle();
  }

  if (uri.getPath() == "/teachTask/update") {
    return new TeachTaskUpdateHandle();
  }

  if (uri.getPath() == "/document/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new DocumentHandle();
  }

  if (uri.getPath() == "/params/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new ParamsGetHandle();
  }

  if (uri.getPath() == "/params/set" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new ParamsSetHandle();
  }

  if (uri.getPath() == "/wifi/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new WifiGetHandle();
  }

  if (uri.getPath() == "/wifi/set" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new WifiSetHandle();
  }

  if (uri.getPath() == "/floors/set" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_POST) {
    return new FloorsSetHandle();
  }

  if (uri.getPath() == "/floors/get" &&
      request.getMethod() == Poco::Net::HTTPRequest::HTTP_GET) {
    return new FloorsGetHandle();
  }

  LOG_ERROR_STREAM(uri.getPath() << " not exist!!!");

  return new NullHandle();
}

void FloorsSetHandle::handleRequest(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/floors/set?type=" << type);

    if (!type.empty()) {
      std::stringstream buffer;
      buffer << request.stream().rdbuf();
      Json json = Json::parse(buffer);

      std::string zone_id = json["info"]["zoneSetid"].get<std::string>();
      std::string map_id = json["info"]["mapId"].get<std::string>();
      std::string floor = json["info"]["floor"].get<std::string>();
      Floor sfloor;
      sfloor.floor = floor;
      sfloor.zone_id = zone_id;
      sfloor.map_id = map_id;
      sfloor.id = "";

      bool could_update =
          DataManager::Instance().SetFloor(sfloor, kRelocationUpdate);
      if (!could_update) {
        response.send() << HttpResponse::CreatHttpResponse(
            kHttpError, "", std::string("update floor failed."));
        return;
      }
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void FloorsGetHandle::handleRequest(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/floors/get?type=" << type);

    Json json;
    if (!type.empty()) {
      std::map<std::string, Floor> floors = DataManager::Instance().GetFloor();

      auto list = Json::array();
      for (const auto &floor : floors) {
        list.push_back({{"zoneSetid", floor.second.zone_id},
                        {"mapId", floor.second.map_id},
                        {"floor", floor.second.floor},
                        {"id", floor.second.id}});
      }
      json["list"] = list;
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "json is empty");
      return;
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ConfirmSendHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);

    LOG_INFO_STREAM("/confirm/send");

    NoBlockService("manualConfirm", 1);

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ConfirmSendHandle::NoBlockService(const std::string &type,
                                       const int8_t &data) {
  auto no_block_call = [&](const std::string &type, const int8_t &data) {
    ros::NodeHandle nh;
    ros::ServiceClient client =
        nh.serviceClient<cotek_msgs::manual_confirm>(type);
    cotek_msgs::manual_confirm request;
    request.request.data = data;

    if (!client.call(request)) {
      LOG_ERROR_THROTTLE(1, "call confirm service failed !!!");
    }
  };
  thread_pool_.enqueue(no_block_call, type, data);
}

void DocumentHandle::handleRequest(PocoHttpServerRequset &request,
                                   PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }

    LOG_INFO_STREAM("/document/get?type=" << type);

    if (!type.empty()) {
      SendHelpFile(request, response);
      return;
    }

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }

  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
}

void DocumentHandle::SendHelpFile(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    std::string type("help");
    if (format.has("type")) {
      type = format.get("type");
    }
    std::string user_dir = Poco::Path::home();
    std::string help_file = user_dir + "config/doc/" + type + ".pdf";
    std::ifstream file(help_file, std::ios::binary);
    if (!file) {
      std::string info = "Failed to open help pdf file(" + type + ".pdf)";
      throw std::runtime_error(info);
    }

    // 获取文件大小
    file.seekg(0, std::ios::end);
    std::streamsize file_size = file.tellg();
    file.seekg(0, std::ios::beg);

    // 设置响应头
    response.setContentType("application/pdf");
    response.setContentLength(file_size);

    // 发送文件内容作为响应
    std::ostream &ostr = response.send();
    ostr << file.rdbuf();

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.setStatusAndReason(Poco::Net::HTTPResponse::HTTP_NOT_FOUND);
    response.setContentLength(0);
    response.send();
  }
}

inline const std::string get_current_systime(
    const std::string &fmt = "%04d%02d%02d%02d%02d%02d") {
  auto now =
      std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
  struct tm *ptm = localtime(&now);
  char date[64] = {};
  sprintf(date, fmt.c_str(), (int)ptm->tm_year + 1900, (int)ptm->tm_mon + 1,
          (int)ptm->tm_mday, (int)ptm->tm_hour, (int)ptm->tm_min,
          (int)ptm->tm_sec);
  return std::move(std::string(date));
}

// 去除相对路径开头的斜杠
static std::string getRelativePath(const Poco::Path &fullPath,
                                   const std::string &basePath) {
  std::string relativePath = fullPath.toString().substr(basePath.length());
  if (!relativePath.empty() && relativePath[0] == '/') {
    relativePath = relativePath.substr(1);
  }
  return relativePath;
}

// 递归添加目录及其内容到压缩包
static void addDirectoryToZip(Poco::Zip::Compress &compress,
                              const Poco::Path &path,
                              const std::string &basePath) {
  for (Poco::DirectoryIterator it(path); it != Poco::DirectoryIterator();
       ++it) {
    if (it->isDirectory()) {
      addDirectoryToZip(compress, it.path(), basePath);
    } else {
      try {
        compress.addFile(it.path().toString(),
                         getRelativePath(it.path(), basePath));
      } catch (const Poco::Exception &ex) {
        LOG_ERROR_STREAM("Error adding file: " << ex.displayText() << " ("
                                               << it.path().toString() << ")");
      }
    }
  }
}

// 压缩目录
static void compressDirectory(const std::string &dirPath,
                              const std::string &zipFilePath) {
  std::ofstream ofs(zipFilePath, std::ios::binary);
  Poco::Zip::Compress compress(ofs, true);

  Poco::Path path(dirPath);
  std::string basePath = path.toString();
  if (!basePath.empty() && basePath.back() != '/') {
    basePath += "/";
  }
  addDirectoryToZip(compress, path, basePath);

  compress.close();  // finalize the archive
  ofs.close();
}

void SystemHandle::handleRequest(PocoHttpServerRequset &request,
                                 PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/system/set?type=" << type);

    bool ret = true;
    // 0、备份地图和重定位文件
    try {
      std::string source_dir = Poco::Path::home() + "config/map/888888/";
      std::string backup_dir = Poco::Path::home() + "config/backup/";

      Poco::File dir(backup_dir);
      if (!dir.exists()) {
        dir.createDirectories();
      }
      std::string map_zip =
          backup_dir + "map_" + get_current_systime() + ".zip";
      compressDirectory(source_dir, map_zip);
      LOG_INFO("backup map success, file = %s", map_zip.c_str());

      std::string relocation_file =
          Poco::Path::home() + "config/map/relocation_list.json";
      std::string relocation_back =
          backup_dir + "relocation_list_" + get_current_systime() + ".json";
      Poco::File source_file(relocation_file);
      Poco::File backup_file(relocation_back);
      if (source_file.exists()) {
        source_file.copyTo(backup_file.path());
        LOG_INFO("backup relocation success, file = %s",
                 relocation_back.c_str());
      }
    } catch (...) {
    }

    // 1、清空地图数据
    std::string map_dir = Poco::Path::home() + "config/map/888888/";
    resetMap(map_dir);
    // 2、清空定位点数据
    std::string relocation_file =
        Poco::Path::home() + "config/map/relocation_list.json";
    resetRelocation(relocation_file);
    std::string floor_file = Poco::Path::home() + "config/map/floor_list.json";
    resetRelocation(floor_file);
    // 3、清空任务数据
    resetDB();

    // zone_id map_id
    std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
        cotek_config::ConfigType::AGV_BASIC_CONFIG);

    std::regex zone_pattern(R"("current_map_id":\s*"\d+")");
    std::stringstream zone_id_stream;
    zone_id_stream << "\"current_map_id\": \""
                   << "1"
                   << "\"";
    std::string new_config_json =
        std::regex_replace(basic_json_str, zone_pattern, zone_id_stream.str());

    std::regex map_pattern(R"("current_zone_id":\s*"\d+")");
    std::stringstream map_id_stream;
    map_id_stream << "\"current_zone_id\": \""
                  << "888888"
                  << "\"";
    new_config_json =
        std::regex_replace(new_config_json, map_pattern, map_id_stream.str());

    LOG_INFO_STREAM("agv_basic_config: " << new_config_json);

    BasicConfigHelper::Instance().SaveLocal(
        cotek_config::ConfigType::AGV_BASIC_CONFIG, new_config_json);

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    if (ret) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");
    } else {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "error");
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());

    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void SystemHandle::resetMap(const std::string &dir_path) {
  try {
    Poco::File dir(dir_path);

    if (!dir.exists()) {
      LOG_WARN_STREAM("directory(" << dir_path << ") is not exist");
      return;
    }

    if (!dir.isDirectory()) {
      LOG_WARN_STREAM("path(" << dir_path << ") is not directory");
      return;
    }

    Poco::DirectoryIterator it(dir_path);
    Poco::DirectoryIterator end;

    for (; it != end; ++it) {
      Poco::File file(it->path());
      if (file.isDirectory()) {
        Poco::Path entry_path(file.path());
        std::string entry_name = entry_path.getBaseName();
        if (entry_name != "0") {
          Poco::File(file.path()).remove(true);  // `true` 表示递归删除内容
          LOG_INFO_STREAM("reset map(" << file.path() << ") success");
        }
      }
    }
  } catch (const Poco::FileNotFoundException &e) {
    LOG_WARN_STREAM("reset map failed: " << e.displayText());
  }
}

void SystemHandle::resetRelocation(const std::string &file_path) {
  nlohmann::ordered_json json;
  json["reLocationList"] = nlohmann::json::array();
  json["default"] = nlohmann::json::array();
  std::ofstream out_file(file_path);
  out_file << json.dump(4);

  DataManager::Instance().ResetRelocation();
  LOG_INFO_STREAM("reset relocation(" << file_path << ") success");
}

void SystemHandle::resetFloor(const std::string &file_path) {
  nlohmann::ordered_json json;
  json["list"] = nlohmann::json::array();
  std::ofstream out_file(file_path);
  out_file << json.dump(4);

  DataManager::Instance().ResetRelocation();
  LOG_INFO_STREAM("reset floor(" << file_path << ") success");
}

int SystemHandle::resetDB() {
  int ret = 0;
  cotek_msgs::std_cmd cmd;
  if (DataManager::Instance().RosServiceCall("resetDB", cmd)) {
    ret = cmd.response.code;
    LOG_INFO_STREAM("reset db return " << ret);
  } else {
    LOG_ERROR_STREAM("reset db call service failed!!!");
    ret = 1000;
  }
  return ret;
}

void LanguageSetHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/language/set?type=" << type);

    if (!type.empty()) {
      DataManager::Instance().SetLanguage(type);

      // language
      std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::AGV_BASIC_CONFIG);

      std::regex language_pattern(R"("language"\s*:\s*".*?")");
      std::stringstream language_stream;
      language_stream << "\"language\": \"" << type << "\"";
      LOG_INFO_STREAM("language stream: " << language_stream.str());
      std::string new_config_json = std::regex_replace(
          basic_json_str, language_pattern, language_stream.str());

      LOG_INFO_STREAM("language json:\n" << new_config_json);

      BasicConfigHelper::Instance().SaveLocal(
          cotek_config::ConfigType::AGV_BASIC_CONFIG, new_config_json);

      LOG_INFO_STREAM("save agv_basic_config(language:" << type << ") success");
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void LanguageGetHandle::handleRequest(PocoHttpServerRequset &request,
                                      PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/Language/get?type=" << type);
    std::string language = DataManager::Instance().GetLanguage();

    nlohmann::ordered_json json;
    json["language"]["language"] = language;
    json["language"]["safetyType"] = nlohmann::ordered_json();
    json["language"]["safetyLevel"] = nlohmann::ordered_json();
    json["language"]["errorType"] = nlohmann::ordered_json();
    json["language"]["errorCode"] = nlohmann::ordered_json();

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "json is empty");
      return;
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void WifiGetHandle::handleRequest(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);

  try {
    std::string type = format.get("type");
    LOG_INFO_STREAM("/wifi/get?type=" << type);

    if (type == "scan") {
      WifiScan(request, response);
      return;
    }

    if (type == "status") {
      GetStatus(request, response);
      return;
    }

  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
  }

  response.send() << HttpResponse::CreatHttpResponse(kHttpError, "");
}

void WifiGetHandle::WifiScan(PocoHttpServerRequset &request,
                             PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  std::string name;
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);

  try {
    Json json;
    auto ssid_list = Json::array();

    auto wifi_ptr = std::make_shared<WiFi>();
    auto res = wifi_ptr->scan();
    for (const auto &name : res) {
      if (name.ssid == "") continue;
      ssid_list.push_back({{"name", name.ssid}});
    }
    json["ssid"]["list"] = ssid_list;

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "json is empty");
      return;
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    if (res.size() > 0) {
      if (!res[0].vaild) {
        response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                           "scan failed");
      } else {
        response.send() << HttpResponse::CreatHttpResponse(kHttpOk,
                                                           msg.MsgData());
      }
    } else {
      response.send() << HttpResponse::CreatHttpResponse(kHttpOk,
                                                         msg.MsgData());
    }
  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void WifiGetHandle::GetStatus(PocoHttpServerRequset &request,
                              PocoHttpServerResponse &response) {
  PocoHtmlForm format(request);
  CreateHttpHeadRes(request, response);
  std::string name;
  response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);

  try {
    Json json;

    auto wifi_ptr = std::make_shared<WiFi>();
    auto res = wifi_ptr->getStatus();
    json["info"]["state"] = res.state;
    json["info"]["ssid"] = res.ssid;
    json["info"]["ip"] = res.ip;
    json["info"]["signal"] = res.signal;
    json["info"]["security"] = res.security;
    json["info"]["bandwidth"] = res.bandwidth;
    json["info"]["mac"] = res.mac;

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "json is empty");
      return;
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    if (!res.vaild) {
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "get status failed");
    } else {
      response.send() << HttpResponse::CreatHttpResponse(kHttpOk,
                                                         msg.MsgData());
    }
  } catch (const Poco::Exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void WifiSetHandle::handleRequest(PocoHttpServerRequset &request,
                                  PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/wifi/set");

    if (type == "connect") {
      std::stringstream buffer;
      buffer << request.stream().rdbuf();
      Json json = Json::parse(buffer);

      std::string ssid = json["info"]["ssid"];
      std::string password = json["info"]["password"];
      std::string mode = json["info"]["mode"];
      std::string ip, gateway, netmask, dns;
      if (mode != "auto") {
        ip = json["info"]["ip"];
        gateway = json["info"]["gateway"];
        netmask = json["info"]["subnet"];
        dns = json["info"]["dns"];
      }

      auto wifi_ptr = std::make_shared<WiFi>();
      bool res;
      if (mode == "manual") {
        res =
            wifi_ptr->StaticConnect(ssid, password, ip, netmask, gateway, dns);
      } else {
        res = wifi_ptr->DHCPConnect(ssid, password);
      }

      response.setContentType("application/json");
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      if (res) {
        response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");
      } else {
        response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                           "connect failed");
      }
    } else if (type == "disconnect") {
      auto wifi_ptr = std::make_shared<WiFi>();
      bool res;

      res = wifi_ptr->disconnectWiFi();
      response.setContentType("application/json");
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      if (res) {
        response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");
      } else {
        response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                            "disconnect failed"); 
      }
    } else if (type == "forget") {
      auto wifi_ptr = std::make_shared<WiFi>();
      bool res;

      res = wifi_ptr->forgetCurrentWifi();
      response.setContentType("application/json");
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      if (res) {
        response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");
      } else {
        response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                            "forget failed"); 
      }
    }

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ParamsGetHandle::handleRequest(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/params/get?type=" << type);
    nlohmann::ordered_json json;

    if (type == "basic") {
      int id = DataManager::Instance().GetLoraId();
      json["params"]["id"] = id;
    } else if (type == "avoid") {
      std::string config_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::AVOID_CONFIG);
      Json config_json = Json::parse(config_str);

      json["params"]["io"] =
          config_json["enable_config"]["enable_io_state"].get<bool>();
      json["params"]["navilaser1"] =
          config_json["enable_config"]["enable_navi_laser_1"].get<bool>();
      json["params"]["avoidlaser0"] =
          config_json["enable_config"]["enable_avoid_laser_0"].get<bool>();
      json["params"]["avoidlaser1"] =
          config_json["enable_config"]["enable_avoid_laser_1"].get<bool>();
      json["params"]["avoidlaser2"] =
          config_json["enable_config"]["enable_avoid_laser_2"].get<bool>();
      json["params"]["avoidlaser3"] =
          config_json["enable_config"]["enable_avoid_laser_3"].get<bool>();
      json["params"]["avoidlaser4"] =
          config_json["enable_config"]["enable_avoid_laser_4"].get<bool>();
      json["params"]["avoidlaser5"] =
          config_json["enable_config"]["enable_avoid_laser_5"].get<bool>();
      json["params"]["avoidcamera0"] =
          config_json["enable_config"]["enable_avoid_camera_0"].get<bool>();
      json["params"]["avoidcamera1"] =
          config_json["enable_config"]["enable_avoid_camera_1"].get<bool>();
      json["params"]["avoidcamera2"] =
          config_json["enable_config"]["enable_avoid_camera_2"].get<bool>();
      json["params"]["avoidcamera3"] =
          config_json["enable_config"]["enable_avoid_camera_3"].get<bool>();
      json["params"]["avoidcamera4"] =
          config_json["enable_config"]["enable_avoid_camera_4"].get<bool>();
      json["params"]["avoidcamera5"] =
          config_json["enable_config"]["enable_avoid_camera_5"].get<bool>();
    } else if (type == "charge") {
      std::string config_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::LOGIC_CONFIG);
      Json config_json = Json::parse(config_str);

      json["params"]["charge"] =
          config_json["fork_lift_logic_option"]["charge_power"].get<int>();
      json["params"]["work"] =
          config_json["fork_lift_logic_option"]["work_power"].get<int>();
      json["params"]["work"] =
          config_json["fork_lift_logic_option"]["enable_auto_charge"].get<bool>();
    } else if (type == "action") {
      std::string config_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::ACTION_CONFIG);
      Json config_json = Json::parse(config_str);

      json["params"]["enable_confirm"] =
          config_json["manual_confirm"]["overtime_confirm"].get<bool>();
      json["params"]["wait_time"] =
          config_json["manual_confirm"]["wait_time"].get<double>(); 
      json["params"]["check_pallet"] =
          config_json["forklift_action"]["common_config"]["check_pallet"].get<bool>(); 
    } else if (type == "path") {
      std::string config_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::PATH_CONFIG);
      Json config_json = Json::parse(config_str);

      json["params"]["fast_v"] =
          config_json["path_speed"]["fast"].get<double>();
      json["params"]["mid_v"] =
          config_json["path_speed"]["middle"].get<double>();
      json["params"]["slow_v"] =
          config_json["path_speed"]["slow"].get<double>();
      json["params"]["forward"] =
          config_json["path_traffic"]["car_forward_length"].get<double>();
      json["params"]["backward"] =
          config_json["path_traffic"]["car_backward_length"].get<double>();
      json["params"]["sideward"] =
          config_json["path_traffic"]["car_side_length"].get<double>();
    } else if (type == "avoidarea") {
      std::string config_str = BasicConfigHelper::Instance().GetConfig(
          cotek_config::ConfigType::AVOID_AREA);
      Json config_json = Json::parse(config_str);
      auto &map_array = config_json["map_area"];
      auto params_list = Json::array();

      for (auto &map : map_array) {
        if (map["id"] == 22) {
          auto &&slow_level1 = map["slow_level1"];
          double forward{0}, side{0}, backward{0};
          for (auto &coord : slow_level1) {
            double x = coord["x"].get<double>();
            double y = coord["y"].get<double>();
            if (x > 0 && x > forward) forward = x;
            if (y > 0 && y > side) side = y;
            if (x < 0 && backward > x) backward = x;
          } 
          params_list.push_back({{"type", "far"},
                                  {"forward", forward},
                                  {"side", side},
                                  {"backward", backward}});
        }
        if (map["id"] == 23) {
          auto &&slow_level1 = map["slow_level1"];
          double forward{0}, side{0}, backward{0};
          for (auto &coord : slow_level1) {
            double x = coord["x"].get<double>();
            double y = coord["y"].get<double>();
            if (x > 0 && x > forward) forward = x;
            if (y > 0 && y > side) side = y;
            if (x < 0 && backward > x) backward = x;
          } 
          params_list.push_back({{"type", "mid"},
                                  {"forward", forward},
                                  {"side", side},
                                  {"backward", backward}});
        }
        if (map["id"] == 24) {
          auto &&slow_level1 = map["slow_level1"];
          double forward{0}, side{0}, backward{0};
          for (auto &coord : slow_level1) {
            double x = coord["x"].get<double>();
            double y = coord["y"].get<double>();
            if (x > 0 && x > forward) forward = x;
            if (y > 0 && y > side) side = y;
            if (x < 0 && backward > x) backward = x;
          } 
          params_list.push_back({{"type", "near"},
                                  {"forward", forward},
                                  {"side", side},
                                  {"backward", backward}});
          auto &&stop = map["stop"];
          for (auto &coord : stop) {
            double x = coord["x"].get<double>();
            double y = coord["y"].get<double>();
            if (x > 0 && x > forward) forward = x;
            if (y > 0 && y > side) side = y;
            if (x < 0 && backward > x) backward = x;
          } 
          params_list.push_back({{"type", "collision"},
                                  {"forward", forward},
                                  {"side", side},
                                  {"backward", backward}});
        }

        json["params"]["list"] = params_list;
      }

    } else {
      LOG_INFO_STREAM("not support type: " << type);
    }

    ProtoclMessageVda5050 msg;
    msg.SetMsgBind(DataManager::Instance().GetSerialNum());
    msg.SetMsgJson(json);
    if (!msg.PackMsg(GetVersion())) {
      response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
      response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                         "json is empty");
      return;
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, msg.MsgData());

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

void ParamsSetHandle::handleRequest(PocoHttpServerRequset &request,
                                    PocoHttpServerResponse &response) {
  try {
    PocoHtmlForm format(request);
    CreateHttpHeadRes(request, response);
    std::string type;
    if (format.has("type")) {
      type = format.get("type");
    }
    LOG_INFO_STREAM("/params/set?type=" << type);

    if (!type.empty()) {
      std::stringstream buffer;
      buffer << request.stream().rdbuf();
      Json json = Json::parse(buffer);

      if (type == "basic") {
        int new_id = json["params"]["id"].get<int>();

        std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
            cotek_config::ConfigType::AGV_BASIC_CONFIG);

        std::regex pattern(R"("lora_id"\s*:\s*\d+)");
        std::stringstream stream;
        stream << "\"lora_id\": " << new_id;
        LOG_INFO_STREAM("stream: " << stream.str());

        std::string new_config_json =
            std::regex_replace(basic_json_str, pattern, stream.str());

        BasicConfigHelper::Instance().SaveLocal(
            cotek_config::ConfigType::AGV_BASIC_CONFIG, new_config_json);
      } else if (type == "avoid") {
        bool io = json["params"]["io"].get<bool>();
        bool navi1 = json["params"]["navilaser1"].get<bool>();
        bool avoid0 = json["params"]["avoidlaser0"].get<bool>();
        bool avoid1 = json["params"]["avoidlaser1"].get<bool>();
        bool avoid2 = json["params"]["avoidlaser2"].get<bool>();
        bool avoid3 = json["params"]["avoidlaser3"].get<bool>();
        bool avoid4 = json["params"]["avoidlaser4"].get<bool>();
        bool avoid5 = json["params"]["avoidlaser5"].get<bool>();
        bool camera0 = json["params"]["avoidcamera0"].get<bool>();
        bool camera1 = json["params"]["avoidcamera1"].get<bool>();
        bool camera2 = json["params"]["avoidcamera2"].get<bool>();
        bool camera3 = json["params"]["avoidcamera3"].get<bool>();
        bool camera4 = json["params"]["avoidcamera4"].get<bool>();
        bool camera5 = json["params"]["avoidcamera5"].get<bool>();

        std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
            cotek_config::ConfigType::AVOID_CONFIG);

        std::regex pattern(R"("enable_io_state"\s*:\s*(true|false))");
        std::stringstream stream;
        stream << std::boolalpha << "\"enable_io_state\": " << io;
        std::string new_config_json =
            std::regex_replace(basic_json_str, pattern, stream.str());

        pattern = std::regex(R"("enable_navi_laser_1"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_navi_laser_1\": " << navi1;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("enable_avoid_laser_0"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_laser_0\": " << avoid0;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_laser_1"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_laser_1\": " << avoid1;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_laser_2"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_laser_2\": " << avoid2;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_laser_3"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_laser_3\": " << avoid3;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_laser_4"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_laser_4\": " << avoid4;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_laser_5"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_laser_5\": " << avoid5;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("enable_avoid_camera_0"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_camera_0\": " << camera0;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_camera_1"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_camera_1\": " << camera1;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_camera_2"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_camera_2\": " << camera2;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_camera_3"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_camera_3\": " << camera3;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_camera_4"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_camera_4\": " << camera4;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());
        pattern = std::regex(R"("enable_avoid_camera_5"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_avoid_camera_5\": " << camera5;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        BasicConfigHelper::Instance().SaveLocal(
            cotek_config::ConfigType::AVOID_CONFIG, new_config_json);
      } else if (type == "charge") {
        int charge = json["params"]["charge"].get<int>();
        int work = json["params"]["work"].get<int>();
        bool enable = json["params"]["enable_auto_charge"].get<bool>();
        std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
            cotek_config::ConfigType::LOGIC_CONFIG);

        std::regex pattern(R"("charge_power"\s*:\s*\d+)");
        std::stringstream stream;
        stream << "\"charge_power\": " << charge;
        std::string new_config_json =
            std::regex_replace(basic_json_str, pattern, stream.str());

        pattern = std::regex(R"("work_power"\s*:\s*\d+)");
        stream.str("");
        stream << "\"work_power\": " << work;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("enable_auto_charge"\s*:\s*(true|false))");
        stream.str("");
        stream << std::boolalpha << "\"enable_auto_charge\": " << enable;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        BasicConfigHelper::Instance().SaveLocal(
            cotek_config::ConfigType::LOGIC_CONFIG, new_config_json); 
      } else if (type == "action") {
        bool enable_confirm = json["params"]["enable_confirm"].get<bool>();
        int wait_time = json["params"]["wait_time"].get<int>();
        bool check_pallet = json["params"]["check_pallet"].get<bool>();

        std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
            cotek_config::ConfigType::ACTION_CONFIG);

        std::regex pattern(R"("overtime_confirm"\s*:\s*(true|false))");
        std::stringstream stream;
        stream << std::boolalpha << "\"overtime_confirm\": " << enable_confirm; 
        std::string new_config_json = std::regex_replace(
            basic_json_str, pattern, stream.str());

        pattern = std::regex(R"("wait_time"\s*:\s*\d+)");
        stream.str("");
        stream << "\"wait_time\": " << wait_time;
        new_config_json = std::regex_replace(
            new_config_json, pattern, stream.str());

        pattern = std::regex(R"("check_pallet"\s*:\s*(true|false))");
        stream.str("");
        stream << "\"check_pallet\": " << check_pallet;
        new_config_json = std::regex_replace(
            new_config_json, pattern, stream.str());

        BasicConfigHelper::Instance().SaveLocal(
            cotek_config::ConfigType::ACTION_CONFIG, new_config_json); 
      } else if (type == "avoidarea") {
        std::string area_type = json["params"]["type"].get<std::string>();
        double forward = json["params"]["forward"].get<double>();
        double backward = json["params"]["backward"].get<double>();
        double side = json["params"]["side"].get<double>();

        std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
            cotek_config::ConfigType::AVOID_AREA);
        Json j = Json::parse(basic_json_str);        
        Json new_points = Json::array({
            {{"x", std::fabs(forward)}, {"y", std::fabs(side)}},
            {{"x", std::fabs(forward)}, {"y", -std::fabs(side)}},
            {{"x", -std::fabs(backward)}, {"y", std::fabs(side)}},
            {{"x", -std::fabs(backward)}, {"y", -std::fabs(side)}},
        });

        if (area_type == "far") {
          for (auto& area : j["map_area"]) {
            if (area.contains("id") && area["id"] == 22) {
              area["slow_level1"].clear();  // 清空 slow_level1
              area["slow_level1"] = new_points;
            }
          }
        } else if (area_type == "mid") {
          for (auto& area : j["map_area"]) {
            if (area.contains("id") && area["id"] == 23) {
              area["slow_level1"].clear();  // 清空 slow_level1
              area["slow_level1"] = new_points;
            }
          }
        } else if (area_type == "near") {
          for (auto& area : j["map_area"]) {
            if (area.contains("id") && area["id"] == 24) {
              area["slow_level1"].clear();  // 清空 slow_level1
              area["slow_level1"] = new_points;
            }
          }
        } else if (area_type == "collision") {
          for (auto& area : j["map_area"]) {
            if (area.contains("id") && 
                (area["id"] == 22 || area["id"] == 23 || area["id"] == 24)) {
              area["stop"].clear();  // 清空 slow_level1
              area["stop"] = new_points;
            }
          }
        }   

        std::string json_str = j.dump(4);
        BasicConfigHelper::Instance().SaveLocal(
            cotek_config::ConfigType::AVOID_AREA, json_str); 
      } else if (type == "path") {
        double fast = json["params"]["fast_v"].get<double>();
        double mid = json["params"]["mid_v"].get<double>();
        double slow = json["params"]["slow_v"].get<double>();
        double forward = json["params"]["forward"].get<double>();
        double backward = json["params"]["backward"].get<double>();
        double side = json["params"]["sideward"].get<double>();

        std::string basic_json_str = BasicConfigHelper::Instance().GetConfig(
            cotek_config::ConfigType::PATH_CONFIG);

        std::regex pattern(R"("slow"\s*:\s*\d+\.\d+\b)");
        std::stringstream stream;
        stream << "\"slow\":" << slow;
        std::string new_config_json =
            std::regex_replace(basic_json_str, pattern, stream.str());

        pattern = std::regex(R"("middle"\s*:\s*\d+\.\d+\b)");
        stream.str("");
        stream << "\"middle\":" << mid;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("fast"\s*:\s*\d+\.\d+\b)");
        stream.str("");
        stream << "\"fast\":" << fast;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("car_forward_length"\s*:\s*\d+\.\d+\b)");
        stream.str("");
        stream << "\"car_forward_length\":" << forward;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("car_backward_length"\s*:\s*\d+\.\d+\b)");
        stream.str("");
        stream << "\"car_backward_length\":" << backward;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        pattern = std::regex(R"("car_side_length"\s*:\s*\d+\.\d+\b)");
        stream.str("");
        stream << "\"car_side_length\":" << side;
        new_config_json =
            std::regex_replace(new_config_json, pattern, stream.str());

        BasicConfigHelper::Instance().SaveLocal(
            cotek_config::ConfigType::PATH_CONFIG, new_config_json);
      } else {
        LOG_INFO_STREAM("not support type: " << type);
      }
    }

    response.setContentType("application/json");
    response.setStatus(Poco::Net::HTTPResponse::HTTP_OK);
    response.send() << HttpResponse::CreatHttpResponse(kHttpOk, "");

  } catch (const std::exception &ex) {
    LOG_ERROR_STREAM(ex.what());
    response.send() << HttpResponse::CreatHttpResponse(kHttpError, "",
                                                       ex.what());
  }
}

}  // namespace cotek_communicate