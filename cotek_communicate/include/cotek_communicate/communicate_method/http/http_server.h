/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_SERVER_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_SERVER_H_

#include <Poco/Net/HTTPRequestHandlerFactory.h>
#include <ros/ros.h>

#include <deque>
#include <functional>
#include <string>
#include <thread>
#include <utility>

#include "Poco/Net/HTTPServer.h"
#include "Poco/Net/ServerSocket.h"
#include "Poco/Util/ServerApplication.h"
#include "cotek_common/agv_basic_option.h"
#include "cotek_common/cotek_protocal.h"
#include "cotek_common/util/httplib.h"
#include "cotek_communicate/communicate_method/http/http_handle_factory.h"

namespace cotek_communicate {

class HttpServer : public PocoHttpServer {
 public:
  HttpServer() = delete;
  explicit HttpServer(const CommunicateOption& option,
                      const HttpHandleFactory& factory);

  bool Init();
  bool Start();

 private:
};
}  // namespace cotek_communicate
#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_HTTP_HTTP_SERVER_H_