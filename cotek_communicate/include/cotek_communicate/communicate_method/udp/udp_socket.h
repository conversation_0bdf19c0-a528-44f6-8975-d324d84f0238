/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_UDP_SOCKET_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_UDP_SOCKET_H_

#include <ros/ros.h>

#include <boost/asio.hpp>
#include <deque>
#include <functional>
#include <string>
#include <thread>
#include <utility>

#include "cotek_common/log_porting.h"
#include "cotek_communicate/communicate_method/communicate_interface.h"

namespace cotek_communicate {

using boost::asio::ip::address_v4;
using boost::asio::ip::udp;

template <typename MessageType>
class UdpSocket : public CommunicateInterface<MessageType> {
public:
  /**
   * \brief constructor
   * \param ip remote ip address
   * \param port remote service port
   */
  explicit UdpSocket(std::string ip, unsigned int server_port,
                     unsigned int local_port)
      : local_host_(udp::v4(), local_port),
        remote_host_(address_v4::from_string(ip), server_port),
        server_port_(server_port) {
    socket_ = std::make_shared<udp::socket>(service_, local_host_);
  }

  /**
   * \brief destructor
   */
  ~UdpSocket() {
    service_.post([this]() { socket_->close(); });
    service_.stop();
  }

  bool Init(const CommunicateOption &option) override { return true; }

  bool Stop() override { return true; }

  void Run() override {
    runner_ = std::make_shared<std::thread>([this]() {
      try {
        service_.run();
      } catch (boost::system::system_error &e) {
        LOG_ERROR_STREAM(e.what());
      }
    });
  }

  void Close() {
    service_.post([this]() {
      socket_->close();
      LOG_INFO_STREAM("Udp socket closed...");
    });
  }

  void Listen(std::function<void(MessageType)> handler) override {
    DoRead(handler);
  }

  void Send(const MessageType &msg, const Qos &qos) override {
    service_.post([this, msg]() {
      bool write_in_progress = !w_messageQueue_.empty();
      w_messageQueue_.push_back(msg);

      if (!write_in_progress) {
        DoWrite();
      }
    });
  }

  // 往指定ip发送
  // void Send(const MessageType &msg, const std::string &remote_bind_id,
  //           const Qos &qos) override {
  //   udp::endpoint remote_host =
  //       udp::endpoint(address_v4::from_string(remote_bind_id), server_port_);
  //   service_.post([this, msg, remote_host]() {
  //     bool write_in_progress = !w_messageQueue_.empty();
  //     w_messageQueue_.push_back(msg);

  //     if (!write_in_progress) {
  //       DoWrite(remote_host);
  //     }
  //   });
  // }

  MessageType CreatEmptyMessage() override { return MessageType(); }

protected:
  void DoRead(std::function<void(MessageType)> handler) {
    r_message_.Clear();
    socket_->async_receive_from(
        boost::asio::buffer(r_message_.Data(), r_message_.ReadBufferSize()),
        local_host_,
        [this, handler](boost::system::error_code ec, std::size_t length) {
          if (!ec) {
            if (length <= 0) {
              LOG_ERROR_STREAM("Receive data length = 0.");
            } else {
              LOG_DEBUG_STREAM("udp async read length: " << length);
              handler(r_message_);
            }
          } else {
            LOG_ERROR_STREAM(ec.message());
            // socket_->close();
          }

          DoRead(handler);
        });
  }

  void DoWrite() {
    socket_->async_send_to(
        boost::asio::buffer(w_messageQueue_.front().Data(),
                            w_messageQueue_.front().SendBufferSize()),
        remote_host_, [this](boost::system::error_code ec, std::size_t length) {
          if (!ec) {
            w_messageQueue_.pop_front();

            if (!w_messageQueue_.empty()) {
              DoWrite();
            }
          } else {
            LOG_ERROR_STREAM(ec.message());
            // socket_->close();
          }
        });
  }

  // 往指定ip发送
  void DoWrite(const udp::endpoint &endpoint) {
    socket_->async_send_to(
        boost::asio::buffer(w_messageQueue_.front().Data(),
                            w_messageQueue_.front().SendBufferSize()),
        endpoint,
        [this, endpoint](boost::system::error_code ec, std::size_t length) {
          if (!ec) {
            w_messageQueue_.pop_front();

            if (!w_messageQueue_.empty()) {
              DoWrite(endpoint);
            }
          } else {
            LOG_ERROR_STREAM(ec.message());
            // socket_->close();
          }
        });
  }

private:
  std::deque<MessageType> w_messageQueue_;
  MessageType r_message_;

  boost::asio::io_service service_;
  std::shared_ptr<udp::socket> socket_;
  std::shared_ptr<std::thread> runner_;

  udp::endpoint local_host_;
  udp::endpoint remote_host_;
  unsigned int server_port_;
};

} // namespace cotek_communicate

#endif // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_UDP_UDP_SOCKET_H_
