/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_COMMUNICATE_INTERFACE_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_COMMUNICATE_INTERFACE_H_

#include <ros/ros.h>

#include <deque>
#include <functional>
#include <string>
#include <thread>
#include <utility>

#include "cotek_common/agv_basic_option.h"
#include "cotek_common/cotek_protocal.h"

using Qos = cotek_protocal::Qos;

namespace cotek_communicate {

template <typename MessageType>
class CommunicateInterface {
 public:
  virtual ~CommunicateInterface() {}

  virtual bool Init(const CommunicateOption &option) = 0;

  virtual void Listen(std::function<void(MessageType)> handler) = 0;

  virtual void Run() = 0;

  virtual void Send(const MessageType &msg, const Qos &qos) = 0;

  // virtual void Send(const MessageType &msg, const std::string
  // &remote_bind_id,
  //                   const Qos &qos) = 0;

  virtual MessageType CreatEmptyMessage() = 0;

  virtual bool Stop() = 0;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_COMMUNICATE_INTERFACE_H_