/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_SYSTEM_INFO_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_SYSTEM_INFO_H_
#include <array>
#include <atomic>
#include <cstdint>
#include <iostream>
#include <mutex>
#include <thread>

namespace cotek_communicate {

// CPU occupancy structure
struct CpuOccupy {
  std::array<char, 20> name = {};
  unsigned int user = 0;
  unsigned int nice = 0;
  unsigned int system = 0;
  unsigned int idle = 0;
};
// Memory occupancy structure
struct MemOccupy {
  std::array<char, 20> name = {};
  uint64_t total = 0;
  std::array<char, 20> name2 = {};
  uint64_t free = 0;
  uint64_t available = 0;
};
// System information class
class SystemInfo {
 public:
  SystemInfo();
  ~SystemInfo();
  bool Init(const std::string &server);
  bool Update(const std::string &server);
  void Start();
  void Stop();
  int GetCpuUsage() {
    std::lock_guard<std::mutex> lock(mutex_);
    return cpuUsage_;
  }
  int GetMemUsage() {
    std::lock_guard<std::mutex> lock(mutex_);
    return memUsage_;
  }
  int GetDiskUsage() {
    std::lock_guard<std::mutex> lock(mutex_);
    return diskUsage_;
  }
  int GetPingLatency() {
    std::lock_guard<std::mutex> lock(mutex_);
    return pingLatency_;
  }
  std::string GetBlueMac();

 private:
  void CalculateSystemInfo();
  void GetCpuOccupy(CpuOccupy *cpuStat);
  void GetMemOccupy(MemOccupy *memStat);
  float CalculateCpuUsage(const CpuOccupy *oldStat, const CpuOccupy *newStat);
  float CalculateDiskUsage();
  float CalculateMemUsage(const MemOccupy *memStat);
  float CalculatePingLatency();

 private:
  std::thread thread_;
  std::mutex mutex_;
  std::atomic<bool> isRunning_;
  int cpuUsage_;
  int memUsage_;
  int diskUsage_;
  int pingLatency_;

  std::string server_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_SYSTEM_INFO_H_