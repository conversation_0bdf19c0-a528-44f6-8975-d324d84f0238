/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_V1_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_V1_H_
#include <openssl/md5.h>
#include <openssl/rand.h>

#include <cstdio>
#include <cstring>
#include <iostream>
#include <string>
#include <vector>

#include "cotek_common/cotek_protocal.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"

namespace cotek_communicate {

/**
 * \class ProtoclMessageV1
 * \brief data message for udp communication
 */
class ProtoclMessageV1 : ProtoclMessageFactoryInterface {
 public:
  ProtoclMessageV1() : buffer_{0}, msg_length_(0) {}

  explicit ProtoclMessageV1(const std::string &bind_id)
      : buffer_{0}, msg_length_(0) {
    protocal_pack_.bind_id = bind_id;
  }

  bool ParseMsgToJson() override;
  bool PackMsg() override;
  bool PackMsg(const std::string& version) override { return true; }
  void Clear() override;

  inline void SetMsgBind(const std::string &bind_id) override {
    protocal_pack_.bind_id = bind_id;
  }
  inline void SetMsgType(const std::string &msg_type) override {
    protocal_pack_.msg_type = msg_type;
  }
  inline void SetMsgData(const std::string &msg_data) override {
    protocal_pack_.msg_data = msg_data;
  }

  inline void SetMsgJson(const Json &msg_json) override {}

  inline std::string MsgBind() const override {
    return msg_json_["bindId"].string_value();
  }
  inline std::string MsgType() const override {
    return msg_json_["msgType"].string_value();
  }
  inline std::string MsgData() const override {
    return msg_json_["msgData"].string_value();
  }
  inline Json MsgJson() const override { return Json(); }

  inline char *Data() override { return buffer_; }

  inline std::size_t ReadBufferSize() const override {
    return cotek_protocal::kMaxUdpBuffer;
  }

  inline std::size_t SendBufferSize() const override { return msg_length_; }

 private:
  bool CheckMsgHash();
  void GenerateMsgHash();

  // 计算校验码 md5sum
  std::string CalculateMd5Sum(const std::string &msg_data);
  // 例： 0xa1 --> "a1"
  std::string Char2String(const unsigned char *chr, int num);

  // 数据缓存, 发送接收的单个数据包都不能超过最大缓存 kMaxUdpBuffer
  char buffer_[cotek_protocal::kMaxUdpBuffer];
  // 发送数据长度
  std::size_t msg_length_;
  // 协议包
  cotek_protocal::CotekUdpProtocal protocal_pack_;
  // 协议包反序列化后的 json对象
  json11::Json msg_json_;
};

}  // end of namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_FACTORY_PROTOCL_MESSAGE_V1_H_
