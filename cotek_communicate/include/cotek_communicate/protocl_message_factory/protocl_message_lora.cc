/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate/protocl_message_factory/protocl_message_lora.h"

#include <algorithm>
#include <cstdint>
#include <exception>

#include "cotek_common/cotek_crc_check.h"
#include "cotek_common/log_porting.h"

namespace cotek_communicate {

std::vector<uint8_t> ProtoclMessageLora::FindProtoclHeadData(
    const std::vector<uint8_t>& data) {
  try {
    std::vector<uint8_t> header = {0x66, 0x88};
    std::vector<uint8_t> tail = {'\r', '\n'};

    auto start =
        std::search(data.begin(), data.end(), header.begin(), header.end());
    auto end = std::search(start, data.end(), tail.begin(), tail.end());

    if (start != data.end() && end != data.end()) {
      // 创建一个新的数组，包含从0x66 0x88开始到\r\n结束的所有元素
      std::vector<uint8_t> new_data(start, end + 2);
      return new_data;
    }
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }

  // 如果没有找到0x66 0x88或者\r\n，返回一个空的数组
  return std::vector<uint8_t>();
}

bool ProtoclMessageLora::CheckProtoclData(const std::vector<uint8_t>& data) {
  // 数据总长度为 数据长度码+9
  try {
    std::vector<uint8_t> temp_data(data);
    if (data.size() != data[4] + 9) {
      LOG_ERROR_STREAM("lora data length error!!! orign_data_length: "
                       << data.size() << " length_data: " << data[4] + 9);
      return false;
    }
    auto&& crc_h = crc::GetCRC16H(&temp_data[0], data.size() - 4);
    auto&& crc_l = crc::GetCRC16L(&temp_data[0], data.size() - 4);
    if (crc_h != temp_data[data.size() - 4] ||
        crc_l != temp_data[data.size() - 3]) {
      LOG_ERROR("check CRC error!!!");
      return false;
    }

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }

  return true;
}

std::vector<std::vector<uint8_t>> ProtoclMessageLora::ParseToMultiPack(
    const std::vector<uint8_t>& data) {
  try {
    std::vector<uint8_t> header = {0x66, 0x88};
    std::vector<uint8_t> tail = {'\r', '\n'};
    std::vector<std::vector<uint8_t>> frames;

    auto start = data.begin();
    while (start != data.end()) {
      start = std::search(start, data.end(), header.begin(), header.end());
      if (start != data.end()) {
        auto end = std::search(start, data.end(), tail.begin(), tail.end());
        if (end != data.end()) {
          std::vector<uint8_t> new_data(start, end + 2);
          frames.push_back(new_data);
          start = end + 2;
        } else {
          break;
        }
      }
    }
    return frames;
  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
  }

  // 如果没有找到0x66 0x88或者\r\n，返回一个空的数组
  return std::vector<std::vector<uint8_t>>();
}

ProtoclMessageLora::ProtoclMessageLora(const std::vector<uint8_t>& data)
    : orign_data_(data) {}

bool ProtoclMessageLora::ParseMsg() {
  try {
    auto&& data = FindProtoclHeadData(orign_data_);
    if (data.empty()) {
      return false;
    }

    if (!CheckProtoclData(data)) {
      return false;
    }
    // 保留一帧数据
    orign_data_ = data;

    serial_num_ = orign_data_[2];
    fun_code_ = static_cast<LoraFunCode>(orign_data_[3]);
    data_size_ = orign_data_[4];
    data_ =
        std::vector<uint8_t>(orign_data_.begin() + 5, orign_data_.end() - 4);
    return true;

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
    return false;
  }
}

bool ProtoclMessageLora::PackMsg() {
  bool ret = false;
  try {
    if (data_.empty() || fun_code() == LoraFunCode::NONE || serialnum() == 0) {
      return ret;
    }
    orign_data_.clear();
    orign_data_.push_back(0x66);
    orign_data_.push_back(0x88);
    orign_data_.push_back(serialnum());
    orign_data_.push_back(fun_code());
    orign_data_.push_back(data_.size());
    orign_data_.insert(orign_data_.end(), data_.begin(), data_.end());

    auto&& crc_h = crc::GetCRC16H(&orign_data_[0], orign_data_.size());
    auto&& crc_l = crc::GetCRC16L(&orign_data_[0], orign_data_.size());
    orign_data_.push_back(crc_h);
    orign_data_.push_back(crc_l);
    orign_data_.push_back('\r');
    orign_data_.push_back('\n');
    ret = true;

  } catch (const std::exception& ex) {
    LOG_ERROR(ex.what());
    return false;
  }
  return ret;
}

}  // end of namespace cotek_communicate
