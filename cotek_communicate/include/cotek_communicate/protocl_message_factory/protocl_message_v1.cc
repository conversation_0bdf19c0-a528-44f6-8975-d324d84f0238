/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#include "cotek_communicate//protocl_message_factory/protocl_message_v1.h"

namespace cotek_communicate {

bool ProtoclMessageV1::ParseMsgToJson() {
  std::string msg_string(buffer_);
  std::string err;
  msg_json_ = json11::Json::parse(msg_string, err);
  if (!err.empty()) {
    return false;
  }
  return true;
}

bool ProtoclMessageV1::PackMsg() {
  // 打包整个数据包
  msg_json_ = json11::Json::object{{"bindId", protocal_pack_.bind_id},
                                   {"msgType", protocal_pack_.msg_type},
                                   {"msgHash", protocal_pack_.msg_hash},
                                   {"msgData", protocal_pack_.msg_data}};
  std::string msg_string(msg_json_.dump());
#if 0
    LOG_INFO_STREAM("send udp msg size: " << msg_string.size());
    LOG_DEBUG_STREAM("udp msg: " << msg_string);
#endif
  msg_length_ = msg_string.copy(buffer_, msg_string.size());
  // std::strcpy(buffer_, msg_string.c_str());
  buffer_[msg_length_] = '\0';
  return true;
}

void ProtoclMessageV1::Clear() {
  std::memset(buffer_, 0, cotek_protocal::kMaxUdpBuffer);
  msg_length_ = 0;
  protocal_pack_.bind_id.clear();
  protocal_pack_.msg_type.clear();
  protocal_pack_.msg_data.clear();
  protocal_pack_.msg_hash.clear();
  msg_json_ = json11::Json();
}

bool ProtoclMessageV1::CheckMsgHash() {
#if 0 // 需要校验就打开
    return msg_json_["msgHash"].string_value() ==
                   CalculateMd5Sum(msg_json_["msgData"].string_value())
               ? true
               : false;
#else
  return true;
#endif
}
void ProtoclMessageV1::GenerateMsgHash() {
  protocal_pack_.msg_hash = CalculateMd5Sum(protocal_pack_.msg_data);
}

// 计算校验码 md5sum
std::string ProtoclMessageV1::CalculateMd5Sum(const std::string &msg_data) {
  unsigned char md5_sum[16];
  MD5_CTX ctx;
  MD5_Init(&ctx);
  MD5_Update(&ctx, protocal_pack_.msg_data.data(),
             protocal_pack_.msg_data.size());
  MD5_Final(md5_sum, &ctx);
#if 0
      for (int i = 0; i < 16; ++i) {
        LOG_DEBUG("%02x ", md5_sum[i]);
      }
#endif
  return Char2String(md5_sum, sizeof(md5_sum));
}

// 例： 0xa1 --> "a1"
std::string ProtoclMessageV1::Char2String(const unsigned char *chr, int num) {
  std::string temp;
  for (int i = 0; i < num; i++) {
    unsigned char temp_high = *(chr + i) / 16;
    unsigned char temp_low = *(chr + i) % 16;
    temp_high = temp_high > 9 ? temp_high - 10 + 'a' : temp_high + '0';
    temp_low = temp_low > 9 ? temp_low - 10 + 'a' : temp_low + '0';
    temp.push_back(temp_high);
    temp.push_back(temp_low);
  }
  return temp;
}
} // namespace cotek_communicate
