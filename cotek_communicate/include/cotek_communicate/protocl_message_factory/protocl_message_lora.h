/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_LORA_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_LORA_H_

#include <cstdint>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <string>
#include <vector>

#include "cotek_common/cotek_protocal.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"

namespace cotek_communicate {

// 30Byte 缓存
constexpr uint32_t kMaxLoraBuffer = 30;

enum LoraFunCode : uint8_t {
  NONE = 0X00,
  TRAFFIC = 0X01,         // 交管
  QUERY_RESOURCE = 0X11,  // 请求设备资源
  EVENT_RESOURCE = 0X12,  // 反馈设备资源

  QUERY_TASK = 0X13,      // PAD任务下发
  EVENT_TASK = 0X14,      // 反馈任务

  REQUEST_SYSTEM = 0X21,  // 请求系统对接(电梯,自动门)
  EVENT_SYSTEM = 0X22,    // 系统对接反馈

  REQUEST_SYNC = 0X31,  // 请求数据同步
  EVENT_SYNC = 0X32     // 数据同步反馈

};

class ProtoclMessageLora {
 public:
  ProtoclMessageLora(){};
  explicit ProtoclMessageLora(const std::vector<uint8_t> &data);

  static std::vector<std::vector<uint8_t>> ParseToMultiPack(
      const std::vector<uint8_t> &data);

  bool ParseMsg();

  bool PackMsg();

  inline std::vector<uint8_t> orign_data() const { return orign_data_; }
  inline uint8_t serialnum() const { return serial_num_; }
  inline LoraFunCode fun_code() const { return fun_code_; }
  inline uint8_t data_size() const { return data_size_; }
  inline std::vector<uint8_t> data() const { return data_; }

  inline void set_serialnum(const uint8_t &num) { serial_num_ = num; }
  inline void set_data_size(const int &size) { data_size_ = size; }
  inline void set_fun_code(const LoraFunCode &code) { fun_code_ = code; }
  inline void set_data(const std::vector<uint8_t> &data) { data_ = data; }

 private:
  // 数据缓存, 发送接收的单个数据包都不能超过最大缓存 kMaxUdpBuffer

  std::vector<uint8_t> FindProtoclHeadData(const std::vector<uint8_t> &data);

  bool CheckProtoclData(const std::vector<uint8_t> &data);
  bool DataLengthVaild(const std::vector<uint8_t> &data);

  std::vector<uint8_t> orign_data_;

  uint8_t serial_num_{0};
  uint8_t data_size_{0};
  LoraFunCode fun_code_{LoraFunCode::NONE};
  std::vector<uint8_t> data_;
};

}  // end of namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_LORA_H_
