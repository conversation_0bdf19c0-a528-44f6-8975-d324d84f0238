/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_FACTORY_PROTOCL_MESSAGE_FACTORY_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_FACTORY_PROTOCL_MESSAGE_FACTORY_H_
#include <openssl/md5.h>
#include <openssl/rand.h>

#include <cstdio>
#include <cstring>
#include <iostream>
#include <string>
#include <vector>

#include "cotek_common/cotek_protocal.h"
#include "cotek_common/nlohmann/json.hpp"

namespace cotek_communicate {

using Json = nlohmann::ordered_json;
using n_excetion = nlohmann::json_abi_v3_11_2::detail::exception;

constexpr char kVersion[] = "3.0.0";

/**
 * \class ProtoclMessageV1
 * \brief data message for udp communication
 */
class ProtoclMessageFactoryInterface {
 public:
  virtual ~ProtoclMessageFactoryInterface() {}

  virtual bool ParseMsgToJson() = 0;

  virtual bool PackMsg() = 0;
  virtual bool PackMsg(const std::string& version) = 0;

  virtual void Clear() = 0;

  /**
   * 设置数据接口
   */

  virtual void SetMsgBind(const std::string &bind_id) = 0;

  virtual void SetMsgType(const std::string &msg_type) = 0;

  virtual void SetMsgData(const std::string &msg_data) = 0;

  virtual void SetMsgJson(const Json &msg_json) = 0;

  virtual std::string MsgBind() const = 0;

  virtual std::string MsgType() const = 0;

  virtual std::string MsgData() const = 0;

  virtual Json MsgJson() const = 0;

  virtual char *Data() = 0;

  virtual std::size_t ReadBufferSize() const = 0;

  virtual std::size_t SendBufferSize() const = 0;
};

}  // end of namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_FACTORY_PROTOCL_MESSAGE_FACTORY_H_
