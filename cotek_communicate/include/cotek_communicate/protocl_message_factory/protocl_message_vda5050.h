/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_VDA5050_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_VDA5050_H_
#include <openssl/md5.h>
#include <openssl/rand.h>

#include <cstdio>
#include <cstring>
#include <iostream>
#include <string>
#include <vector>

#include "cotek_common/cotek_protocal.h"
#include "cotek_common/log_porting.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"

namespace cotek_communicate {
/**
 * \class ProtoclMessageVda5050
 * \brief data message for udp communication
 */

// constexpr char kVersion[] = "3.1.0";

class ProtoclMessageVda5050 : public ProtoclMessageFactoryInterface {
 public:
  ProtoclMessageVda5050();

  bool ParseMsgToJson() override;
  bool PackMsg() override { return true; }
  bool PackMsg(const std::string& version) override;

  inline void SetMsgBind(const std::string &bind_id) override {
    serialnum_ = bind_id;
  }
  inline void SetMsgType(const std::string &data) override { msg_type_ = data; }
  inline void SetMsgJson(const Json &json) override { msg_json_ = json; }
  inline void SetMsgData(const std::string &data) override { msg_data_ = data; }

  inline char *Data() override { return buffer_; }
  inline std::string MsgBind() const override { return serialnum_; }
  inline std::string MsgType() const override { return msg_type_; }
  inline std::string MsgData() const override { return msg_data_; }
  inline Json MsgJson() const override { return msg_json_; }

  inline std::size_t SendBufferSize() const override { return msg_length_; }
  std::size_t ReadBufferSize() const override {
    return cotek_protocal::kMaxUdpBuffer;
  }

  void Clear() override;

  Json GenerateHeader(const std::string& version);

 private:
  std::string GenerateTime();

  // 数据缓存, 发送接收的单个数据包都不能超过最大缓存 kMaxUdpBuffer
  char buffer_[cotek_protocal::kMaxUdpBuffer];
  // 发送数据长度
  std::size_t msg_length_;

  // 协议包反序列化后的 json对象
  Json msg_json_;

  std::string msg_type_;
  std::string msg_data_;

  std::string serialnum_;

  static uint32_t header_id_;
};

}  // end of namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_PROTOCL_MESSAGE_FACTORY_PROTOCL_MESSAGE_VDA5050_H_
