/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "cotek_communicate/protocl_message_factory/protocl_message_vda5050.h"

#include <chrono>
#include <iomanip>
#include <iostream>
#include <sstream>

namespace cotek_communicate {

uint32_t ProtoclMessageVda5050::header_id_ = 1;

ProtoclMessageVda5050::ProtoclMessageVda5050() : buffer_{0}, msg_length_(0) {}

bool ProtoclMessageVda5050::ParseMsgToJson() {
  try {
    if (msg_data_.empty()) {
      msg_data_ = std::string(buffer_);
    }

    msg_json_ = Json::parse(msg_data_);

    if (msg_type_.empty()) {
      msg_type_ = msg_json_["msgType"].get<std::string>();
    }
  } catch (const n_excetion &ex) {
    LOG_ERROR_STREAM(ex.what());
    return false;
  }
  return true;
}

bool ProtoclMessageVda5050::PackMsg(const std::string& version) {
  try {
    if (msg_json_.is_null()) {
      msg_json_ = Json::parse(msg_data_);
    }
    msg_json_.merge_patch(GenerateHeader(version));
  } catch (const n_excetion &ex) {
    LOG_ERROR_STREAM(ex.what());
    return false;
  }
  // std::string msg_string(msg_json_.dump(4));
  std::string msg_string(msg_json_.dump());
#if 0
  LOG_INFO_STREAM(" msg: " << msg_string);
#endif
  msg_data_ = msg_string;
  msg_length_ = msg_string.copy(buffer_, msg_string.size());
  buffer_[msg_length_] = '\0';
  return true;
}

inline void ProtoclMessageVda5050::Clear() {
  std::memset(buffer_, 0, cotek_protocal::kMaxUdpBuffer);
  msg_length_ = 0;
  msg_data_.clear();
  msg_json_.clear();
  msg_type_.clear();
}

Json ProtoclMessageVda5050::GenerateHeader(const std::string& version) {
  Json header;
  header["headerId"] = header_id_++;
  header["timestamp"] = GenerateTime();
  // header["version"] = kVersion;
  header["version"] = version;
  header["manufacturer"] = std::string("CoTEK");
  header["serialNum"] = serialnum_;
  return header;
}

std::string ProtoclMessageVda5050::GenerateTime() {
  // 获取当前时间点
  std::chrono::system_clock::time_point now = std::chrono::system_clock::now();

  // 转换为 UTC 时间
  std::time_t time = std::chrono::system_clock::to_time_t(now);

  // 输出时间字符串到字符串流
  std::ostringstream oss;
  oss << std::put_time(std::gmtime(&time), "%Y-%m-%dT%H:%M:%SZ");

  // 将字符串流的内容保存到字符串变量中
  std::string timeString = oss.str();

  return timeString;
}

}  // namespace cotek_communicate
