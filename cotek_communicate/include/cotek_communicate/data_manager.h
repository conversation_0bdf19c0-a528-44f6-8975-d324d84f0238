/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_DATA_MANAGER_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_DATA_MANAGER_H_

#include <Poco/RWLock.h>
#include <nav_msgs/OccupancyGrid.h>
#include <pcl_conversions/pcl_conversions.h>
#include <ros/ros.h>
#include <sensor_msgs/CompressedImage.h>
#include <sensor_msgs/PointCloud2.h>

#include <functional>
#include <memory>
#include <string>
#include <thread>
#include <vector>
#include <mutex>

#include "cotek_common/agv_basic_option.h"
#include "cotek_common/cotek_enum_type.h"
#include "cotek_common/cotek_tf_name.h"
#include "cotek_common/cotek_topic_name.h"
#include "cotek_common/db/cotek_var.h"
#include "cotek_common/geometry/cotek_geometry.h"
#include "cotek_common/util/singleton.h"
#include "cotek_communicate/amr_sync.h"
#include "cotek_communicate/image_stream.h"
#include "cotek_communicate/protocl_message_factory/protocl_message_factory.h"
#include "cotek_communicate/resource_agent.h"
#include "cotek_communicate/system_info.h"
#include "cotek_msgs/agv_position.h"
#include "cotek_msgs/area.h"
#include "cotek_msgs/battery_feedback.h"
#include "cotek_msgs/battery_state.h"
#include "cotek_msgs/edit_slam_3d_map.h"
#include "cotek_msgs/fault_report.h"
#include "cotek_msgs/io_feedback.h"
#include "cotek_msgs/learning_state.h"
#include "cotek_msgs/task_info.h"
#include "cotek_msgs/loads.h"
#include "cotek_msgs/motor_feedback.h"
#include "cotek_msgs/move_cmd.h"
#include "cotek_msgs/move_feedback.h"
#include "cotek_msgs/odom_info.h"
#include "cotek_msgs/path_state.h"
#include "cotek_msgs/points_2d.h"
#include "cotek_msgs/safety_states.h"
#include "cotek_msgs/self_check.h"
#include "cotek_msgs/traffic_state.h"
#include "cotek_msgs/update_event.h"
#include "cotek_msgs/velocity.h"
#include "cotek_msgs/wire_encoder_feedback.h"
#include "cotek_msgs/slam_3d_pose_deviation.h"
#include "nav_msgs/Odometry.h"
#include "ros/duration.h"
#include "ros/publisher.h"
#include "ros/service_client.h"
#include "ros/subscriber.h"
#include "ros/time.h"
#include "sensor_msgs/LaserScan.h"
#include "std_msgs/Int32.h"

namespace cotek_communicate {

using Pose = cotek_geometry::Pose;
using task_t = common::task_t;
using node_t = common::node_t;
using db_task_t = common::db_task_t;
using task_info_t = common::task_info_t;
using relocation_t = common::relocation_t;

enum RelocationType {
  kRelocationUpdate   = 0,
  kRelocationInsert   = 1,
  kRelocationDelete   = 2,
  kRelocationDefault  = 3,
};

enum ImageType {
  kImageNone          = 0,
  kImageLocal         = 1,
  kImageMap           = 2,
  kImageRelocation    = 4,
  kImagePath          = 8,
};

enum TaskType {
  kTaskBuffer         = 0,
  kTaskRefresh        = 1,
  kTaskDelete         = 2,
};

struct ForkStatus {
  double fork_height = 88888.;
  double fork_lateral = 88888.;
  double fork_side = 88888.;
  double fork_tilt = 88888.;
  double fork_rotate = 88888.;
};

struct Floor {
  std::string id;
  std::string zone_id;
  std::string map_id;
  std::string floor;
};

template <typename T>
class TimedDataQueue : public std::deque<T> {
 public:
  TimedDataQueue() : duration_(3.0), time_(ros::Time::now()) {}
  void PushData(const T &data) {
    Poco::ScopedWriteRWLock write_lock(rw_lock_);
    if (this->size() > 2) {
      this->pop_front();
    }
    this->push_back(data);
    time_ = ros::Time::now();
  }

  T GetData() {
    T temp;
    Poco::ScopedReadRWLock read_lock(rw_lock_);
    if (ros::Time::now() - time_ > ros::Duration(duration_) || this->empty()) {
      return temp;
    }
    return this->back();
  }

  T GetData(const double &duration) {
    T temp;
    Poco::ScopedReadRWLock read_lock(rw_lock_);
    if (ros::Time::now() - time_ > ros::Duration(duration) || this->empty()) {
      return temp;
    }
    return this->back();
  }

  bool ClearData() {
    Poco::ScopedWriteRWLock write_lock(rw_lock_);
    this->clear();
    return true;
  }

  void SetDuration(const double &duration) { duration_ = duration; }

 private:
  double duration_;
  ros::Time time_;
  Poco::RWLock rw_lock_;
};

class DataManager final {
 public:
  ~DataManager() {};

  bool CreatStremRequest(const std::string &type);
  std::string GetImageStreamData(const std::string &type);
  ImageInfo GetImageInfo(const std::string &type);
  bool ClearImageInfo(const std::string &type);

  std::string GetBlueMac();

  std::vector<task_t> GetTaskList(const std::string &task_id = "", int refresh = kTaskBuffer); // 0-buffer 1-refresh 2-delete
  std::vector<task_t> GetTasks(const std::string &map_id);

  bool SetRelocation(const relocation_t &data, int type = 0);
  bool GetRelocation(const std::string &id, relocation_t &data);
  bool GetRelocation(const std::string &map_id, std::vector<relocation_t> &datas);
  Json GetRelocation();
  void ResetRelocation();

  bool SetFloor(const Floor &data, int type);
  bool GetFloor(const std::string &map_id, std::vector<Floor> &datas);
  // nlohmann::ordered_json GetFloor();
  std::map<std::string, Floor> GetFloor();

  bool RefreshImage(const std::string &id = "");
  std::vector<uint8_t> GetImage(const std::string &id, int type = kImageLocal); // 0-local 2-map 3-relocation 4-path

  bool DeleteJson(const std::string &type);
  Json GetJson(const std::string &type, const std::string &id = "");
  bool SetJson(const std::string &type, const Json &json);

  bool Init(const CommunicateOption &option);

  bool UpdateOption(const CommunicateOption &option);

  bool CreateSyncAmr(const AmrSyncInfo &info);

  std::shared_ptr<ResourceAgent> GetResourceAgent() {
    return resource_agent_ptr_;
  }

  CommunicateOption GetCommunicateOption() const { return option_; };

  std::string GetSerialNum() const;

  int GetPopup() { return popup_; }
  int GetTraffic() { return traffic_; }
  int GetManualConfirm() { 
    if (ros::Time::now() - confirm_time_ > ros::Duration(0.5)) return 0;
    else return manual_confirm_; 
  }

  template <typename T>
  void RosPublish(const std::string &topic, const T &msg) {
    try {
      auto it = msg_publishers_.find(topic);
      if (it != msg_publishers_.end()) {
        it->second.publish(msg);
      } else {
        LOG_ERROR_STREAM("No publisher found for topic: " << topic);
      }
    } catch (const std::exception &ex) {
      LOG_ERROR(ex.what());
    }
  }

  template <typename T>
  bool RosServiceCall(const std::string &topic, T &msg) {
    try {
      auto it = srv_clients_.find(topic);
      if (it != srv_clients_.end()) {
        if (!it->second.isValid()) {
          return false;
        }
        return it->second.call(msg);
      } else {
        LOG_ERROR_STREAM("No service client found for service: " << topic);
        return false;
      }
    } catch (const std::exception &ex) {
      LOG_ERROR(ex.what());
      return false;
    }
  }

  void setMapName(const std::string &name) { map_name_ = name; }
  std::string GetMapName() { return map_name_; }
  std::string GetMapId() { return map_id_; }
  std::string GetZoneId() { return zone_id_; }

  std::string GetLanguage() { return option_.language; }
  int GetLoraId() { return option_.lora_option.lora_id; }
  void SetLanguage(const std::string &language) { option_.language = language; }

 private:
  bool RosInit();
  bool RelocationInit();
  bool ImagesInit();
  bool FloorInit();

  void Slam3DPoseDevCallback(const cotek_msgs::slam_3d_pose_deviation::ConstPtr &msg);
  void AgvPositionCallback(const cotek_msgs::agv_position::ConstPtr &msg);
  void VelocityCallback(const cotek_msgs::move_feedback::ConstPtr &msg);
  void CmdVelocityCallback(const cotek_msgs::move_cmd::ConstPtr &msg);
  void SafetyStateCallback(const cotek_msgs::safety_states::ConstPtr &msg);
  void FaultReportCallback(const cotek_msgs::fault_report::ConstPtr &msg);
  void BatteryCallback(const cotek_msgs::battery_feedback::ConstPtr &msg);
  void LimitSwitchCallback(const std_msgs::Int32::ConstPtr &msg);
  void LoadsCallback(const cotek_msgs::loads::ConstPtr &msg);
  void PathStateCallback(const cotek_msgs::path_state::ConstPtr &msg);
  void OdomInfoCallback(const cotek_msgs::odom_info::ConstPtr &msg);
  void SelfCheckCallback(const cotek_msgs::self_check::ConstPtr &msg);

  void OdomCallback(const nav_msgs::Odometry::ConstPtr &msg);
  void MotorCallback(const cotek_msgs::motor_feedback::ConstPtr &msg);
  void IoCallback(const cotek_msgs::io_feedback::ConstPtr &msg);
  void EncoderCallback(const cotek_msgs::wire_encoder_feedback::ConstPtr &msg);

  void ScanMatchPointsCallback(const cotek_msgs::points_2d::ConstPtr &msg);
  void ReflectorMapPointsCallback(const cotek_msgs::points_2d::ConstPtr &msg);
  void ReflectorMatchPointsCallback(const cotek_msgs::points_2d::ConstPtr &msg);
  void LostCheckMapPointsCallback(const cotek_msgs::points_2d::ConstPtr &msg);
  void LostCheckMatchPointsCallback(const cotek_msgs::points_2d::ConstPtr &msg);

  void MatchPointsCloudCallback(const sensor_msgs::PointCloud2::ConstPtr &msg);

  void LaserPointsCallback(const sensor_msgs::LaserScan::ConstPtr &msg);
  void Laser3dPointsCallback(const sensor_msgs::PointCloud2::ConstPtr &msg);

  Json MatchPointsToJson(const std::string &type);
  Json Match3dPointsToJson(const std::string &type);
  Json Map3dPointsToJson(const std::string &type);

  Json LaserPointsToJson(const std::string &type);
  Json Laser3dPointsToJson(const std::string &type);

  Json ImageDataToJson(const std::string &data);

  Json ForkStatusToJson(const std::string &type);

  Json MotorDataToJson(const std::string &type);

  Json IoDataToJson(const std::string &type);

  Json EncoderToJson(const std::string &type);

  Json ForkDataToJson(const std::string &type);

  Json QuarySystemInfo(const std::string &type);

  Json TaskPointToJson(const std::vector<common::node_t> &type);
  Json TaskListToJson(const std::string &type, bool detail = false, const std::string &id = "");
  Json TaskInfoToJson(const std::string &type);
  Json TaskRunningToJson(const std::string &id);
  Json PathInfoToJson(const std::string &id);

  void ImageCallback(const sensor_msgs::CompressedImageConstPtr &msg);

  void MapCallback(const nav_msgs::OccupancyGrid::ConstPtr &msg);
  void Map3dCallback(const sensor_msgs::PointCloud2::ConstPtr &msg);

  void WeightAreaCallback(const cotek_msgs::area::ConstPtr &msg);

  void HighAreaCallback(const cotek_msgs::area::ConstPtr &msg);

  void EncoderToForkStatus(const std::string &frame, const double &value);

  void TtrafficAreaCallback(const cotek_msgs::traffic_state::ConstPtr &msg);

  void UpdateEventCallback(const cotek_msgs::update_event::ConstPtr &msg);

  void LearnStateCallback(const cotek_msgs::learning_state::ConstPtr &msg);

  void TaskInfoCallback(const cotek_msgs::task_info::ConstPtr &msg);


  DECLARE_SINGLETON(DataManager);
  DataManager();

  std::vector<ros::Subscriber> sub_vec_;
  std::map<std::string, ros::Publisher> pub_map_;

  std::map<std::string, std::shared_ptr<ImageStreamer>> image_strem_map_;
  std::map<std::string,
           std::unique_ptr<TimedDataQueue<sensor_msgs::PointCloud2>>>
      map3d_points_map_;
  std::map<std::string, std::unique_ptr<TimedDataQueue<sensor_msgs::LaserScan>>>
      laser_points_map_;
  std::map<std::string,
           std::unique_ptr<TimedDataQueue<sensor_msgs::PointCloud2>>>
      laser_3d_points_map_;
  std::map<std::string, std::unique_ptr<TimedDataQueue<cotek_msgs::points_2d>>>
      match_points_map_;
  std::map<std::string,
           std::unique_ptr<TimedDataQueue<sensor_msgs::PointCloud2>>>
      match_3d_points_map_;

  std::map<std::string, std::unique_ptr<TimedDataQueue<Json>>> json_map_;

  std::map<std::string, std::unique_ptr<TimedDataQueue<Json>>> motor_map_;

  std::map<std::string, std::unique_ptr<TimedDataQueue<Json>>> io_map_;

  std::map<std::string, std::unique_ptr<TimedDataQueue<Json>>> encoder_map_;

  std::map<std::string, std::unique_ptr<TimedDataQueue<Json>>> fork_status_map_;

  ForkStatus fork_status_;

  SystemInfo system_info_;
  CommunicateOption option_;

  std::unique_ptr<AmrSyncManager> amr_sync_manager_ptr_;

  std::map<std::string, ros::Publisher> msg_publishers_;
  std::map<std::string, ros::ServiceClient> srv_clients_;

  std::shared_ptr<ResourceAgent> resource_agent_ptr_;

  std::string map_name_;
  std::string map_id_;
  std::string zone_id_;
  int map_width_{0};
  int map_height_{0};

  float last_odom_{0.0};
  Pose pose_;
  uint32_t point_index_{0};
  std::string last_id_;
  double remain_distance_{0.0};
  double remain_time_{0.0};
  double vel_{0.0};
  task_info_t task_info_;
  std::map<std::string, Floor> floors_;
  std::map<std::string, common::relocation_t> relocations_;
  std::map<std::string, std::vector<uint8_t>> images_;
  std::map<std::string, task_t> tasks_;
  ros::Time task_info_time_;
  std::mutex task_mtx_;
  int popup_{0};
  int traffic_{0};
  int manual_confirm_{0};

  ros::Time confirm_time_{ros::Time::now()};

  // std::shared_mutex shared_mutex_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_DATA_MANAGER_H_
