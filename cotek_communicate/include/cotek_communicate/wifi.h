#include <iostream>
#include <vector>
#include <string>
#include <cstdio>
#include <sstream>
#include <map>

// 定义WiFi网络信息结构
struct WifiNetwork {
    bool vaild{false};
    std::string ssid{""};
    std::string bssid{""};
    int signalStrength{-1};
    std::string security{""};
};

struct WifiStatus {
    bool vaild{false};
    std::string state{"disconnect"};
    std::string ssid{""};
    std::string ip{""};
    int signal{-1};
    std::string security{""};
    std::string mac{""};
    std::string bandwidth{""};
};

class WiFi {
public:
    WiFi() = default;
    ~WiFi() {};

    std::vector<WifiNetwork> scan();
    bool DHCPConnect(const std::string& ssid, const std::string& password);
    bool StaticConnect(const std::string& ssid,
                       const std::string& password,
                       const std::string& ip,
                       const std::string& netmask,
                       const std::string& gateway,
                       const std::string& dns);    
    bool disconnectWiFi();
    WifiStatus getStatus();
    bool forgetCurrentWifi();
    
private:

    std::string execCommand(const std::string& cmd);

};
 