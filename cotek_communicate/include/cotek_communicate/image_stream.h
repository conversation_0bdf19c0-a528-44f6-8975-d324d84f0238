/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_NAVIGATION_INCLUDE_COTEK_COMMUNICATE_IMAGE_STREAM_H_
#define COTEK_NAVIGATION_INCLUDE_COTEK_COMMUNICATE_IMAGE_STREAM_H_

#include <Poco/RWLock.h>
#include <ros/ros.h>
#include <sensor_msgs/CompressedImage.h>

#include <ios>
#include <iostream>
#include <memory>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <sstream>
#include <string>
#include <vector>

namespace cotek_communicate {

struct ImageInfo {
  double resolution{0.};
  uint32_t width{0};
  uint32_t height{0};
  double orign_x{0.};
  double orign_y{0.};
};

class ImageStreamer {
 public:
  explicit ImageStreamer(const std::string &topic) : topic_(topic){};

  virtual ~ImageStreamer(){};

  virtual void SetData(const std::vector<std::int8_t> &data) = 0;

  virtual std::string GetData() = 0;

  virtual bool ClearData() = 0;

  std::string GetTopic() { return topic_; };

  void SetImageInfo(const ImageInfo &info) { info_ = info; }

  ImageInfo GetImageInfo() { return info_; }

  void FlushTime() { last_time_ = ros::Time::now(); }

  virtual bool CheckTimeout() {
    return (ros::Time::now() - last_time_ > ros::Duration(1.0));
  }

 protected:
  ImageInfo info_;
  ros::Time last_time_;

 private:
  std::string topic_;
};

class RosCompressedStream : public ImageStreamer {
 public:
  RosCompressedStream() = delete;
  RosCompressedStream(const RosCompressedStream &) = delete;
  RosCompressedStream(const RosCompressedStream &&) = delete;
  void operator=(const RosCompressedStream &) = delete;

  explicit RosCompressedStream(const std::string &topic)
      : ImageStreamer(topic), request_cnt_(0) {}
  ~RosCompressedStream() {}

  void SetData(const std::vector<std::int8_t> &data) override {
    Poco::ScopedWriteRWLock write_lock(rw_lock_);
    image_data_ = data;
    FlushTime();
  }

  std::string GetData() override;

  bool ClearData() override {
    Poco::ScopedWriteRWLock write_lock(rw_lock_);
    image_data_.clear();
    return true;
  }

 private:
  std::vector<int8_t> image_data_;
  uint8_t request_cnt_;

  Poco::RWLock rw_lock_;
};

class MapStream : public ImageStreamer {
 public:
  MapStream() = delete;
  MapStream(const MapStream &) = delete;
  MapStream(const MapStream &&) = delete;
  void operator=(const MapStream &) = delete;

  explicit MapStream(const std::string &topic)
      : ImageStreamer(topic), request_cnt_(0) {}
  ~MapStream() {}

  void SetData(const std::vector<std::int8_t> &data) override {
    Poco::ScopedWriteRWLock write_lock(rw_lock_);
    image_data_ = data;
    FlushTime();
  }

  bool ClearData() override {
    Poco::ScopedWriteRWLock write_lock(rw_lock_);
    image_data_.clear();
    return true;
  }

  bool CheckTimeout() override;

  std::string GetData() override;

 private:
  cv::Mat ConverOccupancyGridToPngMat(const std::vector<int8_t> &data,
                                      const ImageInfo &info);

  std::vector<int8_t> image_data_;

  uint8_t request_cnt_;

  Poco::RWLock rw_lock_;
};

}  // namespace cotek_communicate

#endif  // COTEK_NAVIGATION_INCLUDE_COTEK_COMMUNICATE_IMAGE_STREAM_H_