/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */
#ifndef COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_AMR_SYNC_H_
#define COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_AMR_SYNC_H_
#include <ros/ros.h>

#include <array>
#include <atomic>
#include <cstdint>
#include <iostream>
#include <memory>
#include <mutex>
#include <thread>

#include "cotek_common/geometry/cotek_geometry.h"
#include "cotek_common/geometry/pose.h"
#include "cotek_common/nlohmann/json.hpp"
#include "cotek_common/thread_pool.h"
#include "ros/publisher.h"
#include "ros/subscriber.h"

namespace cotek_communicate {
using Pose = cotek_geometry::Pose;

struct AmrSyncInfo {
  std::string name;
  std::string ip;
  int port{0};
  std::vector<Pose> box_poses;
};

struct AmrSyncData {
  bool vaild{false};
  Pose pose;
  std::string agv_state;
  uint8_t type{0};
  double velocity{0.};
};

class AmrSync {
 public:
  explicit AmrSync(const AmrSyncInfo& info);
  ~AmrSync();

  AmrSyncData GetData() const { return amr_data_; }
  AmrSyncInfo GetInfo() const { return amr_info_; }

  bool GetSyncFlag() const { return sync_ing_; }
  bool GetStopWebSync() const { return stop_web_sync_; }

  bool IsInSyncArea();
  void DataSync();

  bool IsOffline();
  bool IsOutArea();

  bool IsNotSyncState();

  bool IsTimieout();

  bool AmrShouldDelete();
  bool Stop();

 private:
  void BuildDataChannel();

  uint8_t sync_failed_cnt_{0};
  uint8_t pose_failed_cnt_{0};
  bool sync_ing_;
  AmrSyncInfo amr_info_;
  AmrSyncData amr_data_;
  std::mutex mutex_;
  bool stop_web_sync_{false};
  ros::Time start_time_;
  cotek_common::ThreadPool thread_pool_;
};

class AmrSyncManager {
 public:
  AmrSyncManager();
  ~AmrSyncManager();

  bool CreateSyncAmr(const AmrSyncInfo& info);

  bool DeleteSyncAmr(const AmrSyncInfo& info);

 private:
  void Run();

  ros::Publisher data_pub_;

  std::shared_ptr<std::thread> executor_;
  AmrSyncInfo amr_info_;
  std::mutex mutex_;
  std::map<std::string, std::shared_ptr<AmrSync>> sync_amrs_;
};

}  // namespace cotek_communicate

#endif  // COTEK_COMMUNICATE_INCLUDE_COTEK_COMMUNICATE_SYSTEM_INFO_H_