cmake_minimum_required(VERSION 2.8.3)
project(cotek_communicate)

cmake_policy(SET CMP0046 OLD)

## Compile as C++11, supported in ROS Kinetic and newer
add_compile_options(-std=c++14 -g)

#set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O2")  # 中等优化

# set(CMAKE_BUILD_TYPE "Debug")   # Debug Release

# set(CMAKE_C_FLAGS "-Wall")
# set(CMAKE_C_FLAGS "-lpthread")
# set(CMAKE_C_FLAGS "-fms-extensions")
# set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS} -O0 -g -ggdb")
# set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS} -O3 -DNDEBUG")

# if(CMAKE_COMPILER_IS_GNUCXX)
#     set(CMAKE_CXX_FLAGS "-std=c++14")
#     set(CMAKE_CXX_FLAGS "-lpthread")
#     set(CMAKE_CXX_FLAGS "-Wall")
#     set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS} -O0 -g -ggdb")
#     set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS} -O3") 
# endif(CMAKE_COMPILER_IS_GNUCXX)



# Release should not output anything
# if (CMAKE_BUILD_TYPE STREQUAL Release)
#     add_definitions(
#         -DROSCONSOLE_MIN_SEVERITY=ROSCONSOLE_SEVERITY_NONE
#     )
# endif ()

# enable plooc compile with c++
# add_definitions(-D__OOC_CPP__)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  roslib
  std_msgs
  pcl_conversions
  tf
  message_generation
  cotek_cmake
  cotek_msgs
  cotek_msgs
  cotek_common
  cotek_path
  serial
  # 注意！！ pcl库与poco库有严重冲突会导致莫名bug
  # pcl_ros
  # pcl_conversions
  # pcl_msgs
)

## System dependencies are found with CMake's conventions
# find_package(Boost REQUIRED COMPONENTS system)
find_package(OpenSSL REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(Poco REQUIRED COMPONENTS Foundation Net Zip Util)
find_package(OpenCV CONFIG REQUIRED)
find_package(PkgConfig REQUIRED)
# 查找蓝牙库
pkg_check_modules(DBUS REQUIRED dbus-1)

if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
  set(BLUETOOTH_LIBRARIES /usr/lib/aarch64-linux-gnu/libbluetooth.so)
elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
  set(BLUETOOTH_LIBRARIES /usr/lib/x86_64-linux-gnu/libbluetooth.so)
endif()

# find_package(Bluetooth REQUIRED)
message(" OpenCV_FOUND  ${OpenCV_FOUND} !!")

link_directories(${CMAKE_PREFIX_PATH}/lib)

## Uncomment this if the package has a setup.py. This macro ensures
## modules and global scripts declared therein get installed
## See http://ros.org/doc/api/catkin/html/user_guide/setup_dot_py.html
# catkin_python_setup()

################################################
## Declare ROS messages, services and actions ##
################################################

## To declare and build messages, services or actions from within this
## package, follow these steps:
## * Let MSG_DEP_SET be the set of packages whose message types you use in
##   your messages/services/actions (e.g. std_msgs, actionlib_msgs, ...).
## * In the file package.xml:
##   * add a build_depend tag for "message_generation"
##   * add a build_depend and a exec_depend tag for each package in MSG_DEP_SET
##   * If MSG_DEP_SET isn't empty the following dependency has been pulled in
##     but can be declared for certainty nonetheless:
##     * add a exec_depend tag for "message_runtime"
## * In this file (CMakeLists.txt):
##   * add "message_generation" and every package in MSG_DEP_SET to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * add "message_runtime" and every package in MSG_DEP_SET to
##     catkin_package(CATKIN_DEPENDS ...)
##   * uncomment the add_*_files sections below as needed
##     and list every .msg/.srv/.action file to be processed
##   * uncomment the generate_messages entry below
##   * add every package in MSG_DEP_SET to generate_messages(DEPENDENCIES ...)

## Generate messages in the 'msg' folder
# add_message_files(
#   FILES
#   Message1.msg
#   Message2.msg
# )

## Generate services in the 'srv' folder
# add_service_files(
#   FILES
#   Service1.srv
#   Service2.srv
# )

## Generate actions in the 'action' folder
# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

## Generate added messages and services with any dependencies listed here
# generate_messages(
#   DEPENDENCIES
#   std_msgs  # Or other packages containing msgs
# )

################################################
## Declare ROS dynamic reconfigure parameters ##
################################################

## To declare and build dynamic reconfigure parameters within this
## package, follow these steps:
## * In the file package.xml:
##   * add a build_depend and a exec_depend tag for "dynamic_reconfigure"
## * In this file (CMakeLists.txt):
##   * add "dynamic_reconfigure" to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * uncomment the "generate_dynamic_reconfigure_options" section below
##     and list every .cfg file to be processed

## Generate dynamic reconfigure parameters in the 'cfg' folder
# generate_dynamic_reconfigure_options(
#   cfg/DynReconf1.cfg
#   cfg/DynReconf2.cfg
# )

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if your package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
catkin_package(
#  INCLUDE_DIRS include
 LIBRARIES cotek_communicate
 CATKIN_DEPENDS roscpp pcl_conversions
#  DEPENDS system_lib
)

###########
## Build ##
###########
link_directories(${CATKIN_DEVEL_PREFIX}/../cotek_path/lib)

## Specify additional locations of header files
## Your package locations should be listed before other locations
include_directories(${OPENSSL_INCLUDE_DIR})
include_directories(
  src
  include

  include/cotek_communicate/communicate_method/mqtt

  ${catkin_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  ${DBUS_INCLUDE_DIRS}
)


## Declare a C++ library
add_library(${PROJECT_NAME}
  src/wifi.cc
  src/communicate.cc
  src/data_manager.cc
  src/image_stream.cc
  src/system_info.cc
  src/resource_agent.cc
  include/cotek_communicate/communicate_method/lora/lora_manager.cc
  src/amr_sync.cc
  include/cotek_communicate/communicate_method/http/http_handle_factory.cc
  include/cotek_communicate/protocl_message_factory/protocl_message_v1.cc
  include/cotek_communicate/protocl_message_factory/protocl_message_vda5050.cc
  include/cotek_communicate/protocl_message_factory/protocl_message_lora.cc

)

## Add cmake target dependencies of the library
## as an example, code may need to be generated before libraries
## either from message generation or dynamic reconfigure
add_dependencies(${PROJECT_NAME} ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## Declare a C++ executable
## With catkin_make all packages are built within a single CMake context
## The recommended prefix ensures that target names across packages don't collide
add_executable(${PROJECT_NAME}_node src/cotek_communicate_node.cc)
add_dependencies(${PROJECT_NAME}_node cotek_msgs_gencpp)

## Rename C++ executable without prefix
## The above recommended prefix causes long target names, the following renames the
## target back to the shorter version for ease of user use
## e.g. "rosrun someones_pkg node" instead of "rosrun someones_pkg someones_pkg_node"
# set_target_properties(${PROJECT_NAME}_node PROPERTIES OUTPUT_NAME node PREFIX "")

## Add cmake target dependencies of the executable
## same as for the library above
add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS} )

if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
  target_link_libraries(${PROJECT_NAME}
    -lpthread
    ${catkin_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${Poco_LIBRARIES}
    # pcl_ros
    ${OpenCV_LIBS}
    ${BLUETOOTH_LIBRARIES}
    ${DBUS_LIBRARIES}
  )
  
  ## Specify libraries to link a library or executable target against
  target_link_libraries(${PROJECT_NAME}_node
    -lpthread
    ${PROJECT_NAME}
    ${catkin_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${Poco_LIBRARIES}
    ${OpenCV_LIBS}
    ${BLUETOOTH_LIBRARIES}
    ${DBUS_LIBRARIES}
    path_db
  )
elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
  target_link_libraries(${PROJECT_NAME}
    paho-mqttpp3
    -lpthread
    ${catkin_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${Poco_LIBRARIES}
    # pcl_ros
    ${OpenCV_LIBS}
    ${BLUETOOTH_LIBRARIES}
    ${DBUS_LIBRARIES}
  )
  
  ## Specify libraries to link a library or executable target against
  target_link_libraries(${PROJECT_NAME}_node
    paho-mqttpp3
    -lpthread
    ${PROJECT_NAME}
    ${catkin_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${Poco_LIBRARIES}
    ${OpenCV_LIBS}
    ${BLUETOOTH_LIBRARIES}
    ${DBUS_LIBRARIES} 
    path_db
  )
endif()

#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# install(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark executables and/or libraries for installation
install(TARGETS ${PROJECT_NAME} ${PROJECT_NAME}_node
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

## Mark cpp header files for installation
install(DIRECTORY include/${PROJECT_NAME}/
  DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
  FILES_MATCHING PATTERN "*.h"
  PATTERN ".git" EXCLUDE
)

## Mark other files for installation (e.g. launch and bag files, etc.)
install(FILES
  launch/cotek_communicate.launch
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
)

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_cotek_communicate.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)


## Add gtest based cpp test target and link libraries
if (CATKIN_ENABLE_TESTING)
  find_package(rostest REQUIRED)
  find_package(GTest REQUIRED)
  # include_directories(include ${catkin_INCLUDE_DIRS} ${GTEST_INCLUDE_DIRS})
   catkin_package()
  catkin_add_gtest(${PROJECT_NAME}-test
    # test/cotek_navigation.test
    test/test_main.cc
    # test/test_straight_path.cc
    test/test_protocl_message_vda5050.cc
  )
  
  target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME} ${catkin_LIBRARIES})
  
  include(CodeCoverage)
    coverage_add_target(run_tests_${PROJECT_NAME})
endif()

