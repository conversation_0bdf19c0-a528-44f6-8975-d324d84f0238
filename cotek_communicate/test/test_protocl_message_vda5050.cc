/**
 * Copyright (c) 2023 COTEK Inc. All rights reserved.
 */

#include "cotek_communicate/protocl_message_factory/protocl_message_vda5050.h"
#include <angles/angles.h>
#include <cstring>
#include <gtest/gtest.h>
#include <stdint.h>
#include <stdio.h>

namespace cotek_communicate {

TEST(ProtoclMessageVda5050, ParseMsgToJson) {
  CommunicateOption option;
  ProtoclMessageVda5050 protocl(option);

  char message[] =
      "{\"agvId\":12,\"sequenceNum\":0, \"msgType\": \"order\",\"taskType\":"
      "\"FinishRequest\",\"state\":1}";

  std::cout << std::strlen(message) << std::endl;

  std::memcpy(protocl.Data(), reinterpret_cast<char *>(&message),
              std::strlen(message));

  bool ret =  protocl.ParseMsgToJson();
  EXPECT_EQ(ret, true);

  auto agvId = protocl.MsgDataJson()["agvId"].get<int>();
  EXPECT_EQ(12, agvId);
}

} // namespace cotek_communicate
